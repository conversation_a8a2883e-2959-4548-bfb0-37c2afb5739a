#!/usr/bin/env python3
"""
SmartData模板引擎Kafka消息插件使用示例 (修复版)

展示Kafka消息处理的概念和模拟实现：
1. 消息生产和消费
2. 主题管理
3. 消息序列化
4. 错误处理和重试
"""

import sys
import os
import time
import json
import random
from datetime import datetime
from typing import Dict, Any, List
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.template_ext import create_template_engine

class MockKafkaClient:
    """模拟Kafka客户端"""
    
    def __init__(self):
        self.topics = {}
        self.messages = []
        self.metrics = {
            'messages_produced': 0,
            'messages_consumed': 0,
            'errors': 0
        }
    
    def create_topic(self, topic_name: str, partitions: int = 3, replication: int = 1) -> Dict[str, Any]:
        """创建主题"""
        if topic_name not in self.topics:
            self.topics[topic_name] = {
                'partitions': partitions,
                'replication': replication,
                'messages': [],
                'created_at': datetime.now().isoformat()
            }
            return {'success': True, 'topic': topic_name}
        else:
            return {'success': False, 'error': f'Topic {topic_name} already exists'}
    
    def produce_message(self, topic: str, message: Dict[str, Any], key: str = None) -> Dict[str, Any]:
        """生产消息"""
        try:
            if topic not in self.topics:
                self.create_topic(topic)
            
            msg = {
                'id': f'msg_{len(self.messages)}_{int(time.time())}',
                'topic': topic,
                'key': key,
                'value': message,
                'timestamp': datetime.now().isoformat(),
                'partition': random.randint(0, self.topics[topic]['partitions'] - 1),
                'offset': len(self.topics[topic]['messages'])
            }
            
            self.topics[topic]['messages'].append(msg)
            self.messages.append(msg)
            self.metrics['messages_produced'] += 1
            
            return {
                'success': True,
                'message_id': msg['id'],
                'partition': msg['partition'],
                'offset': msg['offset']
            }
            
        except Exception as e:
            self.metrics['errors'] += 1
            return {'success': False, 'error': str(e)}
    
    def consume_messages(self, topic: str, count: int = 10) -> Dict[str, Any]:
        """消费消息"""
        try:
            if topic not in self.topics:
                return {'success': False, 'error': f'Topic {topic} does not exist'}
            
            messages = self.topics[topic]['messages'][-count:] if count > 0 else self.topics[topic]['messages']
            self.metrics['messages_consumed'] += len(messages)
            
            return {
                'success': True,
                'messages': messages,
                'count': len(messages),
                'topic': topic
            }
            
        except Exception as e:
            self.metrics['errors'] += 1
            return {'success': False, 'error': str(e)}
    
    def get_topic_info(self, topic: str) -> Dict[str, Any]:
        """获取主题信息"""
        if topic in self.topics:
            topic_data = self.topics[topic].copy()
            topic_data['message_count'] = len(topic_data['messages'])
            topic_data['latest_message'] = topic_data['messages'][-1] if topic_data['messages'] else None
            return {'success': True, 'topic_info': topic_data}
        else:
            return {'success': False, 'error': f'Topic {topic} not found'}

def kafka_messaging_examples():
    """Kafka消息处理完整示例"""
    print("=== SmartData模板引擎Kafka消息插件示例 (修复版) ===")
    
    # 创建模板引擎和模拟Kafka客户端
    engine = create_template_engine()
    kafka_client = MockKafkaClient()
    
    # 1. 主题管理示例
    print("\n📋 1. 主题管理示例")
    
    # 创建主题
    topics_to_create = ['user_events', 'order_events', 'system_logs']
    topic_results = []
    
    for topic in topics_to_create:
        result = kafka_client.create_topic(topic, partitions=3, replication=1)
        topic_results.append({'topic': topic, 'result': result})
    
    topic_template = """
Kafka主题管理:
=============

📋 主题创建结果:
{%- for item in topics %}
- {{ item.topic }}: 
  {%- if item.result.success -%}
  ✅ 创建成功
  {%- else -%}
  ❌ {{ item.result.error }}
  {%- endif -%}
{%- endfor %}

📊 主题配置:
- 分区数: 3
- 副本数: 1
- 消息保留: 7天 (默认)
- 压缩类型: gzip

💡 主题设计原则:
- 按业务领域划分主题
- 合理设置分区数量
- 考虑消息顺序要求
- 设置适当的保留策略
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(topic_template, topics=topic_results)
    print(result)
    
    # 2. 消息生产示例
    print("\n📤 2. 消息生产示例")
    
    # 生产用户事件消息
    user_events = [
        {'user_id': 1001, 'action': 'login', 'timestamp': datetime.now().isoformat()},
        {'user_id': 1002, 'action': 'purchase', 'amount': 99.99, 'timestamp': datetime.now().isoformat()},
        {'user_id': 1003, 'action': 'logout', 'timestamp': datetime.now().isoformat()}
    ]
    
    produce_results = []
    for event in user_events:
        result = kafka_client.produce_message('user_events', event, key=str(event['user_id']))
        produce_results.append({'event': event, 'result': result})
    
    produce_template = """
消息生产结果:
============

📤 生产统计:
- 总消息数: {{ results | length }}
- 成功数: {{ results | selectattr('result.success') | list | length }}
- 失败数: {{ results | rejectattr('result.success') | list | length }}

📋 消息详情:
{%- for item in results %}
{{ loop.index }}. 用户{{ item.event.user_id }} - {{ item.event.action }}:
   {%- if item.result.success -%}
   ✅ 分区{{ item.result.partition }}, 偏移量{{ item.result.offset }}
   {%- else -%}
   ❌ {{ item.result.error }}
   {%- endif -%}
{%- endfor %}

⚡ 生产配置:
- 批量大小: 16KB
- 压缩类型: gzip
- 确认级别: all
- 重试次数: 3
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(produce_template, results=produce_results)
    print(result)
    
    # 3. 消息消费示例
    print("\n📥 3. 消息消费示例")
    
    # 消费消息
    consume_result = kafka_client.consume_messages('user_events', count=5)
    
    consume_template = """
消息消费结果:
============

📥 消费统计:
{%- if result.success -%}
- 主题: {{ result.topic }}
- 消费消息数: {{ result.count }}
- 消费状态: ✅ 成功

📋 消息内容:
{%- for msg in result.messages %}
{{ loop.index }}. 消息ID: {{ msg.id }}
   - 分区: {{ msg.partition }}, 偏移量: {{ msg.offset }}
   - 键: {{ msg.key }}
   - 内容: {{ msg.value | tojson }}
   - 时间: {{ msg.timestamp }}
{%- endfor %}
{%- else -%}
❌ 消费失败: {{ result.error }}
{%- endif -%}

⚙️ 消费配置:
- 消费组: template_processor
- 自动提交: true
- 会话超时: 30s
- 心跳间隔: 3s
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(consume_template, result=consume_result)
    print(result)
    
    # 4. 主题监控示例
    print("\n📊 4. 主题监控示例")
    
    # 获取所有主题信息
    topic_infos = []
    for topic_name in kafka_client.topics.keys():
        info_result = kafka_client.get_topic_info(topic_name)
        if info_result['success']:
            topic_infos.append(info_result['topic_info'])
    
    monitoring_template = """
Kafka集群监控:
=============

📊 集群概览:
- 主题数量: {{ topics | length }}
- 总消息数: {{ topics | map(attribute='message_count') | sum }}
- 总分区数: {{ topics | map(attribute='partitions') | sum }}

📋 主题详情:
{%- for topic in topics %}
🔸 {{ topic.messages[0].topic if topic.messages else '未知主题' }}:
   - 消息数: {{ topic.message_count }}
   - 分区数: {{ topic.partitions }}
   - 副本数: {{ topic.replication }}
   - 创建时间: {{ topic.created_at }}
   {%- if topic.latest_message -%}
   - 最新消息: {{ topic.latest_message.timestamp }}
   {%- endif -%}
{%- endfor %}

⚡ 系统指标:
- 生产消息数: {{ metrics.messages_produced }}
- 消费消息数: {{ metrics.messages_consumed }}
- 错误次数: {{ metrics.errors }}
- 成功率: {{ ((metrics.messages_produced - metrics.errors) / metrics.messages_produced * 100) | round(2) if metrics.messages_produced > 0 else 0 }}%

💡 监控建议:
- 监控消息积压情况
- 关注消费延迟指标
- 检查分区均衡性
- 监控磁盘使用率
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(monitoring_template, topics=topic_infos, metrics=kafka_client.metrics)
    print(result)
    
    # 5. 错误处理和重试示例
    print("\n🛡️ 5. 错误处理和重试示例")
    
    # 模拟错误场景
    error_scenarios = [
        {'scenario': '无效主题', 'action': lambda: kafka_client.consume_messages('invalid_topic')},
        {'scenario': '空消息', 'action': lambda: kafka_client.produce_message('user_events', None)},
        {'scenario': '正常消息', 'action': lambda: kafka_client.produce_message('user_events', {'test': 'data'})}
    ]
    
    error_results = []
    for scenario in error_scenarios:
        try:
            result = scenario['action']()
            error_results.append({'scenario': scenario['scenario'], 'result': result})
        except Exception as e:
            error_results.append({'scenario': scenario['scenario'], 'result': {'success': False, 'error': str(e)}})
    
    error_template = """
错误处理测试:
============

🛡️ 错误场景测试:
{%- for test in tests %}
{{ loop.index }}. {{ test.scenario }}:
   {%- if test.result.success -%}
   ✅ 处理成功
   {%- else -%}
   ❌ {{ test.result.error }}
   {%- endif -%}
{%- endfor %}

🔧 错误处理机制:
- 自动重试: 3次
- 退避策略: 指数退避
- 死信队列: 失败消息转发
- 监控告警: 错误率阈值

💡 最佳实践:
- 实现幂等性处理
- 设置合理的超时时间
- 使用事务保证一致性
- 监控和告警机制
- 优雅降级策略
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(error_template, tests=error_results)
    print(result)
    
    # 6. Kafka架构示例
    print("\n🏗️ 6. Kafka架构示例")
    
    architecture_template = """
Kafka消息架构:
=============

🏗️ 架构组件:
1. Broker集群:
   - 多个Kafka节点
   - 分布式存储
   - 自动故障转移

2. 主题和分区:
   - 逻辑消息分类
   - 水平扩展单位
   - 并行处理能力

3. 生产者:
   - 消息发送
   - 分区策略
   - 批量优化

4. 消费者:
   - 消息接收
   - 消费组管理
   - 偏移量控制

📊 性能特性:
- 高吞吐量: > 100K msg/sec
- 低延迟: < 10ms
- 持久化: 磁盘存储
- 可扩展: 水平扩展

🔧 配置优化:
{
    "broker": {
        "num.network.threads": 8,
        "num.io.threads": 8,
        "socket.send.buffer.bytes": 102400,
        "socket.receive.buffer.bytes": 102400
    },
    "producer": {
        "batch.size": 16384,
        "linger.ms": 5,
        "compression.type": "gzip"
    },
    "consumer": {
        "fetch.min.bytes": 1,
        "fetch.max.wait.ms": 500,
        "max.poll.records": 500
    }
}

💡 使用场景:
- 实时数据流处理
- 微服务间通信
- 日志收集和分析
- 事件驱动架构
- 数据管道构建
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(architecture_template)
    print(result)
    
    print("\n📊 功能总结:")
    print("📋 主题管理: 创建、配置、监控")
    print("📤 消息生产: 批量发送、分区策略、压缩优化")
    print("📥 消息消费: 消费组、偏移量管理、并行处理")
    print("📊 集群监控: 性能指标、健康检查、告警")
    print("🛡️ 错误处理: 重试机制、死信队列、降级策略")
    
    print("\n💡 使用要点:")
    print("✅ 高吞吐 - 支持大规模消息处理")
    print("✅ 可靠性 - 持久化存储和副本机制")
    print("✅ 可扩展 - 水平扩展和负载均衡")
    print("✅ 实时性 - 低延迟消息传递")

if __name__ == "__main__":
    try:
        kafka_messaging_examples()
        print("\n🎉 Kafka消息插件示例运行完成！")
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
        print("\n💡 这是一个概念演示，实际Kafka需要相应的服务器环境")
