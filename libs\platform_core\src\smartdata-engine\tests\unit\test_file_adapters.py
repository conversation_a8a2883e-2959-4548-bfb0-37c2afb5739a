"""
文件适配器单元测试（HTML、XML、增强CSV/JSON）
"""

import unittest
import asyncio
import tempfile
import os
from unittest.mock import Mock, patch

import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from core.adapters.file.html_adapter import HTMLAdapter
from core.adapters.file.xml_adapter import XMLAdapter
from core.data_contract import DataResult


class TestHTMLAdapter(unittest.TestCase):
    """HTML适配器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.adapter = HTMLAdapter()
        
        # 创建测试HTML文件
        self.test_html = """
        <!DOCTYPE html>
        <html>
        <head><title>Test Page</title></head>
        <body>
            <h1>Test Data</h1>
            <table>
                <tr><th>ID</th><th>Name</th><th>Age</th></tr>
                <tr><td>1</td><td>Alice</td><td>30</td></tr>
                <tr><td>2</td><td>Bob</td><td>25</td></tr>
            </table>
            <div class="content">
                <p>Paragraph 1</p>
                <p>Paragraph 2</p>
            </div>
        </body>
        </html>
        """
        
        self.temp_file = None
    
    def tearDown(self):
        """测试后清理"""
        if self.temp_file and os.path.exists(self.temp_file):
            os.unlink(self.temp_file)
    
    def _create_temp_html_file(self, content=None):
        """创建临时HTML文件"""
        if content is None:
            content = self.test_html
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
            f.write(content)
            self.temp_file = f.name
        
        return self.temp_file
    
    def test_supported_types(self):
        """测试支持的类型"""
        types = self.adapter.supported_types()
        
        expected_types = ['html', 'html_file', 'htm', 'htm_file', 'web_page']
        for expected_type in expected_types:
            self.assertIn(expected_type, types)
    
    def test_can_handle_string(self):
        """测试字符串数据源识别"""
        self.assertTrue(self.adapter.can_handle('test.html'))
        self.assertTrue(self.adapter.can_handle('test.htm'))
        self.assertTrue(self.adapter.can_handle('file://test.html'))
        self.assertFalse(self.adapter.can_handle('test.txt'))
        self.assertFalse(self.adapter.can_handle('test.csv'))
    
    def test_can_handle_dict(self):
        """测试字典数据源识别"""
        self.assertTrue(self.adapter.can_handle({'file_path': 'test.html'}))
        self.assertTrue(self.adapter.can_handle({'path': 'test.htm'}))
        self.assertFalse(self.adapter.can_handle({'url': 'http://example.com'}))
        self.assertFalse(self.adapter.can_handle({'data': [1, 2, 3]}))
    
    @patch('core.adapters.file.html_adapter.BS4_AVAILABLE', True)
    def test_sync_read_html_with_bs4(self):
        """测试同步HTML读取（有BeautifulSoup）"""
        html_file = self._create_temp_html_file()
        
        # 模拟BeautifulSoup可用
        with patch('core.adapters.file.html_adapter.BeautifulSoup') as mock_bs:
            mock_soup = Mock()
            mock_soup.find_all.return_value = []  # 没有表格
            mock_soup.get_text.return_value = "Test content"
            mock_title = Mock()
            mock_title.string = "Test Page"
            mock_soup.title = mock_title
            mock_bs.return_value = mock_soup

            result = self.adapter._sync_read_html(html_file)

            self.assertIsInstance(result, list)
            self.assertGreater(len(result), 0)
            # 应该返回文本内容
            self.assertIn('content', result[0])
            self.assertEqual(result[0]['content'], "Test content")
    
    @patch('core.adapters.file.html_adapter.BS4_AVAILABLE', False)
    def test_sync_read_html_without_bs4(self):
        """测试同步HTML读取（无BeautifulSoup）"""
        html_file = self._create_temp_html_file()
        
        with self.assertRaises(ImportError):
            self.adapter._sync_read_html(html_file)
    
    def test_sync_write_html(self):
        """测试同步HTML写入"""
        test_data = [
            {'id': 1, 'name': 'Alice', 'age': 30},
            {'id': 2, 'name': 'Bob', 'age': 25}
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            html_file = f.name
        
        try:
            result = self.adapter._sync_write_html(html_file, test_data)
            
            self.assertEqual(result, 2)  # 写入了2条记录
            self.assertTrue(os.path.exists(html_file))
            
            # 检查文件内容
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
                self.assertIn('<table>', content)
                self.assertIn('Alice', content)
                self.assertIn('Bob', content)
        finally:
            if os.path.exists(html_file):
                os.unlink(html_file)
    
    def test_generate_html_table(self):
        """测试HTML表格生成"""
        test_data = [
            {'name': 'Alice', 'age': 30},
            {'name': 'Bob', 'age': 25}
        ]
        
        html_content = self.adapter._generate_html_table(test_data)
        
        self.assertIn('<!DOCTYPE html>', html_content)
        self.assertIn('<table>', html_content)
        self.assertIn('<th>name</th>', html_content)
        self.assertIn('<th>age</th>', html_content)
        self.assertIn('<td>Alice</td>', html_content)
        self.assertIn('<td>30</td>', html_content)
    
    def test_generate_html_table_empty(self):
        """测试空数据HTML表格生成"""
        html_content = self.adapter._generate_html_table([])
        
        self.assertIn('无数据', html_content)
    
    def test_file_not_found(self):
        """测试文件不存在的情况"""
        with self.assertRaises(FileNotFoundError):
            self.adapter._sync_read_html('nonexistent.html')


class TestXMLAdapter(unittest.TestCase):
    """XML适配器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.adapter = XMLAdapter()
        
        # 创建测试XML内容
        self.test_xml = """<?xml version="1.0" encoding="UTF-8"?>
        <data>
            <item id="1">
                <name>Alice</name>
                <age>30</age>
                <city>New York</city>
            </item>
            <item id="2">
                <name>Bob</name>
                <age>25</age>
                <city>Los Angeles</city>
            </item>
        </data>
        """
        
        self.temp_file = None
    
    def tearDown(self):
        """测试后清理"""
        if self.temp_file and os.path.exists(self.temp_file):
            os.unlink(self.temp_file)
    
    def _create_temp_xml_file(self, content=None):
        """创建临时XML文件"""
        if content is None:
            content = self.test_xml
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False, encoding='utf-8') as f:
            f.write(content)
            self.temp_file = f.name
        
        return self.temp_file
    
    def test_supported_types(self):
        """测试支持的类型"""
        types = self.adapter.supported_types()
        
        expected_types = ['xml', 'xml_file', 'xsd', 'xsd_file', 'soap', 'soap_file']
        for expected_type in expected_types:
            self.assertIn(expected_type, types)
    
    def test_can_handle_string(self):
        """测试字符串数据源识别"""
        self.assertTrue(self.adapter.can_handle('test.xml'))
        self.assertTrue(self.adapter.can_handle('test.xsd'))
        self.assertTrue(self.adapter.can_handle('file://test.xml'))
        self.assertFalse(self.adapter.can_handle('test.txt'))
        self.assertFalse(self.adapter.can_handle('test.html'))
    
    def test_can_handle_dict(self):
        """测试字典数据源识别"""
        self.assertTrue(self.adapter.can_handle({'file_path': 'test.xml'}))
        self.assertTrue(self.adapter.can_handle({'path': 'test.xsd'}))
        self.assertFalse(self.adapter.can_handle({'url': 'http://example.com'}))
        self.assertFalse(self.adapter.can_handle({'data': [1, 2, 3]}))
    
    def test_sync_read_xml(self):
        """测试同步XML读取"""
        xml_file = self._create_temp_xml_file()
        
        result = self.adapter._sync_read_xml(xml_file)
        
        self.assertIsInstance(result, list)
        self.assertGreater(len(result), 0)
        
        # 检查数据结构
        first_item = result[0]
        self.assertIn('name', first_item)
        self.assertIn('age', first_item)
        self.assertIn('city', first_item)
    
    def test_sync_write_xml(self):
        """测试同步XML写入"""
        test_data = [
            {'id': 1, 'name': 'Alice', 'age': 30},
            {'id': 2, 'name': 'Bob', 'age': 25}
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False) as f:
            xml_file = f.name
        
        try:
            result = self.adapter._sync_write_xml(xml_file, test_data)
            
            self.assertEqual(result, 2)  # 写入了2条记录
            self.assertTrue(os.path.exists(xml_file))
            
            # 检查文件内容
            with open(xml_file, 'r', encoding='utf-8') as f:
                content = f.read()
                self.assertIn('<?xml version', content)
                self.assertIn('<data>', content)
                self.assertIn('<item>', content)
                self.assertIn('Alice', content)
                self.assertIn('Bob', content)
        finally:
            if os.path.exists(xml_file):
                os.unlink(xml_file)
    
    def test_xml_to_dict_list(self):
        """测试XML到字典列表的转换"""
        import xml.etree.ElementTree as ET
        
        xml_content = """
        <items>
            <item id="1">
                <name>Alice</name>
                <age>30</age>
            </item>
            <item id="2">
                <name>Bob</name>
                <age>25</age>
            </item>
        </items>
        """
        
        root = ET.fromstring(xml_content)
        result = self.adapter._xml_to_dict_list(root)
        
        self.assertIsInstance(result, list)
        self.assertGreater(len(result), 0)
    
    def test_generate_xml_from_data(self):
        """测试从数据生成XML"""
        import xml.etree.ElementTree as ET
        
        test_data = [
            {'name': 'Alice', 'age': 30},
            {'name': 'Bob', 'age': 25}
        ]
        
        root = self.adapter._generate_xml_from_data(test_data, 'data')
        
        self.assertEqual(root.tag, 'data')
        items = root.findall('item')
        self.assertEqual(len(items), 2)
        
        # 检查第一个item
        first_item = items[0]
        name_elem = first_item.find('name')
        age_elem = first_item.find('age')
        
        self.assertIsNotNone(name_elem)
        self.assertIsNotNone(age_elem)
        self.assertEqual(name_elem.text, 'Alice')
        self.assertEqual(age_elem.text, '30')
    
    def test_dict_to_xml_element(self):
        """测试字典到XML元素的转换"""
        import xml.etree.ElementTree as ET
        
        test_dict = {
            'name': 'Alice',
            'age': 30,
            'address': {
                'street': '123 Main St',
                'city': 'New York'
            },
            'hobbies': ['reading', 'swimming']
        }
        
        parent = ET.Element('person')
        self.adapter._dict_to_xml_element(test_dict, parent)
        
        # 检查简单字段
        name_elem = parent.find('name')
        age_elem = parent.find('age')
        self.assertIsNotNone(name_elem)
        self.assertIsNotNone(age_elem)
        self.assertEqual(name_elem.text, 'Alice')
        self.assertEqual(age_elem.text, '30')
        
        # 检查嵌套字典
        address_elem = parent.find('address')
        self.assertIsNotNone(address_elem)
        street_elem = address_elem.find('street')
        self.assertIsNotNone(street_elem)
        self.assertEqual(street_elem.text, '123 Main St')
        
        # 检查列表
        hobbies_elems = parent.findall('hobbies')
        self.assertEqual(len(hobbies_elems), 2)
        self.assertEqual(hobbies_elems[0].text, 'reading')
        self.assertEqual(hobbies_elems[1].text, 'swimming')
    
    def test_sync_validate_xml(self):
        """测试同步XML验证"""
        xml_file = self._create_temp_xml_file()
        
        result = self.adapter._sync_validate_xml(xml_file)
        
        self.assertIsInstance(result, dict)
        self.assertIn('valid', result)
        self.assertIn('file_path', result)
        self.assertIn('root_element', result)
        self.assertIn('validation_type', result)
        
        self.assertTrue(result['valid'])
        self.assertEqual(result['file_path'], xml_file)
        self.assertEqual(result['validation_type'], 'well_formed')
    
    def test_sync_validate_xml_invalid(self):
        """测试无效XML验证"""
        invalid_xml = "<?xml version='1.0'?><unclosed><tag></unclosed>"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False, encoding='utf-8') as f:
            f.write(invalid_xml)
            invalid_file = f.name
        
        try:
            result = self.adapter._sync_validate_xml(invalid_file)
            
            self.assertIsInstance(result, dict)
            self.assertIn('valid', result)
            self.assertFalse(result['valid'])
            self.assertIn('error', result)
        finally:
            if os.path.exists(invalid_file):
                os.unlink(invalid_file)
    
    def test_file_not_found(self):
        """测试文件不存在的情况"""
        with self.assertRaises(FileNotFoundError):
            self.adapter._sync_read_xml('nonexistent.xml')


class TestAsyncFileAdapters(unittest.TestCase):
    """异步文件适配器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.html_adapter = HTMLAdapter()
        self.xml_adapter = XMLAdapter()
    
    def test_html_async_connection_creation(self):
        """测试HTML异步连接创建"""
        async def async_test():
            connection = await self.html_adapter._create_async_connection('test.html')
            
            self.assertIsInstance(connection, dict)
            self.assertIn('file_path', connection)
            self.assertEqual(connection['file_path'], 'test.html')
        
        asyncio.run(async_test())
    
    def test_xml_async_connection_creation(self):
        """测试XML异步连接创建"""
        async def async_test():
            connection = await self.xml_adapter._create_async_connection('test.xml')
            
            self.assertIsInstance(connection, dict)
            self.assertIn('file_path', connection)
            self.assertEqual(connection['file_path'], 'test.xml')
        
        asyncio.run(async_test())
    
    @patch('core.adapters.file.html_adapter.AIOFILES_AVAILABLE', False)
    def test_html_async_without_aiofiles(self):
        """测试HTML异步操作（无aiofiles）"""
        async def async_test():
            with self.assertRaises(ImportError):
                await self.html_adapter._async_read_html('test.html')
        
        asyncio.run(async_test())
    
    @patch('core.adapters.file.xml_adapter.AIOFILES_AVAILABLE', False)
    def test_xml_async_without_aiofiles(self):
        """测试XML异步操作（无aiofiles）"""
        async def async_test():
            with self.assertRaises(ImportError):
                await self.xml_adapter._async_read_xml('test.xml')
        
        asyncio.run(async_test())


if __name__ == '__main__':
    unittest.main()
