"""
基础适配器类和接口定义

定义所有数据适配器的基础接口和通用实现
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Callable, Optional, Union
import logging
import time
from dataclasses import dataclass

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from core.enterprise_data_architecture import IDataAdapter, DataResult


class BaseDataAdapter(IDataAdapter):
    """
    基础数据适配器
    
    提供所有适配器的通用功能和默认实现
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self._operations_cache = None
        self._metadata_cache = None
    
    @abstractmethod
    def supported_types(self) -> List[str]:
        """返回支持的数据类型列表"""
        pass
    
    @abstractmethod
    def can_handle(self, data_source: Any) -> bool:
        """检查是否能处理指定的数据源"""
        pass
    
    def create_proxy(self, data_source: Any, lifecycle_manager) -> 'DataProxy':
        """创建数据代理对象"""
        from core.enterprise_data_architecture import DataProxy
        return DataProxy(data_source, self, lifecycle_manager)
    
    def get_operations(self) -> Dict[str, Callable]:
        """获取支持的操作列表"""
        if self._operations_cache is None:
            self._operations_cache = self._build_operations()
        return self._operations_cache
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取适配器元数据"""
        if self._metadata_cache is None:
            self._metadata_cache = self._build_metadata()
        return self._metadata_cache
    
    @abstractmethod
    def _build_operations(self) -> Dict[str, Callable]:
        """构建操作列表"""
        pass
    
    def _build_metadata(self) -> Dict[str, Any]:
        """构建元数据"""
        return {
            'adapter_name': self.__class__.__name__,
            'adapter_version': '1.0.0',
            'supported_types': self.supported_types(),
            'operations': list(self.get_operations().keys()),
            'created_at': time.time()
        }
    
    def _create_result(self, success: bool, data: Any = None, error: str = None, 
                      operation: str = 'unknown', execution_time: float = 0.0,
                      **kwargs) -> DataResult:
        """创建标准化结果"""
        return DataResult(
            success=success,
            data=data,
            error=error,
            operation=operation,
            source_type=self.__class__.__name__,
            adapter_type=self.__class__.__name__,
            execution_time=execution_time,
            **kwargs
        )
    
    def _execute_with_timing(self, operation_func: Callable, *args, **kwargs) -> DataResult:
        """执行操作并记录时间"""
        start_time = time.time()
        operation_name = operation_func.__name__
        
        try:
            self.logger.debug(f"执行操作: {operation_name}, 参数: {args}, {kwargs}")
            result = operation_func(*args, **kwargs)
            execution_time = (time.time() - start_time) * 1000
            
            return self._create_result(
                success=True,
                data=result,
                operation=operation_name,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            self.logger.error(f"操作 {operation_name} 执行失败: {e}")
            
            return self._create_result(
                success=False,
                error=str(e),
                operation=operation_name,
                execution_time=execution_time
            )


# ============================================================================
# 专用适配器接口
# ============================================================================

class IDatabaseAdapter(BaseDataAdapter):
    """数据库适配器接口"""
    
    @abstractmethod
    def query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """执行查询"""
        pass
    
    @abstractmethod
    def execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """执行命令"""
        pass
    
    @abstractmethod
    def transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """执行事务"""
        pass
    
    def batch(self, connection: Any, operations: List[Dict]) -> Dict:
        """批量执行（默认实现）"""
        results = []
        for operation in operations:
            if operation.get('type') == 'query':
                result = self.query(connection, operation['sql'], operation.get('params'))
            else:
                result = self.execute(connection, operation['sql'], operation.get('params'))
            results.append(result)
        
        return {
            'results': results,
            'total_operations': len(operations),
            'success': True
        }


class IApiAdapter(BaseDataAdapter):
    """API适配器接口"""
    
    @abstractmethod
    def get(self, client: Any, endpoint: str, params: Dict = None, headers: Dict = None) -> Dict:
        """GET请求"""
        pass
    
    @abstractmethod
    def post(self, client: Any, endpoint: str, data: Dict = None, headers: Dict = None) -> Dict:
        """POST请求"""
        pass
    
    @abstractmethod
    def put(self, client: Any, endpoint: str, data: Dict = None, headers: Dict = None) -> Dict:
        """PUT请求"""
        pass
    
    @abstractmethod
    def delete(self, client: Any, endpoint: str, headers: Dict = None) -> Dict:
        """DELETE请求"""
        pass


class IFileAdapter(BaseDataAdapter):
    """文件适配器接口"""
    
    @abstractmethod
    def read(self, file_source: Any, path: str = None) -> str:
        """读取文件"""
        pass
    
    @abstractmethod
    def write(self, file_source: Any, content: str, path: str = None) -> bool:
        """写入文件"""
        pass
    
    @abstractmethod
    def list(self, file_source: Any, directory: str = None) -> List[str]:
        """列出文件"""
        pass
    
    @abstractmethod
    def exists(self, file_source: Any, path: str = None) -> bool:
        """检查文件是否存在"""
        pass


# ============================================================================
# 工具类和数据结构
# ============================================================================

@dataclass
class ConnectionInfo:
    """连接信息"""
    host: str
    port: int
    database: str
    username: str
    password: str
    options: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'host': self.host,
            'port': self.port,
            'database': self.database,
            'username': self.username,
            'password': '***',  # 隐藏密码
            'options': self.options or {}
        }


@dataclass
class OperationResult:
    """操作结果"""
    success: bool
    data: Any = None
    error: str = None
    affected_rows: int = 0
    execution_time: float = 0.0
    metadata: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'success': self.success,
            'data': self.data,
            'error': self.error,
            'affected_rows': self.affected_rows,
            'execution_time': self.execution_time,
            'metadata': self.metadata or {}
        }
