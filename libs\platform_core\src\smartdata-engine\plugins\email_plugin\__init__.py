"""
Email插件

提供企业级邮件处理能力，包括：
- 邮件发送和接收
- 模板渲染
- 附件处理
- 批量邮件
- 邮件队列
- 安全认证
"""

# 插件定义 - 按照统一标准
PLUGIN_DEFINITIONS = [
    {
        'id': 'email_processor',
        'name': '邮件处理器',
        'description': '企业级邮件发送和处理器，支持模板渲染、附件、批量发送等功能',
        'version': '1.0.0',
        'type': 'processor',
        'category': 'email',
        'priority': 70,
        'class_name': 'EmailProcessor',
        'module_file': 'email_processor',
        'auto_load': True,
        'enabled': True,
        'capabilities': [
            'email_sending',
            'email_receiving',
            'template_rendering',
            'attachment_handling',
            'batch_sending',
            'email_queue',
            'smtp_support',
            'imap_support',
            'pop3_support',
            'security_authentication',
            'html_email',
            'plain_text_email',
            'embedded_images',
            'email_validation',
            'delivery_tracking'
        ],
        'supported_protocols': ['smtp', 'smtps', 'imap', 'imaps', 'pop3', 'pop3s'],
        'supported_operations': [
            'send', 'send_batch', 'receive', 'list_emails',
            'validate_email', 'render_template', 'queue_email'
        ],
        'dependencies': [],
        'optional_dependencies': [
            'aiosmtplib', 'aioimaplib', 'jinja2', 'email-validator',
            'pillow', 'premailer', 'celery'
        ],
        'author': 'SmartData Team',
        'license': 'MIT',
        'tags': ['email', 'smtp', 'imap', 'template', 'notification']
    }
]


def get_plugin_definitions():
    """获取插件定义"""
    return PLUGIN_DEFINITIONS


try:
    # Email处理器
    from .email_processor import EmailProcessor
    
    # Email模板引擎
    from .email_templates import (
        EmailTemplateEngine,
        EmailTemplate,
        TemplateRenderer
    )
    
    # Email连接器
    from .email_connectors import (
        EmailConnectorFactory,
        SMTPConnector,
        IMAPConnector,
        POP3Connector
    )
    
    # Email工具
    from .email_utils import (
        EmailValidator,
        EmailFormatter,
        EmailContentProcessor,
        EmailHeaderParser,
        EmailAttachmentHandler
    )

    __all__ = [
        # 处理器
        'EmailProcessor',
        
        # 模板引擎
        'EmailTemplateEngine',
        'EmailTemplate',
        'TemplateRenderer',
        
        # 连接器
        'EmailConnectorFactory',
        'SMTPConnector',
        'IMAPConnector',
        'POP3Connector',
        
        # 工具
        'EmailValidator',
        'EmailFormatter',
        'EmailContentProcessor',
        'EmailHeaderParser',
        'EmailAttachmentHandler',
        
        # 插件定义
        'PLUGIN_DEFINITIONS',
        'get_plugin_definitions'
    ]

except ImportError as e:
    # 优雅处理导入错误
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"Email插件部分功能导入失败: {e}")
    
    __all__ = ['PLUGIN_DEFINITIONS', 'get_plugin_definitions']
