"""
DataRegistry数据适配器注册表测试

测试DataRegistry类的所有功能，确保插件化扩展的正确性
"""

import pytest
from typing import Dict, Any, Callable, List, Optional
from unittest.mock import Mock, MagicMock

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from core.enterprise_data_architecture import (
    DataRegistry, IDataAdapter, UnsupportedDataTypeError, 
    AdapterCreationError, DataProxy
)


class MockAdapter1:
    """模拟适配器1"""
    
    def supported_types(self) -> List[str]:
        return ['mock_type1', 'mock_type2']
    
    def can_handle(self, data_source: Any) -> bool:
        return isinstance(data_source, dict) and data_source.get('type') in ['mock_type1', 'mock_type2']
    
    def create_proxy(self, data_source: Any, lifecycle_manager):
        return DataProxy(data_source, self, lifecycle_manager)
    
    def get_operations(self) -> Dict[str, Callable]:
        return {'operation1': lambda source: 'result1'}
    
    def get_metadata(self) -> Dict[str, Any]:
        return {'adapter_name': 'MockAdapter1', 'version': '1.0.0'}


class MockAdapter2:
    """模拟适配器2"""
    
    def supported_types(self) -> List[str]:
        return ['mock_type3']
    
    def can_handle(self, data_source: Any) -> bool:
        return isinstance(data_source, dict) and data_source.get('type') == 'mock_type3'
    
    def create_proxy(self, data_source: Any, lifecycle_manager):
        return DataProxy(data_source, self, lifecycle_manager)
    
    def get_operations(self) -> Dict[str, Callable]:
        return {'operation2': lambda source: 'result2'}
    
    def get_metadata(self) -> Dict[str, Any]:
        return {'adapter_name': 'MockAdapter2', 'version': '2.0.0'}


class BrokenAdapter:
    """有问题的适配器，用于测试错误处理"""
    
    def supported_types(self) -> List[str]:
        raise Exception("Broken adapter initialization")
    
    def can_handle(self, data_source: Any) -> bool:
        return False
    
    def create_proxy(self, data_source: Any, lifecycle_manager):
        raise Exception("Cannot create proxy")
    
    def get_operations(self) -> Dict[str, Callable]:
        return {}


class TestDataRegistry:
    """DataRegistry类测试套件"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.registry = DataRegistry()
    
    def test_registry_initialization(self):
        """测试注册表初始化"""
        assert isinstance(self.registry.adapters, dict)
        assert isinstance(self.registry.type_detectors, list)
        assert len(self.registry.type_detectors) > 0  # 应该有内置检测器
        assert hasattr(self.registry, 'logger')
        assert hasattr(self.registry, '_lock')
    
    def test_register_single_adapter(self):
        """测试注册单个适配器"""
        self.registry.register_adapter(MockAdapter1)

        # 验证适配器已注册
        supported_types = self.registry.get_supported_types()
        assert 'mock_type1' in supported_types
        assert 'mock_type2' in supported_types
        assert len(supported_types) >= 2

        # 注册自定义类型检测器用于测试
        def mock_detector(data_source):
            if isinstance(data_source, dict) and 'type' in data_source:
                return data_source['type']
            return None

        self.registry.register_type_detector(mock_detector)

        # 验证可以获取适配器
        adapter = self.registry.get_adapter({'type': 'mock_type1'})
        assert isinstance(adapter, MockAdapter1)
    
    def test_register_multiple_adapters(self):
        """测试注册多个适配器"""
        self.registry.register_adapter(MockAdapter1)
        self.registry.register_adapter(MockAdapter2)

        supported_types = self.registry.get_supported_types()
        assert 'mock_type1' in supported_types
        assert 'mock_type2' in supported_types
        assert 'mock_type3' in supported_types

        # 注册自定义类型检测器用于测试
        def mock_detector(data_source):
            if isinstance(data_source, dict) and 'type' in data_source:
                return data_source['type']
            return None

        self.registry.register_type_detector(mock_detector)

        # 验证可以获取不同的适配器
        adapter1 = self.registry.get_adapter({'type': 'mock_type1'})
        adapter2 = self.registry.get_adapter({'type': 'mock_type3'})

        assert isinstance(adapter1, MockAdapter1)
        assert isinstance(adapter2, MockAdapter2)
    
    def test_register_adapter_override(self):
        """测试适配器覆盖"""
        # 注册第一个适配器
        self.registry.register_adapter(MockAdapter1)

        # 创建一个新的适配器类，支持相同的类型
        class OverrideAdapter:
            def supported_types(self):
                return ['mock_type1']

            def get_operations(self):
                return {'override_op': lambda source: 'override_result'}

        # 注册覆盖适配器
        self.registry.register_adapter(OverrideAdapter)

        # 注册自定义类型检测器用于测试
        def mock_detector(data_source):
            if isinstance(data_source, dict) and 'type' in data_source:
                return data_source['type']
            return None

        self.registry.register_type_detector(mock_detector)

        # 验证新适配器已覆盖旧适配器
        adapter = self.registry.get_adapter({'type': 'mock_type1'})
        assert isinstance(adapter, OverrideAdapter)
    
    def test_register_broken_adapter(self):
        """测试注册有问题的适配器"""
        with pytest.raises(AdapterCreationError) as exc_info:
            self.registry.register_adapter(BrokenAdapter)
        
        assert "BrokenAdapter" in str(exc_info.value)
        assert "Broken adapter initialization" in str(exc_info.value)
    
    def test_custom_type_detector(self):
        """测试自定义类型检测器"""
        # 注册适配器
        self.registry.register_adapter(MockAdapter1)
        
        # 注册自定义类型检测器
        def custom_detector(data_source: Any) -> Optional[str]:
            if isinstance(data_source, str) and data_source.startswith('custom:'):
                return 'mock_type1'
            return None
        
        self.registry.register_type_detector(custom_detector)
        
        # 测试自定义检测器
        adapter = self.registry.get_adapter('custom:test_data')
        assert isinstance(adapter, MockAdapter1)
    
    def test_builtin_detectors(self):
        """测试内置类型检测器"""
        # 测试连接字符串检测
        test_cases = [
            ('********************************/db', 'postgresql_connection_string'),
            ('mysql://user:pass@host:3306/db', 'mysql_connection_string'),
            ('sqlite:///path/to/db.sqlite', 'sqlite_connection_string'),
            ('http://api.example.com', 'http_url'),
            ('https://api.example.com', 'http_url'),
            ('file:///path/to/file', 'file_path'),
            ('/absolute/path/to/file', 'file_path')
        ]
        
        for data_source, expected_type in test_cases:
            detected_type = self.registry._detect_type(data_source)
            assert detected_type == expected_type, f"Failed to detect {expected_type} for {data_source}"
    
    def test_database_connection_detection(self):
        """测试数据库连接对象检测"""
        # 模拟PostgreSQL连接
        class MockPsycopgConnection:
            def execute(self, sql): pass
            def cursor(self): pass
        
        # 模拟MySQL连接
        class MockMySQLConnection:
            def execute(self, sql): pass
            def cursor(self): pass
        
        # 模拟SQLite连接
        class MockSQLiteConnection:
            def execute(self, sql): pass
            def cursor(self): pass
        
        # 模拟通用数据库连接
        class MockGenericConnection:
            def execute(self, sql): pass
            def cursor(self): pass
        
        test_cases = [
            (MockPsycopgConnection(), 'postgresql_connection'),
            (MockMySQLConnection(), 'mysql_connection'),
            (MockSQLiteConnection(), 'sqlite_connection'),
            (MockGenericConnection(), 'database_connection')
        ]
        
        for connection, expected_type in test_cases:
            detected_type = self.registry._detect_type(connection)
            assert detected_type == expected_type
    
    def test_file_like_detection(self):
        """测试文件类对象检测"""
        # 模拟文件对象
        class MockFile:
            def read(self): pass
            def write(self, data): pass
        
        class MockReadOnlyFile:
            def read(self): pass
        
        class MockWriteOnlyFile:
            def write(self, data): pass
        
        test_cases = [
            (MockFile(), 'file_like'),
            (MockReadOnlyFile(), 'readable_file'),
            (MockWriteOnlyFile(), 'writable_file')
        ]
        
        for file_obj, expected_type in test_cases:
            detected_type = self.registry._detect_type(file_obj)
            assert detected_type == expected_type
    
    def test_unsupported_data_type(self):
        """测试不支持的数据类型"""
        # 尝试获取未注册类型的适配器
        with pytest.raises(UnsupportedDataTypeError) as exc_info:
            self.registry.get_adapter({'type': 'unsupported_type'})
        
        error = exc_info.value
        assert "未知类型" in error.message
        assert error.error_code == 'UNSUPPORTED_DATA_TYPE'
        assert 'available_types' in error.details
    
    def test_get_adapter_info(self):
        """测试获取适配器信息"""
        self.registry.register_adapter(MockAdapter1)
        self.registry.register_adapter(MockAdapter2)
        
        info = self.registry.get_adapter_info()
        
        # 验证信息结构
        assert 'mock_type1' in info
        assert 'mock_type2' in info
        assert 'mock_type3' in info
        
        # 验证MockAdapter1的信息
        adapter1_info = info['mock_type1']
        assert adapter1_info['class_name'] == 'MockAdapter1'
        assert 'metadata' in adapter1_info
        assert 'operations' in adapter1_info
        assert adapter1_info['metadata']['adapter_name'] == 'MockAdapter1'
        assert 'operation1' in adapter1_info['operations']
    
    def test_unregister_adapter(self):
        """测试注销适配器"""
        self.registry.register_adapter(MockAdapter1)
        
        # 验证适配器已注册
        assert 'mock_type1' in self.registry.get_supported_types()
        
        # 注销适配器
        success = self.registry.unregister_adapter('mock_type1')
        assert success is True
        
        # 验证适配器已注销
        assert 'mock_type1' not in self.registry.get_supported_types()
        
        # 尝试注销不存在的适配器
        success = self.registry.unregister_adapter('nonexistent_type')
        assert success is False
    
    def test_clear_adapters(self):
        """测试清除所有适配器"""
        self.registry.register_adapter(MockAdapter1)
        self.registry.register_adapter(MockAdapter2)
        
        # 验证适配器已注册
        assert len(self.registry.get_supported_types()) >= 3
        
        # 清除所有适配器
        count = self.registry.clear_adapters()
        assert count >= 3
        
        # 验证所有适配器已清除
        assert len(self.registry.get_supported_types()) == 0
    
    def test_thread_safety(self):
        """测试线程安全"""
        import threading
        import time
        
        results = []
        errors = []
        
        def register_adapter():
            try:
                self.registry.register_adapter(MockAdapter1)
                results.append('registered')
            except Exception as e:
                errors.append(str(e))
        
        def get_adapter():
            try:
                time.sleep(0.01)  # 稍微延迟以增加竞争条件
                # 注册检测器
                def mock_detector(data_source):
                    if isinstance(data_source, dict) and 'type' in data_source:
                        return data_source['type']
                    return None
                self.registry.register_type_detector(mock_detector)

                adapter = self.registry.get_adapter({'type': 'mock_type1'})
                results.append('got_adapter')
            except Exception as e:
                errors.append(str(e))
        
        # 创建多个线程同时操作注册表
        threads = []
        for _ in range(5):
            threads.append(threading.Thread(target=register_adapter))
            threads.append(threading.Thread(target=get_adapter))
        
        # 启动所有线程
        for thread in threads:
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证没有错误发生
        assert len(errors) == 0, f"Thread safety test failed with errors: {errors}"
        assert len(results) > 0
    
    def test_detector_error_handling(self):
        """测试检测器错误处理"""
        def broken_detector(data_source: Any) -> Optional[str]:
            raise Exception("Detector error")
        
        self.registry.register_type_detector(broken_detector)
        
        # 即使检测器出错，也应该能继续使用其他检测器
        detected_type = self.registry._detect_type('postgresql://test')
        assert detected_type == 'postgresql_connection_string'


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, '-v'])
