"""
调试适配器检测问题
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from core.enterprise_data_architecture import DataRegistry
from core.adapters.database.sqlite import SQLiteAdapter
from core.adapters.database.async_sqlite import AsyncSQLiteAdapter
from core.adapters.database.postgresql import PostgreSQLAdapter
from core.adapters.database.mysql import MySQLAdapter
from core.adapters.api.rest_api import RestAPIAdapter
from core.adapters.file.csv_adapter import CSVAdapter
from core.adapters.file.json_adapter import J<PERSON>NAdapter
from core.adapters.file.html_adapter import HTMLAdapter
from core.adapters.file.xml_adapter import XMLAdapter
from core.adapters.memory.data_list_adapter import DataListAdapter

def debug_adapter_detection():
    """调试适配器检测"""
    print("🔍 调试适配器检测问题")
    print("=" * 50)
    
    # 创建注册表
    registry = DataRegistry()
    
    # 注册所有适配器
    print("📋 注册所有适配器...")
    registry.register_adapter(SQLiteAdapter)
    registry.register_adapter(AsyncSQLiteAdapter)
    registry.register_adapter(PostgreSQLAdapter)
    registry.register_adapter(MySQLAdapter)
    registry.register_adapter(RestAPIAdapter)
    registry.register_adapter(CSVAdapter)
    registry.register_adapter(JSONAdapter)
    registry.register_adapter(HTMLAdapter)
    registry.register_adapter(XMLAdapter)
    registry.register_adapter(DataListAdapter)
    
    # 检查注册的类型
    print(f"已注册的类型: {list(registry.adapters.keys())}")
    
    # 测试多个数据源
    test_sources = [
        ':memory:',
        'test.csv',
        'data.json',
        'page.html',
        'config.xml',
        'https://api.example.com',
        {'base_url': 'https://api.test.com'},
        {'file_path': 'data.csv'},
        [{'id': 1, 'name': 'test'}],
        {'host': 'localhost', 'database': 'test', 'port': 5432}
    ]

    for test_source in test_sources:
        print(f"\n🎯 测试数据源: {test_source}")

        # 检测类型
        detected_type = registry._detect_type(test_source)
        print(f"检测到的类型: {detected_type}")

        # 检查适配器是否存在
        if detected_type:
            adapter_class = registry.adapters.get(detected_type)
            print(f"适配器类: {adapter_class}")

            if adapter_class:
                # 测试适配器的can_handle方法
                try:
                    adapter_instance = adapter_class()
                    can_handle = adapter_instance.can_handle(test_source)
                    print(f"can_handle结果: {can_handle}")
                except Exception as e:
                    print(f"can_handle测试失败: {e}")
            else:
                print("❌ 没有找到对应的适配器类")
        else:
            print("❌ 类型检测失败")
    
    # 测试内置检测器
    print(f"\n🔧 测试内置检测器...")
    for detector in registry.type_detectors:
        try:
            result = detector(test_source)
            print(f"检测器 {detector.__name__}: {result}")
        except Exception as e:
            print(f"检测器 {detector.__name__} 失败: {e}")
    
    # 测试SQLite适配器的supported_types
    print(f"\n📊 SQLite适配器支持的类型:")
    sqlite_adapter = SQLiteAdapter()
    print(f"同步SQLite: {sqlite_adapter.supported_types()}")
    
    async_sqlite_adapter = AsyncSQLiteAdapter()
    print(f"异步SQLite: {async_sqlite_adapter.supported_types()}")
    
    # 测试can_handle方法
    print(f"\n🎯 测试can_handle方法:")
    print(f"同步SQLite can_handle(':memory:'): {sqlite_adapter.can_handle(':memory:')}")
    print(f"异步SQLite can_handle(':memory:'): {async_sqlite_adapter.can_handle(':memory:')}")

if __name__ == '__main__':
    debug_adapter_detection()
