"""
XML文件数据适配器

支持XML文件的同步和异步读写操作，包括XPath查询和XML生成
"""

from typing import Any, Dict, List, Optional, Union, AsyncIterator
import logging
import xml.etree.ElementTree as ET
import os
from pathlib import Path

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../'))

from core.unified_adapter import UnifiedDataAdapter

# 尝试导入异步文件操作库
try:
    import aiofiles
    AIOFILES_AVAILABLE = True
except ImportError:
    AIOFILES_AVAILABLE = False
    aiofiles = None

# 尝试导入lxml用于更强大的XML处理
try:
    from lxml import etree
    LXML_AVAILABLE = True
except ImportError:
    LXML_AVAILABLE = False
    etree = None


class XMLAdapter(UnifiedDataAdapter):
    """
    XML文件数据适配器
    
    支持XML文件特有功能：
    - 同步和异步文件读写
    - XPath查询支持
    - XML Schema验证
    - 命名空间处理
    - 数据到XML转换
    """
    
    def __init__(self):
        super().__init__()
        if not AIOFILES_AVAILABLE:
            self.logger.warning("aiofiles未安装，异步文件操作功能受限")
        if not LXML_AVAILABLE:
            self.logger.warning("lxml未安装，高级XML功能受限")
        
        # 默认配置
        self.default_encoding = 'utf-8'
        self.default_parser = 'xml'
    
    def supported_types(self) -> List[str]:
        """返回支持的数据类型列表"""
        return [
            'xml',
            'xml_file',
            'xsd',
            'xsd_file',
            'soap',
            'soap_file'
        ]
    
    def _is_supported_connection_string(self, connection_string: str) -> bool:
        """检查是否支持的连接字符串"""
        return (connection_string.endswith(('.xml', '.xsd', '.soap')) or
                connection_string.startswith('file://'))
    
    def _is_supported_connection_object(self, connection: Any) -> bool:
        """检查是否支持的连接对象"""
        return (isinstance(connection, dict) and 
                ('file_path' in connection or 'path' in connection))
    
    def can_handle(self, data_source: Any) -> bool:
        """检查是否能处理指定的数据源"""
        if isinstance(data_source, str):
            return self._is_supported_connection_string(data_source)
        else:
            return self._is_supported_connection_object(data_source)
    
    def _build_operations(self) -> Dict[str, callable]:
        """构建XML特有操作列表"""
        operations = {
            'read_xml': self._sync_read_xml,
            'write_xml': self._sync_write_xml,
            'xpath_query': self._sync_xpath_query,
            'validate_xml': self._sync_validate_xml,
        }
        
        # 添加异步操作
        async_ops = self.get_async_operations()
        for name, func in async_ops.items():
            operations[name] = func
        
        return operations
    
    def _parse_file_config(self, connection_source: Any) -> Dict[str, Any]:
        """解析文件配置"""
        if isinstance(connection_source, str):
            # 简单文件路径
            if connection_source.startswith('file://'):
                file_path = connection_source[7:]  # 移除 'file://'
            else:
                file_path = connection_source
            
            return {
                'file_path': file_path,
                'encoding': self.default_encoding,
                'parser': self.default_parser,
                'xpath_query': None,
                'namespaces': {},
                'root_element': 'data'
            }
        elif isinstance(connection_source, dict):
            # 详细配置对象
            config = {
                'file_path': connection_source.get('file_path') or connection_source.get('path'),
                'encoding': connection_source.get('encoding', self.default_encoding),
                'parser': connection_source.get('parser', self.default_parser),
                'xpath_query': connection_source.get('xpath_query'),
                'namespaces': connection_source.get('namespaces', {}),
                'root_element': connection_source.get('root_element', 'data')
            }
            
            return config
        else:
            raise ValueError(f"不支持的文件配置类型: {type(connection_source)}")
    
    # ========================================================================
    # 同步方法实现
    # ========================================================================
    
    def _sync_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """同步查询实现 - 读取XML文件"""
        # 将SQL查询映射为XPath查询
        return self._sync_read_xml(connection, params)
    
    def _sync_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """同步执行实现 - 写入XML文件"""
        if params and 'data' in params:
            return self._sync_write_xml(connection, params['data'])
        return 0
    
    def _sync_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """同步事务实现 - 批量文件操作"""
        results = []
        total_affected = 0
        
        for operation in operations:
            op_type = operation.get('type', 'read')
            data = operation.get('data')
            
            if op_type == 'read':
                result = self._sync_read_xml(connection)
                results.append(result)
            elif op_type == 'write':
                affected = self._sync_write_xml(connection, data)
                results.append(affected)
                total_affected += affected
            elif op_type == 'xpath_query':
                xpath = operation.get('xpath')
                result = self._sync_xpath_query(connection, xpath)
                results.append(result)
        
        return {
            'results': results,
            'total_operations': len(operations),
            'total_affected': total_affected,
            'success': True
        }
    
    def _sync_read_xml(self, connection: Any, params: Dict = None) -> List[Dict]:
        """同步读取XML文件"""
        config = self._parse_file_config(connection)
        file_path = config['file_path']
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"XML文件不存在: {file_path}")
        
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            # 根据配置提取数据
            if config['xpath_query']:
                return self._xpath_query_on_element(root, config['xpath_query'], config['namespaces'])
            else:
                return self._xml_to_dict_list(root)
            
        except Exception as e:
            self.logger.error(f"读取XML文件失败 {file_path}: {e}")
            raise
    
    def _sync_write_xml(self, connection: Any, data: List[Dict]) -> int:
        """同步写入XML文件"""
        config = self._parse_file_config(connection)
        file_path = config['file_path']
        
        if not data:
            return 0
        
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path) or '.', exist_ok=True)
            
            # 生成XML内容
            root = self._generate_xml_from_data(data, config['root_element'])
            tree = ET.ElementTree(root)
            
            # 写入文件
            tree.write(file_path, encoding=config['encoding'], xml_declaration=True)
            
            self.logger.info(f"成功写入XML文件: {file_path}, 记录数: {len(data)}")
            return len(data)
            
        except Exception as e:
            self.logger.error(f"写入XML文件失败 {file_path}: {e}")
            raise
    
    def _sync_xpath_query(self, connection: Any, xpath: str) -> List[Dict]:
        """同步XPath查询"""
        config = self._parse_file_config(connection)
        file_path = config['file_path']
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"XML文件不存在: {file_path}")
        
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            return self._xpath_query_on_element(root, xpath, config['namespaces'])
            
        except Exception as e:
            self.logger.error(f"XPath查询失败 {file_path}: {e}")
            raise
    
    def _sync_validate_xml(self, connection: Any, schema_path: str = None) -> Dict:
        """同步XML验证"""
        config = self._parse_file_config(connection)
        file_path = config['file_path']
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"XML文件不存在: {file_path}")
        
        try:
            # 基本XML格式验证
            tree = ET.parse(file_path)
            
            validation_result = {
                'valid': True,
                'file_path': file_path,
                'root_element': tree.getroot().tag,
                'validation_type': 'well_formed'
            }
            
            # 如果有lxml和schema，进行更严格的验证
            if LXML_AVAILABLE and schema_path and os.path.exists(schema_path):
                validation_result.update(self._validate_with_schema(file_path, schema_path))
            
            return validation_result
            
        except ET.ParseError as e:
            return {
                'valid': False,
                'file_path': file_path,
                'error': str(e),
                'validation_type': 'well_formed'
            }
        except Exception as e:
            self.logger.error(f"XML验证失败 {file_path}: {e}")
            raise
    
    def _xml_to_dict_list(self, element: ET.Element) -> List[Dict]:
        """将XML元素转换为字典列表"""
        def element_to_dict(elem):
            result = {}
            
            # 添加属性
            if elem.attrib:
                result.update(elem.attrib)
            
            # 处理子元素
            children = list(elem)
            if children:
                child_dict = {}
                for child in children:
                    child_data = element_to_dict(child)
                    if child.tag in child_dict:
                        # 如果标签已存在，转换为列表
                        if not isinstance(child_dict[child.tag], list):
                            child_dict[child.tag] = [child_dict[child.tag]]
                        child_dict[child.tag].append(child_data)
                    else:
                        child_dict[child.tag] = child_data
                result.update(child_dict)
            elif elem.text and elem.text.strip():
                # 如果有文本内容
                result['text'] = elem.text.strip()
            
            return result
        
        # 如果根元素有多个同类型子元素，返回列表
        children = list(element)
        if children and len(set(child.tag for child in children)) == 1:
            # 所有子元素标签相同，返回列表
            return [element_to_dict(child) for child in children]
        else:
            # 返回单个元素
            return [element_to_dict(element)]
    
    def _xpath_query_on_element(self, element: ET.Element, xpath: str, namespaces: Dict = None) -> List[Dict]:
        """在元素上执行XPath查询"""
        try:
            if LXML_AVAILABLE:
                # 使用lxml进行更强大的XPath查询
                lxml_element = etree.fromstring(ET.tostring(element))
                results = lxml_element.xpath(xpath, namespaces=namespaces or {})
                
                xpath_results = []
                for result in results:
                    if hasattr(result, 'tag'):  # 元素
                        xpath_results.extend(self._xml_to_dict_list(ET.fromstring(etree.tostring(result))))
                    else:  # 文本或属性
                        xpath_results.append({'value': str(result)})
                
                return xpath_results
            else:
                # 使用标准库的有限XPath支持
                results = element.findall(xpath)
                return [self._xml_to_dict_list(result)[0] for result in results]
                
        except Exception as e:
            self.logger.error(f"XPath查询执行失败: {e}")
            return []
    
    def _generate_xml_from_data(self, data: List[Dict], root_name: str) -> ET.Element:
        """从数据生成XML元素"""
        root = ET.Element(root_name)
        
        for item in data:
            item_element = ET.SubElement(root, 'item')
            self._dict_to_xml_element(item, item_element)
        
        return root
    
    def _dict_to_xml_element(self, data: Dict, parent: ET.Element):
        """将字典转换为XML元素"""
        for key, value in data.items():
            if isinstance(value, dict):
                child = ET.SubElement(parent, key)
                self._dict_to_xml_element(value, child)
            elif isinstance(value, list):
                for item in value:
                    child = ET.SubElement(parent, key)
                    if isinstance(item, dict):
                        self._dict_to_xml_element(item, child)
                    else:
                        child.text = str(item)
            else:
                child = ET.SubElement(parent, key)
                child.text = str(value)
    
    def _validate_with_schema(self, xml_path: str, schema_path: str) -> Dict:
        """使用Schema验证XML"""
        if not LXML_AVAILABLE:
            return {'schema_validation': 'lxml_not_available'}
        
        try:
            # 加载Schema
            with open(schema_path, 'r') as schema_file:
                schema_doc = etree.parse(schema_file)
                schema = etree.XMLSchema(schema_doc)
            
            # 加载XML文档
            with open(xml_path, 'r') as xml_file:
                xml_doc = etree.parse(xml_file)
            
            # 验证
            is_valid = schema.validate(xml_doc)
            
            result = {
                'schema_valid': is_valid,
                'schema_path': schema_path,
                'validation_type': 'schema'
            }
            
            if not is_valid:
                result['schema_errors'] = [str(error) for error in schema.error_log]
            
            return result
            
        except Exception as e:
            return {
                'schema_valid': False,
                'schema_path': schema_path,
                'validation_type': 'schema',
                'error': str(e)
            }

    # ========================================================================
    # 异步方法实现
    # ========================================================================

    async def _create_async_connection(self, connection_source: Any) -> Any:
        """创建异步文件连接"""
        config = self._parse_file_config(connection_source)

        # 验证文件路径
        file_path = config['file_path']
        if not os.path.exists(os.path.dirname(file_path) or '.'):
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

        self.logger.info(f"创建异步XML文件连接: {file_path}")
        return config

    async def _close_async_connection(self, connection: Any) -> None:
        """关闭异步文件连接"""
        self.logger.debug("异步XML文件连接已关闭")

    async def _create_async_pool(self, connection_source: Any, min_size: int = 5, max_size: int = 20) -> Any:
        """创建异步连接池（文件不需要连接池）"""
        return await self._create_async_connection(connection_source)

    async def _async_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """异步查询实现 - 读取XML文件"""
        return await self._async_read_xml(connection, params)

    async def _async_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """异步执行实现 - 写入XML文件"""
        if params and 'data' in params:
            return await self._async_write_xml(connection, params['data'])
        return 0

    async def _async_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步事务实现 - 批量文件操作"""
        results = []
        total_affected = 0

        for operation in operations:
            op_type = operation.get('type', 'read')
            data = operation.get('data')

            if op_type == 'read':
                result = await self._async_read_xml(connection)
                results.append(result)
            elif op_type == 'write':
                affected = await self._async_write_xml(connection, data)
                results.append(affected)
                total_affected += affected
            elif op_type == 'xpath_query':
                xpath = operation.get('xpath')
                result = await self._async_xpath_query(connection, xpath)
                results.append(result)

        return {
            'results': results,
            'total_operations': len(operations),
            'total_affected': total_affected,
            'success': True
        }

    async def _async_batch(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步批量操作实现"""
        return await self._async_transaction(connection, operations)

    async def _async_stream_query(self, connection: Any, sql: str, params: Dict = None) -> AsyncIterator[Dict]:
        """异步流式查询实现 - 逐个返回XML元素"""
        if not AIOFILES_AVAILABLE:
            raise ImportError("aiofiles未安装，无法执行异步文件流式读取")

        config = connection
        file_path = config['file_path']

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"XML文件不存在: {file_path}")

        try:
            async with aiofiles.open(file_path, 'r', encoding=config['encoding']) as xmlfile:
                content = await xmlfile.read()
                root = ET.fromstring(content)

                # 根据配置流式返回数据
                if config['xpath_query']:
                    results = self._xpath_query_on_element(root, config['xpath_query'], config['namespaces'])
                    for result in results:
                        yield result
                else:
                    # 逐个返回子元素
                    for child in root:
                        child_data = self._xml_to_dict_list(child)
                        for item in child_data:
                            yield item

        except Exception as e:
            self.logger.error(f"异步流式读取XML文件失败 {file_path}: {e}")
            raise

    async def _async_read_xml(self, connection: Any, params: Dict = None) -> List[Dict]:
        """异步读取XML文件"""
        if not AIOFILES_AVAILABLE:
            raise ImportError("aiofiles未安装，无法执行异步文件读取")

        config = connection
        file_path = config['file_path']

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"XML文件不存在: {file_path}")

        try:
            async with aiofiles.open(file_path, 'r', encoding=config['encoding']) as xmlfile:
                content = await xmlfile.read()
                root = ET.fromstring(content)

                # 根据配置提取数据
                if config['xpath_query']:
                    return self._xpath_query_on_element(root, config['xpath_query'], config['namespaces'])
                else:
                    return self._xml_to_dict_list(root)

        except Exception as e:
            self.logger.error(f"异步读取XML文件失败 {file_path}: {e}")
            raise

    async def _async_write_xml(self, connection: Any, data: List[Dict]) -> int:
        """异步写入XML文件"""
        if not AIOFILES_AVAILABLE:
            raise ImportError("aiofiles未安装，无法执行异步文件写入")

        config = connection
        file_path = config['file_path']

        if not data:
            return 0

        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path) or '.', exist_ok=True)

            # 生成XML内容
            root = self._generate_xml_from_data(data, config['root_element'])
            xml_content = ET.tostring(root, encoding='unicode', xml_declaration=True)

            async with aiofiles.open(file_path, 'w', encoding=config['encoding']) as xmlfile:
                await xmlfile.write(xml_content)

            self.logger.info(f"成功异步写入XML文件: {file_path}, 记录数: {len(data)}")
            return len(data)

        except Exception as e:
            self.logger.error(f"异步写入XML文件失败 {file_path}: {e}")
            raise

    async def _async_xpath_query(self, connection: Any, xpath: str) -> List[Dict]:
        """异步XPath查询"""
        if not AIOFILES_AVAILABLE:
            raise ImportError("aiofiles未安装，无法执行异步文件读取")

        config = connection
        file_path = config['file_path']

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"XML文件不存在: {file_path}")

        try:
            async with aiofiles.open(file_path, 'r', encoding=config['encoding']) as xmlfile:
                content = await xmlfile.read()
                root = ET.fromstring(content)
                return self._xpath_query_on_element(root, xpath, config['namespaces'])

        except Exception as e:
            self.logger.error(f"异步XPath查询失败 {file_path}: {e}")
            raise

    # ========================================================================
    # XML特有功能
    # ========================================================================

    def get_async_operations(self) -> Dict[str, Any]:
        """获取支持的异步操作列表"""
        operations = super().get_async_operations()

        # 添加XML特有异步操作
        operations.update({
            'async_read_xml': self._async_read_xml,
            'async_write_xml': self._async_write_xml,
            'async_xpath_query': self._async_xpath_query,
        })

        return operations
