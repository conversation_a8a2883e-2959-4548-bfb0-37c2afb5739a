# 🏗️ 插件标准规范

## 🎯 规范目标
基于file_loader插件的成功经验，制定统一的插件开发标准，确保所有插件的架构一致性、功能完整性和集成质量。

## 📋 插件标准规范

### 1. **插件定义标准** ✅

#### 📄 **PLUGIN_DEFINITIONS格式**
```python
PLUGIN_DEFINITIONS = [
    {
        # 基础信息 (必需)
        'id': 'plugin_id',                    # 插件唯一标识
        'name': '插件显示名称',                 # 插件显示名称
        'description': '详细功能描述',          # 功能描述
        'version': '2.0.0',                   # 版本号 (语义化版本)
        'type': 'processor',                  # 插件类型
        'category': 'plugin_category',        # 插件分类
        'priority': 70,                       # 优先级 (0-100)
        
        # 加载信息 (必需)
        'class_name': 'PluginProcessor',      # 主处理器类名
        'module_file': 'plugin_processor',    # 模块文件名
        'auto_load': True,                    # 是否自动加载
        'enabled': True,                      # 是否启用
        
        # 功能信息 (推荐)
        'capabilities': [                     # 功能能力列表
            'capability1',
            'capability2'
        ],
        'supported_formats': [                # 支持的格式/类型
            'format1',
            'format2'
        ],
        
        # 依赖信息 (可选)
        'dependencies': [                     # 依赖的其他插件
            'required_plugin_id'
        ],
        'optional_dependencies': [            # 可选依赖
            'optional_plugin_id'
        ],
        
        # 元数据 (可选)
        'author': '作者名称',                  # 作者
        'license': 'MIT',                     # 许可证
        'homepage': 'https://...',            # 主页
        'documentation': 'https://...',       # 文档地址
        'tags': ['tag1', 'tag2']             # 标签
    }
]

def get_plugin_definitions():
    """获取插件定义 - 支持动态加载"""
    return PLUGIN_DEFINITIONS
```

### 2. **插件架构标准** ✅

#### 🏗️ **目录结构标准**
```
plugins/
├── plugin_name/
│   ├── __init__.py              # 插件定义和导出
│   ├── plugin_processor.py     # 主处理器 (必需)
│   ├── factory.py              # 工厂模式 (推荐)
│   ├── smart_loader.py         # 智能加载器 (推荐)
│   ├── handlers/               # 处理器目录 (可选)
│   │   ├── handler1.py
│   │   └── handler2.py
│   ├── utils/                  # 工具模块 (可选)
│   │   └── helpers.py
│   ├── tests/                  # 测试文件 (推荐)
│   │   └── test_plugin.py
│   └── README.md               # 插件文档 (推荐)
```

#### 🔧 **核心组件标准**

##### **1. 主处理器 (必需)**
```python
class PluginProcessor:
    """插件主处理器 - 必需组件"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.PluginProcessor")
        
    def can_process(self, data: Any) -> bool:
        """检查是否可以处理数据 - 必需方法"""
        pass
    
    def process(self, data: Any, context: Optional[Dict] = None) -> SmartDataObject:
        """处理数据 - 必需方法"""
        pass
    
    def get_processor_info(self) -> Dict[str, Any]:
        """获取处理器信息 - 推荐方法"""
        return {
            "name": "PluginProcessor",
            "version": "2.0.0",
            "description": "插件处理器描述",
            "capabilities": [],
            "supported_types": []
        }
```

##### **2. 工厂模式 (推荐)**
```python
class PluginFactory:
    """插件工厂 - 推荐组件"""
    
    # 处理器映射
    HANDLER_CLASSES = {
        'type1': Handler1,
        'type2': Handler2,
    }
    
    @classmethod
    def detect_type(cls, data: Any) -> str:
        """检测数据类型"""
        pass
    
    @classmethod
    def create_handler(cls, data_type: str) -> Optional[IHandler]:
        """创建处理器"""
        pass
    
    @classmethod
    def get_supported_types(cls) -> List[str]:
        """获取支持的类型"""
        return list(cls.HANDLER_CLASSES.keys())
```

##### **3. 智能加载器 (推荐)**
```python
class SmartPluginLoader:
    """智能插件加载器 - 推荐组件"""
    
    def __init__(self, enable_debug: bool = False):
        self.coordinator = AsyncSyncCoordinator(enable_debug=enable_debug)
        self._cache: Dict[str, Any] = {}
        
    def load(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """智能加载 - 自动选择最佳处理方式"""
        pass
    
    async def load_async(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """异步加载"""
        pass
```

### 3. **接口标准** ✅

#### 🔌 **处理器接口**
```python
class IPluginHandler(ABC):
    """插件处理器接口 - 标准接口"""
    
    @abstractmethod
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理数据"""
        pass
    
    @abstractmethod
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理数据"""
        pass
    
    @abstractmethod
    def get_handler_type(self) -> str:
        """获取处理器类型"""
        pass
    
    @abstractmethod
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        pass
```

### 4. **集成标准** ✅

#### 🔗 **SmartDataLoader集成**
```python
def plugin_method(self, data: Any, **options):
    """插件方法 - SmartDataLoader集成标准"""
    try:
        # 尝试使用智能加载器
        from plugins.plugin_name.smart_loader import global_loader
        
        load_options = options.copy() if options else {}
        result = global_loader.load(data, load_options)
        logging.getLogger(__name__).info(f"使用智能加载器: {data}")
        return result
        
    except ImportError:
        # 回退到原始处理器
        logging.getLogger(__name__).warning("智能加载器不可用，使用原始处理器")
        from plugins.plugin_name.plugin_processor import PluginProcessor
        processor = PluginProcessor()
        return processor.process(data, options)
    except Exception as e:
        logging.getLogger(__name__).error(f"智能加载器失败: {e}")
        # 最后回退
        from plugins.plugin_name.plugin_processor import PluginProcessor
        processor = PluginProcessor()
        return processor.process(data, options)
```

### 5. **异步支持标准** ✅

#### ⚡ **异步优先设计**
```python
class AsyncFirstPlugin:
    """异步优先插件设计标准"""
    
    def __init__(self):
        self.coordinator = AsyncSyncCoordinator(enable_debug=True)
    
    def process(self, data: Any) -> Any:
        """同步处理 - 智能协调"""
        return self.coordinator.smart_call(self._async_process, data)
    
    async def process_async(self, data: Any) -> Any:
        """异步处理 - 原生异步"""
        return await self._async_process(data)
    
    async def _async_process(self, data: Any) -> Any:
        """内部异步处理逻辑"""
        pass
```

### 6. **缓存标准** ✅

#### 💾 **智能缓存机制**
```python
class CacheablePlugin:
    """可缓存插件标准"""
    
    def __init__(self):
        self._cache: Dict[str, Any] = {}
        self._handler_cache: Dict[str, Any] = {}
    
    def _get_cache_key(self, data: Any, options: Dict = None) -> str:
        """生成缓存键"""
        pass
    
    def _should_cache(self, options: Dict = None) -> bool:
        """判断是否应该缓存"""
        return options.get('cache', True) if options else True
    
    def clear_cache(self):
        """清理缓存"""
        self._cache.clear()
        self._handler_cache.clear()
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            'cache_size': len(self._cache),
            'handler_cache_size': len(self._handler_cache),
            'cached_keys': list(self._cache.keys())
        }
```

### 7. **测试标准** ✅

#### 🧪 **测试规范**
```python
class TestPluginStandard:
    """插件测试标准"""
    
    @pytest.fixture
    def template_engine(self):
        """模板引擎测试夹具"""
        registry = PluginRegistry()
        return create_template_engine(registry, debug=True)
    
    def test_plugin_factory(self):
        """测试插件工厂"""
        # 测试类型检测
        # 测试处理器创建
        # 测试支持的类型
        pass
    
    def test_smart_loader(self):
        """测试智能加载器"""
        # 测试基础加载
        # 测试异步加载
        # 测试批量加载
        # 测试缓存功能
        pass
    
    def test_template_integration(self, template_engine):
        """测试模板引擎集成"""
        # 测试模板中的使用
        # 测试参数传递
        # 测试错误处理
        pass
    
    def test_async_support(self):
        """测试异步支持"""
        # 测试异步处理
        # 测试智能协调
        # 测试性能优化
        pass
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试异常情况
        # 测试回退机制
        # 测试错误信息
        pass
```

### 8. **文档标准** ✅

#### 📚 **README.md模板**
```markdown
# 插件名称

## 功能描述
简要描述插件的主要功能和用途。

## 支持的格式/类型
- 格式1: 描述
- 格式2: 描述

## 使用示例

### 模板中使用
```jinja2
{% set result = sd.plugin_method(data) %}
```

### Python中使用
```python
from plugins.plugin_name.smart_loader import global_loader
result = global_loader.load(data)
```

## 配置选项
| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| option1 | str | 'default' | 选项描述 |

## 性能特性
- 异步支持: ✅
- 智能缓存: ✅
- 批量处理: ✅

## 版本历史
- v2.0.0: 完整重构，支持异步和智能缓存
- v1.0.0: 初始版本
```

## 🎯 插件开发检查清单

### ✅ **必需项目**
- [ ] 插件定义 (PLUGIN_DEFINITIONS)
- [ ] 主处理器 (PluginProcessor)
- [ ] 接口实现 (IPluginHandler)
- [ ] SmartDataLoader集成
- [ ] 基础测试

### ✅ **推荐项目**
- [ ] 工厂模式 (PluginFactory)
- [ ] 智能加载器 (SmartPluginLoader)
- [ ] 异步支持 (AsyncSyncCoordinator)
- [ ] 缓存机制
- [ ] 完整测试套件
- [ ] 文档 (README.md)

### ✅ **高级项目**
- [ ] 性能优化
- [ ] 错误处理
- [ ] 日志记录
- [ ] 监控指标
- [ ] 扩展接口

## 🏆 标准符合性评估

### 📊 **评估标准**
| 项目 | 权重 | 评分标准 |
|------|------|----------|
| **插件定义** | 20% | 完整性、规范性 |
| **架构设计** | 25% | 工厂模式、智能加载器 |
| **异步支持** | 20% | 异步优先、智能协调 |
| **集成质量** | 15% | 模板引擎集成 |
| **测试覆盖** | 10% | 测试完整性 |
| **文档质量** | 10% | 文档完整性 |

### 🎯 **符合性等级**
- **A级 (9.0-10.0)**: 完全符合标准，企业级质量
- **B级 (7.0-8.9)**: 基本符合标准，良好质量
- **C级 (5.0-6.9)**: 部分符合标准，需要改进
- **D级 (0.0-4.9)**: 不符合标准，需要重构

## 🚀 标准实施计划

### 1. **现有插件标准化** (P0)
- [ ] database插件: A级 ✅
- [ ] file_loader插件: A级 ✅
- [ ] ai插件: 待实施
- [ ] cache插件: 待评估
- [ ] http插件: 待评估

### 2. **新插件开发** (P1)
- [ ] 使用标准模板
- [ ] 强制性检查清单
- [ ] 代码审查标准

### 3. **工具支持** (P2)
- [ ] 插件生成器
- [ ] 标准检查工具
- [ ] 自动化测试

**🏗️ 插件标准规范制定完成！为所有插件提供统一的开发标准和质量保证！** 🎉
