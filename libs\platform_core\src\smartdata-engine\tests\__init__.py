"""
企业级模板引擎测试套件

提供全面的单元测试、集成测试和性能测试
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置测试日志
logging.basicConfig(
    level=logging.WARNING,  # 测试时减少日志输出
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 测试配置
TEST_CONFIG = {
    'enable_integration_tests': True,
    'enable_performance_tests': True,
    'enable_stress_tests': False,  # 默认关闭压力测试
    'test_database_url': ':memory:',  # 使用内存数据库进行测试
    'test_timeout': 30,  # 测试超时时间（秒）
    'coverage_threshold': 90,  # 代码覆盖率阈值
}

__version__ = '1.0.0'
__all__ = ['TEST_CONFIG']
