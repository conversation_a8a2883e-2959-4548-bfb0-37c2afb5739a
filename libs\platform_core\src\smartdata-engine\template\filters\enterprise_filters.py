"""
企业级过滤器

为模板引擎提供企业级数据处理所需的过滤器
"""

import re
import math
from typing import Any, Dict, List, Union, Iterable, Callable
from collections import defaultdict


def multiply_filter(value: Any, multiplier: Any) -> Any:
    """
    乘法过滤器
    
    支持：
    - 数字乘法
    - 列表元素乘法
    - 列表与列表对应元素乘法
    """
    try:
        if isinstance(value, (list, tuple)):
            if isinstance(multiplier, (list, tuple)) and len(value) == len(multiplier):
                # 对应元素相乘
                return [v * m for v, m in zip(value, multiplier)]
            else:
                # 每个元素乘以同一个数
                return [v * multiplier for v in value]
        else:
            # 直接乘法
            return value * multiplier
    except (TypeError, ValueError):
        return value


def flatten_filter(value: Any, depth: int = None) -> List[Any]:
    """
    扁平化过滤器
    
    将嵌套列表扁平化为一维列表
    """
    try:
        if not isinstance(value, (list, tuple)):
            return [value]
        
        result = []
        for item in value:
            if isinstance(item, (list, tuple)) and (depth is None or depth > 0):
                next_depth = None if depth is None else depth - 1
                result.extend(flatten_filter(item, next_depth))
            else:
                result.append(item)
        return result
    except Exception:
        return list(value) if hasattr(value, '__iter__') else [value]


def group_by_filter(value: Iterable, attribute: str) -> Dict[Any, List[Any]]:
    """
    分组过滤器
    
    按指定属性对列表进行分组
    """
    try:
        groups = defaultdict(list)
        
        for item in value:
            if isinstance(item, dict):
                key = item.get(attribute)
            elif hasattr(item, attribute):
                key = getattr(item, attribute)
            else:
                key = None
            
            groups[key].append(item)
        
        return dict(groups)
    except Exception:
        return {}


def safe_divide_filter(value: Any, divisor: Any, default: Any = 0) -> Any:
    """
    安全除法过滤器
    
    避免除零错误
    """
    try:
        if divisor == 0:
            return default
        return value / divisor
    except (TypeError, ValueError, ZeroDivisionError):
        return default


def format_number_filter(value: Any, decimals: int = 2, thousands_sep: str = ',') -> str:
    """
    数字格式化过滤器
    
    格式化数字显示
    """
    try:
        num = float(value)
        if decimals == 0:
            formatted = f"{int(num):,}"
        else:
            formatted = f"{num:,.{decimals}f}"
        
        if thousands_sep != ',':
            formatted = formatted.replace(',', thousands_sep)
        
        return formatted
    except (TypeError, ValueError):
        return str(value)


def percentage_filter(value: Any, total: Any, decimals: int = 1) -> str:
    """
    百分比过滤器
    
    计算并格式化百分比
    """
    try:
        if total == 0:
            return "0.0%"
        
        percentage = (float(value) / float(total)) * 100
        return f"{percentage:.{decimals}f}%"
    except (TypeError, ValueError, ZeroDivisionError):
        return "0.0%"


def truncate_filter(value: Any, length: int = 50, suffix: str = '...') -> str:
    """
    截断过滤器
    
    截断长文本
    """
    try:
        text = str(value)
        if len(text) <= length:
            return text
        return text[:length] + suffix
    except Exception:
        return str(value)


def slugify_filter(value: Any) -> str:
    """
    URL友好化过滤器
    
    生成URL友好的字符串
    """
    try:
        text = str(value).lower()
        # 移除特殊字符
        text = re.sub(r'[^\w\s-]', '', text)
        # 替换空格和多个连字符
        text = re.sub(r'[-\s]+', '-', text)
        return text.strip('-')
    except Exception:
        return str(value)


def deep_get_filter(data: Any, path: str, default: Any = None) -> Any:
    """
    深度获取过滤器
    
    通过路径获取嵌套数据
    """
    try:
        keys = path.split('.')
        current = data
        
        for key in keys:
            if isinstance(current, dict):
                current = current.get(key)
            elif hasattr(current, key):
                current = getattr(current, key)
            else:
                return default
            
            if current is None:
                return default
        
        return current
    except Exception:
        return default


def unique_filter(value: Iterable, attribute: str = None) -> List[Any]:
    """
    去重过滤器
    
    移除重复项
    """
    try:
        if attribute:
            seen = set()
            result = []
            for item in value:
                if isinstance(item, dict):
                    key = item.get(attribute)
                elif hasattr(item, attribute):
                    key = getattr(item, attribute)
                else:
                    key = item
                
                if key not in seen:
                    seen.add(key)
                    result.append(item)
            return result
        else:
            # 简单去重
            return list(dict.fromkeys(value))
    except Exception:
        return list(value) if hasattr(value, '__iter__') else [value]


def sort_by_filter(value: Iterable, attribute: str, reverse: bool = False) -> List[Any]:
    """
    排序过滤器
    
    按指定属性排序
    """
    try:
        def get_sort_key(item):
            if isinstance(item, dict):
                return item.get(attribute, 0)
            elif hasattr(item, attribute):
                return getattr(item, attribute, 0)
            else:
                return item
        
        return sorted(value, key=get_sort_key, reverse=reverse)
    except Exception:
        return list(value)


def chunk_filter(value: Iterable, size: int) -> List[List[Any]]:
    """
    分块过滤器
    
    将列表分成指定大小的块
    """
    try:
        items = list(value)
        return [items[i:i + size] for i in range(0, len(items), size)]
    except Exception:
        return [list(value)]


def sum_by_filter(value: Iterable, attribute: str) -> Any:
    """
    按属性求和过滤器
    
    对指定属性求和
    """
    try:
        total = 0
        for item in value:
            if isinstance(item, dict):
                val = item.get(attribute, 0)
            elif hasattr(item, attribute):
                val = getattr(item, attribute, 0)
            else:
                val = 0
            
            total += float(val) if val is not None else 0
        
        return total
    except Exception:
        return 0


def avg_by_filter(value: Iterable, attribute: str) -> float:
    """
    按属性求平均值过滤器
    
    对指定属性求平均值
    """
    try:
        items = list(value)
        if not items:
            return 0
        
        total = sum_by_filter(items, attribute)
        return total / len(items)
    except Exception:
        return 0


def max_by_filter(value: Iterable, attribute: str) -> Any:
    """
    按属性求最大值过滤器
    """
    try:
        def get_value(item):
            if isinstance(item, dict):
                return item.get(attribute, 0)
            elif hasattr(item, attribute):
                return getattr(item, attribute, 0)
            else:
                return item
        
        return max(value, key=get_value)
    except Exception:
        return None


def min_by_filter(value: Iterable, attribute: str) -> Any:
    """
    按属性求最小值过滤器
    """
    try:
        def get_value(item):
            if isinstance(item, dict):
                return item.get(attribute, 0)
            elif hasattr(item, attribute):
                return getattr(item, attribute, 0)
            else:
                return item
        
        return min(value, key=get_value)
    except Exception:
        return None


def bool_filter(value: Any) -> bool:
    """
    布尔过滤器

    将值转换为布尔类型
    """
    try:
        return bool(value)
    except Exception:
        return False


def count_by_filter(value: Iterable, attribute: str, target_value: Any = None) -> int:
    """
    按属性计数过滤器
    
    统计指定属性值的数量
    """
    try:
        count = 0
        for item in value:
            if isinstance(item, dict):
                val = item.get(attribute)
            elif hasattr(item, attribute):
                val = getattr(item, attribute)
            else:
                val = item
            
            if target_value is None:
                # 计数非空值
                if val is not None:
                    count += 1
            else:
                # 计数特定值
                if val == target_value:
                    count += 1
        
        return count
    except Exception:
        return 0


# 过滤器注册表
ENTERPRISE_FILTERS = {
    'multiply': multiply_filter,
    'flatten': flatten_filter,
    'group_by': group_by_filter,
    'safe_divide': safe_divide_filter,
    'format_number': format_number_filter,
    'percentage': percentage_filter,
    'truncate': truncate_filter,
    'slugify': slugify_filter,
    'deep_get': deep_get_filter,
    'unique': unique_filter,
    'sort_by': sort_by_filter,
    'chunk': chunk_filter,
    'sum_by': sum_by_filter,
    'avg_by': avg_by_filter,
    'max_by': max_by_filter,
    'min_by': min_by_filter,
    'count_by': count_by_filter,
    'bool': bool_filter,
}


def register_enterprise_filters(env):
    """
    注册企业级过滤器到Jinja2环境
    """
    env.filters.update(ENTERPRISE_FILTERS)
    return env
