#!/usr/bin/env python3
"""
模板连接管理器

提供模板作用域内的数据库连接单例管理：
- 模板级别的连接复用
- 自动连接生命周期管理
- 上下文感知的连接清理
- 性能监控和统计
"""

import asyncio
import logging
import time
import weakref
from typing import Dict, Any, Optional, Set
from contextlib import asynccontextmanager
from datetime import datetime, timedelta

try:
    from .global_pool_manager import global_pool_manager, PoolConfig
    from .connectors import ConnectionConfig
except ImportError:
    # 处理相对导入问题
    import sys
    import os
    current_dir = os.path.dirname(__file__)
    sys.path.insert(0, current_dir)
    from global_pool_manager import global_pool_manager, PoolConfig
    from connectors import ConnectionConfig


class TemplateConnectionManager:
    """模板作用域连接管理器
    
    为单个模板提供连接管理服务：
    - 连接单例模式：同一模板内相同数据库只创建一个连接
    - 自动清理：模板执行完成后自动释放所有连接
    - 性能监控：跟踪连接使用情况和性能指标
    """
    
    def __init__(self, template_id: str):
        self.template_id = template_id
        self._connections: Dict[str, Any] = {}
        self._connection_times: Dict[str, float] = {}
        self._lock = asyncio.Lock()
        self.logger = logging.getLogger(f"{__name__}.TemplateConnectionManager")
        self._active = True
        self._created_at = datetime.now()
        self._query_count = 0
        self._total_query_time = 0.0
    
    async def get_connection(self, 
                           db_instance: str,
                           environment: str,
                           config: ConnectionConfig,
                           pool_config: Optional[PoolConfig] = None) -> Any:
        """获取模板作用域内的单例连接
        
        Args:
            db_instance: 数据库实例名称
            environment: 环境名称
            config: 数据库连接配置
            pool_config: 连接池配置
            
        Returns:
            数据库连接对象
        """
        if not self._active:
            raise RuntimeError(f"模板连接管理器已关闭: {self.template_id}")
        
        # 生成连接池键
        db_type = self._infer_db_type(config)
        pool_key = global_pool_manager.get_pool_key(db_instance, environment, db_type)
        
        # 检查是否已有连接
        if pool_key not in self._connections:
            async with self._lock:
                if pool_key not in self._connections and self._active:
                    await self._create_connection(pool_key, db_instance, environment, config, pool_config)
        
        return self._connections.get(pool_key)
    
    async def _create_connection(self, 
                               pool_key: str,
                               db_instance: str, 
                               environment: str,
                               config: ConnectionConfig,
                               pool_config: Optional[PoolConfig]):
        """创建新连接"""
        try:
            start_time = time.time()
            
            # 获取全局连接池
            pool = await global_pool_manager.get_or_create_pool(
                db_instance, environment, config, pool_config
            )
            
            # 从连接池获取连接
            connection = await global_pool_manager.get_connection(pool_key)
            
            # 保存连接和时间信息
            self._connections[pool_key] = connection
            self._connection_times[pool_key] = time.time() - start_time
            
            self.logger.debug(f"模板 {self.template_id} 获取连接: {pool_key} "
                            f"(耗时: {self._connection_times[pool_key]:.3f}s)")
            
        except Exception as e:
            self.logger.error(f"模板 {self.template_id} 创建连接失败 {pool_key}: {e}")
            raise
    
    def _infer_db_type(self, config: ConnectionConfig) -> str:
        """推断数据库类型"""
        if hasattr(config, 'db_type') and config.db_type:
            return config.db_type
        
        # 根据端口推断
        port_mapping = {
            3306: 'mysql',
            5432: 'postgresql', 
            6379: 'redis',
            27017: 'mongodb',
            9200: 'elasticsearch',
            1521: 'oracle'
        }
        
        return port_mapping.get(config.port, 'mysql')
    
    async def execute_query(self, 
                          connection: Any, 
                          query: str, 
                          parameters: Optional[Dict[str, Any]] = None) -> Any:
        """执行查询并记录性能指标"""
        start_time = time.time()
        
        try:
            # 这里需要根据具体的连接器实现来执行查询
            # 暂时返回模拟结果
            result = await self._execute_query_with_connection(connection, query, parameters)
            
            # 记录性能指标
            query_time = time.time() - start_time
            self._query_count += 1
            self._total_query_time += query_time
            
            self.logger.debug(f"模板 {self.template_id} 查询执行完成 "
                            f"(耗时: {query_time:.3f}s, 总查询: {self._query_count})")
            
            return result
            
        except Exception as e:
            self.logger.error(f"模板 {self.template_id} 查询执行失败: {e}")
            raise
    
    async def _execute_query_with_connection(self, connection: Any, query: str, parameters: Optional[Dict[str, Any]]) -> Any:
        """使用连接执行查询 - 需要根据具体连接器实现"""
        # 这是一个占位符实现，实际需要根据连接器类型来实现
        # 可以通过连接对象的类型来判断使用哪种执行方式
        
        # 模拟查询执行
        await asyncio.sleep(0.001)  # 模拟查询延迟
        
        return {
            'success': True,
            'data': [{'result': 'mock_data'}],
            'affected_rows': 1,
            'execution_time': 0.001
        }
    
    async def cleanup(self):
        """清理模板作用域连接"""
        if not self._active:
            return
        
        self._active = False
        
        cleanup_tasks = []
        for pool_key, connection in self._connections.items():
            task = asyncio.create_task(self._release_connection(pool_key, connection))
            cleanup_tasks.append(task)
        
        if cleanup_tasks:
            try:
                await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            except Exception as e:
                self.logger.error(f"模板 {self.template_id} 清理连接时出错: {e}")
        
        self._connections.clear()
        self._connection_times.clear()
        
        # 记录统计信息
        lifetime = (datetime.now() - self._created_at).total_seconds()
        avg_query_time = self._total_query_time / max(1, self._query_count)
        
        self.logger.info(f"模板 {self.template_id} 连接管理器已清理 "
                        f"(生命周期: {lifetime:.1f}s, 查询数: {self._query_count}, "
                        f"平均查询时间: {avg_query_time:.3f}s)")
    
    async def _release_connection(self, pool_key: str, connection: Any):
        """释放单个连接"""
        try:
            await global_pool_manager.release_connection(pool_key, connection)
            self.logger.debug(f"模板 {self.template_id} 释放连接: {pool_key}")
        except Exception as e:
            self.logger.error(f"模板 {self.template_id} 释放连接失败 {pool_key}: {e}")
    
    @asynccontextmanager
    async def connection_scope(self):
        """连接作用域上下文管理器"""
        try:
            yield self
        finally:
            await self.cleanup()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取模板连接统计信息"""
        lifetime = (datetime.now() - self._created_at).total_seconds()
        avg_query_time = self._total_query_time / max(1, self._query_count)
        
        return {
            'template_id': self.template_id,
            'active': self._active,
            'connection_count': len(self._connections),
            'query_count': self._query_count,
            'total_query_time': self._total_query_time,
            'avg_query_time': avg_query_time,
            'lifetime': lifetime,
            'created_at': self._created_at.isoformat(),
            'connections': list(self._connections.keys())
        }


class TemplateConnectionRegistry:
    """模板连接管理器注册表
    
    管理所有活跃的模板连接管理器：
    - 自动创建和清理管理器
    - 提供全局统计信息
    - 支持批量清理操作
    """
    
    def __init__(self):
        self._managers: Dict[str, TemplateConnectionManager] = {}
        self._lock = asyncio.Lock()
        self.logger = logging.getLogger(f"{__name__}.TemplateConnectionRegistry")
        
        # 使用弱引用来自动清理不再使用的管理器
        self._weak_refs: Set[weakref.ReferenceType] = set()
    
    async def get_manager(self, template_id: str) -> TemplateConnectionManager:
        """获取模板连接管理器"""
        if template_id not in self._managers:
            async with self._lock:
                if template_id not in self._managers:
                    manager = TemplateConnectionManager(template_id)
                    self._managers[template_id] = manager
                    
                    # 创建弱引用用于自动清理
                    weak_ref = weakref.ref(manager, lambda ref: self._cleanup_weak_ref(ref))
                    self._weak_refs.add(weak_ref)
                    
                    self.logger.debug(f"创建模板连接管理器: {template_id}")
        
        return self._managers[template_id]
    
    def _cleanup_weak_ref(self, ref: weakref.ReferenceType):
        """清理弱引用"""
        self._weak_refs.discard(ref)
    
    async def cleanup_manager(self, template_id: str):
        """清理指定模板的连接管理器"""
        if template_id in self._managers:
            manager = self._managers[template_id]
            await manager.cleanup()
            del self._managers[template_id]
            self.logger.debug(f"清理模板连接管理器: {template_id}")
    
    async def cleanup_all(self):
        """清理所有模板连接管理器"""
        manager_ids = list(self._managers.keys())
        cleanup_tasks = []
        
        for template_id in manager_ids:
            task = asyncio.create_task(self.cleanup_manager(template_id))
            cleanup_tasks.append(task)
        
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        self.logger.info(f"清理了 {len(manager_ids)} 个模板连接管理器")
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有模板的统计信息"""
        stats = {}
        for template_id, manager in self._managers.items():
            stats[template_id] = manager.get_stats()
        return stats
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取汇总统计信息"""
        total_managers = len(self._managers)
        total_connections = sum(len(m._connections) for m in self._managers.values())
        total_queries = sum(m._query_count for m in self._managers.values())
        
        return {
            'total_managers': total_managers,
            'total_connections': total_connections,
            'total_queries': total_queries,
            'active_templates': list(self._managers.keys())
        }


# 全局模板连接管理器注册表
template_registry = TemplateConnectionRegistry()


# 便捷函数
async def get_template_connection_manager(template_id: str) -> TemplateConnectionManager:
    """获取模板连接管理器"""
    return await template_registry.get_manager(template_id)


async def cleanup_template_manager(template_id: str):
    """清理模板连接管理器"""
    await template_registry.cleanup_manager(template_id)


async def cleanup_all_template_managers():
    """清理所有模板连接管理器"""
    await template_registry.cleanup_all()


def get_template_stats() -> Dict[str, Any]:
    """获取模板连接统计信息"""
    return {
        'summary': template_registry.get_summary_stats(),
        'details': template_registry.get_all_stats()
    }
