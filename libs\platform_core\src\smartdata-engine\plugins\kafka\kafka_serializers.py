"""
Kafka序列化器

提供多种序列化支持：
- JSON序列化器
- Avro序列化器
- Protobuf序列化器
- 自定义序列化器
"""

import json
import logging
from typing import Any, Dict, Optional, Union, Callable
from abc import ABC, abstractmethod


class BaseKafkaSerializer(ABC):
    """Kafka序列化器基类"""
    
    @abstractmethod
    def serialize(self, data: Any) -> bytes:
        """序列化数据"""
        pass
    
    @abstractmethod
    def deserialize(self, data: bytes) -> Any:
        """反序列化数据"""
        pass


class JsonKafkaSerializer(BaseKafkaSerializer):
    """JSON序列化器"""
    
    def __init__(self, encoding: str = 'utf-8', ensure_ascii: bool = False):
        self.encoding = encoding
        self.ensure_ascii = ensure_ascii
        self.logger = logging.getLogger(f"{__name__}.JsonKafkaSerializer")
    
    def serialize(self, data: Any) -> bytes:
        """序列化为JSON字节"""
        try:
            if data is None:
                return b''
            
            json_str = json.dumps(data, ensure_ascii=self.ensure_ascii, default=str)
            return json_str.encode(self.encoding)
        except Exception as e:
            self.logger.error(f"JSON序列化失败: {e}")
            raise
    
    def deserialize(self, data: bytes) -> Any:
        """从JSON字节反序列化"""
        try:
            if not data:
                return None
            
            json_str = data.decode(self.encoding)
            return json.loads(json_str)
        except Exception as e:
            self.logger.error(f"JSON反序列化失败: {e}")
            raise


class StringKafkaSerializer(BaseKafkaSerializer):
    """字符串序列化器"""
    
    def __init__(self, encoding: str = 'utf-8'):
        self.encoding = encoding
        self.logger = logging.getLogger(f"{__name__}.StringKafkaSerializer")
    
    def serialize(self, data: Any) -> bytes:
        """序列化为字符串字节"""
        try:
            if data is None:
                return b''
            
            if isinstance(data, bytes):
                return data
            
            return str(data).encode(self.encoding)
        except Exception as e:
            self.logger.error(f"字符串序列化失败: {e}")
            raise
    
    def deserialize(self, data: bytes) -> str:
        """从字符串字节反序列化"""
        try:
            if not data:
                return ''
            
            return data.decode(self.encoding)
        except Exception as e:
            self.logger.error(f"字符串反序列化失败: {e}")
            raise


class AvroKafkaSerializer(BaseKafkaSerializer):
    """Avro序列化器"""
    
    def __init__(self, schema: Optional[str] = None, schema_registry_url: Optional[str] = None):
        self.schema = schema
        self.schema_registry_url = schema_registry_url
        self.logger = logging.getLogger(f"{__name__}.AvroKafkaSerializer")
        
        # 检查avro库可用性
        self._avro_available = self._check_avro_availability()
        
        if self._avro_available and schema:
            self._init_avro_schema()
    
    def _check_avro_availability(self) -> bool:
        """检查Avro库是否可用"""
        try:
            import avro.schema
            import avro.io
            return True
        except ImportError:
            self.logger.warning("avro-python3未安装，Avro序列化功能将受限")
            return False
    
    def _init_avro_schema(self):
        """初始化Avro模式"""
        if not self._avro_available:
            return
        
        try:
            import avro.schema
            self.avro_schema = avro.schema.parse(self.schema)
        except Exception as e:
            self.logger.error(f"Avro模式解析失败: {e}")
            self.avro_schema = None
    
    def serialize(self, data: Any) -> bytes:
        """Avro序列化"""
        if not self._avro_available:
            raise ImportError("avro-python3未安装，请运行: pip install avro-python3")
        
        if not hasattr(self, 'avro_schema') or self.avro_schema is None:
            raise ValueError("Avro模式未设置")
        
        try:
            import avro.io
            import io
            
            writer = avro.io.DatumWriter(self.avro_schema)
            bytes_writer = io.BytesIO()
            encoder = avro.io.BinaryEncoder(bytes_writer)
            writer.write(data, encoder)
            return bytes_writer.getvalue()
        except Exception as e:
            self.logger.error(f"Avro序列化失败: {e}")
            raise
    
    def deserialize(self, data: bytes) -> Any:
        """Avro反序列化"""
        if not self._avro_available:
            raise ImportError("avro-python3未安装，请运行: pip install avro-python3")
        
        if not hasattr(self, 'avro_schema') or self.avro_schema is None:
            raise ValueError("Avro模式未设置")
        
        try:
            import avro.io
            import io
            
            reader = avro.io.DatumReader(self.avro_schema)
            bytes_reader = io.BytesIO(data)
            decoder = avro.io.BinaryDecoder(bytes_reader)
            return reader.read(decoder)
        except Exception as e:
            self.logger.error(f"Avro反序列化失败: {e}")
            raise


class ProtobufKafkaSerializer(BaseKafkaSerializer):
    """Protobuf序列化器"""
    
    def __init__(self, message_class: Optional[type] = None):
        self.message_class = message_class
        self.logger = logging.getLogger(f"{__name__}.ProtobufKafkaSerializer")
        
        # 检查protobuf库可用性
        self._protobuf_available = self._check_protobuf_availability()
    
    def _check_protobuf_availability(self) -> bool:
        """检查Protobuf库是否可用"""
        try:
            import google.protobuf
            return True
        except ImportError:
            self.logger.warning("protobuf未安装，Protobuf序列化功能将受限")
            return False
    
    def serialize(self, data: Any) -> bytes:
        """Protobuf序列化"""
        if not self._protobuf_available:
            raise ImportError("protobuf未安装，请运行: pip install protobuf")
        
        if self.message_class is None:
            raise ValueError("Protobuf消息类未设置")
        
        try:
            if isinstance(data, self.message_class):
                return data.SerializeToString()
            else:
                # 尝试从字典创建消息
                message = self.message_class()
                if isinstance(data, dict):
                    for key, value in data.items():
                        if hasattr(message, key):
                            setattr(message, key, value)
                return message.SerializeToString()
        except Exception as e:
            self.logger.error(f"Protobuf序列化失败: {e}")
            raise
    
    def deserialize(self, data: bytes) -> Any:
        """Protobuf反序列化"""
        if not self._protobuf_available:
            raise ImportError("protobuf未安装，请运行: pip install protobuf")
        
        if self.message_class is None:
            raise ValueError("Protobuf消息类未设置")
        
        try:
            message = self.message_class()
            message.ParseFromString(data)
            return message
        except Exception as e:
            self.logger.error(f"Protobuf反序列化失败: {e}")
            raise


class CustomKafkaSerializer(BaseKafkaSerializer):
    """自定义序列化器"""
    
    def __init__(self, serialize_func: Callable[[Any], bytes], deserialize_func: Callable[[bytes], Any]):
        self.serialize_func = serialize_func
        self.deserialize_func = deserialize_func
        self.logger = logging.getLogger(f"{__name__}.CustomKafkaSerializer")
    
    def serialize(self, data: Any) -> bytes:
        """使用自定义函数序列化"""
        try:
            return self.serialize_func(data)
        except Exception as e:
            self.logger.error(f"自定义序列化失败: {e}")
            raise
    
    def deserialize(self, data: bytes) -> Any:
        """使用自定义函数反序列化"""
        try:
            return self.deserialize_func(data)
        except Exception as e:
            self.logger.error(f"自定义反序列化失败: {e}")
            raise


class SerializerFactory:
    """序列化器工厂"""
    
    _serializers = {
        'json': JsonKafkaSerializer,
        'string': StringKafkaSerializer,
        'avro': AvroKafkaSerializer,
        'protobuf': ProtobufKafkaSerializer,
        'custom': CustomKafkaSerializer
    }
    
    @classmethod
    def get_serializer(cls, serializer_type: str, **kwargs) -> Callable[[Any], bytes]:
        """获取序列化函数"""
        if serializer_type not in cls._serializers:
            raise ValueError(f"不支持的序列化器类型: {serializer_type}")
        
        serializer_class = cls._serializers[serializer_type]
        serializer = serializer_class(**kwargs)
        return serializer.serialize
    
    @classmethod
    def get_deserializer(cls, serializer_type: str, **kwargs) -> Callable[[bytes], Any]:
        """获取反序列化函数"""
        if serializer_type not in cls._serializers:
            raise ValueError(f"不支持的序列化器类型: {serializer_type}")
        
        serializer_class = cls._serializers[serializer_type]
        serializer = serializer_class(**kwargs)
        return serializer.deserialize
    
    @classmethod
    def register_serializer(cls, name: str, serializer_class: type):
        """注册自定义序列化器"""
        cls._serializers[name] = serializer_class
    
    @classmethod
    def get_supported_types(cls) -> list:
        """获取支持的序列化器类型"""
        return list(cls._serializers.keys())


# 便捷函数
def create_json_serializer(encoding: str = 'utf-8', ensure_ascii: bool = False) -> JsonKafkaSerializer:
    """创建JSON序列化器"""
    return JsonKafkaSerializer(encoding=encoding, ensure_ascii=ensure_ascii)


def create_string_serializer(encoding: str = 'utf-8') -> StringKafkaSerializer:
    """创建字符串序列化器"""
    return StringKafkaSerializer(encoding=encoding)


def create_avro_serializer(schema: str, schema_registry_url: Optional[str] = None) -> AvroKafkaSerializer:
    """创建Avro序列化器"""
    return AvroKafkaSerializer(schema=schema, schema_registry_url=schema_registry_url)


def create_protobuf_serializer(message_class: type) -> ProtobufKafkaSerializer:
    """创建Protobuf序列化器"""
    return ProtobufKafkaSerializer(message_class=message_class)


def create_custom_serializer(serialize_func: Callable[[Any], bytes], 
                           deserialize_func: Callable[[bytes], Any]) -> CustomKafkaSerializer:
    """创建自定义序列化器"""
    return CustomKafkaSerializer(serialize_func=serialize_func, deserialize_func=deserialize_func)
