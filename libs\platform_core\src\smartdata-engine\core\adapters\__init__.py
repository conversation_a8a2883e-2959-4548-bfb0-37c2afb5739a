"""
数据适配器模块

提供各种数据源的适配器实现，支持插件化扩展
"""

from .base import BaseDataAdapter, IDatabaseAdapter, IApiAdapter, IFileAdapter
from .database import DatabaseAdapterBase, PostgreSQLAdapter, MySQLAdapter, SQLiteAdapter
from .database import AsyncPostgreSQLAdapter, AsyncMySQLAdapter, AsyncSQLiteAdapter
from .api import RestAPIAdapter
from .file import CSVAdapter, JSONAdapter, HTMLAdapter, XMLAdapter
from .memory import DataListAdapter

__all__ = [
    # 基础适配器
    'BaseDataAdapter',
    'IDatabaseAdapter',
    'IApiAdapter',
    'IFileAdapter',

    # 同步数据库适配器
    'DatabaseAdapterBase',
    'PostgreSQLAdapter',
    'MySQLAdapter',
    'SQLiteAdapter',

    # 异步数据库适配器
    'AsyncPostgreSQLAdapter',
    'AsyncMySQLAdapter',
    'AsyncSQLiteAdapter',

    # API适配器
    'RestAPIAdapter',

    # 文件适配器
    'CSVAdapter',
    'JSONAdapter',
    'HTMLAdapter',
    'XMLAdapter',

    # 内存适配器
    'DataListAdapter',
]
