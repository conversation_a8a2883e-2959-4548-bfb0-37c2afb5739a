"""
企业级模板引擎集成演示

展示新的统一适配器系统与模板引擎的深度集成
"""

import asyncio
import logging
import tempfile
import os
from typing import Dict, Any

import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../'))

from template.enterprise_template_factory import (
    EnterpriseTemplateFactory, 
    EnterpriseTemplateConfig,
    TemplateEngineMode,
    create_enterprise_template_engine
)


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def demo_basic_template_rendering():
    """演示基础模板渲染"""
    print("\n🎯 基础模板渲染演示")
    print("=" * 60)
    
    # 创建企业级模板引擎
    engine = EnterpriseTemplateFactory.create_engine()
    
    # 基础模板
    template = """
Hello {{ name }}!
Today is {{ date }}.
Your score is {{ score }}.
"""
    
    context = {
        'name': 'Alice',
        'date': '2025-07-28',
        'score': 95
    }
    
    try:
        result = engine.render(template, context)
        print("✅ 基础模板渲染成功:")
        print(result)
    except Exception as e:
        print(f"❌ 基础模板渲染失败: {e}")


def demo_database_integration():
    """演示数据库集成"""
    print("\n🗄️ 数据库集成演示")
    print("=" * 60)
    
    # 创建企业级模板引擎
    engine = EnterpriseTemplateFactory.create_engine()
    
    # 数据库模板
    template = """
{% set users = sd.database(':memory:').query('SELECT * FROM users') %}
用户列表:
{% for user in users %}
- {{ user.name }} ({{ user.email }})
{% endfor %}
总用户数: {{ users|length }}
"""
    
    # 创建临时SQLite数据库
    db_connection = ':memory:'
    
    context = {
        'db_connection': db_connection
    }
    
    try:
        result = engine.render(template, context)
        print("✅ 数据库集成渲染成功:")
        print(result)
    except Exception as e:
        print(f"❌ 数据库集成渲染失败: {e}")


def demo_file_integration():
    """演示文件集成"""
    print("\n📁 文件集成演示")
    print("=" * 60)
    
    # 创建企业级模板引擎
    engine = EnterpriseTemplateFactory.create_engine()
    
    # 创建临时CSV文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        f.write("id,name,age,city\n")
        f.write("1,Alice,30,New York\n")
        f.write("2,Bob,25,Los Angeles\n")
        f.write("3,Charlie,35,Chicago\n")
        csv_file = f.name
    
    try:
        # 文件模板
        template = f"""
{{% set data = sd.file('{csv_file}').read() %}}
CSV文件数据:
{{% for row in data %}}
- {{ row.name }} ({{ row.age }}岁) 来自 {{ row.city }}
{{% endfor %}}
总记录数: {{ data|length }}
"""
        
        result = engine.render(template)
        print("✅ 文件集成渲染成功:")
        print(result)
        
    except Exception as e:
        print(f"❌ 文件集成渲染失败: {e}")
    finally:
        # 清理临时文件
        if os.path.exists(csv_file):
            os.unlink(csv_file)


def demo_api_integration():
    """演示API集成"""
    print("\n🌐 API集成演示")
    print("=" * 60)
    
    # 创建企业级模板引擎
    engine = EnterpriseTemplateFactory.create_engine()
    
    # API模板
    template = """
{% set api_config = {'base_url': 'https://jsonplaceholder.typicode.com'} %}
{% set posts = sd.api(api_config).get('/posts?_limit=3') %}
最新文章:
{% for post in posts %}
- {{ post.title }}
{% endfor %}
"""
    
    try:
        result = engine.render(template)
        print("✅ API集成渲染成功:")
        print(result)
    except Exception as e:
        print(f"❌ API集成渲染失败 (可能是网络问题): {e}")


def demo_memory_data_integration():
    """演示内存数据集成"""
    print("\n💾 内存数据集成演示")
    print("=" * 60)
    
    # 创建企业级模板引擎
    engine = EnterpriseTemplateFactory.create_engine()
    
    # 内存数据模板
    template = """
{% set products = sd.memory(product_list).filter({'category': 'electronics'}) %}
电子产品:
{% for product in products %}
- {{ product.name }}: ${{ product.price }}
{% endfor %}

{% set total = sd.memory(product_list).aggregate({'price': 'sum'}) %}
总价值: ${{ total.price_sum }}
"""
    
    context = {
        'product_list': [
            {'name': 'Laptop', 'price': 999.99, 'category': 'electronics'},
            {'name': 'Mouse', 'price': 29.99, 'category': 'electronics'},
            {'name': 'Book', 'price': 19.99, 'category': 'books'},
            {'name': 'Phone', 'price': 699.99, 'category': 'electronics'}
        ]
    }
    
    try:
        result = engine.render(template, context)
        print("✅ 内存数据集成渲染成功:")
        print(result)
    except Exception as e:
        print(f"❌ 内存数据集成渲染失败: {e}")


async def demo_async_template_rendering():
    """演示异步模板渲染"""
    print("\n⚡ 异步模板渲染演示")
    print("=" * 60)
    
    # 创建企业级模板引擎
    engine = EnterpriseTemplateFactory.create_high_performance_engine()
    
    # 异步模板
    template = """
{% set start_time = now() %}
处理开始时间: {{ start_time }}

{% set data = sd.loader(data_source) %}
数据处理结果:
{% for item in data %}
- {{ item.name }}: {{ item.value }}
{% endfor %}

处理完成!
"""
    
    context = {
        'data_source': [
            {'name': 'Item 1', 'value': 100},
            {'name': 'Item 2', 'value': 200},
            {'name': 'Item 3', 'value': 300}
        ]
    }
    
    try:
        result = await engine.render_async(template, context)
        print("✅ 异步模板渲染成功:")
        print(result)
    except Exception as e:
        print(f"❌ 异步模板渲染失败: {e}")


def demo_performance_monitoring():
    """演示性能监控"""
    print("\n📊 性能监控演示")
    print("=" * 60)
    
    # 创建高性能模板引擎
    engine = EnterpriseTemplateFactory.create_high_performance_engine()
    
    # 执行多次渲染
    template = "Hello {{ name }}! Your ID is {{ id }}."
    
    for i in range(10):
        context = {'name': f'User{i}', 'id': i}
        engine.render(template, context)
    
    # 获取性能统计
    stats = engine.get_performance_stats()
    
    print("✅ 性能统计:")
    print(f"  渲染次数: {stats['render_count']}")
    print(f"  总渲染时间: {stats['total_render_time']:.4f}秒")
    print(f"  平均渲染时间: {stats['average_render_time']:.4f}秒")
    print(f"  错误次数: {stats['error_count']}")
    print(f"  错误率: {stats['error_rate']:.2%}")
    print(f"  支持的适配器数量: {stats['registered_adapters']}")
    print(f"  活跃作用域数量: {stats['active_scopes']}")


def demo_different_engine_modes():
    """演示不同引擎模式"""
    print("\n🔧 不同引擎模式演示")
    print("=" * 60)
    
    modes = [
        (TemplateEngineMode.ENTERPRISE, "企业级模式"),
        (TemplateEngineMode.PERFORMANCE, "高性能模式"),
        (TemplateEngineMode.DEBUG, "调试模式"),
        (TemplateEngineMode.HYBRID, "混合模式")
    ]
    
    template = "Mode: {{ mode }}, Data: {{ data }}"
    context = {'data': 'test'}
    
    for mode, description in modes:
        try:
            engine = create_enterprise_template_engine(mode=mode)
            context['mode'] = description
            result = engine.render(template, context)
            print(f"✅ {description}: {result.strip()}")
            
            # 显示支持的数据类型数量
            types_count = len(engine.get_supported_data_types())
            print(f"   支持 {types_count} 种数据类型")
            
        except Exception as e:
            print(f"❌ {description} 失败: {e}")


async def main():
    """主演示函数"""
    print("🚀 企业级模板引擎集成演示")
    print("=" * 80)
    print("本演示展示新的统一适配器系统与模板引擎的深度集成")
    
    # 设置日志
    setup_logging()
    
    try:
        # 基础功能演示
        demo_basic_template_rendering()
        demo_database_integration()
        demo_file_integration()
        demo_api_integration()
        demo_memory_data_integration()
        
        # 异步功能演示
        await demo_async_template_rendering()
        
        # 高级功能演示
        demo_performance_monitoring()
        demo_different_engine_modes()
        
        print("\n🎉 企业级模板引擎集成演示完成！")
        print("=" * 80)
        print("📈 集成特性总结:")
        print("  • 统一数据访问: 所有数据源通过统一接口访问")
        print("  • 智能适配器选择: 自动选择最佳适配器")
        print("  • 企业级生命周期管理: 自动资源管理和清理")
        print("  • 同步/异步支持: 灵活的渲染模式")
        print("  • 向后兼容: 与现有模板引擎无缝集成")
        print("  • 性能监控: 内置性能统计和监控")
        print("  • 多种引擎模式: 适应不同使用场景")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    asyncio.run(main())
