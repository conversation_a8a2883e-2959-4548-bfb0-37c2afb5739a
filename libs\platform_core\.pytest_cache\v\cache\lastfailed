{"src/smartdata-engine/tests/test_smart_matcher_integration.py": true, "src/smartdata-engine/tests/test_complete_integration.py": true, "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_path_finding_in_template": true, "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_value_retrieval_in_template": true, "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_key_finding_in_template": true, "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_json_string_handling": true, "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_xml_string_handling": true, "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_html_string_handling": true, "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_error_handling": true, "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_performance_with_large_data": true, "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_integration_with_smart_data": true, "src/smartdata-engine/tests/test_file_loader_enhancement.py::TestFileLoaderEnhancement": true, "src/smartdata-engine/tests/test_async_sync_coordinator_fix.py": true, "src/smartdata-engine/plugins/rest/tests/test_comprehensive_rest_methods.py::TestComprehensiveRestMethods::test_rest_authentication_methods": true, "src/smartdata-engine/plugins/ai/tests/test_ai_plugin_standards.py": true, "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncPerformance::test_single_query_performance": true, "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncPerformance::test_parallel_queries_performance": true, "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncPerformance::test_sequential_vs_parallel_comparison": true, "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncPerformance::test_stream_query_performance": true, "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncPerformance::test_template_scope_parallel_performance": true, "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncPerformance::test_connection_pool_performance": true, "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncPerformance::test_memory_usage_efficiency": true, "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncPerformance::test_performance_monitoring": true, "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncIntegrationPerformance::test_real_sqlite_performance": true}