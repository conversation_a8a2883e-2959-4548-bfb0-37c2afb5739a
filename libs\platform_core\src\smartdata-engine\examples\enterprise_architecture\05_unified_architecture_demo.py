#!/usr/bin/env python3
"""
统一架构演示

展示统一架构改进后的强大功能：
1. 自然Python语法支持
2. 统一的XPath/JSONPath支持
3. 可编辑的数据操作
4. 多格式数据处理
"""

import sys
import os
import json
import tempfile
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.enterprise_template_integration import EnterpriseTemplateIntegration
from template.extensions.natural_syntax_extension import PythonStyleTemplateEngine
from core.unified_data_enhancement import EnhancedDict, EnhancedList, enhance_data


def unified_architecture_demo():
    """统一架构功能演示"""
    print("=== 统一架构改进演示 ===")
    
    # 🚀 示例1：增强字典的XPath功能
    print("\n🚀 示例1：增强字典的XPath功能")
    
    # 创建复杂的嵌套数据
    complex_data = {
        'company': {
            'name': '智慧科技有限公司',
            'employees': [
                {
                    'id': 1,
                    'name': '张三',
                    'department': '技术部',
                    'skills': ['Python', 'JavaScript', 'SQL'],
                    'salary': 15000,
                    'projects': [
                        {'name': '项目A', 'status': '进行中'},
                        {'name': '项目B', 'status': '已完成'}
                    ]
                },
                {
                    'id': 2,
                    'name': '李四',
                    'department': '销售部',
                    'skills': ['销售', '客户管理'],
                    'salary': 12000,
                    'projects': [
                        {'name': '项目C', 'status': '已完成'}
                    ]
                }
            ],
            'departments': ['技术部', '销售部', '市场部']
        }
    }
    
    # 使用统一数据增强器
    enhanced_data = enhance_data(complex_data)
    
    print("✅ 数据增强完成")
    print(f"数据类型: {type(enhanced_data)}")
    
    # 测试路径访问
    print("\n📍 路径访问测试:")
    print(f"公司名称: {enhanced_data.get('company.name')}")
    print(f"员工数量: {len(enhanced_data.get('company.employees', []))}")
    print(f"第一个员工姓名: {enhanced_data.get('company.employees[0].name')}")
    print(f"第一个员工技能: {enhanced_data.get('company.employees[0].skills')}")
    print(f"技术部门是否存在: {enhanced_data.exists('company.departments')}")
    
    # 测试数据修改
    print("\n✏️ 数据修改测试:")
    enhanced_data.set('company.location', '北京市朝阳区')
    enhanced_data.set('company.employees[0].bonus', 5000)
    
    print(f"新增公司地址: {enhanced_data.get('company.location')}")
    print(f"员工奖金: {enhanced_data.get('company.employees[0].bonus')}")
    
    # 测试数据删除
    print("\n🗑️ 数据删除测试:")
    print(f"删除前部门数: {len(enhanced_data.get('company.departments', []))}")
    enhanced_data.delete('company.departments[2]')  # 删除市场部
    print(f"删除后部门数: {len(enhanced_data.get('company.departments', []))}")
    
    # 🎯 示例2：自然语法模板
    print("\n🎯 示例2：自然语法模板演示")
    
    # 创建Python风格模板引擎
    python_engine = PythonStyleTemplateEngine()
    
    # 自然语法模板
    natural_template = """
员工评分系统
============

{% function calculate_score(employee) %}
   base_score = employee.salary / 1000
   skill_bonus = employee.skills | length * 5
   project_bonus = employee.projects | length * 10
   return base_score + skill_bonus + project_bonus
{% endfunction %}

{% function get_department_stats(employees, dept_name) %}
   dept_employees = employees | selectattr('department', 'equalto', dept_name) | list
   total_salary = dept_employees | sum(attribute='salary')
   avg_salary = total_salary / (dept_employees | length) if dept_employees else 0
   return {'count': dept_employees | length, 'total_salary': total_salary, 'avg_salary': avg_salary}
{% endfunction %}

员工评分:
{%- for emp in company.employees %}
{{ emp.name }} ({{ emp.department }}):
  基础分数: {{ calculate_score(emp) }}
  薪资: ¥{{ emp.salary }}
  技能数: {{ emp.skills | length }}
  项目数: {{ emp.projects | length }}
{%- endfor %}

部门统计:
技术部: {{ get_department_stats(company.employees, '技术部') }}
销售部: {{ get_department_stats(company.employees, '销售部') }}
    """.strip()
    
    # 预处理模板（转换为Jinja2语法）
    processed_template = python_engine.preprocess_template(natural_template)
    
    print("🔄 自然语法模板预处理完成")
    print("预处理后的模板片段:")
    print(processed_template[:200] + "...")
    
    # 🔧 示例3：模板引擎集成
    print("\n🔧 示例3：企业级模板引擎集成")
    
    # 创建企业级模板集成器
    integration = EnterpriseTemplateIntegration(
        enable_async=False,
        enable_legacy_support=False,
        enable_debug=False
    )
    
    # 创建模板作用域
    scope = integration.create_template_scope('unified_demo')
    
    # 简化的模板（使用增强数据）
    simple_template = """
统一架构功能演示
===============
公司: {{ company.name }}
地址: {{ company.location }}
员工总数: {{ company.employees | length }}

员工列表:
{%- for emp in company.employees %}
{{ loop.index }}. {{ emp.name }} - {{ emp.department }}
   薪资: ¥{{ "{:,}".format(emp.salary) }}
   技能: {{ emp.skills | join(', ') }}
   项目: {{ emp.projects | length }}个
   {%- if emp.bonus %}
   奖金: ¥{{ "{:,}".format(emp.bonus) }}
   {%- endif %}
{%- endfor %}

部门统计:
{%- for dept in company.departments %}
- {{ dept }}: {{ company.employees | selectattr('department', 'equalto', dept) | list | length }}人
{%- endfor %}

数据路径访问演示:
- 第一个员工: {{ company.employees[0].name }}
- 技术部员工数: {{ company.employees | selectattr('department', 'equalto', '技术部') | list | length }}
- 平均薪资: ¥{{ "{:,}".format((company.employees | sum(attribute='salary')) // (company.employees | length)) }}
    """.strip()
    
    # 渲染模板
    context = {'company': enhanced_data.get('company')}
    result = integration.render_template_sync(simple_template, context)
    
    print("模板渲染结果:")
    print(result)
    
    # 清理作用域
    integration.cleanup_template_scope('unified_demo')
    
    # 📊 示例4：JSON数据的XPath操作
    print("\n📊 示例4：JSON数据的XPath操作")
    
    # 创建JSON数据
    json_data = {
        'api_response': {
            'status': 'success',
            'data': {
                'users': [
                    {'id': 1, 'name': 'Alice', 'active': True},
                    {'id': 2, 'name': 'Bob', 'active': False},
                    {'id': 3, 'name': 'Charlie', 'active': True}
                ],
                'pagination': {
                    'page': 1,
                    'total': 3,
                    'per_page': 10
                }
            }
        }
    }
    
    # 增强JSON数据
    enhanced_json = enhance_data(json_data)
    
    print("JSON XPath操作演示:")
    print(f"API状态: {enhanced_json.get('api_response.status')}")
    print(f"用户总数: {enhanced_json.get('api_response.data.pagination.total')}")
    print(f"第一个用户: {enhanced_json.get('api_response.data.users[0].name')}")
    print(f"活跃用户数: {len([u for u in enhanced_json.get('api_response.data.users', []) if u.get('active')])}")
    
    # 修改JSON数据
    enhanced_json.set('api_response.data.pagination.page', 2)
    enhanced_json.set('api_response.data.users[0].last_login', '2024-07-29')
    
    print(f"修改后页码: {enhanced_json.get('api_response.data.pagination.page')}")
    print(f"用户最后登录: {enhanced_json.get('api_response.data.users[0].last_login')}")
    
    # 🎨 示例5：路径查找功能
    print("\n🎨 示例5：路径查找功能")
    
    # 查找所有包含'name'的路径
    name_paths = enhanced_data.find('*name*')
    print(f"包含'name'的路径: {name_paths[:5]}...")  # 只显示前5个
    
    # 查找所有数组路径
    array_paths = enhanced_json.find('*[*')
    print(f"数组路径: {array_paths}")
    
    print("\n🎉 统一架构演示完成！")
    print("\n💡 统一架构优势:")
    print("1. ✅ 自然Python语法：更贴近原生Python编程体验")
    print("2. ✅ 统一XPath支持：所有数据类型都支持路径访问")
    print("3. ✅ 可编辑数据：支持路径的增删改操作")
    print("4. ✅ 多格式处理：JSON、XML、HTML等格式统一处理")
    print("5. ✅ 增强字典：Python dict的路径访问增强")
    print("6. ✅ 向后兼容：保留所有旧模板引擎功能")
    print("7. ✅ 性能优化：智能数据增强，按需处理")
    
    print("\n📖 功能对比:")
    print("旧架构: 功能强大但语法繁琐")
    print("新架构: 保持强大功能，语法更自然")
    print("统一架构: 两者优势结合，体验最佳")


if __name__ == "__main__":
    unified_architecture_demo()
