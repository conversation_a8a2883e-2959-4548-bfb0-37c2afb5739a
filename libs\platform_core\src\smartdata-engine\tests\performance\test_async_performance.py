"""
异步性能测试

测试异步架构的性能表现，对比同步和异步操作的性能差异
"""

import pytest
import asyncio
import time
import statistics
from typing import List, Dict, Any
from unittest.mock import Mock, patch

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from core.async_data_proxy import AsyncDataProxy
from core.async_template_scope import AsyncTemplateScope
from core.async_lifecycle_manager import AsyncLifecycleManager
from core.adapters.database.async_sqlite import AsyncSQLiteAdapter
from core.enterprise_data_architecture import DataRegistry


class MockAsyncAdapter:
    """模拟异步适配器用于性能测试"""
    
    def __init__(self, delay_ms: float = 100):
        self.delay_ms = delay_ms / 1000.0  # 转换为秒
        self.operation_count = 0
    
    async def create_async_connection(self, source):
        await asyncio.sleep(0.01)  # 模拟连接创建时间
        return Mock()
    
    async def close_async_connection(self, connection):
        await asyncio.sleep(0.01)
    
    async def async_query(self, connection, sql, params=None):
        await asyncio.sleep(self.delay_ms)
        self.operation_count += 1
        return [{'id': i, 'name': f'user_{i}'} for i in range(10)]
    
    async def async_execute(self, connection, sql, params=None):
        await asyncio.sleep(self.delay_ms)
        self.operation_count += 1
        return 1
    
    async def async_transaction(self, connection, operations):
        await asyncio.sleep(self.delay_ms * len(operations))
        self.operation_count += len(operations)
        return {'success': True, 'total_operations': len(operations)}
    
    async def async_batch(self, connection, operations):
        await asyncio.sleep(self.delay_ms * len(operations))
        self.operation_count += len(operations)
        return {'success': True, 'total_operations': len(operations)}
    
    async def async_stream_query(self, connection, sql, params=None):
        for i in range(100):
            await asyncio.sleep(self.delay_ms / 100)
            yield {'id': i, 'data': f'row_{i}'}


@pytest.mark.asyncio
class TestAsyncPerformance:
    """异步性能测试套件"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.registry = DataRegistry()
    
    async def test_single_query_performance(self):
        """测试单个查询的性能"""
        adapter = MockAsyncAdapter(delay_ms=50)
        proxy = AsyncDataProxy(':memory:', adapter)
        
        # 测试单个查询
        start_time = time.time()
        result = await proxy.query("SELECT * FROM users")
        end_time = time.time()
        
        execution_time = (end_time - start_time) * 1000
        
        assert result.success is True
        assert len(result.data) == 10
        assert execution_time < 100  # 应该在100ms内完成
        
        await proxy.close()
    
    async def test_parallel_queries_performance(self):
        """测试并行查询的性能优势"""
        adapter = MockAsyncAdapter(delay_ms=100)
        proxy = AsyncDataProxy(':memory:', adapter)
        
        # 准备多个查询
        queries = [
            {'sql': 'SELECT * FROM users', 'params': None},
            {'sql': 'SELECT * FROM orders', 'params': None},
            {'sql': 'SELECT * FROM products', 'params': None},
            {'sql': 'SELECT * FROM categories', 'params': None},
            {'sql': 'SELECT * FROM reviews', 'params': None}
        ]
        
        # 测试并行查询
        start_time = time.time()
        results = await proxy.parallel_queries(queries)
        end_time = time.time()
        
        parallel_time = (end_time - start_time) * 1000
        
        # 验证结果
        assert len(results) == 5
        for result in results:
            if not isinstance(result, Exception):
                assert result.success is True
        
        # 并行执行应该接近单个查询的时间（约100ms），而不是5倍时间（500ms）
        assert parallel_time < 200  # 应该远小于串行执行时间
        
        await proxy.close()
    
    async def test_sequential_vs_parallel_comparison(self):
        """对比串行和并行执行的性能差异"""
        adapter = MockAsyncAdapter(delay_ms=50)
        proxy = AsyncDataProxy(':memory:', adapter)
        
        queries = [
            {'sql': f'SELECT * FROM table_{i}', 'params': None}
            for i in range(10)
        ]
        
        # 测试串行执行
        start_time = time.time()
        sequential_results = []
        for query in queries:
            result = await proxy.query(query['sql'], query['params'])
            sequential_results.append(result)
        sequential_time = (time.time() - start_time) * 1000
        
        # 测试并行执行
        start_time = time.time()
        parallel_results = await proxy.parallel_queries(queries)
        parallel_time = (time.time() - start_time) * 1000
        
        # 验证结果数量相同
        assert len(sequential_results) == len(parallel_results) == 10
        
        # 并行执行应该显著快于串行执行
        speedup = sequential_time / parallel_time
        assert speedup > 5  # 至少5倍的性能提升
        
        print(f"串行执行时间: {sequential_time:.2f}ms")
        print(f"并行执行时间: {parallel_time:.2f}ms")
        print(f"性能提升: {speedup:.2f}x")
        
        await proxy.close()
    
    async def test_stream_query_performance(self):
        """测试流式查询的性能"""
        adapter = MockAsyncAdapter(delay_ms=10)
        proxy = AsyncDataProxy(':memory:', adapter)
        
        # 测试流式查询
        start_time = time.time()
        row_count = 0
        
        async for result in proxy.stream_query("SELECT * FROM large_table"):
            if result.success:
                row_count += len(result.data)
                
                # 检查是否能及时处理数据
                current_time = time.time()
                elapsed = (current_time - start_time) * 1000
                
                # 流式查询应该能够持续产生数据，而不是等待所有数据
                if row_count >= 50:  # 处理了一半数据
                    assert elapsed < 2000  # 应该在2秒内开始产生数据
                    break
        
        total_time = (time.time() - start_time) * 1000
        
        assert row_count > 0
        print(f"流式查询处理 {row_count} 行数据，耗时: {total_time:.2f}ms")
        
        await proxy.close()
    
    async def test_template_scope_parallel_performance(self):
        """测试模板作用域并行数据获取的性能"""
        # 注册适配器
        self.registry.register_adapter(AsyncSQLiteAdapter)
        
        async with AsyncTemplateScope("performance_test", self.registry) as scope:
            # 注册多个数据源（使用内存数据库）
            db1 = await scope.register_async_data_source('db1', ':memory:')
            db2 = await scope.register_async_data_source('db2', ':memory:')
            db3 = await scope.register_async_data_source('db3', ':memory:')
            
            # 创建测试表和数据
            for db in [db1, db2, db3]:
                await db.execute("CREATE TABLE test_table (id INTEGER, name TEXT)")
                for i in range(100):
                    await db.execute("INSERT INTO test_table VALUES (?, ?)", {'1': i, '2': f'name_{i}'})
            
            # 准备查询配置
            queries = {
                'db1': {'sql': 'SELECT COUNT(*) as count FROM test_table'},
                'db2': {'sql': 'SELECT AVG(id) as avg_id FROM test_table'},
                'db3': {'sql': 'SELECT MAX(id) as max_id FROM test_table'}
            }
            
            # 测试并行查询
            start_time = time.time()
            results = await scope.parallel_queries(queries)
            parallel_time = (time.time() - start_time) * 1000
            
            # 验证结果
            assert len(results) == 3
            assert all(result.success for result in results.values())
            
            print(f"模板作用域并行查询耗时: {parallel_time:.2f}ms")
            
            # 并行查询应该比串行快
            assert parallel_time < 500  # 应该在500ms内完成
    
    async def test_connection_pool_performance(self):
        """测试连接池的性能影响"""
        adapter = MockAsyncAdapter(delay_ms=20)
        proxy = AsyncDataProxy(':memory:', adapter)
        
        # 测试使用连接池的并发查询
        concurrent_queries = 20
        
        async def single_query():
            return await proxy.query("SELECT * FROM users", use_pool=True)
        
        # 并发执行多个查询
        start_time = time.time()
        tasks = [single_query() for _ in range(concurrent_queries)]
        results = await asyncio.gather(*tasks)
        pool_time = (time.time() - start_time) * 1000
        
        # 验证结果
        assert len(results) == concurrent_queries
        assert all(result.success for result in results if not isinstance(result, Exception))
        
        print(f"连接池并发查询 {concurrent_queries} 次，耗时: {pool_time:.2f}ms")
        
        # 连接池应该能够有效处理并发请求
        assert pool_time < 1000  # 应该在1秒内完成
        
        await proxy.close()
    
    async def test_memory_usage_efficiency(self):
        """测试内存使用效率"""
        adapter = MockAsyncAdapter(delay_ms=1)
        
        # 创建多个代理对象
        proxies = []
        for i in range(100):
            proxy = AsyncDataProxy(f':memory:{i}', adapter)
            proxies.append(proxy)
        
        # 并发执行查询
        start_time = time.time()
        tasks = [proxy.query("SELECT * FROM users") for proxy in proxies]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        execution_time = (time.time() - start_time) * 1000
        
        # 验证结果
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) > 50  # 至少一半成功
        
        print(f"100个并发代理查询耗时: {execution_time:.2f}ms")
        
        # 清理资源
        for proxy in proxies:
            await proxy.close()
        
        # 异步架构应该能够高效处理大量并发
        assert execution_time < 2000  # 应该在2秒内完成
    
    def test_performance_monitoring(self):
        """测试性能监控功能"""
        adapter = MockAsyncAdapter(delay_ms=50)
        proxy = AsyncDataProxy(':memory:', adapter)
        
        async def run_performance_test():
            # 执行多个操作
            await proxy.query("SELECT * FROM users")
            await proxy.execute("INSERT INTO users VALUES (1, 'test')")
            await proxy.query("SELECT * FROM orders")
            
            # 获取性能统计
            stats = proxy.get_performance_stats()
            
            # 验证统计信息
            assert stats['operation_count'] == 3
            assert stats['proxy_id'] is not None
            assert 'operation_stats' in stats
            
            return stats
        
        # 运行测试
        stats = asyncio.run(run_performance_test())
        
        print(f"性能统计: {stats}")
        
        # 验证性能监控数据
        assert isinstance(stats, dict)
        assert stats['operation_count'] > 0


@pytest.mark.asyncio
class TestAsyncIntegrationPerformance:
    """异步集成性能测试"""
    
    async def test_real_sqlite_performance(self):
        """测试真实SQLite数据库的异步性能"""
        adapter = AsyncSQLiteAdapter()
        proxy = AsyncDataProxy(':memory:', adapter)
        
        # 创建测试表
        await proxy.execute("CREATE TABLE performance_test (id INTEGER PRIMARY KEY, data TEXT)")
        
        # 批量插入数据
        insert_operations = [
            {'type': 'execute', 'sql': 'INSERT INTO performance_test (data) VALUES (?)', 'params': {'1': f'data_{i}'}}
            for i in range(1000)
        ]
        
        start_time = time.time()
        batch_result = await proxy.batch(insert_operations)
        insert_time = (time.time() - start_time) * 1000
        
        assert batch_result.success is True
        print(f"批量插入1000条记录耗时: {insert_time:.2f}ms")
        
        # 测试查询性能
        start_time = time.time()
        query_result = await proxy.query("SELECT COUNT(*) as count FROM performance_test")
        query_time = (time.time() - start_time) * 1000
        
        assert query_result.success is True
        assert query_result.data[0]['count'] == 1000
        print(f"查询耗时: {query_time:.2f}ms")
        
        await proxy.close()


if __name__ == '__main__':
    # 运行异步性能测试
    pytest.main([__file__, '-v', '-s'])
