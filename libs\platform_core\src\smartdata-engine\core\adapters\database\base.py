"""
数据库适配器基础类

提供所有数据库适配器的通用功能和默认实现
"""

from abc import abstractmethod
from typing import Any, Dict, List, Optional, Union
import re
import logging
from urllib.parse import urlparse

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../'))

from ..base import IDatabaseAdapter, ConnectionInfo, OperationResult


class DatabaseAdapterBase(IDatabaseAdapter):
    """
    数据库适配器基础类
    
    提供所有数据库适配器的通用功能：
    - 连接字符串解析
    - SQL参数处理
    - 结果集转换
    - 错误处理
    """
    
    def __init__(self):
        super().__init__()
        self.connection_cache = {}
        self._sql_patterns = self._build_sql_patterns()
    
    def can_handle(self, data_source: Any) -> bool:
        """检查是否能处理指定的数据源"""
        if isinstance(data_source, str):
            # 检查连接字符串
            return self._is_supported_connection_string(data_source)
        elif hasattr(data_source, 'execute') and hasattr(data_source, 'cursor'):
            # 检查连接对象
            return self._is_supported_connection_object(data_source)
        return False
    
    def _build_operations(self) -> Dict[str, callable]:
        """构建数据库操作列表"""
        return {
            'query': self._query_wrapper,
            'execute': self._execute_wrapper,
            'transaction': self._transaction_wrapper,
            'batch': self._batch_wrapper,
            'procedure': self._procedure_wrapper,
            'function': self._function_wrapper,
            'explain': self._explain_wrapper,
            'analyze': self._analyze_wrapper,
            'schema': self._schema_wrapper,
            'tables': self._tables_wrapper,
            'columns': self._columns_wrapper,
        }
    
    # ========================================================================
    # 抽象方法 - 子类必须实现
    # ========================================================================
    
    @abstractmethod
    def _is_supported_connection_string(self, connection_string: str) -> bool:
        """检查是否支持的连接字符串"""
        pass
    
    @abstractmethod
    def _is_supported_connection_object(self, connection: Any) -> bool:
        """检查是否支持的连接对象"""
        pass
    
    @abstractmethod
    def _create_connection(self, connection_source: Union[str, ConnectionInfo]) -> Any:
        """创建数据库连接"""
        pass
    
    @abstractmethod
    def _execute_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """执行查询 - 返回原生结果"""
        pass
    
    @abstractmethod
    def _execute_command(self, connection: Any, sql: str, params: Dict = None) -> int:
        """执行命令 - 返回影响行数"""
        pass
    
    @abstractmethod
    def _get_database_info(self, connection: Any) -> Dict[str, Any]:
        """获取数据库信息"""
        pass
    
    # ========================================================================
    # 公共接口实现
    # ========================================================================
    
    def query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """执行查询"""
        # 如果传入的是连接字符串，先创建连接
        if isinstance(connection, str):
            connection = self._create_connection(connection)
        
        # 预处理SQL和参数
        processed_sql, processed_params = self._preprocess_sql(sql, params)
        
        # 执行查询
        return self._execute_query(connection, processed_sql, processed_params)
    
    def execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """执行命令"""
        # 如果传入的是连接字符串，先创建连接
        if isinstance(connection, str):
            connection = self._create_connection(connection)
        
        # 预处理SQL和参数
        processed_sql, processed_params = self._preprocess_sql(sql, params)
        
        # 执行命令
        return self._execute_command(connection, processed_sql, processed_params)
    
    def transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """执行事务"""
        if isinstance(connection, str):
            connection = self._create_connection(connection)
        
        try:
            # 开始事务
            self._begin_transaction(connection)
            
            results = []
            total_affected = 0
            
            for operation in operations:
                op_type = operation.get('type', 'execute')
                sql = operation['sql']
                params = operation.get('params')
                
                if op_type == 'query':
                    result = self.query(connection, sql, params)
                    results.append(result)
                else:
                    affected = self.execute(connection, sql, params)
                    results.append(affected)
                    total_affected += affected
            
            # 提交事务
            self._commit_transaction(connection)
            
            return {
                'results': results,
                'total_operations': len(operations),
                'total_affected': total_affected,
                'success': True
            }
            
        except Exception as e:
            # 回滚事务
            self._rollback_transaction(connection)
            raise e
    
    # ========================================================================
    # 操作包装器
    # ========================================================================
    
    def _query_wrapper(self, connection: Any, sql: str, params: Dict = None):
        """查询操作包装器"""
        return self.query(connection, sql, params)
    
    def _execute_wrapper(self, connection: Any, sql: str, params: Dict = None):
        """执行操作包装器"""
        return self.execute(connection, sql, params)
    
    def _transaction_wrapper(self, connection: Any, operations: List[Dict]):
        """事务操作包装器"""
        return self.transaction(connection, operations)
    
    def _batch_wrapper(self, connection: Any, operations: List[Dict]):
        """批量操作包装器"""
        return self.batch(connection, operations)
    
    def _procedure_wrapper(self, connection: Any, procedure_name: str, params: Dict = None):
        """存储过程调用包装器"""
        return self._call_procedure(connection, procedure_name, params)
    
    def _function_wrapper(self, connection: Any, function_name: str, params: Dict = None):
        """函数调用包装器"""
        return self._call_function(connection, function_name, params)
    
    def _explain_wrapper(self, connection: Any, sql: str, params: Dict = None):
        """执行计划包装器"""
        explain_sql = f"EXPLAIN {sql}"
        return self.query(connection, explain_sql, params)
    
    def _analyze_wrapper(self, connection: Any, sql: str, params: Dict = None):
        """分析查询包装器"""
        analyze_sql = f"EXPLAIN ANALYZE {sql}"
        return self.query(connection, analyze_sql, params)
    
    def _schema_wrapper(self, connection: Any):
        """获取数据库架构信息"""
        return self._get_schema_info(connection)
    
    def _tables_wrapper(self, connection: Any, schema: str = None):
        """获取表列表"""
        return self._get_tables(connection, schema)
    
    def _columns_wrapper(self, connection: Any, table: str, schema: str = None):
        """获取列信息"""
        return self._get_columns(connection, table, schema)
    
    # ========================================================================
    # 工具方法
    # ========================================================================
    
    def _preprocess_sql(self, sql: str, params: Dict = None) -> tuple:
        """预处理SQL和参数"""
        if not params:
            return sql, {}
        
        # 处理命名参数
        processed_sql = sql
        processed_params = {}
        
        for key, value in params.items():
            # 替换命名参数占位符
            if f":{key}" in processed_sql:
                processed_sql = processed_sql.replace(f":{key}", "%s")
                processed_params[key] = value
            elif f"{{{key}}}" in processed_sql:
                processed_sql = processed_sql.replace(f"{{{key}}}", "%s")
                processed_params[key] = value
        
        return processed_sql, processed_params
    
    def _parse_connection_string(self, connection_string: str) -> ConnectionInfo:
        """解析连接字符串"""
        try:
            parsed = urlparse(connection_string)
            
            return ConnectionInfo(
                host=parsed.hostname or 'localhost',
                port=parsed.port or self._get_default_port(),
                database=parsed.path.lstrip('/') if parsed.path else '',
                username=parsed.username or '',
                password=parsed.password or '',
                options=self._parse_query_params(parsed.query)
            )
        except Exception as e:
            raise ValueError(f"无效的连接字符串: {connection_string}, 错误: {e}")
    
    def _parse_query_params(self, query_string: str) -> Dict[str, Any]:
        """解析查询参数"""
        if not query_string:
            return {}
        
        params = {}
        for param in query_string.split('&'):
            if '=' in param:
                key, value = param.split('=', 1)
                params[key] = value
        
        return params
    
    def _build_sql_patterns(self) -> Dict[str, re.Pattern]:
        """构建SQL模式匹配"""
        return {
            'select': re.compile(r'^\s*SELECT\s+', re.IGNORECASE),
            'insert': re.compile(r'^\s*INSERT\s+', re.IGNORECASE),
            'update': re.compile(r'^\s*UPDATE\s+', re.IGNORECASE),
            'delete': re.compile(r'^\s*DELETE\s+', re.IGNORECASE),
            'create': re.compile(r'^\s*CREATE\s+', re.IGNORECASE),
            'drop': re.compile(r'^\s*DROP\s+', re.IGNORECASE),
            'alter': re.compile(r'^\s*ALTER\s+', re.IGNORECASE),
        }
    
    def _get_sql_type(self, sql: str) -> str:
        """获取SQL类型"""
        for sql_type, pattern in self._sql_patterns.items():
            if pattern.match(sql):
                return sql_type
        return 'unknown'
    
    # ========================================================================
    # 抽象方法的默认实现（子类可以覆盖）
    # ========================================================================
    
    def _get_default_port(self) -> int:
        """获取默认端口"""
        return 5432  # PostgreSQL默认端口
    
    def _begin_transaction(self, connection: Any) -> None:
        """开始事务"""
        if hasattr(connection, 'begin'):
            connection.begin()
        else:
            cursor = connection.cursor()
            cursor.execute("BEGIN")
    
    def _commit_transaction(self, connection: Any) -> None:
        """提交事务"""
        if hasattr(connection, 'commit'):
            connection.commit()
        else:
            cursor = connection.cursor()
            cursor.execute("COMMIT")
    
    def _rollback_transaction(self, connection: Any) -> None:
        """回滚事务"""
        if hasattr(connection, 'rollback'):
            connection.rollback()
        else:
            cursor = connection.cursor()
            cursor.execute("ROLLBACK")
    
    def _call_procedure(self, connection: Any, procedure_name: str, params: Dict = None) -> Any:
        """调用存储过程（默认实现）"""
        param_list = list(params.values()) if params else []
        placeholders = ', '.join(['%s'] * len(param_list))
        sql = f"CALL {procedure_name}({placeholders})"
        return self.query(connection, sql, params)
    
    def _call_function(self, connection: Any, function_name: str, params: Dict = None) -> Any:
        """调用函数（默认实现）"""
        param_list = list(params.values()) if params else []
        placeholders = ', '.join(['%s'] * len(param_list))
        sql = f"SELECT {function_name}({placeholders})"
        return self.query(connection, sql, params)
    
    def _get_schema_info(self, connection: Any) -> Dict[str, Any]:
        """获取数据库架构信息（默认实现）"""
        return self._get_database_info(connection)
    
    def _get_tables(self, connection: Any, schema: str = None) -> List[str]:
        """获取表列表（默认实现）"""
        # 这是一个通用的实现，子类应该覆盖
        sql = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_type = 'BASE TABLE'
        """
        if schema:
            sql += f" AND table_schema = '{schema}'"
        
        result = self.query(connection, sql)
        return [row['table_name'] for row in result]
    
    def _get_columns(self, connection: Any, table: str, schema: str = None) -> List[Dict]:
        """获取列信息（默认实现）"""
        sql = """
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_name = %s
        """
        params = {'table_name': table}
        
        if schema:
            sql += " AND table_schema = %s"
            params['table_schema'] = schema
        
        return self.query(connection, sql, params)
