#!/usr/bin/env python3
"""
HTTP处理器 - 标准化版本

提供透明的HTTP/REST API处理能力，用户体验等同于原生httpx。
遵循插件标准规范，简洁高效。
"""

import logging
import httpx
import asyncio
from typing import Any, Dict, Optional, Union
from urllib.parse import urlparse

try:
    from ...core.smart_data_object import SmartDataObject
    from ...core.async_sync_coordinator import AsyncSyncCoordinator
except ImportError:
    # 后备实现
    class SmartDataObject:
        def __init__(self, data, success=True, error=None):
            self.data = data
            self.success = success
            self.error = error
            self.raw_data = data
        
        def __getattr__(self, name):
            if hasattr(self.data, name):
                return getattr(self.data, name)
            return None
    
    class AsyncSyncCoordinator:
        def __init__(self, enable_debug=False):
            self.enable_debug = enable_debug
        
        def smart_call(self, async_func, *args, **kwargs):
            return asyncio.run(async_func(*args, **kwargs))


class HttpProcessor:
    """HTTP处理器 - 标准插件架构"""
    
    def __init__(self, config: Optional[Dict] = None):
        """初始化HTTP处理器"""
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.HttpProcessor")

        # 插件注册需要的属性
        self.processor_id = 'http'
        self.name = 'HTTP插件'
        self.priority = 80
        
        # 异步协调器
        self.coordinator = AsyncSyncCoordinator(
            enable_debug=self.config.get('enable_debug', False)
        )
        
        # HTTP客户端配置
        self.client_config = {
            'timeout': self.config.get('timeout', 30.0),
            'follow_redirects': self.config.get('follow_redirects', True),
            'verify': self.config.get('verify_ssl', True)
        }
        
        self.logger.info("HTTP处理器初始化完成")
    
    def can_process(self, data: Any) -> bool:
        """检查是否可以处理数据"""
        if isinstance(data, str):
            # 检查是否为URL
            try:
                result = urlparse(data)
                return bool(result.scheme and result.netloc)
            except:
                return False
        
        if isinstance(data, dict):
            # 检查是否为HTTP配置
            return 'url' in data or 'method' in data
        
        return False
    
    def process(self, data: Any, context: Optional[Dict] = None) -> SmartDataObject:
        """处理数据 - 同步接口"""
        return self.coordinator.smart_call(self._async_process, data, context)
    
    async def process_async(self, data: Any, context: Optional[Dict] = None) -> SmartDataObject:
        """处理数据 - 异步接口"""
        return await self._async_process(data, context)
    
    async def _async_process(self, data: Any, context: Optional[Dict] = None) -> SmartDataObject:
        """内部异步处理逻辑"""
        try:
            # 解析请求参数
            request_config = self._parse_request(data, context)
            
            # 执行HTTP请求
            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.request(**request_config)
            
            # 创建智能响应对象
            return self._create_smart_response(response, request_config)
            
        except Exception as e:
            self.logger.error(f"HTTP请求失败: {e}")
            return SmartDataObject(
                data={'error': str(e), 'success': False},
                success=False,
                error=str(e)
            )
    
    def _parse_request(self, data: Any, context: Optional[Dict] = None) -> Dict[str, Any]:
        """解析请求参数 - 透明化处理"""
        if isinstance(data, str):
            # 简单URL请求
            return {
                'method': 'GET',
                'url': data
            }

        if isinstance(data, dict):
            # 配置字典请求
            config = data.copy()

            # 确保有URL
            if 'url' not in config:
                raise ValueError("请求配置必须包含'url'字段")

            # 设置默认方法
            if 'method' not in config:
                config['method'] = 'GET'

            # 处理认证
            if 'auth' in config:
                auth_config = config.pop('auth')
                if isinstance(auth_config, dict) and auth_config.get('type') == 'basic':
                    config['auth'] = (auth_config['username'], auth_config['password'])

            # 确保只包含httpx.request支持的参数
            valid_params = {
                'method', 'url', 'params', 'data', 'json', 'headers',
                'cookies', 'auth', 'timeout', 'follow_redirects'
            }

            # 过滤无效参数
            filtered_config = {k: v for k, v in config.items() if k in valid_params}

            return filtered_config

        raise ValueError(f"不支持的数据类型: {type(data)}")
    
    def _create_smart_response(self, response: httpx.Response, request_config: Dict) -> SmartDataObject:
        """创建智能响应对象 - 透明化httpx响应"""
        try:
            # 判断请求是否成功
            success = 200 <= response.status_code < 300
            
            # 尝试解析JSON
            try:
                json_data = response.json()
            except:
                json_data = None
            
            # 构建响应数据
            response_data = {
                'success': success,
                'status_code': response.status_code,
                'headers': dict(response.headers),
                'text': response.text,
                'content': response.content,
                'json': json_data,
                'url': str(response.url),
                'request': request_config
            }
            
            # 添加错误信息
            if not success:
                response_data['error'] = f"HTTP {response.status_code}: {response.reason_phrase}"
            
            return SmartDataObject(
                data=response_data,
                success=success,
                error=response_data.get('error')
            )
            
        except Exception as e:
            self.logger.error(f"创建响应对象失败: {e}")
            return SmartDataObject(
                data={'error': str(e), 'success': False},
                success=False,
                error=str(e)
            )
    
    def get_processor_info(self) -> Dict[str, Any]:
        """获取处理器信息"""
        return {
            "name": "HttpProcessor",
            "version": "2.0.0",
            "description": "透明的HTTP/REST API处理器",
            "capabilities": [
                "http_requests",
                "rest_api", 
                "json_processing",
                "async_support"
            ],
            "supported_types": [
                "url_string",
                "http_config_dict"
            ]
        }
