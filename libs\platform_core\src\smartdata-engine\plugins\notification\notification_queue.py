"""
通知队列

提供通知队列管理和调度功能
"""

import logging
import asyncio
import time
from typing import Any, Dict, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum
import heapq
import threading


class NotificationStatus(Enum):
    """通知状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    SENT = "sent"
    FAILED = "failed"
    CANCELLED = "cancelled"
    SCHEDULED = "scheduled"


@dataclass
class QueuedNotification:
    """队列中的通知"""
    id: str
    channel: str
    recipient: str
    title: str
    content: str
    priority: int = 5  # 1-10，数字越小优先级越高
    scheduled_time: Optional[float] = None
    created_time: float = None
    status: NotificationStatus = NotificationStatus.PENDING
    retry_count: int = 0
    max_retries: int = 3
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.created_time is None:
            self.created_time = time.time()
        if self.metadata is None:
            self.metadata = {}
    
    def __lt__(self, other):
        """用于优先队列排序"""
        # 首先按调度时间排序，然后按优先级
        if self.scheduled_time and other.scheduled_time:
            if self.scheduled_time != other.scheduled_time:
                return self.scheduled_time < other.scheduled_time
        elif self.scheduled_time:
            return False  # 有调度时间的排在后面
        elif other.scheduled_time:
            return True   # 无调度时间的排在前面
        
        # 按优先级排序（数字越小优先级越高）
        return self.priority < other.priority


class NotificationQueue:
    """通知队列"""
    
    def __init__(self, max_size: int = 10000):
        self.logger = logging.getLogger(f"{__name__}.NotificationQueue")
        self.max_size = max_size
        
        # 优先队列
        self._queue: List[QueuedNotification] = []
        self._queue_lock = threading.RLock()
        
        # 通知存储（用于状态查询）
        self._notifications: Dict[str, QueuedNotification] = {}
        
        # 统计信息
        self._stats = {
            'total_queued': 0,
            'total_sent': 0,
            'total_failed': 0,
            'current_size': 0
        }
    
    def enqueue(self, notification: QueuedNotification) -> bool:
        """入队通知"""
        try:
            with self._queue_lock:
                if len(self._queue) >= self.max_size:
                    self.logger.warning("通知队列已满，无法添加新通知")
                    return False
                
                # 添加到优先队列
                heapq.heappush(self._queue, notification)
                
                # 添加到存储
                self._notifications[notification.id] = notification
                
                # 更新统计
                self._stats['total_queued'] += 1
                self._stats['current_size'] = len(self._queue)
                
                self.logger.debug(f"通知已入队: {notification.id}")
                return True
                
        except Exception as e:
            self.logger.error(f"通知入队失败: {e}")
            return False
    
    def dequeue(self) -> Optional[QueuedNotification]:
        """出队通知"""
        try:
            with self._queue_lock:
                if not self._queue:
                    return None
                
                # 检查队首通知是否可以处理
                while self._queue:
                    notification = self._queue[0]
                    
                    # 检查是否到了调度时间
                    if notification.scheduled_time and notification.scheduled_time > time.time():
                        break  # 还没到时间
                    
                    # 出队
                    notification = heapq.heappop(self._queue)
                    
                    # 检查状态
                    if notification.status == NotificationStatus.CANCELLED:
                        continue  # 跳过已取消的通知
                    
                    # 更新状态
                    notification.status = NotificationStatus.PROCESSING
                    
                    # 更新统计
                    self._stats['current_size'] = len(self._queue)
                    
                    self.logger.debug(f"通知已出队: {notification.id}")
                    return notification
                
                return None
                
        except Exception as e:
            self.logger.error(f"通知出队失败: {e}")
            return None
    
    def get_notification(self, notification_id: str) -> Optional[QueuedNotification]:
        """获取通知"""
        return self._notifications.get(notification_id)
    
    def update_notification_status(self, notification_id: str, status: NotificationStatus, error: Optional[str] = None):
        """更新通知状态"""
        try:
            notification = self._notifications.get(notification_id)
            if notification:
                notification.status = status
                
                if error:
                    notification.metadata['error'] = error
                
                # 更新统计
                if status == NotificationStatus.SENT:
                    self._stats['total_sent'] += 1
                elif status == NotificationStatus.FAILED:
                    self._stats['total_failed'] += 1
                
                self.logger.debug(f"通知状态已更新: {notification_id} -> {status.value}")
            
        except Exception as e:
            self.logger.error(f"更新通知状态失败: {e}")
    
    def cancel_notification(self, notification_id: str) -> bool:
        """取消通知"""
        try:
            notification = self._notifications.get(notification_id)
            if notification and notification.status in [NotificationStatus.PENDING, NotificationStatus.SCHEDULED]:
                notification.status = NotificationStatus.CANCELLED
                self.logger.info(f"通知已取消: {notification_id}")
                return True
            else:
                self.logger.warning(f"无法取消通知: {notification_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"取消通知失败: {e}")
            return False
    
    def get_queue_size(self) -> int:
        """获取队列大小"""
        with self._queue_lock:
            return len(self._queue)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._queue_lock:
            stats = self._stats.copy()
            stats['current_size'] = len(self._queue)
            
            # 按状态统计
            status_counts = {}
            for notification in self._notifications.values():
                status = notification.status.value
                status_counts[status] = status_counts.get(status, 0) + 1
            
            stats['status_counts'] = status_counts
            
            return stats
    
    def clear_completed(self, older_than_hours: int = 24):
        """清理已完成的通知"""
        try:
            cutoff_time = time.time() - (older_than_hours * 3600)
            completed_statuses = {NotificationStatus.SENT, NotificationStatus.FAILED, NotificationStatus.CANCELLED}
            
            to_remove = []
            for notification_id, notification in self._notifications.items():
                if (notification.status in completed_statuses and 
                    notification.created_time < cutoff_time):
                    to_remove.append(notification_id)
            
            for notification_id in to_remove:
                del self._notifications[notification_id]
            
            self.logger.info(f"清理了{len(to_remove)}个已完成的通知")
            return len(to_remove)
            
        except Exception as e:
            self.logger.error(f"清理已完成通知失败: {e}")
            return 0


class NotificationScheduler:
    """通知调度器"""
    
    def __init__(self, queue: NotificationQueue):
        self.logger = logging.getLogger(f"{__name__}.NotificationScheduler")
        self.queue = queue
        self._running = False
        self._scheduler_task = None
    
    async def start(self):
        """启动调度器"""
        if self._running:
            self.logger.warning("调度器已在运行")
            return
        
        self._running = True
        self._scheduler_task = asyncio.create_task(self._scheduler_loop())
        self.logger.info("通知调度器已启动")
    
    async def stop(self):
        """停止调度器"""
        if not self._running:
            return
        
        self._running = False
        
        if self._scheduler_task:
            self._scheduler_task.cancel()
            try:
                await self._scheduler_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("通知调度器已停止")
    
    async def _scheduler_loop(self):
        """调度器主循环"""
        try:
            while self._running:
                try:
                    # 处理队列中的通知
                    notification = self.queue.dequeue()
                    
                    if notification:
                        # 这里应该调用实际的发送逻辑
                        await self._process_notification(notification)
                    else:
                        # 队列为空，等待一段时间
                        await asyncio.sleep(1)
                
                except Exception as e:
                    self.logger.error(f"调度器处理通知失败: {e}")
                    await asyncio.sleep(1)
                    
        except asyncio.CancelledError:
            self.logger.info("调度器循环已取消")
        except Exception as e:
            self.logger.error(f"调度器循环异常: {e}")
    
    async def _process_notification(self, notification: QueuedNotification):
        """处理通知"""
        try:
            # 模拟发送过程
            await asyncio.sleep(0.1)
            
            # 模拟成功/失败
            import random
            if random.random() > 0.1:  # 90%成功率
                self.queue.update_notification_status(notification.id, NotificationStatus.SENT)
                self.logger.debug(f"通知发送成功: {notification.id}")
            else:
                # 重试逻辑
                if notification.retry_count < notification.max_retries:
                    notification.retry_count += 1
                    notification.status = NotificationStatus.PENDING
                    
                    # 重新入队（延迟重试）
                    notification.scheduled_time = time.time() + (notification.retry_count * 60)  # 延迟重试
                    self.queue.enqueue(notification)
                    
                    self.logger.warning(f"通知发送失败，将重试: {notification.id} (第{notification.retry_count}次)")
                else:
                    self.queue.update_notification_status(notification.id, NotificationStatus.FAILED, "超过最大重试次数")
                    self.logger.error(f"通知发送失败，已达最大重试次数: {notification.id}")
            
        except Exception as e:
            self.queue.update_notification_status(notification.id, NotificationStatus.FAILED, str(e))
            self.logger.error(f"处理通知异常: {e}")
    
    def schedule_notification(self, notification: QueuedNotification, delay_seconds: int = 0) -> bool:
        """调度通知"""
        try:
            if delay_seconds > 0:
                notification.scheduled_time = time.time() + delay_seconds
                notification.status = NotificationStatus.SCHEDULED
            
            return self.queue.enqueue(notification)
            
        except Exception as e:
            self.logger.error(f"调度通知失败: {e}")
            return False
