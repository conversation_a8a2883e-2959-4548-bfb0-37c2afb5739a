"""
JSON文件数据适配器

支持JSON文件的同步和异步读写操作
"""

from typing import Any, Dict, List, Optional, Union, AsyncIterator
import logging
import json
import os
from pathlib import Path

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../'))

from core.unified_adapter import UnifiedDataAdapter

# 尝试导入异步文件操作库
try:
    import aiofiles
    AIOFILES_AVAILABLE = True
except ImportError:
    AIOFILES_AVAILABLE = False
    aiofiles = None


class JSONAdapter(UnifiedDataAdapter):
    """
    JSON文件数据适配器
    
    支持JSON文件特有功能：
    - 同步和异步文件读写
    - 嵌套JSON数据处理
    - JSONLines格式支持
    - 数据路径查询（JSONPath）
    - 大文件流式处理
    """
    
    def __init__(self):
        super().__init__()
        if not AIOFILES_AVAILABLE:
            self.logger.warning("aiofiles未安装，异步文件操作功能受限")
        
        # 默认配置
        self.default_encoding = 'utf-8'
        self.default_indent = 2
    
    def supported_types(self) -> List[str]:
        """返回支持的数据类型列表"""
        return [
            'json',
            'json_file',
            'jsonl',
            'jsonlines',
            'ndjson'
        ]
    
    def _is_supported_connection_string(self, connection_string: str) -> bool:
        """检查是否支持的连接字符串"""
        return (connection_string.endswith(('.json', '.jsonl', '.ndjson')) or
                connection_string.startswith('file://'))
    
    def _is_supported_connection_object(self, connection: Any) -> bool:
        """检查是否支持的连接对象"""
        return (isinstance(connection, dict) and 
                ('file_path' in connection or 'path' in connection))
    
    def can_handle(self, data_source: Any) -> bool:
        """检查是否能处理指定的数据源"""
        if isinstance(data_source, str):
            return self._is_supported_connection_string(data_source)
        else:
            return self._is_supported_connection_object(data_source)
    
    def _build_operations(self) -> Dict[str, callable]:
        """构建JSON特有操作列表"""
        operations = {
            'read_json': self._sync_read_json,
            'write_json': self._sync_write_json,
            'append_jsonl': self._sync_append_jsonl,
        }
        
        # 添加异步操作
        async_ops = self.get_async_operations()
        for name, func in async_ops.items():
            operations[name] = func
        
        return operations
    
    def _parse_file_config(self, connection_source: Any) -> Dict[str, Any]:
        """解析文件配置"""
        if isinstance(connection_source, str):
            # 简单文件路径
            if connection_source.startswith('file://'):
                file_path = connection_source[7:]  # 移除 'file://'
            else:
                file_path = connection_source
            
            # 根据文件扩展名确定格式
            is_jsonlines = file_path.endswith(('.jsonl', '.ndjson'))
            
            return {
                'file_path': file_path,
                'encoding': self.default_encoding,
                'indent': self.default_indent,
                'is_jsonlines': is_jsonlines,
                'json_path': None
            }
        elif isinstance(connection_source, dict):
            # 详细配置对象
            file_path = connection_source.get('file_path') or connection_source.get('path')
            is_jsonlines = (file_path.endswith(('.jsonl', '.ndjson')) or 
                          connection_source.get('format') == 'jsonlines')
            
            config = {
                'file_path': file_path,
                'encoding': connection_source.get('encoding', self.default_encoding),
                'indent': connection_source.get('indent', self.default_indent),
                'is_jsonlines': is_jsonlines,
                'json_path': connection_source.get('json_path')
            }
            
            return config
        else:
            raise ValueError(f"不支持的文件配置类型: {type(connection_source)}")
    
    # ========================================================================
    # 同步方法实现
    # ========================================================================
    
    def _sync_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """同步查询实现 - 读取JSON文件"""
        return self._sync_read_json(connection, params)
    
    def _sync_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """同步执行实现 - 写入JSON文件"""
        if params and 'data' in params:
            return self._sync_write_json(connection, params['data'])
        return 0
    
    def _sync_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """同步事务实现 - 批量文件操作"""
        results = []
        total_affected = 0
        
        for operation in operations:
            op_type = operation.get('type', 'read')
            data = operation.get('data')
            
            if op_type == 'read':
                result = self._sync_read_json(connection)
                results.append(result)
            elif op_type == 'write':
                affected = self._sync_write_json(connection, data)
                results.append(affected)
                total_affected += affected
            elif op_type == 'append':
                affected = self._sync_append_jsonl(connection, data)
                results.append(affected)
                total_affected += affected
        
        return {
            'results': results,
            'total_operations': len(operations),
            'total_affected': total_affected,
            'success': True
        }
    
    def _sync_read_json(self, connection: Any, params: Dict = None) -> List[Dict]:
        """同步读取JSON文件"""
        config = self._parse_file_config(connection)
        file_path = config['file_path']
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"JSON文件不存在: {file_path}")
        
        try:
            with open(file_path, 'r', encoding=config['encoding']) as jsonfile:
                if config['is_jsonlines']:
                    # JSONLines格式 - 每行一个JSON对象
                    results = []
                    for line in jsonfile:
                        line = line.strip()
                        if line:
                            obj = json.loads(line)
                            if isinstance(obj, dict):
                                results.append(obj)
                            else:
                                results.append({'data': obj})
                    return results
                else:
                    # 标准JSON格式
                    data = json.load(jsonfile)
                    
                    # 应用JSONPath查询
                    if config['json_path']:
                        data = self._apply_json_path(data, config['json_path'])
                    
                    # 确保返回列表格式
                    if isinstance(data, list):
                        return data
                    elif isinstance(data, dict):
                        return [data]
                    else:
                        return [{'data': data}]
            
        except Exception as e:
            self.logger.error(f"读取JSON文件失败 {file_path}: {e}")
            raise
    
    def _sync_write_json(self, connection: Any, data: List[Dict]) -> int:
        """同步写入JSON文件"""
        config = self._parse_file_config(connection)
        file_path = config['file_path']
        
        if not data:
            return 0
        
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path) or '.', exist_ok=True)
            
            with open(file_path, 'w', encoding=config['encoding']) as jsonfile:
                if config['is_jsonlines']:
                    # JSONLines格式 - 每行一个JSON对象
                    for item in data:
                        json.dump(item, jsonfile, ensure_ascii=False)
                        jsonfile.write('\n')
                else:
                    # 标准JSON格式
                    json.dump(data, jsonfile, ensure_ascii=False, indent=config['indent'])
            
            self.logger.info(f"成功写入JSON文件: {file_path}, 记录数: {len(data)}")
            return len(data)
            
        except Exception as e:
            self.logger.error(f"写入JSON文件失败 {file_path}: {e}")
            raise
    
    def _sync_append_jsonl(self, connection: Any, data: List[Dict]) -> int:
        """同步追加JSONLines文件"""
        config = self._parse_file_config(connection)
        file_path = config['file_path']
        
        if not data:
            return 0
        
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path) or '.', exist_ok=True)
            
            with open(file_path, 'a', encoding=config['encoding']) as jsonfile:
                for item in data:
                    json.dump(item, jsonfile, ensure_ascii=False)
                    jsonfile.write('\n')
            
            self.logger.info(f"成功追加JSONLines文件: {file_path}, 记录数: {len(data)}")
            return len(data)
            
        except Exception as e:
            self.logger.error(f"追加JSONLines文件失败 {file_path}: {e}")
            raise
    
    def _apply_json_path(self, data: Any, json_path: str) -> Any:
        """应用简单的JSONPath查询"""
        # 这里实现简单的JSONPath支持
        # 实际应用中可以使用jsonpath-ng等库
        
        if json_path.startswith('$.'):
            path_parts = json_path[2:].split('.')
            current = data
            
            for part in path_parts:
                if isinstance(current, dict) and part in current:
                    current = current[part]
                elif isinstance(current, list) and part.isdigit():
                    index = int(part)
                    if 0 <= index < len(current):
                        current = current[index]
                    else:
                        return None
                else:
                    return None
            
            return current
        
        return data

    # ========================================================================
    # 异步方法实现
    # ========================================================================

    async def _create_async_connection(self, connection_source: Any) -> Any:
        """创建异步文件连接"""
        config = self._parse_file_config(connection_source)

        # 验证文件路径
        file_path = config['file_path']
        if not os.path.exists(os.path.dirname(file_path) or '.'):
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

        self.logger.info(f"创建异步JSON文件连接: {file_path}")
        return config

    async def _close_async_connection(self, connection: Any) -> None:
        """关闭异步文件连接"""
        self.logger.debug("异步JSON文件连接已关闭")

    async def _create_async_pool(self, connection_source: Any, min_size: int = 5, max_size: int = 20) -> Any:
        """创建异步连接池（文件不需要连接池）"""
        return await self._create_async_connection(connection_source)

    async def _async_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """异步查询实现 - 读取JSON文件"""
        return await self._async_read_json(connection, params)

    async def _async_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """异步执行实现 - 写入JSON文件"""
        if params and 'data' in params:
            return await self._async_write_json(connection, params['data'])
        return 0

    async def _async_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步事务实现 - 批量文件操作"""
        results = []
        total_affected = 0

        for operation in operations:
            op_type = operation.get('type', 'read')
            data = operation.get('data')

            if op_type == 'read':
                result = await self._async_read_json(connection)
                results.append(result)
            elif op_type == 'write':
                affected = await self._async_write_json(connection, data)
                results.append(affected)
                total_affected += affected
            elif op_type == 'append':
                affected = await self._async_append_jsonl(connection, data)
                results.append(affected)
                total_affected += affected

        return {
            'results': results,
            'total_operations': len(operations),
            'total_affected': total_affected,
            'success': True
        }

    async def _async_batch(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步批量操作实现"""
        return await self._async_transaction(connection, operations)

    async def _async_read_json(self, connection: Any, params: Dict = None) -> List[Dict]:
        """异步读取JSON文件"""
        if not AIOFILES_AVAILABLE:
            raise ImportError("aiofiles未安装，无法执行异步文件读取")

        config = connection
        file_path = config['file_path']

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"JSON文件不存在: {file_path}")

        try:
            async with aiofiles.open(file_path, 'r', encoding=config['encoding']) as jsonfile:
                content = await jsonfile.read()

                if config['is_jsonlines']:
                    # JSONLines格式 - 每行一个JSON对象
                    results = []
                    for line in content.split('\n'):
                        line = line.strip()
                        if line:
                            try:
                                obj = json.loads(line)
                                if isinstance(obj, dict):
                                    results.append(obj)
                                else:
                                    results.append({'data': obj})
                            except json.JSONDecodeError:
                                continue
                    return results
                else:
                    # 标准JSON格式
                    data = json.loads(content)

                    # 应用JSONPath查询
                    if config['json_path']:
                        data = self._apply_json_path(data, config['json_path'])

                    # 确保返回列表格式
                    if isinstance(data, list):
                        return data
                    elif isinstance(data, dict):
                        return [data]
                    else:
                        return [{'data': data}]

        except Exception as e:
            self.logger.error(f"异步读取JSON文件失败 {file_path}: {e}")
            raise

    async def _async_write_json(self, connection: Any, data: List[Dict]) -> int:
        """异步写入JSON文件"""
        if not AIOFILES_AVAILABLE:
            raise ImportError("aiofiles未安装，无法执行异步文件写入")

        config = connection
        file_path = config['file_path']

        if not data:
            return 0

        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path) or '.', exist_ok=True)

            async with aiofiles.open(file_path, 'w', encoding=config['encoding']) as jsonfile:
                if config['is_jsonlines']:
                    # JSONLines格式 - 每行一个JSON对象
                    for item in data:
                        line = json.dumps(item, ensure_ascii=False) + '\n'
                        await jsonfile.write(line)
                else:
                    # 标准JSON格式
                    content = json.dumps(data, ensure_ascii=False, indent=config['indent'])
                    await jsonfile.write(content)

            self.logger.info(f"成功异步写入JSON文件: {file_path}, 记录数: {len(data)}")
            return len(data)

        except Exception as e:
            self.logger.error(f"异步写入JSON文件失败 {file_path}: {e}")
            raise

    async def _async_stream_query(self, connection: Any, sql: str, params: Dict = None) -> AsyncIterator[Dict]:
        """异步流式查询实现 - 逐行读取JSONLines"""
        if not AIOFILES_AVAILABLE:
            raise ImportError("aiofiles未安装，无法执行异步文件流式读取")

        config = connection
        file_path = config['file_path']

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"JSON文件不存在: {file_path}")

        try:
            async with aiofiles.open(file_path, 'r', encoding=config['encoding']) as jsonfile:
                if config['is_jsonlines']:
                    # JSONLines格式 - 逐行读取
                    async for line in jsonfile:
                        line = line.strip()
                        if line:
                            try:
                                obj = json.loads(line)
                                if isinstance(obj, dict):
                                    yield obj
                                else:
                                    yield {'data': obj}
                            except json.JSONDecodeError:
                                continue
                else:
                    # 标准JSON格式 - 一次性读取后逐项返回
                    content = await jsonfile.read()
                    data = json.loads(content)

                    # 应用JSONPath查询
                    if config['json_path']:
                        data = self._apply_json_path(data, config['json_path'])

                    # 逐项返回
                    if isinstance(data, list):
                        for item in data:
                            yield item if isinstance(item, dict) else {'data': item}
                    elif isinstance(data, dict):
                        yield data
                    else:
                        yield {'data': data}

        except Exception as e:
            self.logger.error(f"异步流式读取JSON文件失败 {file_path}: {e}")
            raise

    def get_async_operations(self) -> Dict[str, Any]:
        """获取支持的异步操作列表"""
        operations = super().get_async_operations()

        # 添加JSON特有异步操作
        operations.update({
            'async_read_json': self._async_read_json,
            'async_write_json': self._async_write_json,
        })

        return operations
