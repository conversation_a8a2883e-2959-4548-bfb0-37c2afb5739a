"""
通知模板

提供通知模板管理和渲染功能
"""

import logging
from typing import Any, Dict, List, Optional
from dataclasses import dataclass


@dataclass
class NotificationTemplate:
    """通知模板"""
    id: str
    name: str
    title_template: str
    content_template: str
    channel: str
    variables: Optional[Dict[str, str]] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.variables is None:
            self.variables = {}
        if self.metadata is None:
            self.metadata = {}


class NotificationTemplateEngine:
    """通知模板引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.NotificationTemplateEngine")
        self.templates: Dict[str, NotificationTemplate] = {}
        
        # 加载默认模板
        self._load_default_templates()
    
    def _load_default_templates(self):
        """加载默认模板"""
        # 系统通知模板
        self.templates['system_alert'] = NotificationTemplate(
            id='system_alert',
            name='系统警报',
            title_template='系统警报: {{alert_type}}',
            content_template='{{message}}\n时间: {{timestamp}}\n级别: {{level}}',
            channel='email',
            variables={
                'alert_type': '警报类型',
                'message': '警报消息',
                'timestamp': '时间戳',
                'level': '警报级别'
            }
        )
        
        # 用户通知模板
        self.templates['user_notification'] = NotificationTemplate(
            id='user_notification',
            name='用户通知',
            title_template='{{title}}',
            content_template='亲爱的 {{user_name}}，\n\n{{content}}\n\n祝好！',
            channel='email',
            variables={
                'title': '通知标题',
                'user_name': '用户名',
                'content': '通知内容'
            }
        )
        
        # 短信通知模板
        self.templates['sms_alert'] = NotificationTemplate(
            id='sms_alert',
            name='短信警报',
            title_template='{{service_name}}警报',
            content_template='{{message}} - {{timestamp}}',
            channel='sms',
            variables={
                'service_name': '服务名称',
                'message': '警报消息',
                'timestamp': '时间戳'
            }
        )
        
        # 推送通知模板
        self.templates['push_notification'] = NotificationTemplate(
            id='push_notification',
            name='推送通知',
            title_template='{{app_name}} - {{title}}',
            content_template='{{content}}',
            channel='push',
            variables={
                'app_name': '应用名称',
                'title': '通知标题',
                'content': '通知内容'
            }
        )
    
    def add_template(self, template: NotificationTemplate):
        """添加模板"""
        self.templates[template.id] = template
        self.logger.info(f"添加通知模板: {template.name}")
    
    def get_template(self, template_id: str) -> Optional[NotificationTemplate]:
        """获取模板"""
        return self.templates.get(template_id)
    
    def list_templates(self, channel: Optional[str] = None) -> List[Dict[str, Any]]:
        """列出模板"""
        templates = []
        
        for template in self.templates.values():
            if channel is None or template.channel == channel:
                templates.append({
                    'id': template.id,
                    'name': template.name,
                    'channel': template.channel,
                    'variables': list(template.variables.keys())
                })
        
        return templates
    
    def render_template(self, template_id: str, data: Dict[str, Any]) -> Dict[str, str]:
        """渲染模板"""
        template = self.get_template(template_id)
        if not template:
            raise ValueError(f"模板不存在: {template_id}")
        
        try:
            # 简单的模板渲染
            title = self._render_string(template.title_template, data)
            content = self._render_string(template.content_template, data)
            
            return {
                'title': title,
                'content': content,
                'channel': template.channel
            }
            
        except Exception as e:
            self.logger.error(f"模板渲染失败: {e}")
            raise
    
    def _render_string(self, template_string: str, data: Dict[str, Any]) -> str:
        """渲染字符串模板"""
        try:
            result = template_string
            
            # 简单的变量替换
            for key, value in data.items():
                placeholder = f"{{{{{key}}}}}"
                result = result.replace(placeholder, str(value))
            
            return result
            
        except Exception as e:
            self.logger.error(f"字符串模板渲染失败: {e}")
            return template_string
    
    def validate_template(self, template: NotificationTemplate) -> Dict[str, Any]:
        """验证模板"""
        try:
            validation_result = {
                'valid': True,
                'errors': [],
                'warnings': []
            }
            
            # 检查必需字段
            if not template.id:
                validation_result['errors'].append('模板ID不能为空')
                validation_result['valid'] = False
            
            if not template.name:
                validation_result['errors'].append('模板名称不能为空')
                validation_result['valid'] = False
            
            if not template.title_template:
                validation_result['errors'].append('标题模板不能为空')
                validation_result['valid'] = False
            
            if not template.content_template:
                validation_result['errors'].append('内容模板不能为空')
                validation_result['valid'] = False
            
            # 检查变量一致性
            title_vars = self._extract_variables(template.title_template)
            content_vars = self._extract_variables(template.content_template)
            all_vars = title_vars | content_vars
            
            defined_vars = set(template.variables.keys()) if template.variables else set()
            
            # 检查未定义的变量
            undefined_vars = all_vars - defined_vars
            if undefined_vars:
                validation_result['warnings'].append(f'未定义的变量: {", ".join(undefined_vars)}')
            
            # 检查未使用的变量
            unused_vars = defined_vars - all_vars
            if unused_vars:
                validation_result['warnings'].append(f'未使用的变量: {", ".join(unused_vars)}')
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"模板验证失败: {e}")
            return {
                'valid': False,
                'errors': [str(e)]
            }
    
    def _extract_variables(self, template_string: str) -> set:
        """提取模板中的变量"""
        import re
        
        # 查找 {{variable}} 格式的变量
        pattern = r'\{\{(\w+)\}\}'
        matches = re.findall(pattern, template_string)
        
        return set(matches)
    
    def remove_template(self, template_id: str) -> bool:
        """删除模板"""
        if template_id in self.templates:
            del self.templates[template_id]
            self.logger.info(f"删除通知模板: {template_id}")
            return True
        else:
            self.logger.warning(f"模板不存在: {template_id}")
            return False
    
    def get_template_statistics(self) -> Dict[str, Any]:
        """获取模板统计信息"""
        stats = {
            'total_templates': len(self.templates),
            'templates_by_channel': {},
            'total_variables': 0
        }
        
        for template in self.templates.values():
            # 按渠道统计
            channel = template.channel
            if channel not in stats['templates_by_channel']:
                stats['templates_by_channel'][channel] = 0
            stats['templates_by_channel'][channel] += 1
            
            # 变量统计
            if template.variables:
                stats['total_variables'] += len(template.variables)
        
        return stats
