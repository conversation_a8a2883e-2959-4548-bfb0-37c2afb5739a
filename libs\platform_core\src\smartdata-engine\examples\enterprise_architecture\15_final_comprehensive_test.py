#!/usr/bin/env python3
"""
最终综合测试

验证所有修复都已生效，包括：
1. 文件处理修复
2. 复杂数据类型处理
3. 推导式和链式调用
4. 线程安全和异步功能
"""

import sys
import os
import json
import tempfile
import threading
import asyncio

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.thread_safe_template_integration import ThreadSafeTemplateIntegration


def final_comprehensive_test():
    """最终综合测试"""
    print("=== 最终综合测试 ===")
    print("验证线程安全版本的完整功能")
    print("=" * 80)
    
    # 创建线程安全的模板引擎
    engine = ThreadSafeTemplateIntegration(
        enable_async=True,
        enable_debug=False,
        isolation_level='thread',
        cleanup_interval=60,
        max_scope_lifetime=300
    )
    
    test_results = []
    
    # 📁 测试1：文件处理验证
    print("\n📁 测试1：文件处理验证")
    print("-" * 60)
    
    try:
        # 创建测试文件
        test_data = {
            "company": "智慧科技",
            "employees": [
                {"name": "张三", "age": 28, "department": "技术部"},
                {"name": "李四", "age": 32, "department": "销售部"}
            ]
        }
        
        temp_file = tempfile.mktemp(suffix='.json')
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        file_path = temp_file.replace('\\', '/')
        
        template = f"""
文件处理测试
===========
{{%- set data = sd.file('{file_path}').parse() -%}}
公司: {{{{ data.company }}}}
员工数: {{{{ data.employees | length }}}}
第一个员工: {{{{ data.employees[0].name }}}}
        """.strip()
        
        result = engine.render_template_sync(template, {}, 'file_test')
        
        if '智慧科技' in result and '张三' in result:
            test_results.append("✅ 文件处理")
            print("✅ 文件处理正常")
        else:
            test_results.append("❌ 文件处理")
            print("❌ 文件处理失败")
        
        # 清理
        if os.path.exists(temp_file):
            os.unlink(temp_file)
            
    except Exception as e:
        test_results.append("❌ 文件处理")
        print(f"❌ 文件处理异常: {e}")
    
    # 🔄 测试2：复杂数据处理
    print("\n🔄 测试2：复杂数据处理")
    print("-" * 60)
    
    try:
        complex_data = {
            "numbers": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            "employees": [
                {"name": "张三", "salary": 8000, "active": True},
                {"name": "李四", "salary": 12000, "active": True},
                {"name": "王五", "salary": 6000, "active": False}
            ]
        }
        
        template = """
复杂数据处理测试
===============

1. 数字处理:
所有数字: {{ numbers | join(', ') }}
数字总和: {{ numbers | sum }}
平均值: {{ (numbers | sum) // (numbers | length) }}

2. 员工数据:
活跃员工: {{ employees | selectattr('active') | map(attribute='name') | list | join(', ') }}
高薪员工: {{ employees | selectattr('salary', '>', 7000) | map(attribute='name') | list | join(', ') }}
总薪资: {{ employees | sum(attribute='salary') }}

3. JSONPath查询:
所有员工名: {{ sd.jsonpath(employees, '$[*].name') }}
高薪员工: {{ sd.jsonpath(employees, '$[?(@.salary>7000)].name') }}
        """.strip()
        
        result = engine.render_template_sync(template, complex_data, 'complex_test')
        
        if '张三' in result and '总薪资' in result and '26000' in result:
            test_results.append("✅ 复杂数据处理")
            print("✅ 复杂数据处理正常")
        else:
            test_results.append("❌ 复杂数据处理")
            print("❌ 复杂数据处理失败")
            
    except Exception as e:
        test_results.append("❌ 复杂数据处理")
        print(f"❌ 复杂数据处理异常: {e}")
    
    # 🧵 测试3：多线程安全
    print("\n🧵 测试3：多线程安全")
    print("-" * 60)
    
    try:
        def worker(worker_id, results):
            try:
                template = f"""
线程测试 {worker_id}
================
工作线程: {worker_id}
数据: {{{{ data.value }}}}
                """.strip()
                
                context = {'data': {'value': worker_id * 100}}
                result = engine.render_template_sync(template, context, f'thread_{worker_id}')
                
                if f'工作线程: {worker_id}' in result:
                    results.append(f"✅ 线程{worker_id}")
                else:
                    results.append(f"❌ 线程{worker_id}")
                    
            except Exception as e:
                results.append(f"❌ 线程{worker_id}: {e}")
        
        # 启动3个线程
        threads = []
        thread_results = []
        
        for i in range(3):
            thread = threading.Thread(target=worker, args=(i+1, thread_results))
            threads.append(thread)
            thread.start()
        
        # 等待完成
        for thread in threads:
            thread.join()
        
        successful_threads = len([r for r in thread_results if r.startswith('✅')])
        
        if successful_threads == 3:
            test_results.append("✅ 多线程安全")
            print("✅ 多线程安全正常")
        else:
            test_results.append("❌ 多线程安全")
            print(f"❌ 多线程安全失败: {successful_threads}/3")
            
    except Exception as e:
        test_results.append("❌ 多线程安全")
        print(f"❌ 多线程安全异常: {e}")
    
    # ⚡ 测试4：异步功能
    print("\n⚡ 测试4：异步功能")
    print("-" * 60)
    
    try:
        async def async_test():
            template = """
异步测试
=======
任务: {{ task.name }}
状态: {{ task.status }}
            """.strip()
            
            context = {
                'task': {
                    'name': 'async_task',
                    'status': 'running'
                }
            }
            
            result = await engine.render_template_async(template, context, 'async_test')
            return 'async_task' in result and 'running' in result
        
        async_success = asyncio.run(async_test())
        
        if async_success:
            test_results.append("✅ 异步功能")
            print("✅ 异步功能正常")
        else:
            test_results.append("❌ 异步功能")
            print("❌ 异步功能失败")
            
    except Exception as e:
        test_results.append("❌ 异步功能")
        print(f"❌ 异步功能异常: {e}")
    
    # 🧠 测试5：智能数据处理
    print("\n🧠 测试5：智能数据处理")
    print("-" * 60)
    
    try:
        smart_data = {
            "users": [
                {"id": 1, "name": "Alice", "profile": {"age": 25, "city": "北京"}},
                {"id": 2, "name": "Bob", "profile": {"age": 30, "city": "上海"}}
            ]
        }
        
        template = """
智能数据处理测试
===============
用户数: {{ users | length }}
第一个用户: {{ users[0].name }}
用户城市: {{ users | map(attribute='profile.city') | list | join(', ') }}

智能数据类型: {{ sd.smart_data(users).__class__.__name__ }}
增强数据类型: {{ sd.enhanced_data(users).__class__.__name__ }}
        """.strip()
        
        result = engine.render_template_sync(template, smart_data, 'smart_test')
        
        if 'Alice' in result and '北京' in result:
            test_results.append("✅ 智能数据处理")
            print("✅ 智能数据处理正常")
        else:
            test_results.append("❌ 智能数据处理")
            print("❌ 智能数据处理失败")
            
    except Exception as e:
        test_results.append("❌ 智能数据处理")
        print(f"❌ 智能数据处理异常: {e}")
    
    # 📊 生成最终报告
    print("\n📊 最终综合测试报告")
    print("=" * 80)
    
    successful_tests = len([r for r in test_results if r.startswith('✅')])
    total_tests = len(test_results)
    success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print("测试结果:")
    for result in test_results:
        print(f"  {result}")
    
    print(f"\n总体结果:")
    print(f"  成功测试: {successful_tests}/{total_tests}")
    print(f"  成功率: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("\n🎉 完美！所有功能都正常工作")
        print("\n✅ 验证的核心功能:")
        print("  📁 文件处理 - 完美修复")
        print("  🔄 复杂数据处理 - 完全支持")
        print("  🧵 多线程安全 - 完全支持")
        print("  ⚡ 异步功能 - 完全支持")
        print("  🧠 智能数据处理 - 完全支持")
        
        print("\n🚀 线程安全版本完全就绪，具备企业级能力！")
        
    elif success_rate >= 80:
        print("\n✅ 优秀！大部分功能正常工作")
        print("  核心功能完备，可以投入生产使用")
        
    else:
        print("\n⚠️ 需要进一步优化")
        print("  部分核心功能存在问题")
    
    # 关闭引擎
    print("\n🔧 正在关闭模板引擎...")
    engine.shutdown()
    print("✅ 模板引擎已安全关闭")
    
    return success_rate


if __name__ == "__main__":
    success_rate = final_comprehensive_test()
    
    if success_rate == 100:
        print(f"\n🏆 最终综合测试: {success_rate:.1f}% - 完美成功！")
        print("🎯 线程安全版本完全就绪，可以投入生产使用！")
    elif success_rate >= 80:
        print(f"\n📊 最终综合测试: {success_rate:.1f}% - 优秀")
        print("🎯 核心功能完备，基本满足企业级需求")
    else:
        print(f"\n🔧 最终综合测试: {success_rate:.1f}% - 需要优化")
