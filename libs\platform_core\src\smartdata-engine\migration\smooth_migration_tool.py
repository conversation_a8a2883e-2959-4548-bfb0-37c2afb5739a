#!/usr/bin/env python3
"""
平滑迁移工具

提供从旧模板引擎到线程安全版本的平滑迁移方案
"""

import logging
import os
import json
import shutil
import tempfile
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../'))


class MigrationMode(Enum):
    """迁移模式"""
    COMPATIBILITY = "compatibility"  # 兼容模式：保持旧API
    HYBRID = "hybrid"                # 混合模式：新旧并存
    FULL = "full"                    # 完全模式：使用新架构


@dataclass
class MigrationConfig:
    """迁移配置"""
    mode: MigrationMode = MigrationMode.COMPATIBILITY
    enable_thread_safety: bool = True
    enable_legacy_support: bool = True
    backup_original: bool = True
    migration_log_level: str = "INFO"
    test_before_migration: bool = True
    rollback_on_failure: bool = True


@dataclass
class MigrationResult:
    """迁移结果"""
    success: bool
    mode: MigrationMode
    migrated_components: List[str]
    failed_components: List[str]
    warnings: List[str]
    backup_path: Optional[str]
    migration_time: datetime
    performance_comparison: Dict[str, Any]


class SmoothMigrationTool:
    """
    平滑迁移工具
    
    提供三种迁移模式：
    1. 兼容模式：保持旧API，内部使用新架构
    2. 混合模式：新旧API并存，逐步迁移
    3. 完全模式：完全使用新架构API
    """
    
    def __init__(self, config: MigrationConfig = None):
        self.config = config or MigrationConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.setLevel(getattr(logging, self.config.migration_log_level))
        
        # 迁移状态
        self.migration_history = []
        self.backup_paths = []
        
        self.logger.info(f"迁移工具初始化完成 - 模式: {self.config.mode.value}")
    
    def migrate(self, source_path: str, target_path: str = None) -> MigrationResult:
        """
        执行平滑迁移
        
        Args:
            source_path: 源代码路径
            target_path: 目标路径（可选，默认为源路径）
        """
        start_time = datetime.now()
        target_path = target_path or source_path
        
        self.logger.info(f"开始迁移: {source_path} -> {target_path}")
        
        try:
            # 1. 备份原始代码
            backup_path = None
            if self.config.backup_original:
                backup_path = self._create_backup(source_path)
                self.logger.info(f"备份创建: {backup_path}")
            
            # 2. 分析现有代码
            analysis_result = self._analyze_existing_code(source_path)
            self.logger.info(f"代码分析完成: {len(analysis_result['templates'])}个模板文件")
            
            # 3. 执行迁移
            migration_results = self._execute_migration(
                source_path, target_path, analysis_result
            )
            
            # 4. 验证迁移结果
            if self.config.test_before_migration:
                validation_result = self._validate_migration(target_path)
                if not validation_result['success']:
                    raise Exception(f"迁移验证失败: {validation_result['errors']}")
            
            # 5. 性能对比测试
            performance_comparison = self._performance_comparison(
                source_path, target_path
            )
            
            # 创建迁移结果
            result = MigrationResult(
                success=True,
                mode=self.config.mode,
                migrated_components=migration_results['migrated'],
                failed_components=migration_results['failed'],
                warnings=migration_results['warnings'],
                backup_path=backup_path,
                migration_time=datetime.now() - start_time,
                performance_comparison=performance_comparison
            )
            
            self.logger.info("迁移完成成功")
            return result
            
        except Exception as e:
            self.logger.error(f"迁移失败: {e}")
            
            # 回滚处理
            if self.config.rollback_on_failure and backup_path:
                self._rollback(backup_path, target_path)
                self.logger.info("已回滚到原始状态")
            
            return MigrationResult(
                success=False,
                mode=self.config.mode,
                migrated_components=[],
                failed_components=[str(e)],
                warnings=[],
                backup_path=backup_path,
                migration_time=datetime.now() - start_time,
                performance_comparison={}
            )
    
    def _create_backup(self, source_path: str) -> str:
        """创建备份"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"backup_{os.path.basename(source_path)}_{timestamp}"
        backup_path = os.path.join(tempfile.gettempdir(), backup_name)
        
        if os.path.isfile(source_path):
            shutil.copy2(source_path, backup_path)
        else:
            shutil.copytree(source_path, backup_path)
        
        self.backup_paths.append(backup_path)
        return backup_path
    
    def _analyze_existing_code(self, source_path: str) -> Dict[str, Any]:
        """分析现有代码"""
        analysis = {
            'templates': [],
            'legacy_features': [],
            'compatibility_issues': [],
            'migration_complexity': 'low'
        }
        
        # 扫描模板文件
        if os.path.isfile(source_path):
            files = [source_path]
        else:
            files = []
            for root, dirs, filenames in os.walk(source_path):
                for filename in filenames:
                    if filename.endswith(('.py', '.html', '.jinja2', '.j2')):
                        files.append(os.path.join(root, filename))
        
        # 分析每个文件
        for file_path in files:
            file_analysis = self._analyze_file(file_path)
            analysis['templates'].append(file_analysis)
            
            # 收集旧版功能使用情况
            analysis['legacy_features'].extend(file_analysis['legacy_features'])
        
        # 评估迁移复杂度
        if len(analysis['legacy_features']) > 10:
            analysis['migration_complexity'] = 'high'
        elif len(analysis['legacy_features']) > 5:
            analysis['migration_complexity'] = 'medium'
        
        return analysis
    
    def _analyze_file(self, file_path: str) -> Dict[str, Any]:
        """分析单个文件"""
        analysis = {
            'file_path': file_path,
            'legacy_features': [],
            'template_engine_usage': [],
            'migration_needed': False
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检测旧模板引擎的使用
            legacy_patterns = [
                'HybridTemplateEngine',
                'SmartDataFactory',
                'SmartJSONData',
                'SmartXMLData',
                'EnhancedDataTypes',
                'JSONPathResolver'
            ]
            
            for pattern in legacy_patterns:
                if pattern in content:
                    analysis['legacy_features'].append(pattern)
                    analysis['migration_needed'] = True
            
            # 检测模板语法
            template_patterns = [
                '{% set',
                '{% for',
                '{% if',
                '{{ sd.',
                'smart_data(',
                'jsonpath('
            ]
            
            for pattern in template_patterns:
                if pattern in content:
                    analysis['template_engine_usage'].append(pattern)
        
        except Exception as e:
            self.logger.warning(f"文件分析失败 {file_path}: {e}")
        
        return analysis
    
    def _execute_migration(self, source_path: str, target_path: str, 
                          analysis: Dict[str, Any]) -> Dict[str, List[str]]:
        """执行迁移"""
        results = {
            'migrated': [],
            'failed': [],
            'warnings': []
        }
        
        if self.config.mode == MigrationMode.COMPATIBILITY:
            results.update(self._compatibility_migration(source_path, target_path, analysis))
        elif self.config.mode == MigrationMode.HYBRID:
            results.update(self._hybrid_migration(source_path, target_path, analysis))
        elif self.config.mode == MigrationMode.FULL:
            results.update(self._full_migration(source_path, target_path, analysis))
        
        return results
    
    def _compatibility_migration(self, source_path: str, target_path: str, 
                                analysis: Dict[str, Any]) -> Dict[str, List[str]]:
        """兼容模式迁移"""
        results = {'migrated': [], 'failed': [], 'warnings': []}
        
        # 创建兼容性包装器
        wrapper_code = self._generate_compatibility_wrapper()
        
        # 在目标路径创建兼容性文件
        compatibility_file = os.path.join(target_path, 'template_compatibility.py')
        try:
            with open(compatibility_file, 'w', encoding='utf-8') as f:
                f.write(wrapper_code)
            results['migrated'].append('compatibility_wrapper')
        except Exception as e:
            results['failed'].append(f'compatibility_wrapper: {e}')
        
        return results
    
    def _hybrid_migration(self, source_path: str, target_path: str, 
                         analysis: Dict[str, Any]) -> Dict[str, List[str]]:
        """混合模式迁移"""
        results = {'migrated': [], 'failed': [], 'warnings': []}
        
        # 创建新旧API并存的桥接器
        bridge_code = self._generate_hybrid_bridge()
        
        bridge_file = os.path.join(target_path, 'template_bridge.py')
        try:
            with open(bridge_file, 'w', encoding='utf-8') as f:
                f.write(bridge_code)
            results['migrated'].append('hybrid_bridge')
        except Exception as e:
            results['failed'].append(f'hybrid_bridge: {e}')
        
        return results
    
    def _full_migration(self, source_path: str, target_path: str, 
                       analysis: Dict[str, Any]) -> Dict[str, List[str]]:
        """完全模式迁移"""
        results = {'migrated': [], 'failed': [], 'warnings': []}
        
        # 生成完全使用新架构的代码
        new_engine_code = self._generate_new_engine_code()
        
        new_engine_file = os.path.join(target_path, 'new_template_engine.py')
        try:
            with open(new_engine_file, 'w', encoding='utf-8') as f:
                f.write(new_engine_code)
            results['migrated'].append('new_template_engine')
        except Exception as e:
            results['failed'].append(f'new_template_engine: {e}')
        
        return results
    
    def _generate_compatibility_wrapper(self) -> str:
        """生成兼容性包装器代码"""
        return '''#!/usr/bin/env python3
"""
模板引擎兼容性包装器

保持旧API不变，内部使用线程安全的新架构
"""

from template.thread_safe_template_integration import ThreadSafeTemplateIntegration

# 全局线程安全引擎实例
_thread_safe_engine = ThreadSafeTemplateIntegration(
    enable_async=True,
    enable_debug=False,
    isolation_level='thread'
)

class HybridTemplateEngine:
    """兼容性包装器 - 保持旧API"""
    
    def __init__(self, **kwargs):
        # 忽略旧参数，使用新架构
        pass
    
    def render_template(self, template_string: str, context: dict = None) -> str:
        """渲染模板 - 兼容旧API"""
        return _thread_safe_engine.render_template_sync(template_string, context)
    
    async def render_template_async(self, template_string: str, context: dict = None) -> str:
        """异步渲染模板 - 兼容旧API"""
        return await _thread_safe_engine.render_template_async(template_string, context)

# 兼容性函数
def create_template_engine(**kwargs):
    """创建模板引擎 - 兼容旧API"""
    return HybridTemplateEngine(**kwargs)

# 导出兼容性接口
__all__ = ['HybridTemplateEngine', 'create_template_engine']
'''
    
    def _generate_hybrid_bridge(self) -> str:
        """生成混合模式桥接器代码"""
        return '''#!/usr/bin/env python3
"""
模板引擎混合桥接器

新旧API并存，支持逐步迁移
"""

from template.thread_safe_template_integration import ThreadSafeTemplateIntegration

class TemplateBridge:
    """混合模式桥接器"""
    
    def __init__(self):
        self.new_engine = ThreadSafeTemplateIntegration()
        self.legacy_engine = None
        
        # 尝试加载旧引擎
        try:
            from template.hybrid_template_engine import HybridTemplateEngine
            self.legacy_engine = HybridTemplateEngine()
        except ImportError:
            pass
    
    def render_with_new_engine(self, template: str, context: dict = None) -> str:
        """使用新引擎渲染"""
        return self.new_engine.render_template_sync(template, context)
    
    def render_with_legacy_engine(self, template: str, context: dict = None) -> str:
        """使用旧引擎渲染"""
        if self.legacy_engine:
            return self.legacy_engine.render_template(template, context)
        else:
            # 回退到新引擎
            return self.render_with_new_engine(template, context)
    
    def render_auto(self, template: str, context: dict = None, prefer_new: bool = True) -> str:
        """自动选择引擎渲染"""
        if prefer_new:
            try:
                return self.render_with_new_engine(template, context)
            except Exception:
                return self.render_with_legacy_engine(template, context)
        else:
            try:
                return self.render_with_legacy_engine(template, context)
            except Exception:
                return self.render_with_new_engine(template, context)

# 全局桥接器实例
bridge = TemplateBridge()

# 便利函数
def render_template(template: str, context: dict = None, use_new_engine: bool = True) -> str:
    """渲染模板 - 混合模式"""
    return bridge.render_auto(template, context, prefer_new=use_new_engine)
'''
    
    def _generate_new_engine_code(self) -> str:
        """生成新引擎代码"""
        return '''#!/usr/bin/env python3
"""
新架构模板引擎

完全使用线程安全的新架构
"""

from template.thread_safe_template_integration import ThreadSafeTemplateIntegration

# 创建企业级线程安全模板引擎
engine = ThreadSafeTemplateIntegration(
    enable_async=True,
    enable_debug=False,
    isolation_level='thread',
    cleanup_interval=300,
    max_scope_lifetime=3600
)

def render_template(template: str, context: dict = None, scope_id: str = None) -> str:
    """渲染模板"""
    return engine.render_template_sync(template, context, scope_id)

async def render_template_async(template: str, context: dict = None, scope_id: str = None) -> str:
    """异步渲染模板"""
    return await engine.render_template_async(template, context, scope_id)

def create_isolated_scope(scope_id: str = None):
    """创建隔离作用域"""
    return engine.create_isolated_scope(scope_id)

# 导出新架构接口
__all__ = ['render_template', 'render_template_async', 'create_isolated_scope', 'engine']
'''
    
    def _validate_migration(self, target_path: str) -> Dict[str, Any]:
        """验证迁移结果"""
        validation = {
            'success': True,
            'errors': [],
            'warnings': []
        }
        
        # 这里可以添加具体的验证逻辑
        # 例如：导入测试、功能测试等
        
        return validation
    
    def _performance_comparison(self, source_path: str, target_path: str) -> Dict[str, Any]:
        """性能对比测试"""
        comparison = {
            'old_engine_time': 0.0,
            'new_engine_time': 0.0,
            'improvement_ratio': 0.0,
            'memory_usage_old': 0,
            'memory_usage_new': 0
        }
        
        # 这里可以添加具体的性能测试逻辑
        
        return comparison
    
    def _rollback(self, backup_path: str, target_path: str):
        """回滚到备份状态"""
        try:
            if os.path.isfile(backup_path):
                shutil.copy2(backup_path, target_path)
            else:
                if os.path.exists(target_path):
                    shutil.rmtree(target_path)
                shutil.copytree(backup_path, target_path)
            
            self.logger.info(f"回滚成功: {backup_path} -> {target_path}")
        except Exception as e:
            self.logger.error(f"回滚失败: {e}")
    
    def cleanup_backups(self):
        """清理备份文件"""
        for backup_path in self.backup_paths:
            try:
                if os.path.exists(backup_path):
                    if os.path.isfile(backup_path):
                        os.unlink(backup_path)
                    else:
                        shutil.rmtree(backup_path)
                self.logger.info(f"备份清理: {backup_path}")
            except Exception as e:
                self.logger.warning(f"备份清理失败 {backup_path}: {e}")


# 便利函数
def create_migration_tool(mode: MigrationMode = MigrationMode.COMPATIBILITY) -> SmoothMigrationTool:
    """创建迁移工具"""
    config = MigrationConfig(mode=mode)
    return SmoothMigrationTool(config)
