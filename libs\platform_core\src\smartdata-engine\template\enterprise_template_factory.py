"""
企业级模板引擎工厂

提供统一的模板引擎创建和配置接口
"""

import logging
from typing import Any, Dict, List, Optional, Union, Type
from dataclasses import dataclass, field
from enum import Enum

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../'))

from template.enterprise_template_integration import EnterpriseTemplateIntegration
from core.interfaces import ITemplateEngine


class TemplateEngineMode(Enum):
    """模板引擎模式"""
    ENTERPRISE = "enterprise"  # 企业级模式 - 使用新的统一适配器系统
    HYBRID = "hybrid"         # 混合模式 - 新旧系统并存
    LEGACY = "legacy"         # 兼容模式 - 仅使用现有系统
    PERFORMANCE = "performance"  # 高性能模式 - 优化配置
    DEBUG = "debug"           # 调试模式 - 详细日志和调试信息


@dataclass
class EnterpriseTemplateConfig:
    """企业级模板引擎配置"""
    
    # 基础配置
    mode: TemplateEngineMode = TemplateEngineMode.ENTERPRISE
    enable_async: bool = True
    enable_debug: bool = False
    
    # 兼容性配置
    enable_legacy_support: bool = True
    auto_discover_plugins: bool = True
    
    # 性能配置
    enable_caching: bool = True
    cache_size: int = 1000
    enable_connection_pooling: bool = True
    max_connections_per_adapter: int = 10
    
    # 安全配置
    enable_security: bool = True
    max_template_size: int = 1024 * 1024  # 1MB
    max_execution_time: int = 30  # 秒
    allowed_functions: Optional[List[str]] = None
    
    # 监控配置
    enable_performance_monitoring: bool = True
    enable_metrics_collection: bool = True
    
    # 自定义适配器
    custom_adapters: List[Type] = field(default_factory=list)
    
    # 扩展配置
    extensions: Dict[str, Any] = field(default_factory=dict)


class EnterpriseTemplateEngine(ITemplateEngine):
    """
    企业级模板引擎
    
    集成新的统一适配器系统，提供企业级功能：
    - 统一数据访问接口
    - 智能适配器选择
    - 生命周期管理
    - 异步支持
    - 性能监控
    - 安全控制
    """
    
    def __init__(self, config: EnterpriseTemplateConfig):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        if config.enable_debug:
            self.logger.setLevel(logging.DEBUG)
        
        # 创建企业级集成器
        self.integration = EnterpriseTemplateIntegration(
            enable_async=config.enable_async,
            enable_legacy_support=config.enable_legacy_support,
            enable_debug=config.enable_debug
        )
        
        # 注册自定义适配器
        self._register_custom_adapters()
        
        # 性能统计
        self.render_count = 0
        self.total_render_time = 0.0
        self.error_count = 0
        
        self.logger.info(f"企业级模板引擎初始化完成 - 模式: {config.mode.value}")
    
    def _register_custom_adapters(self):
        """注册自定义适配器"""
        for adapter_class in self.config.custom_adapters:
            try:
                self.integration.data_registry.register_adapter(adapter_class)
                self.logger.info(f"注册自定义适配器: {adapter_class.__name__}")
            except Exception as e:
                self.logger.error(f"注册自定义适配器失败 {adapter_class.__name__}: {e}")
    
    def render(self, template: str, context: Dict[str, Any] = None) -> str:
        """同步渲染模板"""
        import time
        start_time = time.perf_counter()
        
        try:
            # 安全检查
            self._validate_template(template)
            
            # 渲染模板
            result = self.integration.render_template_sync(template, context)
            
            # 更新统计
            self.render_count += 1
            self.total_render_time += time.perf_counter() - start_time
            
            return result
            
        except Exception as e:
            self.error_count += 1
            self.logger.error(f"模板渲染失败: {e}")
            raise
    
    async def render_async(self, template: str, context: Dict[str, Any] = None) -> str:
        """异步渲染模板"""
        import time
        start_time = time.perf_counter()
        
        try:
            # 安全检查
            self._validate_template(template)
            
            # 异步渲染模板
            result = await self.integration.render_template_async(template, context)
            
            # 更新统计
            self.render_count += 1
            self.total_render_time += time.perf_counter() - start_time
            
            return result
            
        except Exception as e:
            self.error_count += 1
            self.logger.error(f"异步模板渲染失败: {e}")
            raise
    
    def _validate_template(self, template: str):
        """验证模板安全性"""
        if not self.config.enable_security:
            return
        
        # 检查模板大小
        if len(template) > self.config.max_template_size:
            raise ValueError(f"模板大小超过限制: {len(template)} > {self.config.max_template_size}")
        
        # 可以添加更多安全检查
        # 例如：检查危险函数调用、SQL注入等
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = {
            'render_count': self.render_count,
            'total_render_time': self.total_render_time,
            'average_render_time': self.total_render_time / max(self.render_count, 1),
            'error_count': self.error_count,
            'error_rate': self.error_count / max(self.render_count, 1),
            'config_mode': self.config.mode.value
        }
        
        # 添加集成器统计
        stats.update(self.integration.get_performance_stats())
        
        return stats
    
    def get_supported_data_types(self) -> List[str]:
        """获取支持的数据类型"""
        return self.integration.data_registry.get_supported_types()
    
    def register_adapter(self, adapter_class: Type):
        """动态注册适配器"""
        self.integration.data_registry.register_adapter(adapter_class)
        self.logger.info(f"动态注册适配器: {adapter_class.__name__}")
    
    def cleanup(self):
        """清理资源"""
        # 清理所有模板作用域
        for template_id in list(self.integration.template_scopes.keys()):
            self.integration.cleanup_template_scope(template_id)
        
        # 清理生命周期管理器
        if hasattr(self.integration.sync_lifecycle_manager, 'cleanup'):
            self.integration.sync_lifecycle_manager.cleanup()
        
        if (self.config.enable_async and 
            hasattr(self.integration.async_lifecycle_manager, 'cleanup')):
            import asyncio
            if asyncio.get_event_loop().is_running():
                asyncio.create_task(self.integration.async_lifecycle_manager.cleanup())
            else:
                asyncio.run(self.integration.async_lifecycle_manager.cleanup())
        
        self.logger.info("企业级模板引擎资源清理完成")


class EnterpriseTemplateFactory:
    """企业级模板引擎工厂"""
    
    @staticmethod
    def create_engine(config: Optional[EnterpriseTemplateConfig] = None) -> EnterpriseTemplateEngine:
        """创建企业级模板引擎"""
        if config is None:
            config = EnterpriseTemplateConfig()
        
        return EnterpriseTemplateEngine(config)
    
    @staticmethod
    def create_high_performance_engine() -> EnterpriseTemplateEngine:
        """创建高性能模板引擎"""
        config = EnterpriseTemplateConfig(
            mode=TemplateEngineMode.PERFORMANCE,
            enable_async=True,
            enable_caching=True,
            cache_size=2000,
            enable_connection_pooling=True,
            max_connections_per_adapter=20,
            enable_performance_monitoring=True,
            enable_debug=False
        )
        return EnterpriseTemplateFactory.create_engine(config)
    
    @staticmethod
    def create_debug_engine() -> EnterpriseTemplateEngine:
        """创建调试模式模板引擎"""
        config = EnterpriseTemplateConfig(
            mode=TemplateEngineMode.DEBUG,
            enable_debug=True,
            enable_performance_monitoring=True,
            enable_metrics_collection=True
        )
        return EnterpriseTemplateFactory.create_engine(config)
    
    @staticmethod
    def create_secure_engine() -> EnterpriseTemplateEngine:
        """创建安全模式模板引擎"""
        config = EnterpriseTemplateConfig(
            mode=TemplateEngineMode.ENTERPRISE,
            enable_security=True,
            max_template_size=512 * 1024,  # 512KB
            max_execution_time=15,  # 15秒
            allowed_functions=['len', 'str', 'int', 'float', 'bool', 'list', 'dict']
        )
        return EnterpriseTemplateFactory.create_engine(config)
    
    @staticmethod
    def create_hybrid_engine() -> EnterpriseTemplateEngine:
        """创建混合模式模板引擎（新旧系统并存）"""
        config = EnterpriseTemplateConfig(
            mode=TemplateEngineMode.HYBRID,
            enable_legacy_support=True,
            auto_discover_plugins=True,
            enable_async=True
        )
        return EnterpriseTemplateFactory.create_engine(config)
    
    @staticmethod
    def create_legacy_compatible_engine() -> EnterpriseTemplateEngine:
        """创建完全兼容现有系统的模板引擎"""
        config = EnterpriseTemplateConfig(
            mode=TemplateEngineMode.LEGACY,
            enable_legacy_support=True,
            auto_discover_plugins=True,
            enable_async=False  # 保持与现有系统一致
        )
        return EnterpriseTemplateFactory.create_engine(config)


# 便利函数
def create_enterprise_template_engine(
    mode: TemplateEngineMode = TemplateEngineMode.ENTERPRISE,
    **kwargs
) -> EnterpriseTemplateEngine:
    """创建企业级模板引擎的便利函数"""
    config = EnterpriseTemplateConfig(mode=mode, **kwargs)
    return EnterpriseTemplateFactory.create_engine(config)
