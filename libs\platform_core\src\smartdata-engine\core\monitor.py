"""
企业级监控组件

基于设计文档和现有监控系统的完整监控实现：
- Prometheus指标集成
- 健康检查
- 性能监控
- 告警机制
- 分布式追踪
"""

import time
import psutil
import asyncio
import logging
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import threading
from collections import defaultdict, deque

try:
    from prometheus_client import Counter, Histogram, Gauge, Info, CollectorRegistry, generate_latest
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False
    # 创建功能性模拟类，提供基本的指标收集
    CollectorRegistry = None  # 定义为None以避免NameError

    class MockMetric:
        """模拟指标基类"""
        def __init__(self, name, description, labelnames=None, registry=None):
            self.name = name
            self.description = description
            self.labelnames = labelnames or []
            self._values = {}
            self._labels = {}

        def labels(self, **kwargs):
            """返回带标签的指标实例"""
            label_key = tuple(sorted(kwargs.items()))
            if label_key not in self._labels:
                self._labels[label_key] = MockLabeledMetric(self, kwargs)
            return self._labels[label_key]

    class MockLabeledMetric:
        """模拟带标签的指标"""
        def __init__(self, parent, labels):
            self.parent = parent
            self.labels_dict = labels
            self.value = 0

        def inc(self, amount=1):
            """增加计数"""
            self.value += amount
            logger.debug(f"Mock Counter {self.parent.name} inc: {amount}, total: {self.value}")

        def observe(self, value):
            """观察值"""
            self.value = value
            logger.debug(f"Mock Histogram {self.parent.name} observe: {value}")

        def set(self, value):
            """设置值"""
            self.value = value
            logger.debug(f"Mock Gauge {self.parent.name} set: {value}")

    class Counter(MockMetric):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)

    class Histogram(MockMetric):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)

    class Gauge(MockMetric):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)

    class Info(MockMetric):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)

        def info(self, labels_dict):
            """设置信息标签"""
            self._info_labels = labels_dict
            logger.info(f"Mock Info {self.name}: {labels_dict}")

    def generate_latest(registry=None):
        """生成模拟的Prometheus格式输出"""
        return "# Mock Prometheus metrics - install prometheus_client for real metrics\n"


logger = logging.getLogger(__name__)


# ==================== 监控指标定义 ====================

class HealthStatus(Enum):
    """健康状态"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class SystemMetrics:
    """系统指标"""
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_io: Dict[str, int] = field(default_factory=dict)
    process_count: int = 0
    load_average: List[float] = field(default_factory=list)
    timestamp: float = field(default_factory=time.time)


@dataclass
class ApplicationMetrics:
    """应用指标"""
    request_count: int = 0
    error_count: int = 0
    response_time: float = 0.0
    active_connections: int = 0
    cache_hit_rate: float = 0.0
    memory_usage_mb: float = 0.0
    is_healthy: bool = True
    timestamp: float = field(default_factory=time.time)


@dataclass
class HealthCheckResult:
    """健康检查结果"""
    component: str
    status: HealthStatus
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)
    response_time_ms: float = 0.0


# ==================== Prometheus指标 ====================

class PrometheusMetrics:
    """Prometheus指标管理器 (基于设计文档)"""
    
    def __init__(self, registry: Optional[Any] = None):
        """初始化Prometheus指标"""
        self.registry = registry
        
        if not PROMETHEUS_AVAILABLE:
            logger.warning("Prometheus client not available, metrics disabled")
            return
        
        # 请求指标
        self.REQUEST_COUNT = Counter(
            "smartdata_requests_total",
            "Total data process calls",
            ["processor", "status"],
            registry=registry
        )
        
        self.PROCESS_TIME = Histogram(
            "smartdata_process_seconds",
            "Time spent in process",
            ["processor"],
            registry=registry
        )
        
        # 健康状态指标
        self.HEALTH_STATUS = Gauge(
            "smartdata_health",
            "Health status (1=healthy, 0=unhealthy)",
            ["processor"],
            registry=registry
        )
        
        # 系统指标
        self.CPU_USAGE = Gauge(
            "smartdata_cpu_percent",
            "CPU usage percentage",
            registry=registry
        )
        
        self.MEMORY_USAGE = Gauge(
            "smartdata_memory_percent",
            "Memory usage percentage",
            registry=registry
        )
        
        self.CACHE_HIT_RATE = Gauge(
            "smartdata_cache_hit_rate",
            "Cache hit rate",
            ["cache_type"],
            registry=registry
        )
        
        # 错误指标
        self.ERROR_COUNT = Counter(
            "smartdata_errors_total",
            "Total error count",
            ["processor", "error_type"],
            registry=registry
        )
        
        # 连接指标
        self.ACTIVE_CONNECTIONS = Gauge(
            "smartdata_active_connections",
            "Number of active connections",
            ["connection_type"],
            registry=registry
        )
        
        # 框架信息
        self.FRAMEWORK_INFO = Info(
            "smartdata_framework",
            "Framework information",
            registry=registry
        )
        
        # 设置框架信息
        self.FRAMEWORK_INFO.info({
            'version': '2.0.0',
            'architecture': 'hybrid-v2',
            'python_version': f"{psutil.sys.version_info.major}.{psutil.sys.version_info.minor}"
        })
    
    def record_request(self, processor_id: str, success: bool, elapsed_seconds: float):
        """记录请求指标"""
        if not PROMETHEUS_AVAILABLE:
            return
        
        status = "success" if success else "error"
        self.REQUEST_COUNT.labels(processor=processor_id, status=status).inc()
        self.PROCESS_TIME.labels(processor=processor_id).observe(elapsed_seconds)
    
    def set_health_status(self, processor_id: str, healthy: bool):
        """设置健康状态"""
        if not PROMETHEUS_AVAILABLE:
            return
        
        self.HEALTH_STATUS.labels(processor=processor_id).set(1 if healthy else 0)
    
    def update_system_metrics(self, metrics: SystemMetrics):
        """更新系统指标"""
        if not PROMETHEUS_AVAILABLE:
            return
        
        self.CPU_USAGE.set(metrics.cpu_percent)
        self.MEMORY_USAGE.set(metrics.memory_percent)
    
    def update_cache_metrics(self, cache_type: str, hit_rate: float):
        """更新缓存指标"""
        if not PROMETHEUS_AVAILABLE:
            return
        
        self.CACHE_HIT_RATE.labels(cache_type=cache_type).set(hit_rate)
    
    def record_error(self, processor_id: str, error_type: str):
        """记录错误"""
        if not PROMETHEUS_AVAILABLE:
            return
        
        self.ERROR_COUNT.labels(processor=processor_id, error_type=error_type).inc()
    
    def set_active_connections(self, connection_type: str, count: int):
        """设置活跃连接数"""
        if not PROMETHEUS_AVAILABLE:
            return
        
        self.ACTIVE_CONNECTIONS.labels(connection_type=connection_type).set(count)


# ==================== 健康检查器 ====================

class HealthChecker:
    """健康检查器 (基于现有监控系统)"""
    
    def __init__(self):
        self.checks: Dict[str, Callable[[], HealthCheckResult]] = {}
        self.last_results: Dict[str, HealthCheckResult] = {}
        self._lock = threading.Lock()
    
    def register_check(self, name: str, check_func: Callable[[], HealthCheckResult]):
        """注册健康检查"""
        with self._lock:
            self.checks[name] = check_func
            logger.info(f"Registered health check: {name}")
    
    def unregister_check(self, name: str):
        """注销健康检查"""
        with self._lock:
            if name in self.checks:
                del self.checks[name]
                if name in self.last_results:
                    del self.last_results[name]
                logger.info(f"Unregistered health check: {name}")
    
    async def run_check(self, name: str) -> HealthCheckResult:
        """运行单个健康检查"""
        if name not in self.checks:
            return HealthCheckResult(
                component=name,
                status=HealthStatus.UNKNOWN,
                message=f"Health check '{name}' not found"
            )
        
        start_time = time.perf_counter()
        
        try:
            # 运行检查函数
            check_func = self.checks[name]
            if asyncio.iscoroutinefunction(check_func):
                result = await check_func()
            else:
                result = check_func()
            
            # 计算响应时间
            response_time = (time.perf_counter() - start_time) * 1000
            result.response_time_ms = response_time
            
            # 保存结果
            with self._lock:
                self.last_results[name] = result
            
            return result
            
        except Exception as e:
            response_time = (time.perf_counter() - start_time) * 1000
            
            result = HealthCheckResult(
                component=name,
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {str(e)}",
                details={'error': str(e), 'error_type': type(e).__name__},
                response_time_ms=response_time
            )
            
            with self._lock:
                self.last_results[name] = result
            
            logger.error(f"Health check '{name}' failed: {e}")
            return result
    
    async def run_all_checks(self) -> Dict[str, HealthCheckResult]:
        """运行所有健康检查"""
        results = {}
        
        # 并行运行所有检查
        tasks = []
        for name in list(self.checks.keys()):
            task = asyncio.create_task(self.run_check(name))
            tasks.append((name, task))
        
        # 等待所有检查完成
        for name, task in tasks:
            try:
                result = await task
                results[name] = result
            except Exception as e:
                logger.error(f"Failed to run health check '{name}': {e}")
                results[name] = HealthCheckResult(
                    component=name,
                    status=HealthStatus.UNHEALTHY,
                    message=f"Check execution failed: {str(e)}"
                )
        
        return results
    
    def get_overall_health(self) -> HealthStatus:
        """获取整体健康状态"""
        with self._lock:
            if not self.last_results:
                return HealthStatus.UNKNOWN
            
            statuses = [result.status for result in self.last_results.values()]
            
            # 如果有任何不健康的组件，整体状态为不健康
            if HealthStatus.UNHEALTHY in statuses:
                return HealthStatus.UNHEALTHY
            
            # 如果有降级的组件，整体状态为降级
            if HealthStatus.DEGRADED in statuses:
                return HealthStatus.DEGRADED
            
            # 如果有未知状态的组件，整体状态为未知
            if HealthStatus.UNKNOWN in statuses:
                return HealthStatus.UNKNOWN
            
            # 所有组件都健康
            return HealthStatus.HEALTHY
    
    def get_health_summary(self) -> Dict[str, Any]:
        """获取健康摘要"""
        with self._lock:
            overall_status = self.get_overall_health()
            
            status_counts = defaultdict(int)
            for result in self.last_results.values():
                status_counts[result.status.value] += 1
            
            return {
                'overall_status': overall_status.value,
                'total_checks': len(self.checks),
                'status_counts': dict(status_counts),
                'last_check_time': max(
                    (result.timestamp for result in self.last_results.values()),
                    default=0
                )
            }


# ==================== 系统监控器 ====================

class SystemMonitor:
    """系统监控器 (基于现有系统监控)"""
    
    def __init__(self):
        self.metrics_history: deque = deque(maxlen=1000)
        self._lock = threading.Lock()
    
    async def collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # 网络I/O
            network_io = psutil.net_io_counters()._asdict()
            
            # 进程数量
            process_count = len(psutil.pids())
            
            # 负载平均值 (仅Unix系统)
            load_average = []
            try:
                load_average = list(psutil.getloadavg())
            except AttributeError:
                # Windows系统不支持getloadavg
                pass
            
            metrics = SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_percent=disk_percent,
                network_io=network_io,
                process_count=process_count,
                load_average=load_average
            )
            
            # 保存历史记录
            with self._lock:
                self.metrics_history.append(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")
            return SystemMetrics(
                cpu_percent=0.0,
                memory_percent=0.0,
                disk_percent=0.0
            )
    
    def get_metrics_history(self, limit: int = 100) -> List[SystemMetrics]:
        """获取指标历史"""
        with self._lock:
            return list(self.metrics_history)[-limit:]


# ==================== 全局监控实例 ====================

# 全局Prometheus指标
prometheus_metrics = PrometheusMetrics()

# 全局健康检查器
health_checker = HealthChecker()

# 全局系统监控器
system_monitor = SystemMonitor()


# ==================== 便捷函数 ====================

def record_success(processor_id: str, elapsed_seconds: float):
    """记录成功处理"""
    prometheus_metrics.record_request(processor_id, True, elapsed_seconds)
    prometheus_metrics.set_health_status(processor_id, True)


def record_failure(processor_id: str):
    """记录处理失败"""
    prometheus_metrics.record_request(processor_id, False, 0.0)
    prometheus_metrics.set_health_status(processor_id, False)


def record_error(processor_id: str, error_type: str):
    """记录错误"""
    prometheus_metrics.record_error(processor_id, error_type)


async def get_health_status() -> Dict[str, Any]:
    """获取整体健康状态"""
    # 运行所有健康检查
    check_results = await health_checker.run_all_checks()
    
    # 获取健康摘要
    health_summary = health_checker.get_health_summary()
    
    # 获取系统指标
    system_metrics = await system_monitor.collect_system_metrics()
    
    return {
        'health_summary': health_summary,
        'check_results': {name: {
            'status': result.status.value,
            'message': result.message,
            'response_time_ms': result.response_time_ms,
            'timestamp': result.timestamp
        } for name, result in check_results.items()},
        'system_metrics': {
            'cpu_percent': system_metrics.cpu_percent,
            'memory_percent': system_metrics.memory_percent,
            'disk_percent': system_metrics.disk_percent,
            'process_count': system_metrics.process_count,
            'timestamp': system_metrics.timestamp
        }
    }


def get_metrics_endpoint() -> str:
    """获取Prometheus指标端点数据"""
    if not PROMETHEUS_AVAILABLE:
        return "# Prometheus client not available\n"
    
    try:
        return generate_latest(prometheus_metrics.registry).decode('utf-8')
    except Exception as e:
        logger.error(f"Failed to generate metrics: {e}")
        return f"# Error generating metrics: {e}\n"


# ==================== 内置健康检查 ====================

def _basic_health_check() -> HealthCheckResult:
    """基础健康检查"""
    try:
        # 检查内存使用
        memory = psutil.virtual_memory()
        if memory.percent > 90:
            return HealthCheckResult(
                component="system",
                status=HealthStatus.DEGRADED,
                message=f"High memory usage: {memory.percent:.1f}%",
                details={'memory_percent': memory.percent}
            )
        
        # 检查CPU使用
        cpu_percent = psutil.cpu_percent(interval=0.1)
        if cpu_percent > 90:
            return HealthCheckResult(
                component="system",
                status=HealthStatus.DEGRADED,
                message=f"High CPU usage: {cpu_percent:.1f}%",
                details={'cpu_percent': cpu_percent}
            )
        
        return HealthCheckResult(
            component="system",
            status=HealthStatus.HEALTHY,
            message="System is healthy",
            details={
                'memory_percent': memory.percent,
                'cpu_percent': cpu_percent
            }
        )
        
    except Exception as e:
        return HealthCheckResult(
            component="system",
            status=HealthStatus.UNHEALTHY,
            message=f"Health check failed: {str(e)}",
            details={'error': str(e)}
        )


# 注册基础健康检查
health_checker.register_check("system", _basic_health_check)
