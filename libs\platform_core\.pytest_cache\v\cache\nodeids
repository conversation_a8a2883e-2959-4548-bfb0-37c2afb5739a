["src/smartdata-engine/plugins/http/tests/test_http_plugin.py::TestHttpPluginStandard::test_error_handling", "src/smartdata-engine/plugins/http/tests/test_http_plugin.py::TestHttpPluginStandard::test_http_methods_comprehensive", "src/smartdata-engine/plugins/http/tests/test_http_plugin.py::TestHttpPluginStandard::test_plugin_factory", "src/smartdata-engine/plugins/http/tests/test_http_plugin.py::TestHttpPluginStandard::test_smart_loader", "src/smartdata-engine/plugins/http/tests/test_http_plugin.py::TestHttpPluginStandard::test_template_integration", "src/smartdata-engine/plugins/remote_file/tests/test_refactored_integration.py::TestRefactoredRemoteFilePlugin::test_backward_compatibility", "src/smartdata-engine/plugins/remote_file/tests/test_refactored_integration.py::TestRefactoredRemoteFilePlugin::test_enterprise_processor_functionality", "src/smartdata-engine/plugins/remote_file/tests/test_refactored_integration.py::TestRefactoredRemoteFilePlugin::test_no_redundant_files", "src/smartdata-engine/plugins/remote_file/tests/test_refactored_integration.py::TestRefactoredRemoteFilePlugin::test_performance_features", "src/smartdata-engine/plugins/remote_file/tests/test_refactored_integration.py::TestRefactoredRemoteFilePlugin::test_plugin_standards_compliance", "src/smartdata-engine/plugins/remote_file/tests/test_refactored_integration.py::TestRefactoredRemoteFilePlugin::test_real_sftp_functionality", "src/smartdata-engine/plugins/remote_file/tests/test_refactored_integration.py::TestRefactoredRemoteFilePlugin::test_smart_data_object_integration", "src/smartdata-engine/plugins/remote_file/tests/test_refactored_integration.py::TestRefactoredRemoteFilePlugin::test_smart_remote_loader_integration", "src/smartdata-engine/plugins/remote_file/tests/test_refactored_integration.py::TestRefactoredRemoteFilePlugin::test_unified_connection_pool_interface", "src/smartdata-engine/plugins/remote_file/tests/test_refactored_integration.py::TestRefactoredRemoteFilePlugin::test_unified_processor_interface", "src/smartdata-engine/plugins/remote_file/tests/test_refactored_integration.py::TestRefactoredRemoteFilePlugin::test_unified_protocol_interface", "src/smartdata-engine/plugins/remote_host/tests/test_remote_host_plugin_standards.py::TestRemoteHostPluginStandards::test_async_support_compliance", "src/smartdata-engine/plugins/remote_host/tests/test_remote_host_plugin_standards.py::TestRemoteHostPluginStandards::test_configuration_compliance", "src/smartdata-engine/plugins/remote_host/tests/test_remote_host_plugin_standards.py::TestRemoteHostPluginStandards::test_connection_support_compliance", "src/smartdata-engine/plugins/remote_host/tests/test_remote_host_plugin_standards.py::TestRemoteHostPluginStandards::test_documentation_compliance", "src/smartdata-engine/plugins/remote_host/tests/test_remote_host_plugin_standards.py::TestRemoteHostPluginStandards::test_error_handling_compliance", "src/smartdata-engine/plugins/remote_host/tests/test_remote_host_plugin_standards.py::TestRemoteHostPluginStandards::test_factory_pattern_compliance", "src/smartdata-engine/plugins/remote_host/tests/test_remote_host_plugin_standards.py::TestRemoteHostPluginStandards::test_handler_detection_compliance", "src/smartdata-engine/plugins/remote_host/tests/test_remote_host_plugin_standards.py::TestRemoteHostPluginStandards::test_handler_interface_compliance", "src/smartdata-engine/plugins/remote_host/tests/test_remote_host_plugin_standards.py::TestRemoteHostPluginStandards::test_main_processor_compliance", "src/smartdata-engine/plugins/remote_host/tests/test_remote_host_plugin_standards.py::TestRemoteHostPluginStandards::test_performance_features_compliance", "src/smartdata-engine/plugins/remote_host/tests/test_remote_host_plugin_standards.py::TestRemoteHostPluginStandards::test_plugin_definitions_compliance", "src/smartdata-engine/plugins/remote_host/tests/test_remote_host_plugin_standards.py::TestRemoteHostPluginStandards::test_plugin_metadata_compliance", "src/smartdata-engine/plugins/remote_host/tests/test_remote_host_plugin_standards.py::TestRemoteHostPluginStandards::test_security_features_compliance", "src/smartdata-engine/plugins/remote_host/tests/test_remote_host_plugin_standards.py::TestRemoteHostPluginStandards::test_smart_loader_compliance", "src/smartdata-engine/plugins/remote_host/tests/test_remote_host_plugin_standards.py::test_run_async_tests", "src/smartdata-engine/plugins/rest/tests/test_comprehensive_rest_methods.py::TestComprehensiveRestMethods::test_rest_authentication_methods", "src/smartdata-engine/plugins/rest/tests/test_comprehensive_rest_methods.py::TestComprehensiveRestMethods::test_rest_delete_method_comprehensive", "src/smartdata-engine/plugins/rest/tests/test_comprehensive_rest_methods.py::TestComprehensiveRestMethods::test_rest_error_handling_comprehensive", "src/smartdata-engine/plugins/rest/tests/test_comprehensive_rest_methods.py::TestComprehensiveRestMethods::test_rest_get_method_comprehensive", "src/smartdata-engine/plugins/rest/tests/test_comprehensive_rest_methods.py::TestComprehensiveRestMethods::test_rest_head_options_methods", "src/smartdata-engine/plugins/rest/tests/test_comprehensive_rest_methods.py::TestComprehensiveRestMethods::test_rest_patch_method_comprehensive", "src/smartdata-engine/plugins/rest/tests/test_comprehensive_rest_methods.py::TestComprehensiveRestMethods::test_rest_post_method_comprehensive", "src/smartdata-engine/plugins/rest/tests/test_comprehensive_rest_methods.py::TestComprehensiveRestMethods::test_rest_put_method_comprehensive", "src/smartdata-engine/plugins/rest/tests/test_rest_plugin_standards.py::TestRestPluginStandards::test_async_support_compliance", "src/smartdata-engine/plugins/rest/tests/test_rest_plugin_standards.py::TestRestPluginStandards::test_configuration_compliance", "src/smartdata-engine/plugins/rest/tests/test_rest_plugin_standards.py::TestRestPluginStandards::test_documentation_compliance", "src/smartdata-engine/plugins/rest/tests/test_rest_plugin_standards.py::TestRestPluginStandards::test_error_handling_compliance", "src/smartdata-engine/plugins/rest/tests/test_rest_plugin_standards.py::TestRestPluginStandards::test_factory_pattern_compliance", "src/smartdata-engine/plugins/rest/tests/test_rest_plugin_standards.py::TestRestPluginStandards::test_handler_interface_compliance", "src/smartdata-engine/plugins/rest/tests/test_rest_plugin_standards.py::TestRestPluginStandards::test_main_processor_compliance", "src/smartdata-engine/plugins/rest/tests/test_rest_plugin_standards.py::TestRestPluginStandards::test_performance_features_compliance", "src/smartdata-engine/plugins/rest/tests/test_rest_plugin_standards.py::TestRestPluginStandards::test_plugin_definitions_compliance", "src/smartdata-engine/plugins/rest/tests/test_rest_plugin_standards.py::TestRestPluginStandards::test_plugin_metadata_compliance", "src/smartdata-engine/plugins/rest/tests/test_rest_plugin_standards.py::TestRestPluginStandards::test_smart_loader_compliance", "src/smartdata-engine/plugins/rest/tests/test_rest_plugin_standards.py::TestRestPluginStandards::test_template_integration_compliance", "src/smartdata-engine/plugins/rest/tests/test_rest_plugin_standards.py::test_run_async_tests", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_configure", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_detect_dict_config", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_detect_rest_data_type", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_detect_url_string", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_export_prometheus_metrics", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_extract_response_content", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_generate_cache_key", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_get_performance_metrics", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_health_check_cache", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_health_check_connectivity", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_health_check_http_client", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_metadata_property", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_open_and_close", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_parse_request_config_dict", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_parse_request_config_string", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_processor_initialization", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_selftest", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestApiProcessor::test_update_performance_metrics", "src/smartdata-engine/plugins/rest/tests/test_rest_processor.py::TestRestProcessorIntegration::test_full_request_cycle", "src/smartdata-engine/plugins/rest/tests/test_simple_integration.py::TestRestSimpleIntegration::test_rest_api_functions", "src/smartdata-engine/plugins/rest/tests/test_simple_integration.py::TestRestSimpleIntegration::test_rest_async_processing", "src/smartdata-engine/plugins/rest/tests/test_simple_integration.py::TestRestSimpleIntegration::test_rest_basic_request", "src/smartdata-engine/plugins/rest/tests/test_simple_integration.py::TestRestSimpleIntegration::test_rest_cache_management", "src/smartdata-engine/plugins/rest/tests/test_simple_integration.py::TestRestSimpleIntegration::test_rest_configuration", "src/smartdata-engine/plugins/rest/tests/test_simple_integration.py::TestRestSimpleIntegration::test_rest_error_handling", "src/smartdata-engine/plugins/rest/tests/test_simple_integration.py::TestRestSimpleIntegration::test_rest_metadata", "src/smartdata-engine/plugins/rest/tests/test_simple_integration.py::TestRestSimpleIntegration::test_rest_performance_metrics", "src/smartdata-engine/plugins/rest/tests/test_simple_integration.py::TestRestSimpleIntegration::test_rest_processor_can_handle", "src/smartdata-engine/plugins/rest/tests/test_simple_integration.py::TestRestSimpleIntegration::test_rest_processor_creation", "src/smartdata-engine/plugins/rest/tests/test_simple_integration.py::TestRestSimpleIntegration::test_rest_processor_lifecycle", "src/smartdata-engine/plugins/rest/tests/test_simple_integration.py::TestRestSimpleIntegration::test_rest_processor_methods", "src/smartdata-engine/plugins/rest/tests/test_simple_integration.py::TestRestSimpleIntegration::test_rest_processor_supported_types", "src/smartdata-engine/plugins/rest/tests/test_template_integration.py::TestRestTemplateIntegration::test_rest_plugin_registration", "src/smartdata-engine/plugins/rest/tests/test_template_integration.py::TestRestTemplateIntegration::test_template_rest_api_basic", "src/smartdata-engine/plugins/rest/tests/test_template_integration.py::TestRestTemplateIntegration::test_template_rest_caching", "src/smartdata-engine/plugins/rest/tests/test_template_integration.py::TestRestTemplateIntegration::test_template_rest_different_methods", "src/smartdata-engine/plugins/rest/tests/test_template_integration.py::TestRestTemplateIntegration::test_template_rest_error_handling", "src/smartdata-engine/plugins/rest/tests/test_template_integration.py::TestRestTemplateIntegration::test_template_rest_json_processing", "src/smartdata-engine/plugins/rest/tests/test_template_integration.py::TestRestTemplateIntegration::test_template_rest_multiple_apis", "src/smartdata-engine/plugins/rest/tests/test_template_integration.py::TestRestTemplateIntegration::test_template_rest_performance", "src/smartdata-engine/plugins/rest/tests/test_template_integration.py::TestRestTemplateIntegration::test_template_rest_smart_data_integration", "src/smartdata-engine/plugins/rest/tests/test_template_integration.py::TestRestTemplateIntegration::test_template_rest_with_headers", "src/smartdata-engine/plugins/rest/tests/test_template_integration.py::TestRestTemplateIntegration::test_template_rest_with_params", "src/smartdata-engine/plugins/stream/tests/test_stream_plugin_standards.py::TestStreamPluginStandards::test_async_support_compliance", "src/smartdata-engine/plugins/stream/tests/test_stream_plugin_standards.py::TestStreamPluginStandards::test_configuration_compliance", "src/smartdata-engine/plugins/stream/tests/test_stream_plugin_standards.py::TestStreamPluginStandards::test_documentation_compliance", "src/smartdata-engine/plugins/stream/tests/test_stream_plugin_standards.py::TestStreamPluginStandards::test_error_handling_compliance", "src/smartdata-engine/plugins/stream/tests/test_stream_plugin_standards.py::TestStreamPluginStandards::test_factory_pattern_compliance", "src/smartdata-engine/plugins/stream/tests/test_stream_plugin_standards.py::TestStreamPluginStandards::test_handler_detection_compliance", "src/smartdata-engine/plugins/stream/tests/test_stream_plugin_standards.py::TestStreamPluginStandards::test_handler_interface_compliance", "src/smartdata-engine/plugins/stream/tests/test_stream_plugin_standards.py::TestStreamPluginStandards::test_main_processor_compliance", "src/smartdata-engine/plugins/stream/tests/test_stream_plugin_standards.py::TestStreamPluginStandards::test_performance_features_compliance", "src/smartdata-engine/plugins/stream/tests/test_stream_plugin_standards.py::TestStreamPluginStandards::test_plugin_definitions_compliance", "src/smartdata-engine/plugins/stream/tests/test_stream_plugin_standards.py::TestStreamPluginStandards::test_plugin_metadata_compliance", "src/smartdata-engine/plugins/stream/tests/test_stream_plugin_standards.py::TestStreamPluginStandards::test_protocol_support_compliance", "src/smartdata-engine/plugins/stream/tests/test_stream_plugin_standards.py::TestStreamPluginStandards::test_smart_loader_compliance", "src/smartdata-engine/plugins/stream/tests/test_stream_plugin_standards.py::test_run_async_tests", "src/smartdata-engine/tests/integration/test_database_integration.py::TestDatabaseIntegration::test_adapter_metadata_integration", "src/smartdata-engine/tests/integration/test_database_integration.py::TestDatabaseIntegration::test_context_manager_with_database", "src/smartdata-engine/tests/integration/test_database_integration.py::TestDatabaseIntegration::test_database_error_handling", "src/smartdata-engine/tests/integration/test_database_integration.py::TestDatabaseIntegration::test_database_operations_through_proxy", "src/smartdata-engine/tests/integration/test_database_integration.py::TestDatabaseIntegration::test_multiple_database_connections", "src/smartdata-engine/tests/integration/test_database_integration.py::TestDatabaseIntegration::test_postgresql_adapter_registration", "src/smartdata-engine/tests/integration/test_database_integration.py::TestDatabaseIntegration::test_postgresql_connection_string_detection", "src/smartdata-engine/tests/integration/test_database_integration.py::TestDatabaseIntegration::test_postgresql_specific_operations", "src/smartdata-engine/tests/integration/test_database_integration.py::TestDatabaseIntegration::test_scope_cleanup_with_database", "src/smartdata-engine/tests/integration/test_database_integration.py::TestDatabaseIntegration::test_template_scope_database_registration", "src/smartdata-engine/tests/integration/test_database_integration.py::TestMultiDatabaseIntegration::test_mixed_database_connections", "src/smartdata-engine/tests/integration/test_database_integration.py::TestMultiDatabaseIntegration::test_multiple_database_types_registration", "src/smartdata-engine/tests/integration/test_database_integration.py::TestMySQLIntegration::test_mysql_adapter_registration", "src/smartdata-engine/tests/integration/test_database_integration.py::TestMySQLIntegration::test_mysql_connection_string_detection", "src/smartdata-engine/tests/integration/test_database_integration.py::TestMySQLIntegration::test_mysql_specific_operations", "src/smartdata-engine/tests/integration/test_database_integration.py::TestSQLiteIntegration::test_sqlite_adapter_registration", "src/smartdata-engine/tests/integration/test_database_integration.py::TestSQLiteIntegration::test_sqlite_connection_string_detection", "src/smartdata-engine/tests/integration/test_database_integration.py::TestSQLiteIntegration::test_sqlite_memory_database_integration", "src/smartdata-engine/tests/integration/test_database_integration.py::TestSQLiteIntegration::test_sqlite_specific_operations", "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncIntegrationPerformance::test_real_sqlite_performance", "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncPerformance::test_connection_pool_performance", "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncPerformance::test_memory_usage_efficiency", "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncPerformance::test_parallel_queries_performance", "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncPerformance::test_performance_monitoring", "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncPerformance::test_sequential_vs_parallel_comparison", "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncPerformance::test_single_query_performance", "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncPerformance::test_stream_query_performance", "src/smartdata-engine/tests/performance/test_async_performance.py::TestAsyncPerformance::test_template_scope_parallel_performance", "src/smartdata-engine/tests/test_ai_plugin_enhancement.py::TestAIPluginEnhancement::test_ai_caching", "src/smartdata-engine/tests/test_ai_plugin_enhancement.py::TestAIPluginEnhancement::test_ai_providers", "src/smartdata-engine/tests/test_ai_plugin_enhancement.py::TestAIPluginEnhancement::test_ai_service_factory", "src/smartdata-engine/tests/test_ai_plugin_enhancement.py::TestAIPluginEnhancement::test_async_processing", "src/smartdata-engine/tests/test_ai_plugin_enhancement.py::TestAIPluginEnhancement::test_batch_processing", "src/smartdata-engine/tests/test_ai_plugin_enhancement.py::TestAIPluginEnhancement::test_error_handling", "src/smartdata-engine/tests/test_ai_plugin_enhancement.py::TestAIPluginEnhancement::test_global_functions", "src/smartdata-engine/tests/test_ai_plugin_enhancement.py::TestAIPluginEnhancement::test_provider_configuration", "src/smartdata-engine/tests/test_ai_plugin_enhancement.py::TestAIPluginEnhancement::test_service_type_inference", "src/smartdata-engine/tests/test_ai_plugin_enhancement.py::TestAIPluginEnhancement::test_smart_ai_loader", "src/smartdata-engine/tests/test_ai_plugin_enhancement.py::TestAIPluginEnhancement::test_template_integration", "src/smartdata-engine/tests/test_async_sync_coordinator_fix.py::TestAsyncSyncCoordinatorFix::test_coordinator_sync_context", "src/smartdata-engine/tests/test_async_sync_coordinator_fix.py::TestAsyncSyncCoordinatorFix::test_file_loader_sync_usage", "src/smartdata-engine/tests/test_async_sync_coordinator_fix.py::TestAsyncSyncCoordinatorFix::test_mixed_sync_async_calls", "src/smartdata-engine/tests/test_basic_integration.py::TestBasicIntegration::test_advanced_syntax_support", "src/smartdata-engine/tests/test_basic_integration.py::TestBasicIntegration::test_api_connector", "src/smartdata-engine/tests/test_basic_integration.py::TestBasicIntegration::test_basic_template_rendering", "src/smartdata-engine/tests/test_basic_integration.py::TestBasicIntegration::test_chain_operations", "src/smartdata-engine/tests/test_basic_integration.py::TestBasicIntegration::test_component_info", "src/smartdata-engine/tests/test_basic_integration.py::TestBasicIntegration::test_database_connector", "src/smartdata-engine/tests/test_basic_integration.py::TestBasicIntegration::test_engine_creation", "src/smartdata-engine/tests/test_basic_integration.py::TestBasicIntegration::test_engine_stats", "src/smartdata-engine/tests/test_basic_integration.py::TestBasicIntegration::test_error_handling", "src/smartdata-engine/tests/test_basic_integration.py::TestBasicIntegration::test_file_connector", "src/smartdata-engine/tests/test_basic_integration.py::TestBasicIntegration::test_global_functions", "src/smartdata-engine/tests/test_basic_integration.py::TestBasicIntegration::test_lambda_functions", "src/smartdata-engine/tests/test_basic_integration.py::TestBasicIntegration::test_nested_data_access", "src/smartdata-engine/tests/test_basic_integration.py::TestBasicIntegration::test_performance_basic", "src/smartdata-engine/tests/test_basic_integration.py::TestBasicIntegration::test_safe_eval_function", "src/smartdata-engine/tests/test_basic_integration.py::TestBasicIntegration::test_smart_data_integration", "src/smartdata-engine/tests/test_component_status.py::TestComponentStatus::test_component_integration_status", "src/smartdata-engine/tests/test_database_plugin_optimization.py::TestDatabasePluginOptimization::test_backward_compatibility", "src/smartdata-engine/tests/test_database_plugin_optimization.py::TestDatabasePluginOptimization::test_chain_operations", "src/smartdata-engine/tests/test_database_plugin_optimization.py::TestDatabasePluginOptimization::test_connection_methods", "src/smartdata-engine/tests/test_database_plugin_optimization.py::TestDatabasePluginOptimization::test_database_connector_factory", "src/smartdata-engine/tests/test_database_plugin_optimization.py::TestDatabasePluginOptimization::test_database_specific_features", "src/smartdata-engine/tests/test_database_plugin_optimization.py::TestDatabasePluginOptimization::test_database_specific_queries", "src/smartdata-engine/tests/test_database_plugin_optimization.py::TestDatabasePluginOptimization::test_database_type_detection", "src/smartdata-engine/tests/test_database_plugin_optimization.py::TestDatabasePluginOptimization::test_error_handling", "src/smartdata-engine/tests/test_database_plugin_optimization.py::TestDatabasePluginOptimization::test_performance_improvement", "src/smartdata-engine/tests/test_database_plugin_optimization.py::TestDatabasePluginOptimization::test_specific_connector_creation", "src/smartdata-engine/tests/test_database_plugin_optimization.py::TestDatabasePluginOptimization::test_template_integration", "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_complex_data_operations", "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_data_type_detection_in_template", "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_enhanced_data_type_manager_integration", "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_error_handling", "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_html_string_handling", "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_integration_with_smart_data", "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_json_string_handling", "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_key_finding_in_template", "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_path_finding_in_template", "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_performance_with_large_data", "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_value_retrieval_in_template", "src/smartdata-engine/tests/test_enhanced_data_types_integration.py::TestEnhancedDataTypesIntegration::test_xml_string_handling", "src/smartdata-engine/tests/test_file_loader_enhancement.py::TestFileLoaderEnhancement::test_async_loading", "src/smartdata-engine/tests/test_file_loader_enhancement.py::TestFileLoaderEnhancement::test_batch_loading", "src/smartdata-engine/tests/test_file_loader_enhancement.py::TestFileLoaderEnhancement::test_error_handling", "src/smartdata-engine/tests/test_file_loader_enhancement.py::TestFileLoaderEnhancement::test_file_caching", "src/smartdata-engine/tests/test_file_loader_enhancement.py::TestFileLoaderEnhancement::test_file_format_factory", "src/smartdata-engine/tests/test_file_loader_enhancement.py::TestFileLoaderEnhancement::test_file_stats", "src/smartdata-engine/tests/test_file_loader_enhancement.py::TestFileLoaderEnhancement::test_format_handlers", "src/smartdata-engine/tests/test_file_loader_enhancement.py::TestFileLoaderEnhancement::test_format_specific_features", "src/smartdata-engine/tests/test_file_loader_enhancement.py::TestFileLoaderEnhancement::test_global_functions", "src/smartdata-engine/tests/test_file_loader_enhancement.py::TestFileLoaderEnhancement::test_smart_file_loader", "src/smartdata-engine/tests/test_file_loader_enhancement.py::TestFileLoaderEnhancement::test_template_integration", "src/smartdata-engine/tests/test_file_path_normalization.py::TestFilePathNormalization::test_path_normalization_method", "src/smartdata-engine/tests/test_jinja2_extensions.py::TestJinja2Extensions::test_advanced_set_syntax", "src/smartdata-engine/tests/test_jinja2_extensions.py::TestJinja2Extensions::test_advanced_statement_tags", "src/smartdata-engine/tests/test_jinja2_extensions.py::TestJinja2Extensions::test_basic_template_with_extensions", "src/smartdata-engine/tests/test_jinja2_extensions.py::TestJinja2Extensions::test_comprehension_extension_tags", "src/smartdata-engine/tests/test_jinja2_extensions.py::TestJinja2Extensions::test_enhanced_environment_creation", "src/smartdata-engine/tests/test_jinja2_extensions.py::TestJinja2Extensions::test_extension_error_handling", "src/smartdata-engine/tests/test_jinja2_extensions.py::TestJinja2Extensions::test_extension_help", "src/smartdata-engine/tests/test_jinja2_extensions.py::TestJinja2Extensions::test_extension_manager_configuration", "src/smartdata-engine/tests/test_jinja2_extensions.py::TestJinja2Extensions::test_extension_manager_initialization", "src/smartdata-engine/tests/test_jinja2_extensions.py::TestJinja2Extensions::test_extension_memory_usage", "src/smartdata-engine/tests/test_jinja2_extensions.py::TestJinja2Extensions::test_extension_performance", "src/smartdata-engine/tests/test_jinja2_extensions.py::TestJinja2Extensions::test_extension_stats", "src/smartdata-engine/tests/test_jinja2_extensions.py::TestJinja2Extensions::test_global_extension_manager", "src/smartdata-engine/tests/test_jinja2_extensions.py::TestJinja2Extensions::test_list_comprehension_syntax", "src/smartdata-engine/tests/test_jinja2_extensions.py::TestJinja2Extensions::test_multiline_extension_tags", "src/smartdata-engine/tests/test_jinja2_extensions.py::TestJinja2Extensions::test_template_validation", "src/smartdata-engine/tests/test_multiline_extension.py::test_comprehensive_real_scenario", "src/smartdata-engine/tests/test_multiline_extension.py::test_same_template_class", "src/smartdata-engine/tests/test_multiline_extension.py::test_same_template_complex_data", "src/smartdata-engine/tests/test_multiline_extension.py::test_same_template_function", "src/smartdata-engine/tests/test_smart_matcher_integration.py::TestSmartMatcherIntegration::test_database_connector_uses_matcher", "src/smartdata-engine/tests/test_smart_matcher_integration.py::TestSmartMatcherIntegration::test_smart_data_object_uses_matcher", "src/smartdata-engine/tests/test_template_integration.py::TestTemplateIntegration::test_advanced_syntax_support", "src/smartdata-engine/tests/test_template_integration.py::TestTemplateIntegration::test_api_simulation", "src/smartdata-engine/tests/test_template_integration.py::TestTemplateIntegration::test_backward_compatibility", "src/smartdata-engine/tests/test_template_integration.py::TestTemplateIntegration::test_basic_template_rendering", "src/smartdata-engine/tests/test_template_integration.py::TestTemplateIntegration::test_complex_data_processing", "src/smartdata-engine/tests/test_template_integration.py::TestTemplateIntegration::test_comprehensive_integration", "src/smartdata-engine/tests/test_template_integration.py::TestTemplateIntegration::test_database_simulation", "src/smartdata-engine/tests/test_template_integration.py::TestTemplateIntegration::test_error_handling", "src/smartdata-engine/tests/test_template_integration.py::TestTemplateIntegration::test_lambda_filter_function", "src/smartdata-engine/tests/test_template_integration.py::TestTemplateIntegration::test_lambda_map_function", "src/smartdata-engine/tests/test_template_integration.py::TestTemplateIntegration::test_lambda_sort_function", "src/smartdata-engine/tests/test_template_integration.py::TestTemplateIntegration::test_smart_data_object_integration", "src/smartdata-engine/tests/test_template_integration.py::TestTemplateIntegration::test_template_engine_stats", "src/smartdata-engine/tests/test_unified_plugin_standards.py::TestUnifiedPluginStandards::test_plugin_categories", "src/smartdata-engine/tests/test_unified_plugin_standards.py::TestUnifiedPluginStandards::test_plugin_interface_compliance", "src/smartdata-engine/tests/test_unified_plugin_standards.py::test_run_async_tests", "src/smartdata-engine/tests/test_v2_improvements.py::TestV2Improvements::test_backward_compatibility", "src/smartdata-engine/tests/test_v2_improvements.py::TestV2Improvements::test_deduplication_functionality", "src/smartdata-engine/tests/test_v2_improvements.py::TestV2Improvements::test_enhanced_path_result", "src/smartdata-engine/tests/test_v2_improvements.py::TestV2Improvements::test_existence_check_functionality", "src/smartdata-engine/tests/test_v2_improvements.py::TestV2Improvements::test_node_existence_type_enum", "src/smartdata-engine/tests/test_v2_improvements.py::TestV2Improvements::test_performance_with_v2_improvements", "src/smartdata-engine/tests/test_v2_improvements.py::TestV2Improvements::test_plugin_registry_functionality", "src/smartdata-engine/tests/test_v2_improvements.py::TestV2Improvements::test_template_integration_with_v2_features", "src/smartdata-engine/tests/test_v2_improvements.py::TestV2Improvements::test_type_preserving_mixin", "src/smartdata-engine/tests/unit/test_data_proxy.py::TestDataProxy::test_datetime_object_processing", "src/smartdata-engine/tests/unit/test_data_proxy.py::TestDataProxy::test_execution_time_measurement", "src/smartdata-engine/tests/unit/test_data_proxy.py::TestDataProxy::test_lifecycle_management", "src/smartdata-engine/tests/unit/test_data_proxy.py::TestDataProxy::test_multiple_operations", "src/smartdata-engine/tests/unit/test_data_proxy.py::TestDataProxy::test_nested_datetime_processing", "src/smartdata-engine/tests/unit/test_data_proxy.py::TestDataProxy::test_no_lifecycle_manager", "src/smartdata-engine/tests/unit/test_data_proxy.py::TestDataProxy::test_operation_logging", "src/smartdata-engine/tests/unit/test_data_proxy.py::TestDataProxy::test_operation_wrapper_error", "src/smartdata-engine/tests/unit/test_data_proxy.py::TestDataProxy::test_operation_wrapper_success", "src/smartdata-engine/tests/unit/test_data_proxy.py::TestDataProxy::test_proxy_initialization", "src/smartdata-engine/tests/unit/test_data_proxy.py::TestDataProxy::test_proxy_metadata", "src/smartdata-engine/tests/unit/test_data_proxy.py::TestDataProxy::test_source_attribute_access", "src/smartdata-engine/tests/unit/test_data_proxy.py::TestDataProxy::test_unsupported_operation", "src/smartdata-engine/tests/unit/test_data_registry.py::TestDataRegistry::test_builtin_detectors", "src/smartdata-engine/tests/unit/test_data_registry.py::TestDataRegistry::test_clear_adapters", "src/smartdata-engine/tests/unit/test_data_registry.py::TestDataRegistry::test_custom_type_detector", "src/smartdata-engine/tests/unit/test_data_registry.py::TestDataRegistry::test_database_connection_detection", "src/smartdata-engine/tests/unit/test_data_registry.py::TestDataRegistry::test_detector_error_handling", "src/smartdata-engine/tests/unit/test_data_registry.py::TestDataRegistry::test_file_like_detection", "src/smartdata-engine/tests/unit/test_data_registry.py::TestDataRegistry::test_get_adapter_info", "src/smartdata-engine/tests/unit/test_data_registry.py::TestDataRegistry::test_register_adapter_override", "src/smartdata-engine/tests/unit/test_data_registry.py::TestDataRegistry::test_register_broken_adapter", "src/smartdata-engine/tests/unit/test_data_registry.py::TestDataRegistry::test_register_multiple_adapters", "src/smartdata-engine/tests/unit/test_data_registry.py::TestDataRegistry::test_register_single_adapter", "src/smartdata-engine/tests/unit/test_data_registry.py::TestDataRegistry::test_registry_initialization", "src/smartdata-engine/tests/unit/test_data_registry.py::TestDataRegistry::test_thread_safety", "src/smartdata-engine/tests/unit/test_data_registry.py::TestDataRegistry::test_unregister_adapter", "src/smartdata-engine/tests/unit/test_data_registry.py::TestDataRegistry::test_unsupported_data_type", "src/smartdata-engine/tests/unit/test_data_result.py::TestDataResult::test_created_at_timestamp", "src/smartdata-engine/tests/unit/test_data_result.py::TestDataResult::test_dynamic_attribute_access_from_data_dict", "src/smartdata-engine/tests/unit/test_data_result.py::TestDataResult::test_dynamic_attribute_access_from_metadata", "src/smartdata-engine/tests/unit/test_data_result.py::TestDataResult::test_dynamic_attribute_access_from_single_item_list", "src/smartdata-engine/tests/unit/test_data_result.py::TestDataResult::test_dynamic_attribute_access_not_found", "src/smartdata-engine/tests/unit/test_data_result.py::TestDataResult::test_dynamic_attribute_access_priority", "src/smartdata-engine/tests/unit/test_data_result.py::TestDataResult::test_error_handling_in_template_context", "src/smartdata-engine/tests/unit/test_data_result.py::TestDataResult::test_error_result_creation", "src/smartdata-engine/tests/unit/test_data_result.py::TestDataResult::test_from_dict_creation", "src/smartdata-engine/tests/unit/test_data_result.py::TestDataResult::test_get_method_with_default", "src/smartdata-engine/tests/unit/test_data_result.py::TestDataResult::test_post_init_processing", "src/smartdata-engine/tests/unit/test_data_result.py::TestDataResult::test_success_result_creation", "src/smartdata-engine/tests/unit/test_data_result.py::TestDataResult::test_template_friendly_access_patterns", "src/smartdata-engine/tests/unit/test_data_result.py::TestDataResult::test_to_dict_conversion", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestDatabaseAdapterBase::test_adapter_initialization", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestDatabaseAdapterBase::test_batch_operation", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestDatabaseAdapterBase::test_can_handle_connection_object", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestDatabaseAdapterBase::test_can_handle_connection_string", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestDatabaseAdapterBase::test_connection_string_parsing", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestDatabaseAdapterBase::test_execute_operation", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestDatabaseAdapterBase::test_get_metadata", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestDatabaseAdapterBase::test_get_operations", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestDatabaseAdapterBase::test_query_operation", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestDatabaseAdapterBase::test_sql_preprocessing", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestDatabaseAdapterBase::test_sql_type_detection", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestDatabaseAdapterBase::test_supported_types", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestDatabaseAdapterBase::test_transaction_operation", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestMySQLAdapter::test_connection_string_detection", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestMySQLAdapter::test_default_port", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestMySQLAdapter::test_mysql_specific_operations", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestMySQLAdapter::test_pymysql_not_available", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestMySQLAdapter::test_supported_types", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestPostgreSQLAdapter::test_connection_string_detection", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestPostgreSQLAdapter::test_create_connection_with_info", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestPostgreSQLAdapter::test_create_connection_with_string", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestPostgreSQLAdapter::test_default_port", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestPostgreSQLAdapter::test_postgresql_specific_operations", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestPostgreSQLAdapter::test_psycopg2_not_available", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestPostgreSQLAdapter::test_supported_types", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestSQLiteAdapter::test_connection_string_detection", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestSQLiteAdapter::test_database_info", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestSQLiteAdapter::test_default_port", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestSQLiteAdapter::test_memory_database_creation", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestSQLiteAdapter::test_sqlite_specific_operations", "src/smartdata-engine/tests/unit/test_database_adapters.py::TestSQLiteAdapter::test_supported_types", "src/smartdata-engine/tests/unit/test_lifecycle_manager.py::TestLifecycleManager::test_callback_error_handling", "src/smartdata-engine/tests/unit/test_lifecycle_manager.py::TestLifecycleManager::test_cleanup_all_resources", "src/smartdata-engine/tests/unit/test_lifecycle_manager.py::TestLifecycleManager::test_cleanup_nonexistent_resource", "src/smartdata-engine/tests/unit/test_lifecycle_manager.py::TestLifecycleManager::test_cleanup_single_resource", "src/smartdata-engine/tests/unit/test_lifecycle_manager.py::TestLifecycleManager::test_manager_initialization", "src/smartdata-engine/tests/unit/test_lifecycle_manager.py::TestLifecycleManager::test_multiple_cleanup_callbacks", "src/smartdata-engine/tests/unit/test_lifecycle_manager.py::TestLifecycleManager::test_register_multiple_resources", "src/smartdata-engine/tests/unit/test_lifecycle_manager.py::TestLifecycleManager::test_register_single_resource", "src/smartdata-engine/tests/unit/test_lifecycle_manager.py::TestLifecycleManager::test_register_with_cleanup_callback", "src/smartdata-engine/tests/unit/test_lifecycle_manager.py::TestLifecycleManager::test_resource_count_accuracy", "src/smartdata-engine/tests/unit/test_lifecycle_manager.py::TestLifecycleManager::test_thread_safety", "src/smartdata-engine/tests/unit/test_lifecycle_manager.py::TestLifecycleManager::test_weak_reference_cleanup", "src/smartdata-engine/tests/unit/test_template_scope.py::TestTemplateScope::test_cleanup_scope", "src/smartdata-engine/tests/unit/test_template_scope.py::TestTemplateScope::test_context_manager", "src/smartdata-engine/tests/unit/test_template_scope.py::TestTemplateScope::test_get_context_data", "src/smartdata-engine/tests/unit/test_template_scope.py::TestTemplateScope::test_get_data_proxy", "src/smartdata-engine/tests/unit/test_template_scope.py::TestTemplateScope::test_get_scope_id", "src/smartdata-engine/tests/unit/test_template_scope.py::TestTemplateScope::test_get_scope_info", "src/smartdata-engine/tests/unit/test_template_scope.py::TestTemplateScope::test_list_data_sources", "src/smartdata-engine/tests/unit/test_template_scope.py::TestTemplateScope::test_register_multiple_data_sources", "src/smartdata-engine/tests/unit/test_template_scope.py::TestTemplateScope::test_register_single_data_source", "src/smartdata-engine/tests/unit/test_template_scope.py::TestTemplateScope::test_remove_data_source", "src/smartdata-engine/tests/unit/test_template_scope.py::TestTemplateScope::test_scope_initialization", "src/smartdata-engine/tests/unit/test_template_scope.py::TestTemplateScope::test_scope_isolation", "src/smartdata-engine/tests/unit/test_template_scope.py::TestTemplateScope::test_thread_safety", "src/smartdata-engine/tests/unit/test_template_scope.py::TestTemplateScope::test_unsupported_data_type"]