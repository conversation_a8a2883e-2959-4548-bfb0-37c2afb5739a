# SmartData插件开发指南

## 📖 概述

SmartData插件系统提供了一个统一、可扩展的框架，用于集成各种数据处理能力。本指南将帮助您开发高质量的企业级插件。

## 🏗️ 插件架构

### 核心组件

1. **BaseProcessor** - 所有处理器的基类
2. **SmartDataObject** - 统一的数据对象
3. **PluginRegistry** - 插件注册管理器
4. **SmartDataLoader** - 智能数据加载器

### 插件类型

- **Processor插件** - 数据处理器
- **Connector插件** - 连接器
- **Transformer插件** - 数据转换器
- **Validator插件** - 数据验证器

## 🚀 快速开始

### 1. 创建插件目录结构

```
plugins/
└── your_plugin/
    ├── __init__.py
    ├── your_processor.py
    ├── connectors.py
    ├── utils.py
    └── tests/
        └── test_your_plugin.py
```

### 2. 定义插件信息

在 `__init__.py` 中定义插件信息：

```python
# 插件定义 - 按照统一标准
PLUGIN_DEFINITIONS = [
    {
        'id': 'your_plugin_processor',
        'name': '您的插件处理器',
        'description': '插件功能描述',
        'version': '1.0.0',
        'type': 'processor',
        'category': 'your_category',
        'priority': 50,
        'class_name': 'YourProcessor',
        'module_file': 'your_processor',
        'auto_load': True,
        'enabled': True,
        'capabilities': [
            'capability1',
            'capability2'
        ],
        'supported_protocols': ['protocol1', 'protocol2'],
        'dependencies': [],
        'optional_dependencies': ['package1', 'package2'],
        'author': 'Your Name',
        'license': 'MIT',
        'tags': ['tag1', 'tag2']
    }
]
```

### 3. 实现处理器

```python
from core.base_processor import BaseProcessor
from core.smart_data_object import SmartDataObject

class YourProcessor(BaseProcessor):
    def __init__(self):
        super().__init__()
        self.processor_id = "your_plugin_processor"
        self.version = "1.0.0"
        self.priority = 50
    
    def can_process(self, data: Any) -> bool:
        """检查是否可以处理数据"""
        # 实现您的逻辑
        return True
    
    async def process(self, data: Any, options: Dict[str, Any] = None) -> SmartDataObject:
        """处理数据"""
        try:
            # 实现您的处理逻辑
            result = self._process_data(data, options)
            
            return SmartDataObject({
                'success': True,
                'data': result,
                'processor': self.processor_id
            })
        except Exception as e:
            return SmartDataObject({
                'success': False,
                'error': str(e),
                'processor': self.processor_id
            })
    
    def get_processor_info(self) -> Dict[str, Any]:
        """获取处理器信息"""
        return {
            'id': self.processor_id,
            'name': '您的插件处理器',
            'version': self.version,
            'description': '插件功能描述',
            'capabilities': ['capability1', 'capability2'],
            'priority': self.priority
        }
```

## 📋 插件标准

### 必需字段

- `id` - 唯一标识符
- `name` - 插件名称
- `description` - 功能描述
- `version` - 版本号
- `type` - 插件类型
- `category` - 分类
- `class_name` - 处理器类名
- `module_file` - 模块文件名

### 推荐字段

- `priority` - 优先级（0-100）
- `capabilities` - 能力列表
- `supported_protocols` - 支持的协议
- `dependencies` - 必需依赖
- `optional_dependencies` - 可选依赖
- `author` - 作者
- `license` - 许可证
- `tags` - 标签

## 🎯 最佳实践

### 1. 错误处理

```python
async def process(self, data: Any, options: Dict[str, Any] = None) -> SmartDataObject:
    try:
        # 验证输入
        if not self._validate_input(data):
            return SmartDataObject({
                'success': False,
                'error': '输入数据无效',
                'processor': self.processor_id
            })
        
        # 处理数据
        result = await self._process_data(data, options)
        
        return SmartDataObject({
            'success': True,
            'data': result,
            'processor': self.processor_id
        })
        
    except Exception as e:
        self.logger.error(f"处理失败: {e}")
        return SmartDataObject({
            'success': False,
            'error': str(e),
            'processor': self.processor_id
        })
```

### 2. 异步支持

```python
async def process(self, data: Any, options: Dict[str, Any] = None) -> SmartDataObject:
    """异步处理数据"""
    # 使用异步操作
    async with self._get_connection() as conn:
        result = await conn.execute(data)
    
    return SmartDataObject({
        'success': True,
        'data': result,
        'processor': self.processor_id
    })
```

### 3. 配置管理

```python
class YourProcessor(BaseProcessor):
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__()
        self.config = config or {}
        self.default_config = {
            'timeout': 30.0,
            'retries': 3,
            'batch_size': 100
        }
        
    def _get_config_value(self, key: str, default: Any = None):
        """获取配置值"""
        return self.config.get(key, self.default_config.get(key, default))
```

### 4. 缓存支持

```python
class YourProcessor(BaseProcessor):
    def __init__(self):
        super().__init__()
        self._cache = {}
        self.cache_enabled = True
        self.cache_ttl = 300  # 5分钟
    
    async def process(self, data: Any, options: Dict[str, Any] = None) -> SmartDataObject:
        # 检查缓存
        if self.cache_enabled:
            cache_key = self._generate_cache_key(data, options)
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                return cached_result
        
        # 处理数据
        result = await self._process_data(data, options)
        
        # 缓存结果
        if self.cache_enabled:
            self._cache_result(cache_key, result)
        
        return result
```

## 🔧 企业级功能

### 1. 连接池

```python
from .connection_pool import ConnectionPool

class YourProcessor(BaseProcessor):
    def __init__(self):
        super().__init__()
        self.connection_pool = ConnectionPool(
            max_connections=10,
            timeout=30.0
        )
    
    async def _get_connection(self):
        """获取连接"""
        return await self.connection_pool.get_connection()
```

### 2. 重试机制

```python
import asyncio
from functools import wraps

def retry(max_attempts=3, delay=1.0):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:
                        raise
                    await asyncio.sleep(delay * (2 ** attempt))
            return None
        return wrapper
    return decorator

class YourProcessor(BaseProcessor):
    @retry(max_attempts=3, delay=1.0)
    async def _process_with_retry(self, data):
        """带重试的处理"""
        return await self._process_data(data)
```

### 3. 性能监控

```python
import time
from dataclasses import dataclass

@dataclass
class ProcessorStats:
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_time: float = 0.0
    
    @property
    def success_rate(self) -> float:
        return self.successful_requests / self.total_requests if self.total_requests > 0 else 0.0
    
    @property
    def average_time(self) -> float:
        return self.total_time / self.total_requests if self.total_requests > 0 else 0.0

class YourProcessor(BaseProcessor):
    def __init__(self):
        super().__init__()
        self.stats = ProcessorStats()
    
    async def process(self, data: Any, options: Dict[str, Any] = None) -> SmartDataObject:
        start_time = time.time()
        
        try:
            result = await self._process_data(data, options)
            
            # 更新统计
            self.stats.total_requests += 1
            self.stats.successful_requests += 1
            self.stats.total_time += time.time() - start_time
            
            return result
            
        except Exception as e:
            self.stats.total_requests += 1
            self.stats.failed_requests += 1
            self.stats.total_time += time.time() - start_time
            raise
```

## 🧪 测试

### 单元测试示例

```python
import pytest
from your_plugin.your_processor import YourProcessor

class TestYourProcessor:
    def setup_method(self):
        self.processor = YourProcessor()
    
    def test_can_process(self):
        """测试can_process方法"""
        assert self.processor.can_process({'test': 'data'})
        assert not self.processor.can_process('invalid')
    
    @pytest.mark.asyncio
    async def test_process_success(self):
        """测试成功处理"""
        data = {'test': 'data'}
        result = await self.processor.process(data)
        
        assert result.success
        assert result.data is not None
        assert result.processor == self.processor.processor_id
    
    @pytest.mark.asyncio
    async def test_process_error(self):
        """测试错误处理"""
        data = None  # 无效数据
        result = await self.processor.process(data)
        
        assert not result.success
        assert result.error is not None
```

## 📚 参考示例

查看现有插件实现：

- **Remote File插件** - 多协议文件处理
- **Kafka插件** - 消息队列处理
- **Database插件** - 数据库操作
- **Email插件** - 邮件处理

## 🎯 发布清单

发布插件前请检查：

- [ ] 插件定义完整
- [ ] 处理器实现正确
- [ ] 错误处理完善
- [ ] 单元测试覆盖
- [ ] 文档完整
- [ ] 性能测试通过
- [ ] 安全审查完成

## 🤝 贡献

欢迎贡献您的插件！请遵循：

1. 代码规范
2. 测试要求
3. 文档标准
4. 安全准则

---

**Happy Coding! 🚀**
