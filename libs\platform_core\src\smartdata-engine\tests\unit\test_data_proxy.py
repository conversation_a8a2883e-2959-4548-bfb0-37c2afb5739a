"""
DataProxy数据代理层测试

测试DataProxy类的所有功能，确保数据代理的正确性和操作包装
"""

import pytest
import time
from typing import Dict, Any, Callable
from unittest.mock import Mock, MagicMock

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from core.enterprise_data_architecture import (
    DataResult, IDataAdapter, ILifecycleManager, DataProxy,
    DataArchitectureError
)


class MockDataAdapter:
    """模拟数据适配器"""
    
    def __init__(self):
        self.operations = {
            'query': self._mock_query,
            'execute': self._mock_execute,
            'connect': self._mock_connect
        }
    
    def supported_types(self):
        return ['mock_database']
    
    def can_handle(self, data_source):
        return True
    
    def create_proxy(self, data_source, lifecycle_manager):
        return DataProxy(data_source, self, lifecycle_manager)
    
    def get_operations(self):
        return self.operations
    
    def get_metadata(self):
        return {'adapter_type': 'MockAdapter', 'version': '1.0.0'}
    
    def _mock_query(self, source, sql: str):
        """模拟查询操作"""
        if 'error' in sql.lower():
            raise Exception("Mock query error")
        
        return [
            {'id': 1, 'name': 'Alice', 'email': '<EMAIL>'},
            {'id': 2, 'name': 'Bob', 'email': '<EMAIL>'}
        ]
    
    def _mock_execute(self, source, command: str):
        """模拟执行操作"""
        if 'fail' in command.lower():
            raise Exception("Mock execute error")
        
        return {'affected_rows': 1, 'status': 'success'}
    
    def _mock_connect(self, source):
        """模拟连接操作"""
        return {'status': 'connected', 'connection_id': 'mock_123'}


class MockLifecycleManager:
    """模拟生命周期管理器"""
    
    def __init__(self):
        self.resources = {}
        self.cleanup_callbacks = {}
    
    def register_resource(self, resource_id: str, resource: Any, cleanup_callback: Callable = None):
        self.resources[resource_id] = resource
        if cleanup_callback:
            self.cleanup_callbacks[resource_id] = cleanup_callback
    
    def cleanup_resource(self, resource_id: str):
        if resource_id in self.resources:
            if resource_id in self.cleanup_callbacks:
                self.cleanup_callbacks[resource_id]()
            del self.resources[resource_id]
            return True
        return False
    
    def cleanup_all(self):
        count = len(self.resources)
        for resource_id in list(self.resources.keys()):
            self.cleanup_resource(resource_id)
        return count
    
    def get_resource_count(self):
        return len(self.resources)


class TestDataProxy:
    """DataProxy类测试套件"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.mock_source = {'type': 'mock_database', 'host': 'localhost'}
        self.mock_adapter = MockDataAdapter()
        self.mock_lifecycle = MockLifecycleManager()
        self.proxy = DataProxy(self.mock_source, self.mock_adapter, self.mock_lifecycle)
    
    def test_proxy_initialization(self):
        """测试代理对象初始化"""
        assert self.proxy.source == self.mock_source
        assert self.proxy.adapter == self.mock_adapter
        assert self.proxy.lifecycle_manager == self.mock_lifecycle
        assert self.proxy.operations == self.mock_adapter.get_operations()
        assert self.proxy.proxy_id is not None
        assert len(self.proxy.proxy_id) > 0
        
        # 验证资源已注册到生命周期管理器
        assert self.mock_lifecycle.get_resource_count() == 1
        assert self.proxy.proxy_id in self.mock_lifecycle.resources
    
    def test_operation_wrapper_success(self):
        """测试操作包装器成功场景"""
        # 执行查询操作
        result = self.proxy.query("SELECT * FROM users")
        
        # 验证返回的是DataResult对象
        assert isinstance(result, DataResult)
        assert result.success is True
        assert result.data is not None
        assert len(result.data) == 2
        assert result.data[0]['name'] == 'Alice'
        assert result.execution_time > 0
        assert result.operation == 'query'
        assert result.source_type == 'dict'  # type(self.mock_source).__name__
        assert result.adapter_type == 'MockDataAdapter'
    
    def test_operation_wrapper_error(self):
        """测试操作包装器错误场景"""
        # 执行会出错的查询
        result = self.proxy.query("SELECT error FROM table")
        
        # 验证错误被正确包装
        assert isinstance(result, DataResult)
        assert result.success is False
        assert result.error == "Mock query error"
        assert result.data is None
        assert result.execution_time > 0
        assert result.operation == 'query'
    
    def test_multiple_operations(self):
        """测试多个操作"""
        # 查询操作
        query_result = self.proxy.query("SELECT * FROM users")
        assert query_result.success is True
        assert len(query_result.data) == 2
        
        # 执行操作
        execute_result = self.proxy.execute("INSERT INTO users VALUES (3, 'Charlie')")
        assert execute_result.success is True
        assert execute_result.data['affected_rows'] == 1
        
        # 连接操作
        connect_result = self.proxy.connect()
        assert connect_result.success is True
        assert connect_result.data['status'] == 'connected'
    
    def test_datetime_object_processing(self):
        """测试datetime对象处理"""
        from datetime import datetime
        
        # 创建包含datetime对象的适配器
        class DateTimeAdapter(MockDataAdapter):
            def _mock_query(self, source, sql: str):
                return [
                    {'id': 1, 'created_at': datetime.now(), 'name': 'test'}
                ]
        
        datetime_adapter = DateTimeAdapter()
        datetime_proxy = DataProxy(self.mock_source, datetime_adapter, self.mock_lifecycle)
        
        result = datetime_proxy.query("SELECT * FROM table")
        
        # 验证datetime对象被转换为字符串
        assert result.success is True
        assert isinstance(result.data[0]['created_at'], str)
        assert 'T' in result.data[0]['created_at']  # ISO格式包含T
    
    def test_nested_datetime_processing(self):
        """测试嵌套结构中的datetime处理"""
        from datetime import datetime
        
        class NestedDateTimeAdapter(MockDataAdapter):
            def _mock_query(self, source, sql: str):
                return {
                    'users': [
                        {'id': 1, 'created_at': datetime.now()},
                        {'id': 2, 'updated_at': datetime.now()}
                    ],
                    'metadata': {
                        'query_time': datetime.now()
                    }
                }
        
        nested_adapter = NestedDateTimeAdapter()
        nested_proxy = DataProxy(self.mock_source, nested_adapter, self.mock_lifecycle)
        
        result = nested_proxy.query("SELECT * FROM table")
        
        # 验证嵌套结构中的datetime都被处理
        assert result.success is True
        assert isinstance(result.data['users'][0]['created_at'], str)
        assert isinstance(result.data['users'][1]['updated_at'], str)
        assert isinstance(result.data['metadata']['query_time'], str)
    
    def test_source_attribute_access(self):
        """测试从源对象访问属性"""
        # 创建有属性的源对象
        class MockSource:
            def __init__(self):
                self.host = 'localhost'
                self.port = 5432
            
            def get_info(self):
                return {'host': self.host, 'port': self.port}
        
        source_with_attrs = MockSource()
        proxy_with_attrs = DataProxy(source_with_attrs, self.mock_adapter, self.mock_lifecycle)
        
        # 访问源对象的属性
        assert proxy_with_attrs.host == 'localhost'
        assert proxy_with_attrs.port == 5432
        
        # 调用源对象的方法
        info_result = proxy_with_attrs.get_info()
        assert isinstance(info_result, DataResult)
        assert info_result.success is True
        assert info_result.data['host'] == 'localhost'
    
    def test_unsupported_operation(self):
        """测试不支持的操作"""
        with pytest.raises(AttributeError) as exc_info:
            _ = self.proxy.unsupported_operation
        
        assert "操作 'unsupported_operation' 不支持" in str(exc_info.value)
    
    def test_lifecycle_management(self):
        """测试生命周期管理"""
        proxy_id = self.proxy.proxy_id
        
        # 验证资源已注册
        assert self.mock_lifecycle.get_resource_count() == 1
        assert proxy_id in self.mock_lifecycle.resources
        
        # 手动清理资源
        success = self.mock_lifecycle.cleanup_resource(proxy_id)
        assert success is True
        assert self.mock_lifecycle.get_resource_count() == 0
    
    def test_operation_logging(self):
        """测试操作日志记录"""
        # 这个测试验证日志记录功能
        # 由于日志是通过logger记录的，我们主要验证操作能正常执行
        result = self.proxy.query("SELECT * FROM users")
        assert result.success is True
        
        # 验证日志相关的属性设置正确
        assert hasattr(self.proxy, 'logger')
        assert self.proxy.logger.name == 'DataProxy'
    
    def test_execution_time_measurement(self):
        """测试执行时间测量"""
        # 创建一个慢操作的适配器
        class SlowAdapter(MockDataAdapter):
            def _mock_query(self, source, sql: str):
                time.sleep(0.01)  # 睡眠10ms
                return [{'result': 'slow_query'}]
        
        slow_adapter = SlowAdapter()
        slow_proxy = DataProxy(self.mock_source, slow_adapter, self.mock_lifecycle)
        
        result = slow_proxy.query("SELECT * FROM table")
        
        # 验证执行时间被正确测量（应该大于10ms）
        assert result.execution_time >= 10.0  # 以毫秒为单位
        assert result.success is True
    
    def test_proxy_metadata(self):
        """测试代理元数据"""
        result = self.proxy.query("SELECT * FROM users")
        
        # 验证元数据包含代理信息
        assert 'proxy_id' in result.metadata
        assert result.metadata['proxy_id'] == self.proxy.proxy_id
        assert 'adapter_type' in result.metadata
        assert result.metadata['adapter_type'] == 'MockDataAdapter'
    
    def test_no_lifecycle_manager(self):
        """测试没有生命周期管理器的情况"""
        proxy_without_lifecycle = DataProxy(self.mock_source, self.mock_adapter, None)
        
        # 应该能正常工作，只是没有生命周期管理
        result = proxy_without_lifecycle.query("SELECT * FROM users")
        assert result.success is True
        assert result.data is not None


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, '-v'])
