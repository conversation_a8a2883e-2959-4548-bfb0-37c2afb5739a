"""
异步数据代理实现

提供高性能的异步数据操作代理，支持流式查询和并行处理
"""

import asyncio
import time
import uuid
import logging
from typing import Any, Dict, List, Optional, AsyncIterator, Callable, Awaitable

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../'))

from core.async_interfaces import IAsyncDataAdapter, IAsyncLifecycleManager, AsyncOperationResult, AsyncPerformanceMonitor
from core.enterprise_data_architecture import DataResult


class AsyncDataProxy:
    """
    异步数据代理对象
    
    提供高性能的异步数据操作，支持：
    - 异步查询和执行
    - 流式数据处理
    - 连接池管理
    - 性能监控
    - 自动错误处理
    """
    
    def __init__(self, source: Any, adapter: IAsyncDataAdapter, lifecycle_manager: Optional[IAsyncLifecycleManager] = None):
        self.original_source = source
        self.adapter = adapter
        self.lifecycle_manager = lifecycle_manager
        self.proxy_id = str(uuid.uuid4())
        self.logger = logging.getLogger(self.__class__.__name__)
        self.performance_monitor = AsyncPerformanceMonitor()
        
        # 异步连接管理
        self.connection = None
        self.connection_pool = None
        self._connection_lock = asyncio.Lock()
        self._is_closed = False
        
        # 操作统计
        self.operation_count = 0
        self.total_execution_time = 0.0
        
        # 注册到生命周期管理器
        if lifecycle_manager:
            asyncio.create_task(lifecycle_manager.register_async_resource(self.proxy_id, self))
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_connection()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def _ensure_connection(self):
        """确保连接可用"""
        if self._is_closed:
            raise RuntimeError("AsyncDataProxy已关闭")
        
        async with self._connection_lock:
            if self.connection is None:
                try:
                    self.connection = await self.adapter.create_async_connection(self.original_source)
                    self.logger.debug(f"创建异步连接成功: {self.proxy_id}")
                except Exception as e:
                    self.logger.error(f"创建异步连接失败: {e}")
                    raise
    
    async def _ensure_connection_pool(self, min_size: int = 5, max_size: int = 20):
        """确保连接池可用"""
        if self._is_closed:
            raise RuntimeError("AsyncDataProxy已关闭")
        
        async with self._connection_lock:
            if self.connection_pool is None:
                try:
                    self.connection_pool = await self.adapter.create_async_pool(
                        self.original_source, min_size, max_size
                    )
                    self.logger.debug(f"创建异步连接池成功: {self.proxy_id}")
                except Exception as e:
                    self.logger.error(f"创建异步连接池失败: {e}")
                    raise
    
    async def query(self, sql: str, params: Dict = None, use_pool: bool = True) -> DataResult:
        """
        异步查询操作
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            use_pool: 是否使用连接池
            
        Returns:
            查询结果
        """
        operation_name = 'async_query'
        
        async def _execute_query():
            if use_pool:
                await self._ensure_connection_pool()
                # 使用连接池
                async with self.connection_pool.acquire() as conn:
                    return await self.adapter.async_query(conn, sql, params)
            else:
                await self._ensure_connection()
                return await self.adapter.async_query(self.connection, sql, params)
        
        try:
            result = await self.performance_monitor.monitor_operation(operation_name, _execute_query())
            
            # 更新统计信息
            self.operation_count += 1
            
            return DataResult(
                success=True,
                data=result,
                operation=operation_name,
                source_type=type(self.adapter).__name__,
                adapter_type=type(self.adapter).__name__,
                execution_time=self.performance_monitor.operation_stats.get(operation_name, {}).get('avg_time', 0)
            )
            
        except Exception as e:
            self.logger.error(f"异步查询失败: {e}")
            return DataResult(
                success=False,
                error=str(e),
                operation=operation_name,
                source_type=type(self.adapter).__name__,
                adapter_type=type(self.adapter).__name__
            )
    
    async def execute(self, sql: str, params: Dict = None, use_pool: bool = True) -> DataResult:
        """
        异步执行操作
        
        Args:
            sql: SQL执行语句
            params: 执行参数
            use_pool: 是否使用连接池
            
        Returns:
            执行结果
        """
        operation_name = 'async_execute'
        
        async def _execute_command():
            if use_pool:
                await self._ensure_connection_pool()
                async with self.connection_pool.acquire() as conn:
                    return await self.adapter.async_execute(conn, sql, params)
            else:
                await self._ensure_connection()
                return await self.adapter.async_execute(self.connection, sql, params)
        
        try:
            result = await self.performance_monitor.monitor_operation(operation_name, _execute_command())
            
            self.operation_count += 1
            
            return DataResult(
                success=True,
                data=result,
                operation=operation_name,
                source_type=type(self.adapter).__name__,
                adapter_type=type(self.adapter).__name__,
                affected_rows=result,
                execution_time=self.performance_monitor.operation_stats.get(operation_name, {}).get('avg_time', 0)
            )
            
        except Exception as e:
            self.logger.error(f"异步执行失败: {e}")
            return DataResult(
                success=False,
                error=str(e),
                operation=operation_name,
                source_type=type(self.adapter).__name__,
                adapter_type=type(self.adapter).__name__
            )
    
    async def transaction(self, operations: List[Dict], use_pool: bool = True) -> DataResult:
        """
        异步事务操作
        
        Args:
            operations: 事务操作列表
            use_pool: 是否使用连接池
            
        Returns:
            事务结果
        """
        operation_name = 'async_transaction'
        
        async def _execute_transaction():
            if use_pool:
                await self._ensure_connection_pool()
                async with self.connection_pool.acquire() as conn:
                    return await self.adapter.async_transaction(conn, operations)
            else:
                await self._ensure_connection()
                return await self.adapter.async_transaction(self.connection, operations)
        
        try:
            result = await self.performance_monitor.monitor_operation(operation_name, _execute_transaction())
            
            self.operation_count += 1
            
            return DataResult(
                success=True,
                data=result,
                operation=operation_name,
                source_type=type(self.adapter).__name__,
                adapter_type=type(self.adapter).__name__,
                execution_time=self.performance_monitor.operation_stats.get(operation_name, {}).get('avg_time', 0)
            )
            
        except Exception as e:
            self.logger.error(f"异步事务失败: {e}")
            return DataResult(
                success=False,
                error=str(e),
                operation=operation_name,
                source_type=type(self.adapter).__name__,
                adapter_type=type(self.adapter).__name__
            )
    
    async def stream_query(self, sql: str, params: Dict = None, chunk_size: int = 1000) -> AsyncIterator[DataResult]:
        """
        异步流式查询 - 处理大结果集
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            chunk_size: 每次返回的记录数
            
        Yields:
            数据结果块
        """
        await self._ensure_connection()
        
        try:
            chunk = []
            async for row in self.adapter.async_stream_query(self.connection, sql, params):
                chunk.append(row)
                
                if len(chunk) >= chunk_size:
                    yield DataResult(
                        success=True,
                        data=chunk,
                        operation='async_stream_query',
                        source_type=type(self.adapter).__name__,
                        adapter_type=type(self.adapter).__name__,
                        metadata={'chunk_size': len(chunk), 'is_chunk': True}
                    )
                    chunk = []
            
            # 返回最后一个不完整的块
            if chunk:
                yield DataResult(
                    success=True,
                    data=chunk,
                    operation='async_stream_query',
                    source_type=type(self.adapter).__name__,
                    adapter_type=type(self.adapter).__name__,
                    metadata={'chunk_size': len(chunk), 'is_final_chunk': True}
                )
                
        except Exception as e:
            self.logger.error(f"异步流式查询失败: {e}")
            yield DataResult(
                success=False,
                error=str(e),
                operation='async_stream_query',
                source_type=type(self.adapter).__name__,
                adapter_type=type(self.adapter).__name__
            )
    
    async def batch(self, operations: List[Dict], use_pool: bool = True) -> DataResult:
        """
        异步批量操作
        
        Args:
            operations: 批量操作列表
            use_pool: 是否使用连接池
            
        Returns:
            批量操作结果
        """
        operation_name = 'async_batch'
        
        async def _execute_batch():
            if use_pool:
                await self._ensure_connection_pool()
                async with self.connection_pool.acquire() as conn:
                    return await self.adapter.async_batch(conn, operations)
            else:
                await self._ensure_connection()
                return await self.adapter.async_batch(self.connection, operations)
        
        try:
            result = await self.performance_monitor.monitor_operation(operation_name, _execute_batch())
            
            self.operation_count += 1
            
            return DataResult(
                success=True,
                data=result,
                operation=operation_name,
                source_type=type(self.adapter).__name__,
                adapter_type=type(self.adapter).__name__,
                execution_time=self.performance_monitor.operation_stats.get(operation_name, {}).get('avg_time', 0)
            )
            
        except Exception as e:
            self.logger.error(f"异步批量操作失败: {e}")
            return DataResult(
                success=False,
                error=str(e),
                operation=operation_name,
                source_type=type(self.adapter).__name__,
                adapter_type=type(self.adapter).__name__
            )
    
    async def parallel_queries(self, queries: List[Dict]) -> List[DataResult]:
        """
        并行执行多个查询
        
        Args:
            queries: 查询列表，每个查询包含sql和params
            
        Returns:
            查询结果列表
        """
        tasks = []
        for query in queries:
            task = self.query(query['sql'], query.get('params'))
            tasks.append(task)
        
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            'proxy_id': self.proxy_id,
            'operation_count': self.operation_count,
            'total_execution_time': self.total_execution_time,
            'avg_execution_time': self.total_execution_time / max(self.operation_count, 1),
            'operation_stats': self.performance_monitor.get_performance_stats(),
            'is_closed': self._is_closed
        }
    
    async def close(self):
        """关闭异步代理和所有连接"""
        if self._is_closed:
            return
        
        self._is_closed = True
        
        try:
            # 关闭连接池
            if self.connection_pool:
                await self.connection_pool.close()
                self.connection_pool = None
            
            # 关闭单个连接
            if self.connection:
                await self.adapter.close_async_connection(self.connection)
                self.connection = None
            
            # 从生命周期管理器注销
            if self.lifecycle_manager:
                await self.lifecycle_manager.unregister_async_resource(self.proxy_id)
            
            self.logger.debug(f"异步代理已关闭: {self.proxy_id}")
            
        except Exception as e:
            self.logger.error(f"关闭异步代理时出错: {e}")
    
    def __del__(self):
        """析构函数 - 确保资源清理"""
        if not self._is_closed and hasattr(self, 'connection'):
            # 在事件循环中安排清理任务
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.close())
            except RuntimeError:
                # 没有运行的事件循环，无法清理
                pass
