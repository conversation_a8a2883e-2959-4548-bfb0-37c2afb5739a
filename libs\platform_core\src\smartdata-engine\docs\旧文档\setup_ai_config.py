#!/usr/bin/env python3
"""
AI配置设置脚本

帮助用户快速配置AI服务API Key
"""

import os
import sys
from pathlib import Path


def main():
    """主配置函数"""
    print("🤖 SmartData Engine AI插件配置向导")
    print("=" * 50)
    
    # 检查当前配置
    print("\n📋 检查当前配置:")
    current_config = check_current_config()
    
    if current_config:
        print("✅ 已检测到以下配置:")
        for provider, status in current_config.items():
            print(f"   - {provider}: {status}")
        
        choice = input("\n是否要重新配置? (y/N): ").lower()
        if choice != 'y':
            print("配置保持不变。")
            return
    else:
        print("❌ 未检测到任何AI服务配置")
    
    print("\n🔧 开始配置AI服务...")
    
    # 配置选项
    print("\n请选择要配置的AI服务:")
    print("1. OpenAI (推荐) - 需要付费API Key")
    print("2. <PERSON> (Anthropic) - 需要付费API Key")
    print("3. 演示模式 - 使用模拟响应，无需API Key")
    print("4. 显示免费替代方案")
    print("5. 跳过配置")
    
    choice = input("\n请输入选择 (1-5): ").strip()
    
    if choice == "1":
        configure_openai()
    elif choice == "2":
        configure_claude()
    elif choice == "3":
        configure_demo_mode()
    elif choice == "4":
        show_free_alternatives()
    elif choice == "5":
        print("跳过配置。")
    else:
        print("无效选择。")
        return
    
    # 验证配置
    print("\n🧪 验证配置...")
    verify_configuration()


def check_current_config():
    """检查当前配置"""
    config = {}
    
    if os.getenv('OPENAI_API_KEY'):
        config['OpenAI'] = f"已配置 (Key: {os.getenv('OPENAI_API_KEY')[:10]}...)"
    
    if os.getenv('ANTHROPIC_API_KEY'):
        config['Claude'] = f"已配置 (Key: {os.getenv('ANTHROPIC_API_KEY')[:10]}...)"
    
    if os.getenv('AZURE_OPENAI_API_KEY'):
        config['Azure OpenAI'] = f"已配置 (Key: {os.getenv('AZURE_OPENAI_API_KEY')[:10]}...)"
    
    return config


def configure_openai():
    """配置OpenAI"""
    print("\n🔑 配置OpenAI API Key")
    print("-" * 30)
    print("1. 访问: https://platform.openai.com/api-keys")
    print("2. 注册账户并验证手机号")
    print("3. 创建API Key")
    print("4. 复制API Key (格式: sk-...)")
    
    api_key = input("\n请输入OpenAI API Key: ").strip()
    
    if not api_key:
        print("❌ API Key不能为空")
        return
    
    if not api_key.startswith('sk-'):
        print("⚠️  警告: OpenAI API Key通常以 'sk-' 开头")
        confirm = input("确定要使用这个Key吗? (y/N): ").lower()
        if confirm != 'y':
            return
    
    # 设置环境变量
    os.environ['OPENAI_API_KEY'] = api_key
    
    # 可选配置
    org_id = input("组织ID (可选，直接回车跳过): ").strip()
    if org_id:
        os.environ['OPENAI_ORG_ID'] = org_id
    
    print("✅ OpenAI配置完成!")
    print(f"   API Key: {api_key[:10]}...")
    if org_id:
        print(f"   组织ID: {org_id}")
    
    # 保存到文件
    save_config_to_file('OPENAI_API_KEY', api_key)
    if org_id:
        save_config_to_file('OPENAI_ORG_ID', org_id)


def configure_claude():
    """配置Claude"""
    print("\n🧠 配置Claude API Key")
    print("-" * 30)
    print("1. 访问: https://console.anthropic.com/")
    print("2. 注册账户")
    print("3. 创建API Key")
    print("4. 复制API Key (格式: sk-ant-...)")
    
    api_key = input("\n请输入Claude API Key: ").strip()
    
    if not api_key:
        print("❌ API Key不能为空")
        return
    
    if not api_key.startswith('sk-ant-'):
        print("⚠️  警告: Claude API Key通常以 'sk-ant-' 开头")
        confirm = input("确定要使用这个Key吗? (y/N): ").lower()
        if confirm != 'y':
            return
    
    # 设置环境变量
    os.environ['ANTHROPIC_API_KEY'] = api_key
    
    print("✅ Claude配置完成!")
    print(f"   API Key: {api_key[:15]}...")
    
    # 保存到文件
    save_config_to_file('ANTHROPIC_API_KEY', api_key)


def configure_demo_mode():
    """配置演示模式"""
    print("\n🎭 配置演示模式")
    print("-" * 30)
    print("演示模式将使用模拟响应，无需真实的API Key。")
    print("这对于测试和开发非常有用。")
    
    confirm = input("\n确定要启用演示模式吗? (Y/n): ").lower()
    if confirm in ['', 'y', 'yes']:
        os.environ['AI_DEMO_MODE'] = 'true'
        print("✅ 演示模式已启用!")
        save_config_to_file('AI_DEMO_MODE', 'true')
    else:
        print("演示模式未启用。")


def show_free_alternatives():
    """显示免费替代方案"""
    print("\n🆓 免费AI服务替代方案")
    print("-" * 40)
    
    print("\n1. Hugging Face (免费，有限制)")
    print("   - 访问: https://huggingface.co/settings/tokens")
    print("   - 注册并创建Access Token")
    print("   - 免费但有速率限制")
    
    print("\n2. Ollama (完全免费，本地运行)")
    print("   - 访问: https://ollama.ai/")
    print("   - 下载并安装Ollama")
    print("   - 运行: ollama run llama2")
    print("   - 完全本地，无需API Key")
    
    print("\n3. OpenAI兼容的本地服务")
    print("   - 使用text-generation-webui")
    print("   - 使用LocalAI")
    print("   - 使用vLLM")
    
    choice = input("\n要配置Hugging Face吗? (y/N): ").lower()
    if choice == 'y':
        configure_huggingface()


def configure_huggingface():
    """配置Hugging Face"""
    print("\n🤗 配置Hugging Face")
    print("-" * 30)
    
    token = input("请输入Hugging Face Token (hf_...): ").strip()
    
    if not token:
        print("❌ Token不能为空")
        return
    
    if not token.startswith('hf_'):
        print("⚠️  警告: Hugging Face Token通常以 'hf_' 开头")
    
    os.environ['HF_TOKEN'] = token
    print("✅ Hugging Face配置完成!")
    save_config_to_file('HF_TOKEN', token)


def save_config_to_file(key, value):
    """保存配置到文件"""
    config_file = Path('.env')
    
    # 读取现有配置
    existing_lines = []
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            existing_lines = f.readlines()
    
    # 更新或添加配置
    key_found = False
    for i, line in enumerate(existing_lines):
        if line.startswith(f'{key}='):
            existing_lines[i] = f'{key}={value}\n'
            key_found = True
            break
    
    if not key_found:
        existing_lines.append(f'{key}={value}\n')
    
    # 写入文件
    with open(config_file, 'w', encoding='utf-8') as f:
        f.writelines(existing_lines)
    
    print(f"   配置已保存到 {config_file}")


def verify_configuration():
    """验证配置"""
    try:
        # 尝试导入和测试AI插件
        sys.path.insert(0, '.')
        from plugins.ai.smart_ai_loader import global_ai_loader
        from plugins.ai.ai_factory import AIServiceType
        
        # 配置加载器
        if os.getenv('OPENAI_API_KEY'):
            global_ai_loader.configure_provider('openai', {
                'api_key': os.getenv('OPENAI_API_KEY'),
                'organization': os.getenv('OPENAI_ORG_ID')
            })
        
        if os.getenv('ANTHROPIC_API_KEY'):
            global_ai_loader.configure_provider('claude', {
                'api_key': os.getenv('ANTHROPIC_API_KEY')
            })
        
        # 进行简单测试
        if os.getenv('AI_DEMO_MODE') == 'true':
            print("✅ 演示模式配置验证成功")
        elif os.getenv('OPENAI_API_KEY') or os.getenv('ANTHROPIC_API_KEY'):
            print("✅ API Key配置验证成功")
            print("   可以运行真实场景测试: python -m pytest tests/test_ai_real_scenario.py -v")
        else:
            print("⚠️  未检测到有效配置")
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")


def show_usage_examples():
    """显示使用示例"""
    print("\n📖 使用示例")
    print("-" * 20)
    
    print("\n1. Python代码中使用:")
    print("""
from plugins.ai.smart_ai_loader import global_ai_loader

# 文本生成
result = global_ai_loader.process_ai_request(
    'text_generation',
    '写一首关于春天的诗',
    {'provider': 'openai', 'model': 'gpt-3.5-turbo'}
)
print(result.data['generated_text'])
""")
    
    print("\n2. 模板中使用:")
    print("""
{% set poem = sd.ai('text_generation', '写一首关于春天的诗', provider='openai') %}
诗歌: {{ poem.data.generated_text }}
""")


if __name__ == "__main__":
    try:
        main()
        
        print("\n📖 想查看使用示例吗? (y/N): ", end="")
        if input().lower() == 'y':
            show_usage_examples()
        
        print("\n🎉 配置完成! 现在可以使用AI插件了。")
        
    except KeyboardInterrupt:
        print("\n\n👋 配置已取消。")
    except Exception as e:
        print(f"\n❌ 配置过程中出现错误: {e}")
        sys.exit(1)
