"""
数据库数据处理插件

提供多种数据库系统集成能力，支持：
- 关系型数据库（MySQL, PostgreSQL, Oracle, SQL Server）
- NoSQL数据库（MongoDB, Redis, Elasticsearch）
- 内存数据库（SQLite, H2）
- 连接池和事务管理
- SQL查询优化和缓存
- 数据库模式自动发现
"""

# 插件定义 - 按照统一标准
PLUGIN_DEFINITIONS = [
    {
        'id': 'database_processor',
        'name': '数据库数据处理器',
        'description': '基础数据库连接和查询处理器',
        'version': '1.0.0',
        'type': 'processor',
        'category': 'database',
        'priority': 75,
        'class_name': 'DatabaseProcessor',
        'module_file': 'database_processor',
        'auto_load': True,
        'enabled': True,
        'capabilities': [
            'basic_database_support',
            'sql_queries',
            'connection_management'
        ],
        'supported_databases': ['mysql', 'postgresql', 'sqlite'],
        'dependencies': [],
        'optional_dependencies': ['pymysql', 'psycopg2', 'sqlite3'],
        'author': 'SmartData Team',
        'license': 'MIT',
        'tags': ['database', 'sql']
    },
    {
        'id': 'enterprise_database_processor',
        'name': '企业级数据库处理器',
        'description': '企业级数据库处理器，支持多数据库、连接池、查询优化等功能',
        'version': '2.0.0',
        'type': 'processor',
        'category': 'database',
        'priority': 80,
        'class_name': 'EnterpriseDatabaseProcessor',
        'module_file': 'enterprise_database_processor',
        'auto_load': True,
        'enabled': True,
        'capabilities': [
            'multi_database_support',
            'connection_pooling',
            'query_optimization',
            'intelligent_caching',
            'transaction_management',
            'security_authentication',
            'performance_monitoring',
            'failover_support',
            'batch_processing',
            'async_processing'
        ],
        'supported_databases': [
            'mysql', 'postgresql', 'sqlite', 'mongodb', 'redis', 'elasticsearch',
            'oracle', 'sqlserver'
        ],
        'supported_operations': [
            'query', 'test_connection', 'list_tables', 'describe_table',
            'get_stats', 'batch_query', 'transaction'
        ],
        'dependencies': [],
        'optional_dependencies': [
            'aiomysql', 'asyncpg', 'aiosqlite', 'motor', 'aioredis',
            'elasticsearch[async]', 'cx_Oracle', 'aioodbc'
        ],
        'author': 'SmartData Team',
        'license': 'MIT',
        'tags': ['database', 'enterprise', 'sql', 'nosql', 'async']
    }
]

try:
    # 基础数据库处理器
    from .database_processor import DatabaseProcessor, EnterpriseDatabaseProcessor

    # 统一连接器（包含企业级功能）
    from .connectors import (
        ConnectorFactory,
        EnterpriseConnectorFactory,  # 兼容接口
        EnterpriseConnectorWrapper,  # 兼容包装器
        BaseConnector,
        ConnectionResult,
        QueryResult,
        ConnectionConfig
    )

    # 保持向后兼容
    try:
        from .connectors import (
            ConnectorFactory,
            BaseConnector,
            MysqlConnector,
            PostgresConnector,
            SqliteConnector,
            MongoConnector,
            RedisConnector,
            ElasticsearchConnector,
            ConnectionConfig
        )
        from .query_builders import (
            QueryBuilderFactory,
            MySQLQueryBuilder,
            PostgreSQLQueryBuilder,
            MongoQueryBuilder,
            QueryCondition,
            QueryOptions
        )
        from .connection_pool import (
            ConnectionPoolManager,
            PoolConfig,
            PoolStats
        )
    except ImportError:
        # 如果导入失败，使用基础实现
        pass
        RedisConnector = None
        ElasticsearchConnector = None
        ConnectionConfig = None
        QueryBuilderFactory = None
        MySQLQueryBuilder = None
        PostgreSQLQueryBuilder = None
        MongoQueryBuilder = None
        QueryCondition = None
        QueryOptions = None
        ConnectionPoolManager = None
        PoolConfig = None
        PoolStats = None

    __all__ = [
        # 主要处理器
        'DatabaseProcessor',
        'EnterpriseDatabaseProcessor',  # 兼容接口

        # 统一连接器接口
        'ConnectorFactory',
        'EnterpriseConnectorFactory',  # 兼容接口
        'EnterpriseConnectorWrapper',  # 兼容包装器
        'BaseConnector',
        'ConnectionResult',
        'QueryResult',
        'ConnectionConfig',

        # 具体连接器实现
        'MysqlConnector',
        'PostgresConnector',
        'SqliteConnector',
        'MongoConnector',
        'RedisConnector',
        'ElasticsearchConnector',
        'OracleConnector',
        'OceanBaseConnector',

        # 查询构建器
        'QueryBuilderFactory',
        'MySQLQueryBuilder',
        'PostgreSQLQueryBuilder',
        'MongoQueryBuilder',
        'QueryCondition',
        'QueryOptions',

        # 连接池管理
        'ConnectionPoolManager',
        'PoolConfig',
        'PoolStats',

        # 插件定义
        'PLUGIN_DEFINITIONS',
        'get_plugin_definitions'
    ]

except ImportError as e:
    # 优雅处理导入错误
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"Database插件部分功能导入失败: {e}")

    # 保持基本功能可用
    try:
        from .database_processor import DatabaseProcessor
        __all__ = ['DatabaseProcessor', 'PLUGIN_DEFINITIONS', 'get_plugin_definitions']
    except ImportError:
        __all__ = ['PLUGIN_DEFINITIONS', 'get_plugin_definitions']


# 插件定义列表
PLUGIN_DEFINITIONS = [
    {
        'id': 'database_processor',
        'name': '数据库数据处理器',
        'description': '连接和查询各种数据库，支持MySQL、PostgreSQL、SQLite、MongoDB等',
        'version': '2.0.0',
        'type': 'processor',
        'category': 'database',
        'priority': 80,
        'class_name': 'DatabaseProcessor',
        'module_file': 'database_processor',
        'supported_types': [
            'database', 'sql', 'mysql', 'postgresql', 'sqlite',
            'mongodb', 'redis', 'query', 'db'
        ],
        'capabilities': [
            'database:mysql', 'database:postgresql', 'database:sqlite',
            'database:mongodb', 'database:redis', 'query:sql',
            'connection:pool', 'transaction:support', 'cache:query',
            'security:auth', 'performance:optimize'
        ],
        'dependencies': [],
        'config_schema': {
            'connection_string': {'type': 'string', 'required': True},
            'database_type': {
                'type': 'string',
                'enum': ['mysql', 'postgresql', 'sqlite', 'mongodb', 'redis'],
                'required': True
            },
            'max_connections': {'type': 'integer', 'default': 10},
            'timeout': {'type': 'number', 'default': 30.0},
            'auto_commit': {'type': 'boolean', 'default': True},
            'use_ssl': {'type': 'boolean', 'default': False},
            'charset': {'type': 'string', 'default': 'utf8mb4'},
            'pool_recycle': {'type': 'integer', 'default': 3600}
        },
        'security': {
            'requires_auth': True,
            'sensitive_config': ['connection_string', 'password'],
            'permissions': ['database:read', 'database:write']
        },
        'performance': {
            'cache_enabled': True,
            'cache_ttl': 300,
            'batch_size': 1000,
            'parallel_queries': True
        },
        'enabled': True,
        'auto_load': True,
        'enterprise_features': [
            'connection_pooling', 'query_optimization', 'security_audit',
            'performance_monitoring', 'failover_support'
        ]
    }
]


def get_plugin_definitions():
    """获取插件定义列表"""
    return PLUGIN_DEFINITIONS


def get_supported_databases():
    """获取支持的数据库类型"""
    return ['mysql', 'postgresql', 'sqlite', 'mongodb', 'redis', 'elasticsearch']


def get_connection_templates():
    """获取连接字符串模板"""
    return {
        'mysql': 'mysql://username:password@host:port/database',
        'postgresql': 'postgresql://username:password@host:port/database',
        'sqlite': 'sqlite:///path/to/database.db',
        'mongodb': '********************************:port/database',
        'redis': 'redis://username:password@host:port/database'
    }


# 插件元数据
PLUGIN_METADATA = {
    'category': 'database',
    'description': '数据库连接和查询处理器',
    'version': '1.0.0',
    'total_plugins': len(PLUGIN_DEFINITIONS),
    'supported_databases': get_supported_databases(),
    'security_level': 'high',
    'enterprise_ready': True,
    'auto_discovery': True,
    'load_priority': 80
}
