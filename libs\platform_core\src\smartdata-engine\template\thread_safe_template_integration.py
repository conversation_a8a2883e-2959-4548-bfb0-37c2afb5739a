#!/usr/bin/env python3
"""
线程安全的企业级模板引擎集成

解决线程安全、模板隔离、数据干净的问题
"""

import logging
import asyncio
import threading
import weakref
import uuid
from typing import Any, Dict, List, Optional, Union, Callable
from contextlib import contextmanager
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from datetime import datetime

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../'))

from core.enterprise_data_architecture import DataRegistry, DataProxy, LifecycleManager, TemplateScope
from core.data_contract import TemplateDataContract
from core.async_lifecycle_manager import AsyncLifecycleManager
from core.async_template_scope import AsyncTemplateScope


@dataclass
class ScopeMetadata:
    """作用域元数据"""
    scope_id: str
    thread_id: int
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    is_isolated: bool = True
    cleanup_callbacks: List[Callable] = None
    
    def __post_init__(self):
        if self.cleanup_callbacks is None:
            self.cleanup_callbacks = []


class ThreadSafeTemplateScope:
    """线程安全的模板作用域"""
    
    def __init__(self, scope_id: str, base_scope: Union[TemplateScope, AsyncTemplateScope], 
                 thread_id: int, isolation_level: str = 'thread'):
        self.scope_id = scope_id
        self.base_scope = base_scope
        self.thread_id = thread_id
        self.isolation_level = isolation_level
        
        # 线程安全锁
        self._lock = threading.RLock()
        
        # 元数据
        self.metadata = ScopeMetadata(
            scope_id=scope_id,
            thread_id=thread_id,
            created_at=datetime.now(),
            last_accessed=datetime.now()
        )
        
        # 数据源注册表（线程隔离）
        self._data_sources = {}
        self._data_proxies = weakref.WeakValueDictionary()
        
        # 清理标志
        self._is_cleaned = False
        
        self.logger = logging.getLogger(f"{self.__class__.__name__}[{scope_id}]")
    
    @contextmanager
    def thread_safe_access(self):
        """线程安全访问上下文"""
        with self._lock:
            if self._is_cleaned:
                raise RuntimeError(f"作用域 {self.scope_id} 已被清理")
            
            # 更新访问时间
            self.metadata.last_accessed = datetime.now()
            self.metadata.access_count += 1
            
            try:
                yield self
            finally:
                pass  # 可以在这里添加清理逻辑
    
    def register_data_source(self, name: str, source: Any) -> DataProxy:
        """线程安全的数据源注册"""
        with self.thread_safe_access():
            # 检查线程隔离
            if self.isolation_level == 'thread' and threading.current_thread().ident != self.thread_id:
                raise RuntimeError(f"跨线程访问被拒绝: 作用域属于线程 {self.thread_id}, 当前线程 {threading.current_thread().ident}")

            # 注册数据源 - 使用统一接口
            try:
                # 现在base_scope是统一的适配器，都有register_data_source方法
                proxy = self.base_scope.register_data_source(name, source)
            except Exception as e:
                self.logger.debug(f"数据源注册失败: {e}")
                # 回退方案：创建简单代理
                proxy = self._create_simple_proxy(name, source)

            self._data_sources[name] = source
            self._data_proxies[name] = proxy

            self.logger.debug(f"注册数据源: {name} (线程: {self.thread_id})")
            return proxy

    def _create_simple_proxy(self, name: str, source: Any):
        """创建简单的数据代理"""
        class SimpleDataProxy:
            def __init__(self, name, source):
                self.name = name
                self.source = source
                self._data = None

            def get_data(self):
                if self._data is None:
                    if isinstance(self.source, str) and self.source.endswith('.json'):
                        # JSON文件处理
                        import json
                        with open(self.source, 'r', encoding='utf-8') as f:
                            self._data = json.load(f)
                    else:
                        self._data = self.source
                return self._data

            def __getattr__(self, item):
                data = self.get_data()
                if isinstance(data, dict):
                    return data.get(item)
                return getattr(data, item, None)

            def __getitem__(self, key):
                data = self.get_data()
                return data[key]

            def __str__(self):
                return str(self.get_data())

            def __repr__(self):
                return f"SimpleDataProxy({self.name}, {repr(self.source)})"

        return SimpleDataProxy(name, source)

    def cleanup(self):
        """清理作用域资源"""
        with self._lock:
            if self._is_cleaned:
                return

            self.logger.debug(f"清理作用域: {self.scope_id}")

            # 执行清理回调
            for callback in self.metadata.cleanup_callbacks:
                try:
                    callback()
                except Exception as e:
                    self.logger.error(f"清理回调失败: {e}")

            # 清理数据源
            for name, proxy in list(self._data_proxies.items()):
                try:
                    if hasattr(proxy, 'cleanup'):
                        proxy.cleanup()
                except Exception as e:
                    self.logger.error(f"数据源清理失败 {name}: {e}")

            # 清理基础作用域
            try:
                if hasattr(self.base_scope, 'cleanup'):
                    self.base_scope.cleanup()
            except Exception as e:
                self.logger.error(f"基础作用域清理失败: {e}")

            # 清空引用
            self._data_sources.clear()
            self._data_proxies.clear()
            self.metadata.cleanup_callbacks.clear()

            self._is_cleaned = True

    def add_cleanup_callback(self, callback: Callable):
        """添加清理回调"""
        with self._lock:
            self.metadata.cleanup_callbacks.append(callback)

    def __del__(self):
        """析构函数 - 确保资源被清理"""
        if not self._is_cleaned:
            self.cleanup()


class AsyncScopeAdapter:
    """
    异步作用域的同步适配器

    将AsyncTemplateScope的异步接口适配为同步接口，
    解决ThreadSafeTemplateScope与AsyncTemplateScope的接口不兼容问题
    """

    def __init__(self, scope_id: str, data_registry):
        self.scope_id = scope_id
        self.data_registry = data_registry

        # 创建底层异步作用域
        from core.async_template_scope import AsyncTemplateScope
        self._async_scope = AsyncTemplateScope(scope_id, data_registry)

        # 同步执行器
        self._loop = None
        self._thread_pool = None

        # 添加缺失的属性
        self._is_cleaned = False
        self._lock = threading.RLock()  # 添加锁

        self.logger = logging.getLogger(f"{self.__class__.__name__}[{scope_id}]")

    def register_data_source(self, name: str, source: Any):
        """同步的数据源注册接口"""
        try:
            # 简化的异步处理：直接使用asyncio.run
            import asyncio

            # 直接运行异步方法，让asyncio处理事件循环
            return asyncio.run(
                self._async_scope.register_async_data_source(name, source)
            )

        except Exception as e:
            self.logger.debug(f"异步数据源注册失败，使用回退方案: {e}")
            # 回退到创建简单代理
            return self._create_fallback_proxy(name, source)



    def _create_fallback_proxy(self, name: str, source: Any):
        """创建回退代理"""
        class FallbackProxy:
            def __init__(self, name, source):
                self.name = name
                self.source = source
                self._data = None

            def get_data(self):
                if self._data is None:
                    if isinstance(self.source, str) and self.source.endswith('.json'):
                        import json
                        with open(self.source, 'r', encoding='utf-8') as f:
                            self._data = json.load(f)
                    else:
                        self._data = self.source
                return self._data

            def __getattr__(self, item):
                data = self.get_data()
                if isinstance(data, dict):
                    return data.get(item)
                return getattr(data, item, None)

            def __getitem__(self, key):
                data = self.get_data()
                return data[key]

        return FallbackProxy(name, source)

    def cleanup(self):
        """清理资源"""
        with self._lock:
            if self._is_cleaned:
                return

            self.logger.debug(f"清理异步作用域适配器: {self.scope_id}")

            try:
                if hasattr(self._async_scope, 'cleanup'):
                    # 同步执行异步清理
                    import asyncio
                    try:
                        loop = asyncio.get_running_loop()
                        loop.create_task(self._async_scope.cleanup())
                    except RuntimeError:
                        asyncio.run(self._async_scope.cleanup())
            except Exception as e:
                self.logger.error(f"异步作用域清理失败: {e}")
            finally:
                self._is_cleaned = True

    def __getattr__(self, name):
        """代理其他属性到异步作用域"""
        return getattr(self._async_scope, name)

    def __del__(self):
        """析构函数 - 确保资源被清理"""
        try:
            if not self._is_cleaned:
                self.cleanup()
        except Exception:
            pass  # 忽略析构时的错误


class ThreadSafeTemplateIntegration:
    """
    线程安全的企业级模板引擎集成
    
    解决的问题：
    1. 线程安全：使用锁机制保护共享状态
    2. 模板隔离：每个线程/请求独立的作用域
    3. 数据干净：自动清理资源，防止内存泄漏
    """
    
    def __init__(self, 
                 enable_async: bool = True,
                 enable_debug: bool = False,
                 isolation_level: str = 'thread',  # 'thread', 'request', 'global'
                 cleanup_interval: int = 300,      # 清理间隔（秒）
                 max_scope_lifetime: int = 3600):  # 最大作用域生命周期（秒）
        
        self.enable_async = enable_async
        self.enable_debug = enable_debug
        self.isolation_level = isolation_level
        self.cleanup_interval = cleanup_interval
        self.max_scope_lifetime = max_scope_lifetime
        
        self.logger = logging.getLogger(self.__class__.__name__)
        if enable_debug:
            self.logger.setLevel(logging.DEBUG)
        
        # 线程安全锁
        self._global_lock = threading.RLock()
        self._scope_locks = {}  # 每个作用域的独立锁
        self._scope_locks_lock = threading.Lock()
        
        # 核心组件
        self.data_registry = DataRegistry()
        self.data_contract = TemplateDataContract()
        
        # 生命周期管理器
        self.sync_lifecycle_manager = LifecycleManager()
        if enable_async:
            self.async_lifecycle_manager = AsyncLifecycleManager()
        
        # 线程安全的作用域管理
        self._scopes = weakref.WeakValueDictionary()  # 自动清理不再使用的作用域
        self._scope_metadata = {}  # 作用域元数据
        
        # 线程池
        if enable_async:
            self._thread_pool = ThreadPoolExecutor(
                max_workers=4, 
                thread_name_prefix="ThreadSafeTemplate"
            )
        else:
            self._thread_pool = None
        
        # 清理任务
        self._cleanup_task = None
        self._shutdown_event = threading.Event()
        
        # 注册默认适配器
        self._register_default_adapters()
        
        # 启动清理任务
        self._start_cleanup_task()
        
        self.logger.info(f"线程安全模板引擎初始化完成 - 隔离级别: {isolation_level}")
    
    def _register_default_adapters(self):
        """注册默认适配器"""
        with self._global_lock:
            try:
                # 尝试注册文件适配器
                self._register_file_adapters()

                # 注册其他默认适配器
                self._register_legacy_adapters()

                self.logger.debug("默认适配器注册完成")

            except Exception as e:
                self.logger.warning(f"适配器注册失败: {e}")

    def _register_file_adapters(self):
        """注册文件适配器"""
        try:
            # 尝试导入现有的文件适配器
            from core.adapters.file.json_adapter import JSONFileAdapter
            from core.adapters.file.csv_adapter import CSVFileAdapter
            from core.adapters.file.xml_adapter import XMLFileAdapter

            # 注册文件适配器 - 只传递适配器类
            if hasattr(self.data_registry, 'register_adapter'):
                self.data_registry.register_adapter(JSONFileAdapter)
                self.data_registry.register_adapter(CSVFileAdapter)
                self.data_registry.register_adapter(XMLFileAdapter)

            self.logger.debug("文件适配器注册成功")

        except ImportError:
            # 如果现有适配器不可用，创建简单的文件适配器
            self._create_simple_file_adapters()
        except Exception as e:
            self.logger.debug(f"文件适配器注册失败: {e}")
            # 创建简单的文件适配器作为回退
            self._create_simple_file_adapters()

    def _create_simple_file_adapters(self):
        """创建简单的文件适配器"""
        try:
            # 创建简单的JSON文件适配器
            from core.enterprise_data_architecture import IDataAdapter

            class SimpleJSONFileAdapter(IDataAdapter):
                def __init__(self):
                    self.name = "simple_json_file"

                def can_handle(self, data_source):
                    if isinstance(data_source, str):
                        return data_source.endswith('.json')
                    return False

                def supported_types(self):
                    """返回支持的数据类型"""
                    return ['json_file']

                def create_proxy(self, data_source, lifecycle_manager):
                    """创建数据代理对象"""
                    from core.enterprise_data_architecture import DataProxy
                    return DataProxy(data_source, self, lifecycle_manager)

                def connect(self, data_source):
                    """兼容性方法"""
                    return SimpleJSONFileConnection(data_source)

                def get_operations(self):
                    """返回支持的操作"""
                    def parse_operation(proxy, **options):
                        connection = self.connect(proxy.source)
                        return connection.parse()

                    def read_operation(proxy, **options):
                        connection = self.connect(proxy.source)
                        return connection.read()

                    return {
                        'parse': parse_operation,
                        'read': read_operation
                    }

            class SimpleJSONFileConnection:
                def __init__(self, file_path):
                    self.file_path = file_path
                    self._data = None
                    self._loaded = False

                def parse(self):
                    """解析JSON文件"""
                    if not self._loaded:
                        import json
                        import os

                        if not os.path.exists(self.file_path):
                            raise FileNotFoundError(f"文件不存在: {self.file_path}")

                        try:
                            with open(self.file_path, 'r', encoding='utf-8') as f:
                                self._data = json.load(f)
                            self._loaded = True
                        except json.JSONDecodeError as e:
                            raise ValueError(f"JSON文件格式错误: {e}")
                        except Exception as e:
                            raise RuntimeError(f"文件读取失败: {e}")

                    return self._data

                def read(self):
                    """读取文件内容"""
                    return self.parse()

                def close(self):
                    """关闭连接"""
                    self._data = None
                    self._loaded = False

                def __enter__(self):
                    return self

                def __exit__(self, exc_type, exc_val, exc_tb):
                    self.close()

            # 注册简单适配器 - 只传递适配器类
            if hasattr(self.data_registry, 'register_adapter'):
                self.data_registry.register_adapter(SimpleJSONFileAdapter)

            self.logger.debug("简单文件适配器创建成功")

        except Exception as e:
            self.logger.warning(f"简单文件适配器创建失败: {e}")

    def _register_legacy_adapters(self):
        """注册旧模板引擎的适配器"""
        try:
            # 这里可以注册旧模板引擎的特殊适配器
            pass
        except Exception as e:
            self.logger.debug(f"旧版适配器注册失败: {e}")
    
    def _start_cleanup_task(self):
        """启动清理任务"""
        def cleanup_worker():
            while not self._shutdown_event.wait(self.cleanup_interval):
                try:
                    self._cleanup_expired_scopes()
                except Exception as e:
                    self.logger.error(f"清理任务失败: {e}")
        
        self._cleanup_task = threading.Thread(
            target=cleanup_worker,
            name="TemplateCleanup",
            daemon=True
        )
        self._cleanup_task.start()
    
    def _cleanup_expired_scopes(self):
        """清理过期的作用域"""
        current_time = datetime.now()
        expired_scopes = []
        
        with self._global_lock:
            for scope_id, metadata in list(self._scope_metadata.items()):
                # 检查是否过期
                if (current_time - metadata.last_accessed).total_seconds() > self.max_scope_lifetime:
                    expired_scopes.append(scope_id)
        
        # 清理过期作用域
        for scope_id in expired_scopes:
            try:
                self._cleanup_scope(scope_id)
                self.logger.debug(f"清理过期作用域: {scope_id}")
            except Exception as e:
                self.logger.error(f"清理作用域失败 {scope_id}: {e}")
    
    def _cleanup_scope(self, scope_id: str):
        """清理指定作用域"""
        with self._global_lock:
            # 获取作用域
            scope = self._scopes.get(scope_id)
            if scope:
                scope.cleanup()
            
            # 清理元数据
            self._scope_metadata.pop(scope_id, None)
            
            # 清理作用域锁
            with self._scope_locks_lock:
                self._scope_locks.pop(scope_id, None)

    def _create_unified_scope(self, scope_id: str):
        """创建统一的作用域适配器"""
        if self.enable_async:
            # 创建异步作用域的同步适配器
            return AsyncScopeAdapter(scope_id, self.data_registry)
        else:
            # 直接使用同步作用域
            from core.enterprise_data_architecture import TemplateScope
            return TemplateScope(scope_id, self.data_registry)

    @contextmanager
    def create_isolated_scope(self, scope_id: Optional[str] = None):
        """
        创建隔离的模板作用域
        
        这是线程安全、模板隔离、数据干净的核心实现
        """
        if scope_id is None:
            scope_id = f"scope_{uuid.uuid4().hex[:8]}_{threading.current_thread().ident}"
        
        thread_id = threading.current_thread().ident
        
        with self._global_lock:
            # 检查作用域是否已存在
            if scope_id in self._scopes:
                raise ValueError(f"作用域 {scope_id} 已存在")
            
            # 创建统一的基础作用域适配器
            base_scope = self._create_unified_scope(scope_id)
            
            # 创建线程安全包装
            safe_scope = ThreadSafeTemplateScope(
                scope_id=scope_id,
                base_scope=base_scope,
                thread_id=thread_id,
                isolation_level=self.isolation_level
            )
            
            # 注册作用域
            self._scopes[scope_id] = safe_scope
            self._scope_metadata[scope_id] = safe_scope.metadata
            
            # 创建作用域锁
            with self._scope_locks_lock:
                self._scope_locks[scope_id] = threading.RLock()
            
            self.logger.debug(f"创建隔离作用域: {scope_id} (线程: {thread_id})")
        
        try:
            yield safe_scope
        finally:
            # 自动清理作用域
            try:
                self._cleanup_scope(scope_id)
            except Exception as e:
                self.logger.error(f"作用域清理失败 {scope_id}: {e}")
    
    def render_template_sync(self, 
                           template_string: str, 
                           context: Dict[str, Any] = None,
                           scope_id: Optional[str] = None) -> str:
        """线程安全的同步模板渲染"""
        with self.create_isolated_scope(scope_id) as scope:
            # 处理上下文数据
            enhanced_context = self._process_template_context(scope, context or {})
            
            # 添加智能数据加载器
            enhanced_context['sd'] = self._create_smart_loader(scope)
            
            # 渲染模板
            from jinja2 import Environment
            env = Environment()

            # 添加自定义过滤器
            self._add_custom_filters(env)

            template = env.from_string(template_string)
            return template.render(enhanced_context)

    def _add_custom_filters(self, env):
        """添加自定义Jinja2过滤器 - 集成旧模板引擎的完整功能"""
        # 导入旧模板引擎的企业级过滤器
        try:
            from template.filters.enterprise_filters import ENTERPRISE_FILTERS, register_enterprise_filters
            # 注册所有企业级过滤器
            register_enterprise_filters(env)
            self.logger.debug("企业级过滤器注册成功")
        except ImportError as e:
            self.logger.debug(f"企业级过滤器不可用: {e}")
            # 回退到基础过滤器
            self._add_basic_filters(env)

        # 添加推导式扩展
        try:
            from template.extensions.comprehension_extension import ComprehensionExtension
            env.add_extension(ComprehensionExtension)
            self.logger.debug("推导式扩展注册成功")
        except ImportError as e:
            self.logger.debug(f"推导式扩展不可用: {e}")

        # 添加高级语句扩展
        try:
            from template.extensions.advanced_statement_extension import AdvancedStatementExtension
            env.add_extension(AdvancedStatementExtension)
            self.logger.debug("高级语句扩展注册成功")
        except ImportError as e:
            self.logger.debug(f"高级语句扩展不可用: {e}")

    def _add_basic_filters(self, env):
        """添加基础过滤器作为回退"""
        def flatten_filter(items):
            """展平嵌套列表"""
            result = []
            for item in items:
                if isinstance(item, (list, tuple)):
                    result.extend(flatten_filter(item))
                else:
                    result.append(item)
            return result

        def unique_filter(items):
            """去重过滤器"""
            seen = set()
            result = []
            for item in items:
                if item not in seen:
                    seen.add(item)
                    result.append(item)
            return result

        def multiply_filter(value, multiplier):
            """乘法过滤器 - 支持数字和列表乘法"""
            try:
                if isinstance(value, (list, tuple)):
                    if isinstance(multiplier, (list, tuple)) and len(value) == len(multiplier):
                        # 对应元素相乘
                        return [v * m for v, m in zip(value, multiplier)]
                    else:
                        # 每个元素乘以同一个数
                        return [v * multiplier for v in value]
                else:
                    # 直接乘法
                    return value * multiplier
            except (TypeError, ValueError):
                return value

        def enhanced_map_filter(items, *args, **kwargs):
            """增强的 map 过滤器 - 支持过滤器调用语法"""
            if not args:
                # 标准 map 行为
                return map(lambda x: x, items)

            first_arg = args[0]

            # 检查是否是过滤器名称
            if isinstance(first_arg, str) and first_arg in env.filters:
                filter_func = env.filters[first_arg]
                remaining_args = args[1:]

                def apply_filter(item):
                    try:
                        # 对于字典项，从字典中获取参数值
                        if isinstance(item, dict) and remaining_args:
                            filter_args = []
                            for arg in remaining_args:
                                if isinstance(arg, str) and arg in item:
                                    filter_args.append(item[arg])
                                else:
                                    filter_args.append(arg)
                            return filter_func(item, *filter_args)
                        else:
                            return filter_func(item, *remaining_args)
                    except Exception as e:
                        self.logger.debug(f"过滤器 {first_arg} 应用失败: {e}")
                        return item

                return [apply_filter(item) for item in items]

            # 检查是否是属性访问
            elif isinstance(first_arg, str) and first_arg == 'attribute':
                if len(args) > 1:
                    attr_name = args[1]
                    return [item.get(attr_name) if isinstance(item, dict)
                           else getattr(item, attr_name, None) for item in items]

            # 标准 map 行为
            return map(first_arg, items)

        def sum_by_filter(items, attribute=None, start=0):
            """按属性求和过滤器"""
            if attribute:
                return sum(getattr(item, attribute, 0) if hasattr(item, attribute)
                          else item.get(attribute, 0) if isinstance(item, dict) else 0
                          for item in items) + start
            else:
                return sum(items, start)

        def group_by_filter(items, attribute):
            """分组过滤器"""
            from collections import defaultdict
            groups = defaultdict(list)
            for item in items:
                if isinstance(item, dict):
                    key = item.get(attribute)
                elif hasattr(item, attribute):
                    key = getattr(item, attribute)
                else:
                    key = None
                groups[key].append(item)
            return dict(groups)

        def deep_get_filter(data, path, default=None):
            """深度获取过滤器"""
            try:
                keys = path.split('.')
                current = data
                for key in keys:
                    if isinstance(current, dict):
                        current = current.get(key)
                    elif hasattr(current, key):
                        current = getattr(current, key)
                    else:
                        return default
                    if current is None:
                        return default
                return current
            except Exception:
                return default

        def even_filter(value):
            """偶数过滤器"""
            try:
                return int(value) % 2 == 0
            except (ValueError, TypeError):
                return False

        def greater_than_filter(value, threshold):
            """大于过滤器"""
            try:
                return float(value) > float(threshold)
            except (ValueError, TypeError):
                return False

        # 注册基础过滤器
        env.filters.update({
            'flatten': flatten_filter,
            'unique': unique_filter,
            'multiply': multiply_filter,
            'sum_by': sum_by_filter,
            'group_by': group_by_filter,
            'deep_get': deep_get_filter,
        })

        # 不覆盖标准的 map 过滤器，而是添加一个增强版本
        # env.filters['map'] = enhanced_map_filter  # 暂时禁用

        env.tests.update({
            'even': even_filter,
            '>': greater_than_filter,
        })
    
    async def render_template_async(self, 
                                  template_string: str, 
                                  context: Dict[str, Any] = None,
                                  scope_id: Optional[str] = None) -> str:
        """线程安全的异步模板渲染"""
        if not self.enable_async:
            # 回退到同步渲染
            return self.render_template_sync(template_string, context, scope_id)
        
        # 在线程池中执行同步渲染
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._thread_pool,
            self.render_template_sync,
            template_string,
            context,
            scope_id
        )
    
    def _process_template_context(self, scope: ThreadSafeTemplateScope, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理模板上下文 - 集成完整的数据处理功能"""
        enhanced_context = {}

        with scope.thread_safe_access():
            for name, value in context.items():
                if self._is_data_source(value):
                    # 数据源注册为代理对象
                    try:
                        enhanced_context[name] = scope.register_data_source(name, value)
                    except Exception as e:
                        self.logger.debug(f"数据源注册失败 {name}: {e}")
                        # 回退到智能数据处理
                        enhanced_context[name] = self._enhance_template_data(value)
                else:
                    # 普通数据使用完整的智能数据处理
                    enhanced_context[name] = self._enhance_template_data(value)

        return enhanced_context

    def _enhance_template_data(self, value: Any) -> Any:
        """
        增强模板数据 - 集成旧模板引擎的完整功能

        使用SmartDataFactory进行智能数据处理
        """
        # 基本类型保持原样
        if isinstance(value, (str, int, float, bool, type(None))):
            return value

        # 尝试使用旧模板引擎的SmartDataFactory
        try:
            from template.components.smart_data_factory import SmartDataFactory, create_smart_modifier

            # 创建智能修改器
            modifier = create_smart_modifier(
                data=value,
                enable_history=True,
                enable_debug=self.enable_debug
            )

            if modifier:
                self.logger.debug(f"创建智能数据修改器: {type(value)}")
                return modifier

        except ImportError as e:
            self.logger.debug(f"SmartDataFactory不可用: {e}")
        except Exception as e:
            self.logger.debug(f"智能修改器创建失败: {e}")

        # 回退到统一数据增强系统
        try:
            from core.unified_data_enhancement import enhance_data, is_enhanced_data

            if is_enhanced_data(value):
                return value

            return enhance_data(value)

        except ImportError:
            self.logger.debug("统一数据增强系统不可用")

        # 最后回退：直接返回原数据
        return value
    
    def _is_data_source(self, value: Any) -> bool:
        """智能检查是否为数据源"""
        # 简化的数据源检查逻辑
        if isinstance(value, str):
            return any(value.startswith(pattern) for pattern in [
                'sqlite://', 'postgresql://', 'mysql://', 'http://', 'https://'
            ]) or any(value.endswith(ext) for ext in [
                '.csv', '.json', '.xml', '.html'
            ])
        
        if isinstance(value, dict):
            data_source_keys = {'base_url', 'connection_string', 'file_path', 'host'}
            return any(key in value for key in data_source_keys)
        
        return False
    
    def _create_smart_loader(self, scope: ThreadSafeTemplateScope):
        """创建完整功能的智能数据加载器"""
        # 尝试创建集成旧模板引擎功能的加载器
        try:
            return ThreadSafeSmartDataLoader(
                data_registry=self.data_registry,
                template_scope=scope,
                enable_legacy_features=True,
                enable_debug=self.enable_debug
            )
        except Exception as e:
            self.logger.warning(f"创建完整功能加载器失败，使用基础版本: {e}")

            # 回退到基础版本
            from core.enhanced_smart_data_loader import EnhancedSmartDataLoader
            return EnhancedSmartDataLoader(
                data_registry=self.data_registry,
                template_scope=scope.base_scope
            )
    
    def shutdown(self):
        """关闭模板引擎"""
        self.logger.info("关闭线程安全模板引擎...")
        
        # 停止清理任务
        self._shutdown_event.set()
        if self._cleanup_task and self._cleanup_task.is_alive():
            self._cleanup_task.join(timeout=5)
        
        # 清理所有作用域
        with self._global_lock:
            for scope_id in list(self._scopes.keys()):
                try:
                    self._cleanup_scope(scope_id)
                except Exception as e:
                    self.logger.error(f"关闭时清理作用域失败 {scope_id}: {e}")
        
        # 关闭线程池
        if self._thread_pool:
            self._thread_pool.shutdown(wait=True)
        
        self.logger.info("线程安全模板引擎已关闭")
    
    def __del__(self):
        """析构函数"""
        try:
            self.shutdown()
        except:
            pass  # 忽略析构时的错误


class ThreadSafeSmartDataLoader:
    """
    线程安全的完整功能智能数据加载器

    集成旧模板引擎的所有强大功能：
    1. SmartDataFactory智能数据处理
    2. JSONPath/XPath路径查询
    3. 智能数据修改器
    4. 操作历史和事务支持
    """

    def __init__(self, data_registry, template_scope: ThreadSafeTemplateScope,
                 enable_legacy_features: bool = True, enable_debug: bool = False):
        self.data_registry = data_registry
        self.template_scope = template_scope
        self.enable_legacy_features = enable_legacy_features
        self.enable_debug = enable_debug
        self.logger = logging.getLogger(self.__class__.__name__)

        # 初始化旧模板引擎组件
        if enable_legacy_features:
            self._init_legacy_components()

    def _init_legacy_components(self):
        """初始化旧模板引擎组件"""
        try:
            # SmartDataFactory
            from template.components.smart_data_factory import SmartDataFactory
            self.smart_factory = SmartDataFactory(enable_debug=self.enable_debug)

            # JSONPath解析器
            from template.components.jsonpath_resolver import JSONPathResolver
            self.jsonpath_resolver = JSONPathResolver()

            # 增强数据类型管理器
            from template.components.enhanced_data_types import EnhancedDataTypeManager
            self.enhanced_data_manager = EnhancedDataTypeManager(enable_debug=self.enable_debug)

            self.logger.debug("旧模板引擎组件初始化成功")

        except ImportError as e:
            self.logger.warning(f"旧模板引擎组件不可用: {e}")
            self.smart_factory = None
            self.jsonpath_resolver = None
            self.enhanced_data_manager = None

    def database(self, connection_string: str):
        """数据库连接器 - 增强版"""
        # 使用基础数据库功能
        from core.enhanced_smart_data_loader import EnhancedSmartDataLoader
        base_loader = EnhancedSmartDataLoader(self.data_registry, self.template_scope.base_scope)
        return base_loader.database(connection_string)

    def api(self, url_or_config):
        """API连接器 - 增强版"""
        from core.enhanced_smart_data_loader import EnhancedSmartDataLoader
        base_loader = EnhancedSmartDataLoader(self.data_registry, self.template_scope.base_scope)
        return base_loader.api(url_or_config)

    def file(self, file_path: str, **options):
        """文件连接器 - 增强版"""
        try:
            # 首先尝试使用基础加载器
            from core.enhanced_smart_data_loader import EnhancedSmartDataLoader
            base_loader = EnhancedSmartDataLoader(self.data_registry, self.template_scope.base_scope)
            return base_loader.file(file_path, **options)
        except Exception as e:
            self.logger.debug(f"基础文件加载器失败: {e}")

            # 回退到简单文件处理
            return self._simple_file_handler(file_path, **options)

    def _simple_file_handler(self, file_path: str, **options):
        """简单文件处理器"""
        class SimpleFileHandler:
            def __init__(self, file_path):
                self.file_path = file_path

            def parse(self):
                """解析文件内容"""
                import json
                import os

                if not os.path.exists(self.file_path):
                    raise FileNotFoundError(f"文件不存在: {self.file_path}")

                # 根据文件扩展名选择解析方式
                if self.file_path.endswith('.json'):
                    with open(self.file_path, 'r', encoding='utf-8') as f:
                        return json.load(f)
                elif self.file_path.endswith('.txt'):
                    with open(self.file_path, 'r', encoding='utf-8') as f:
                        return f.read()
                else:
                    # 默认尝试JSON解析
                    try:
                        with open(self.file_path, 'r', encoding='utf-8') as f:
                            return json.load(f)
                    except json.JSONDecodeError:
                        # 如果JSON解析失败，返回文本内容
                        with open(self.file_path, 'r', encoding='utf-8') as f:
                            return f.read()

            def read(self):
                """读取文件内容"""
                return self.parse()

        return SimpleFileHandler(file_path)

    def memory(self, data):
        """内存数据处理器 - 完整功能版"""
        if self.enable_legacy_features and self.smart_factory:
            try:
                # 使用SmartDataFactory创建智能修改器
                modifier = self.smart_factory.create_modifier(
                    data=data,
                    enable_history=True,
                    enable_debug=self.enable_debug
                )

                if modifier:
                    self.logger.debug(f"创建智能数据修改器: {type(data)}")
                    return modifier

            except Exception as e:
                self.logger.debug(f"智能修改器创建失败: {e}")

        # 回退到基础内存处理
        from core.enhanced_smart_data_loader import EnhancedSmartDataLoader
        base_loader = EnhancedSmartDataLoader(self.data_registry, self.template_scope.base_scope)
        return base_loader.memory(data)

    def smart_data(self, data, data_type=None):
        """智能数据处理器 - 旧模板引擎完整功能"""
        if self.enable_legacy_features and self.smart_factory:
            try:
                return self.smart_factory.create_modifier(
                    data=data,
                    data_type=data_type,
                    enable_history=True,
                    enable_debug=self.enable_debug
                )
            except Exception as e:
                self.logger.error(f"智能数据处理失败: {e}")

        return data

    def jsonpath(self, data, path: str):
        """JSONPath查询 - 旧模板引擎功能"""
        # 首先提取原始数据
        raw_data = self._extract_raw_data(data)

        if self.enable_legacy_features and self.jsonpath_resolver:
            try:
                # 尝试不同的调用方式
                if hasattr(self.jsonpath_resolver, 'resolve'):
                    results = self.jsonpath_resolver.resolve(raw_data, path)
                    if results:
                        return results[0] if len(results) == 1 else results
                elif hasattr(self.jsonpath_resolver, 'resolve_single'):
                    return self.jsonpath_resolver.resolve_single(raw_data, path)
            except Exception as e:
                self.logger.debug(f"旧版JSONPath查询失败: {e}")

        # 简单的路径解析作为回退
        try:
            return self._simple_jsonpath(raw_data, path)
        except Exception as e:
            self.logger.debug(f"简单JSONPath查询失败: {e}")

        return None

    def _extract_raw_data(self, data):
        """提取原始数据，处理SmartJSONData等包装对象"""
        # 如果是SmartJSONData对象，提取原始数据
        if hasattr(data, 'data') and hasattr(data, '__class__'):
            if 'SmartJSONData' in str(data.__class__):
                return data.data
            elif 'SmartXMLData' in str(data.__class__):
                return data.data
            elif 'SmartHTMLData' in str(data.__class__):
                return data.data

        # 如果是其他智能数据对象，尝试获取原始数据
        if hasattr(data, 'get_raw_data'):
            return data.get_raw_data()
        elif hasattr(data, 'raw_data'):
            return data.raw_data
        elif hasattr(data, '_data'):
            return data._data

        # 返回原始数据
        return data

    def _simple_jsonpath(self, data, path: str):
        """简单的JSONPath实现"""
        if not path.startswith('$.'):
            return None

        # 移除开头的 $.
        path = path[2:]

        current = data
        parts = path.split('.')

        for part in parts:
            if '[' in part and ']' in part:
                # 处理数组索引或过滤器
                if part.endswith(']'):
                    if part.startswith('[') and part.endswith(']'):
                        # 纯数组索引 [0]
                        try:
                            index = int(part[1:-1])
                            current = current[index]
                        except (ValueError, IndexError, TypeError):
                            return None
                    else:
                        # 带key的数组索引 key[0]
                        key_part, index_part = part.split('[', 1)
                        try:
                            index = int(index_part.rstrip(']'))
                            current = current[key_part][index]
                        except (ValueError, IndexError, TypeError, KeyError):
                            return None
                else:
                    # 复杂过滤器，暂不支持
                    return None
            else:
                # 简单的key访问
                try:
                    current = current[part]
                except (KeyError, TypeError):
                    return None

        return current

    def enhanced_data(self, data):
        """增强数据类型处理 - 旧模板引擎功能"""
        if self.enable_legacy_features and self.enhanced_data_manager:
            try:
                data_type = self.enhanced_data_manager.detect_data_type(data)
                if data_type in self.enhanced_data_manager.handlers:
                    handler = self.enhanced_data_manager.handlers[data_type]

                    # 检查handler是否有create_enhanced_result方法
                    if hasattr(handler, 'create_enhanced_result'):
                        return handler.create_enhanced_result(data)
                    else:
                        # 如果没有该方法，直接返回处理后的数据
                        # 对于DictHandler，我们可以创建一个增强的字典包装器
                        return self._create_enhanced_dict_wrapper(data, handler)

            except Exception as e:
                self.logger.error(f"增强数据处理失败: {e}")

        return data

    def _create_enhanced_dict_wrapper(self, data, handler):
        """为字典数据创建增强包装器"""
        class EnhancedDictWrapper:
            def __init__(self, data, handler):
                self._data = data
                self._handler = handler

                # 将原始数据的所有属性和方法代理到包装器
                if isinstance(data, dict):
                    for key, value in data.items():
                        setattr(self, key, value)

            def get_by_path(self, path: str):
                """根据路径获取值"""
                return self._handler.get_value_by_path(self._data, path)

            def set_by_path(self, path: str, value):
                """根据路径设置值"""
                return self._handler.set_value_by_path(self._data, path, value)

            def find_by_value(self, value, max_results=10):
                """根据值查找路径"""
                return self._handler.find_paths_by_value(self._data, value, max_results)

            def find_by_key(self, key, max_results=10):
                """根据键查找路径"""
                return self._handler.find_paths_by_key(self._data, key, max_results)

            def __getitem__(self, key):
                if isinstance(self._data, dict):
                    return self._data[key]
                return getattr(self._data, key)

            def __setitem__(self, key, value):
                if isinstance(self._data, dict):
                    self._data[key] = value
                else:
                    setattr(self._data, key, value)

            def __contains__(self, key):
                if isinstance(self._data, dict):
                    return key in self._data
                return hasattr(self._data, key)

            def __iter__(self):
                if isinstance(self._data, dict):
                    return iter(self._data)
                return iter(dir(self._data))

            def __len__(self):
                if isinstance(self._data, dict):
                    return len(self._data)
                return len(dir(self._data))

            def __str__(self):
                return str(self._data)

            def __repr__(self):
                return f"EnhancedDictWrapper({repr(self._data)})"

        return EnhancedDictWrapper(data, handler)

    def remote(self, host_config):
        """远程主机连接器"""
        from core.enhanced_smart_data_loader import EnhancedSmartDataLoader
        base_loader = EnhancedSmartDataLoader(self.data_registry, self.template_scope.base_scope)
        return base_loader.remote(host_config)


# 便利函数
def create_thread_safe_template_engine(**kwargs) -> ThreadSafeTemplateIntegration:
    """创建线程安全的模板引擎"""
    return ThreadSafeTemplateIntegration(**kwargs)
