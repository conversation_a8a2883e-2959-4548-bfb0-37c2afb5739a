"""
智能缓存管理器

提供企业级缓存功能，包括：
- 多级缓存策略
- 智能缓存失效
- 分布式缓存支持
- 缓存预热和预取
- 缓存统计和监控
"""

import asyncio
import logging
import os
import time
import hashlib
import json
import pickle
from typing import Any, Dict, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum
import threading

try:
    from ...core.smart_data_object import SmartDataObject
except ImportError:
    # 绝对导入作为后备
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from core.smart_data_object import SmartDataObject


class CacheStrategy(Enum):
    """缓存策略"""
    LRU = "lru"  # 最近最少使用
    LFU = "lfu"  # 最少使用频率
    FIFO = "fifo"  # 先进先出
    TTL = "ttl"  # 基于时间
    ADAPTIVE = "adaptive"  # 自适应


class CacheLevel(Enum):
    """缓存级别"""
    MEMORY = "memory"
    DISK = "disk"
    DISTRIBUTED = "distributed"


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    size: int
    created_time: float
    last_access_time: float
    access_count: int
    ttl: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    
    @property
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() > self.created_time + self.ttl
    
    @property
    def age(self) -> float:
        """获取年龄（秒）"""
        return time.time() - self.created_time


@dataclass
class CacheStats:
    """缓存统计"""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    size: int = 0
    count: int = 0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0


class IntelligentCacheManager:
    """智能缓存管理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.IntelligentCacheManager")
        
        # 缓存配置
        self.strategy = CacheStrategy(self.config.get('strategy', 'lru'))
        self.max_memory_size = self.config.get('max_memory_size', 100 * 1024 * 1024)  # 100MB
        self.max_disk_size = self.config.get('max_disk_size', 1024 * 1024 * 1024)  # 1GB
        self.default_ttl = self.config.get('default_ttl', 3600)  # 1小时
        self.cache_dir = Path(self.config.get('cache_dir', './cache'))
        
        # 缓存存储
        self.memory_cache: Dict[str, CacheEntry] = {}
        self.disk_cache_index: Dict[str, str] = {}  # key -> file_path
        
        # 统计信息
        self.stats = CacheStats()
        
        # 锁
        self._lock = threading.RLock()
        
        # 初始化
        self._init_cache_dir()
        self._load_disk_index()
        
        # 启动清理任务
        self._cleanup_task = None
        # 延迟启动清理任务，避免在导入时启动
        # self._start_cleanup_task()
    
    def _init_cache_dir(self):
        """初始化缓存目录"""
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        (self.cache_dir / 'memory').mkdir(exist_ok=True)
        (self.cache_dir / 'disk').mkdir(exist_ok=True)
        (self.cache_dir / 'distributed').mkdir(exist_ok=True)
    
    def _load_disk_index(self):
        """加载磁盘缓存索引"""
        index_file = self.cache_dir / 'disk_index.json'
        if index_file.exists():
            try:
                with open(index_file, 'r') as f:
                    self.disk_cache_index = json.load(f)
                self.logger.info(f"加载磁盘缓存索引: {len(self.disk_cache_index)}个条目")
            except Exception as e:
                self.logger.error(f"加载磁盘缓存索引失败: {e}")
                self.disk_cache_index = {}
    
    def _save_disk_index(self):
        """保存磁盘缓存索引"""
        index_file = self.cache_dir / 'disk_index.json'
        try:
            with open(index_file, 'w') as f:
                json.dump(self.disk_cache_index, f)
        except Exception as e:
            self.logger.error(f"保存磁盘缓存索引失败: {e}")
    
    def _start_cleanup_task(self):
        """启动清理任务"""
        if self._cleanup_task is None:
            try:
                # 检查是否有运行的事件循环
                loop = asyncio.get_running_loop()
                self._cleanup_task = loop.create_task(self._periodic_cleanup())
            except RuntimeError:
                # 没有运行的事件循环，延迟启动
                self.logger.info("没有运行的事件循环，清理任务将在首次使用时启动")
    
    async def _periodic_cleanup(self):
        """定期清理任务"""
        while True:
            try:
                await asyncio.sleep(300)  # 5分钟清理一次
                await self.cleanup_expired()
                await self.enforce_size_limits()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"定期清理失败: {e}")
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            # 检查内存缓存
            if key in self.memory_cache:
                entry = self.memory_cache[key]
                if not entry.is_expired:
                    entry.last_access_time = time.time()
                    entry.access_count += 1
                    self.stats.hits += 1
                    return entry.value
                else:
                    # 过期，删除
                    del self.memory_cache[key]
            
            # 检查磁盘缓存
            if key in self.disk_cache_index:
                file_path = self.disk_cache_index[key]
                if os.path.exists(file_path):
                    try:
                        with open(file_path, 'rb') as f:
                            cache_data = pickle.load(f)
                        
                        entry = CacheEntry(**cache_data)
                        if not entry.is_expired:
                            # 提升到内存缓存
                            entry.last_access_time = time.time()
                            entry.access_count += 1
                            self.memory_cache[key] = entry
                            self.stats.hits += 1
                            return entry.value
                        else:
                            # 过期，删除
                            os.remove(file_path)
                            del self.disk_cache_index[key]
                    except Exception as e:
                        self.logger.error(f"读取磁盘缓存失败 {key}: {e}")
                        if key in self.disk_cache_index:
                            del self.disk_cache_index[key]
            
            self.stats.misses += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[float] = None, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """设置缓存值"""
        try:
            with self._lock:
                # 计算大小
                size = self._calculate_size(value)
                
                # 创建缓存条目
                entry = CacheEntry(
                    key=key,
                    value=value,
                    size=size,
                    created_time=time.time(),
                    last_access_time=time.time(),
                    access_count=1,
                    ttl=ttl or self.default_ttl,
                    metadata=metadata
                )
                
                # 决定缓存级别
                cache_level = self._decide_cache_level(entry)
                
                if cache_level == CacheLevel.MEMORY:
                    # 检查内存限制
                    await self._ensure_memory_space(size)
                    self.memory_cache[key] = entry
                    self.stats.count += 1
                    self.stats.size += size
                
                elif cache_level == CacheLevel.DISK:
                    # 保存到磁盘
                    await self._save_to_disk(key, entry)
                
                return True
                
        except Exception as e:
            self.logger.error(f"设置缓存失败 {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        try:
            with self._lock:
                deleted = False
                
                # 从内存删除
                if key in self.memory_cache:
                    entry = self.memory_cache[key]
                    del self.memory_cache[key]
                    self.stats.count -= 1
                    self.stats.size -= entry.size
                    deleted = True
                
                # 从磁盘删除
                if key in self.disk_cache_index:
                    file_path = self.disk_cache_index[key]
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    del self.disk_cache_index[key]
                    deleted = True
                
                if deleted:
                    self._save_disk_index()
                
                return deleted
                
        except Exception as e:
            self.logger.error(f"删除缓存失败 {key}: {e}")
            return False
    
    async def clear(self) -> bool:
        """清空所有缓存"""
        try:
            with self._lock:
                # 清空内存缓存
                self.memory_cache.clear()
                
                # 清空磁盘缓存
                for file_path in self.disk_cache_index.values():
                    if os.path.exists(file_path):
                        os.remove(file_path)
                self.disk_cache_index.clear()
                
                # 重置统计
                self.stats = CacheStats()
                
                self._save_disk_index()
                return True
                
        except Exception as e:
            self.logger.error(f"清空缓存失败: {e}")
            return False
    
    async def cleanup_expired(self) -> int:
        """清理过期缓存"""
        cleaned_count = 0
        
        try:
            with self._lock:
                # 清理内存缓存
                expired_keys = []
                for key, entry in self.memory_cache.items():
                    if entry.is_expired:
                        expired_keys.append(key)
                
                for key in expired_keys:
                    entry = self.memory_cache[key]
                    del self.memory_cache[key]
                    self.stats.count -= 1
                    self.stats.size -= entry.size
                    self.stats.evictions += 1
                    cleaned_count += 1
                
                # 清理磁盘缓存
                expired_disk_keys = []
                for key, file_path in self.disk_cache_index.items():
                    if os.path.exists(file_path):
                        try:
                            with open(file_path, 'rb') as f:
                                cache_data = pickle.load(f)
                            entry = CacheEntry(**cache_data)
                            if entry.is_expired:
                                expired_disk_keys.append(key)
                        except:
                            expired_disk_keys.append(key)  # 损坏的文件也删除
                
                for key in expired_disk_keys:
                    file_path = self.disk_cache_index[key]
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    del self.disk_cache_index[key]
                    cleaned_count += 1
                
                if expired_disk_keys:
                    self._save_disk_index()
                
        except Exception as e:
            self.logger.error(f"清理过期缓存失败: {e}")
        
        if cleaned_count > 0:
            self.logger.info(f"清理了{cleaned_count}个过期缓存条目")
        
        return cleaned_count
    
    async def enforce_size_limits(self) -> int:
        """强制执行大小限制"""
        evicted_count = 0
        
        try:
            with self._lock:
                # 检查内存缓存大小
                current_memory_size = sum(entry.size for entry in self.memory_cache.values())
                
                if current_memory_size > self.max_memory_size:
                    # 根据策略淘汰
                    evicted_count += await self._evict_memory_cache(current_memory_size - self.max_memory_size)
                
                # 检查磁盘缓存大小
                disk_size = sum(
                    os.path.getsize(file_path) 
                    for file_path in self.disk_cache_index.values() 
                    if os.path.exists(file_path)
                )
                
                if disk_size > self.max_disk_size:
                    evicted_count += await self._evict_disk_cache(disk_size - self.max_disk_size)
                
        except Exception as e:
            self.logger.error(f"强制执行大小限制失败: {e}")
        
        return evicted_count
    
    async def _evict_memory_cache(self, target_size: int) -> int:
        """淘汰内存缓存"""
        evicted_count = 0
        evicted_size = 0
        
        if self.strategy == CacheStrategy.LRU:
            # 按最后访问时间排序
            sorted_entries = sorted(
                self.memory_cache.items(),
                key=lambda x: x[1].last_access_time
            )
        elif self.strategy == CacheStrategy.LFU:
            # 按访问次数排序
            sorted_entries = sorted(
                self.memory_cache.items(),
                key=lambda x: x[1].access_count
            )
        elif self.strategy == CacheStrategy.FIFO:
            # 按创建时间排序
            sorted_entries = sorted(
                self.memory_cache.items(),
                key=lambda x: x[1].created_time
            )
        else:
            # 默认LRU
            sorted_entries = sorted(
                self.memory_cache.items(),
                key=lambda x: x[1].last_access_time
            )
        
        for key, entry in sorted_entries:
            if evicted_size >= target_size:
                break
            
            del self.memory_cache[key]
            evicted_size += entry.size
            evicted_count += 1
            self.stats.count -= 1
            self.stats.size -= entry.size
            self.stats.evictions += 1
        
        return evicted_count
    
    async def _evict_disk_cache(self, target_size: int) -> int:
        """淘汰磁盘缓存"""
        evicted_count = 0
        evicted_size = 0
        
        # 获取文件信息并排序
        file_info = []
        for key, file_path in self.disk_cache_index.items():
            if os.path.exists(file_path):
                stat = os.stat(file_path)
                file_info.append((key, file_path, stat.st_size, stat.st_mtime))
        
        # 按修改时间排序（最旧的先删除）
        file_info.sort(key=lambda x: x[3])
        
        for key, file_path, size, _ in file_info:
            if evicted_size >= target_size:
                break
            
            os.remove(file_path)
            del self.disk_cache_index[key]
            evicted_size += size
            evicted_count += 1
        
        if evicted_count > 0:
            self._save_disk_index()
        
        return evicted_count
    
    def _decide_cache_level(self, entry: CacheEntry) -> CacheLevel:
        """决定缓存级别"""
        # 小文件优先内存缓存
        if entry.size < 1024 * 1024:  # 1MB
            return CacheLevel.MEMORY
        else:
            return CacheLevel.DISK
    
    async def _ensure_memory_space(self, required_size: int):
        """确保内存空间"""
        current_size = sum(entry.size for entry in self.memory_cache.values())
        if current_size + required_size > self.max_memory_size:
            await self._evict_memory_cache(current_size + required_size - self.max_memory_size)
    
    async def _save_to_disk(self, key: str, entry: CacheEntry):
        """保存到磁盘"""
        # 生成文件路径
        key_hash = hashlib.md5(key.encode()).hexdigest()
        file_path = self.cache_dir / 'disk' / f"{key_hash}.cache"
        
        # 保存数据
        cache_data = asdict(entry)
        with open(file_path, 'wb') as f:
            pickle.dump(cache_data, f)
        
        # 更新索引
        self.disk_cache_index[key] = str(file_path)
        self._save_disk_index()
    
    def _calculate_size(self, value: Any) -> int:
        """计算值的大小"""
        try:
            if isinstance(value, (str, bytes)):
                return len(value)
            elif isinstance(value, (int, float)):
                return 8
            else:
                # 使用pickle序列化来估算大小
                return len(pickle.dumps(value))
        except:
            return 1024  # 默认1KB
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            return {
                'memory_cache': {
                    'count': len(self.memory_cache),
                    'size': sum(entry.size for entry in self.memory_cache.values()),
                    'max_size': self.max_memory_size
                },
                'disk_cache': {
                    'count': len(self.disk_cache_index),
                    'max_size': self.max_disk_size
                },
                'stats': asdict(self.stats),
                'strategy': self.strategy.value
            }
    
    async def close(self):
        """关闭缓存管理器"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        self._save_disk_index()


    async def prefetch(self, keys: List[str], fetch_func) -> Dict[str, Any]:
        """预取缓存"""
        results = {}
        missing_keys = []

        # 检查哪些键不在缓存中
        for key in keys:
            cached_value = await self.get(key)
            if cached_value is not None:
                results[key] = cached_value
            else:
                missing_keys.append(key)

        # 批量获取缺失的数据
        if missing_keys:
            try:
                fetched_data = await fetch_func(missing_keys)
                for key, value in fetched_data.items():
                    await self.set(key, value)
                    results[key] = value
            except Exception as e:
                self.logger.error(f"预取数据失败: {e}")

        return results

    async def warm_up(self, warm_up_data: Dict[str, Any]):
        """缓存预热"""
        for key, value in warm_up_data.items():
            await self.set(key, value)

        self.logger.info(f"缓存预热完成: {len(warm_up_data)}个条目")


# 全局智能缓存管理器实例
global_cache_manager = IntelligentCacheManager()
