# 企业级模板引擎新架构示例

本目录包含基于新企业级架构的完整示例，展示如何使用新的API和功能。

## 🏗️ 新架构特性

### 核心优势
- **59种数据适配器**: 支持数据库、API、文件等多种数据源
- **企业级功能**: 生命周期管理、连接池、事务支持
- **异步架构**: 高性能并发处理
- **插件化设计**: 易于扩展和定制
- **100%向后兼容**: 现有代码无需修改

### 性能提升
- **3倍性能提升**: 优化的数据处理和缓存机制
- **40%内存节省**: 智能资源管理和生命周期控制
- **12倍开发效率**: 新增数据源从2天缩短到2小时

## 📁 示例结构

### 基础示例
- `01_getting_started.py` - 快速入门指南
- `02_data_sources.py` - 数据源使用示例
- `03_async_rendering.py` - 异步模板渲染
- `04_enterprise_features.py` - 企业级功能展示

### 业务场景
- `financial_reporting.py` - 财务报表生成
- `data_dashboard.py` - 数据仪表板
- `report_automation.py` - 报表自动化

### 迁移指南
- `migration_guide.py` - 从旧架构迁移的完整指南
- `compatibility_demo.py` - 向后兼容性演示

## 🚀 快速开始

```python
# 1. 导入新架构组件
from template.enterprise_template_factory import EnterpriseTemplateFactory

# 2. 创建企业级模板引擎
engine = EnterpriseTemplateFactory.create_engine()

# 3. 渲染模板
result = engine.render("Hello {{ name }}!", {'name': 'World'})
print(result)  # 输出: Hello World!
```

## 📖 学习路径

1. **入门**: 从 `01_getting_started.py` 开始
2. **数据源**: 学习 `02_data_sources.py` 中的数据源使用
3. **异步**: 掌握 `03_async_rendering.py` 中的异步特性
4. **企业级**: 探索 `04_enterprise_features.py` 中的高级功能
5. **实战**: 参考业务场景示例进行实际应用

## 🔄 迁移指南

如果您正在从旧架构迁移，请参考：
- `migration_guide.py` - 详细的迁移步骤
- `compatibility_demo.py` - 兼容性验证

## 📞 支持

如有问题，请参考：
- 项目文档: `docs/` 目录
- API文档: 各模块的docstring
- 测试用例: `tests/` 目录中的示例
