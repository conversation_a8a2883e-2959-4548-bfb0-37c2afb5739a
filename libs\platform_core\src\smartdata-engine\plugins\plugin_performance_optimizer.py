"""
插件性能优化器

提供插件加载性能优化和缓存管理
"""

import logging
import time
import threading
from typing import Any, Dict, List, Optional, Set
from dataclasses import dataclass, asdict
from pathlib import Path
import importlib
import sys


@dataclass
class PluginLoadStats:
    """插件加载统计"""
    plugin_id: str
    load_time: float
    memory_usage: int
    success: bool
    error: Optional[str] = None
    dependencies: List[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


@dataclass
class PerformanceMetrics:
    """性能指标"""
    total_plugins: int = 0
    loaded_plugins: int = 0
    failed_plugins: int = 0
    total_load_time: float = 0.0
    average_load_time: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0
    memory_usage: int = 0
    
    @property
    def success_rate(self) -> float:
        """加载成功率"""
        return self.loaded_plugins / self.total_plugins if self.total_plugins > 0 else 0.0
    
    @property
    def cache_hit_rate(self) -> float:
        """缓存命中率"""
        total_requests = self.cache_hits + self.cache_misses
        return self.cache_hits / total_requests if total_requests > 0 else 0.0


class PluginPerformanceOptimizer:
    """插件性能优化器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.PluginPerformanceOptimizer")
        
        # 性能统计
        self.metrics = PerformanceMetrics()
        self.load_stats: Dict[str, PluginLoadStats] = {}
        
        # 缓存
        self._module_cache: Dict[str, Any] = {}
        self._definition_cache: Dict[str, Dict[str, Any]] = {}
        self._dependency_cache: Dict[str, List[str]] = {}
        
        # 预加载配置
        self._preload_enabled = True
        self._preload_plugins: Set[str] = set()
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 性能阈值
        self.slow_load_threshold = 1.0  # 1秒
        self.memory_threshold = 50 * 1024 * 1024  # 50MB
    
    def optimize_plugin_loading(self, plugin_registry) -> Dict[str, Any]:
        """优化插件加载"""
        start_time = time.time()
        
        try:
            # 分析插件依赖
            dependency_graph = self._analyze_dependencies(plugin_registry)
            
            # 优化加载顺序
            optimized_order = self._optimize_load_order(dependency_graph)
            
            # 预加载关键插件
            preloaded_count = self._preload_critical_plugins(plugin_registry, optimized_order)
            
            # 启用延迟加载
            lazy_loaded_count = self._enable_lazy_loading(plugin_registry)
            
            optimization_time = time.time() - start_time
            
            return {
                'success': True,
                'optimization_time': optimization_time,
                'dependency_graph': dependency_graph,
                'optimized_order': optimized_order,
                'preloaded_plugins': preloaded_count,
                'lazy_loaded_plugins': lazy_loaded_count,
                'performance_improvement': self._calculate_improvement()
            }
            
        except Exception as e:
            self.logger.error(f"插件加载优化失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'optimization_time': time.time() - start_time
            }
    
    def _analyze_dependencies(self, plugin_registry) -> Dict[str, List[str]]:
        """分析插件依赖关系"""
        dependency_graph = {}
        
        try:
            # 获取所有插件定义
            all_definitions = plugin_registry.get_all_plugin_definitions()
            
            for plugin_def in all_definitions:
                plugin_id = plugin_def['id']
                dependencies = plugin_def.get('dependencies', [])
                optional_deps = plugin_def.get('optional_dependencies', [])
                
                # 只包含必需依赖
                dependency_graph[plugin_id] = dependencies
                
                # 缓存依赖信息
                self._dependency_cache[plugin_id] = dependencies + optional_deps
            
            self.logger.info(f"分析了{len(dependency_graph)}个插件的依赖关系")
            return dependency_graph
            
        except Exception as e:
            self.logger.error(f"依赖分析失败: {e}")
            return {}
    
    def _optimize_load_order(self, dependency_graph: Dict[str, List[str]]) -> List[str]:
        """优化加载顺序（拓扑排序）"""
        try:
            # 拓扑排序算法
            visited = set()
            temp_visited = set()
            result = []
            
            def dfs(node):
                if node in temp_visited:
                    # 检测到循环依赖
                    self.logger.warning(f"检测到循环依赖: {node}")
                    return
                
                if node in visited:
                    return
                
                temp_visited.add(node)
                
                # 访问依赖
                for dep in dependency_graph.get(node, []):
                    if dep in dependency_graph:  # 只处理存在的插件
                        dfs(dep)
                
                temp_visited.remove(node)
                visited.add(node)
                result.append(node)
            
            # 对所有节点执行DFS
            for plugin_id in dependency_graph:
                if plugin_id not in visited:
                    dfs(plugin_id)
            
            # 反转结果（依赖项在前）
            optimized_order = result[::-1]
            
            self.logger.info(f"优化加载顺序: {len(optimized_order)}个插件")
            return optimized_order
            
        except Exception as e:
            self.logger.error(f"加载顺序优化失败: {e}")
            return list(dependency_graph.keys())
    
    def _preload_critical_plugins(self, plugin_registry, optimized_order: List[str]) -> int:
        """预加载关键插件"""
        if not self._preload_enabled:
            return 0
        
        preloaded_count = 0
        
        try:
            # 确定关键插件（高优先级、无依赖、常用）
            critical_plugins = self._identify_critical_plugins(plugin_registry, optimized_order)
            
            for plugin_id in critical_plugins:
                try:
                    start_time = time.time()
                    
                    # 预加载插件模块
                    success = self._preload_plugin_module(plugin_registry, plugin_id)
                    
                    load_time = time.time() - start_time
                    
                    if success:
                        preloaded_count += 1
                        self._preload_plugins.add(plugin_id)
                        
                        # 记录统计
                        self.load_stats[plugin_id] = PluginLoadStats(
                            plugin_id=plugin_id,
                            load_time=load_time,
                            memory_usage=self._estimate_memory_usage(plugin_id),
                            success=True
                        )
                        
                        self.logger.debug(f"预加载插件成功: {plugin_id} ({load_time:.3f}s)")
                    
                except Exception as e:
                    self.logger.warning(f"预加载插件失败 {plugin_id}: {e}")
                    
                    # 记录失败统计
                    self.load_stats[plugin_id] = PluginLoadStats(
                        plugin_id=plugin_id,
                        load_time=0.0,
                        memory_usage=0,
                        success=False,
                        error=str(e)
                    )
            
            self.logger.info(f"预加载了{preloaded_count}个关键插件")
            return preloaded_count
            
        except Exception as e:
            self.logger.error(f"关键插件预加载失败: {e}")
            return preloaded_count
    
    def _identify_critical_plugins(self, plugin_registry, optimized_order: List[str]) -> List[str]:
        """识别关键插件"""
        critical_plugins = []
        
        try:
            all_definitions = plugin_registry.get_all_plugin_definitions()
            
            # 按优先级排序
            sorted_plugins = sorted(
                all_definitions,
                key=lambda x: x.get('priority', 50),
                reverse=True
            )
            
            # 选择前几个高优先级插件
            for plugin_def in sorted_plugins[:5]:  # 前5个
                plugin_id = plugin_def['id']
                
                # 检查是否无依赖或依赖较少
                dependencies = plugin_def.get('dependencies', [])
                if len(dependencies) <= 2:  # 依赖少于等于2个
                    critical_plugins.append(plugin_id)
            
            # 添加常用插件
            common_plugins = ['database_processor', 'api_processor', 'remote_file_processor']
            for plugin_id in common_plugins:
                if plugin_id in optimized_order and plugin_id not in critical_plugins:
                    critical_plugins.append(plugin_id)
            
            return critical_plugins[:8]  # 最多8个关键插件
            
        except Exception as e:
            self.logger.error(f"识别关键插件失败: {e}")
            return []
    
    def _preload_plugin_module(self, plugin_registry, plugin_id: str) -> bool:
        """预加载插件模块"""
        try:
            # 获取插件定义
            plugin_def = plugin_registry.get_plugin_definition(plugin_id)
            if not plugin_def:
                return False
            
            # 构建模块路径
            module_path = f"plugins.{plugin_def.get('category', 'unknown')}.{plugin_def.get('module_file', plugin_id)}"
            
            # 检查缓存
            if module_path in self._module_cache:
                self.metrics.cache_hits += 1
                return True
            
            # 导入模块
            module = importlib.import_module(module_path)
            
            # 缓存模块
            self._module_cache[module_path] = module
            self.metrics.cache_misses += 1
            
            return True
            
        except Exception as e:
            self.logger.debug(f"预加载模块失败 {plugin_id}: {e}")
            return False
    
    def _enable_lazy_loading(self, plugin_registry) -> int:
        """启用延迟加载"""
        lazy_loaded_count = 0
        
        try:
            all_definitions = plugin_registry.get_all_plugin_definitions()
            
            for plugin_def in all_definitions:
                plugin_id = plugin_def['id']
                
                # 跳过已预加载的插件
                if plugin_id in self._preload_plugins:
                    continue
                
                # 为非关键插件启用延迟加载
                if self._setup_lazy_loading(plugin_registry, plugin_id):
                    lazy_loaded_count += 1
            
            self.logger.info(f"为{lazy_loaded_count}个插件启用了延迟加载")
            return lazy_loaded_count
            
        except Exception as e:
            self.logger.error(f"延迟加载设置失败: {e}")
            return lazy_loaded_count
    
    def _setup_lazy_loading(self, plugin_registry, plugin_id: str) -> bool:
        """设置延迟加载"""
        try:
            # 这里可以实现延迟加载的具体逻辑
            # 例如：创建代理对象、延迟导入等
            
            # 简化实现：标记为延迟加载
            plugin_def = plugin_registry.get_plugin_definition(plugin_id)
            if plugin_def:
                plugin_def['lazy_load'] = True
                return True
            
            return False
            
        except Exception as e:
            self.logger.debug(f"延迟加载设置失败 {plugin_id}: {e}")
            return False
    
    def _estimate_memory_usage(self, plugin_id: str) -> int:
        """估算内存使用量"""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            return process.memory_info().rss
            
        except ImportError:
            # 如果没有psutil，返回估算值
            return 1024 * 1024  # 1MB估算
        except Exception:
            return 0
    
    def _calculate_improvement(self) -> Dict[str, float]:
        """计算性能改进"""
        try:
            # 计算各种性能指标的改进
            total_load_time = sum(stat.load_time for stat in self.load_stats.values() if stat.success)

            # 估算改进（这里是简化计算）
            preload_improvement = len(self._preload_plugins) * 0.1  # 每个预加载插件节省0.1秒

            # 安全计算缓存命中率
            total_cache_requests = self.metrics.cache_hits + self.metrics.cache_misses
            cache_hit_rate = self.metrics.cache_hits / total_cache_requests if total_cache_requests > 0 else 0.0
            cache_improvement = cache_hit_rate * 0.05  # 缓存命中率带来的改进

            return {
                'load_time_reduction': preload_improvement,
                'cache_efficiency': cache_improvement,
                'memory_optimization': 0.0,  # 待实现
                'overall_improvement': preload_improvement + cache_improvement
            }

        except Exception as e:
            self.logger.error(f"性能改进计算失败: {e}")
            return {
                'load_time_reduction': 0.0,
                'cache_efficiency': 0.0,
                'memory_optimization': 0.0,
                'overall_improvement': 0.0
            }
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        with self._lock:
            # 更新指标
            self.metrics.total_plugins = len(self.load_stats)
            self.metrics.loaded_plugins = sum(1 for stat in self.load_stats.values() if stat.success)
            self.metrics.failed_plugins = sum(1 for stat in self.load_stats.values() if not stat.success)
            self.metrics.total_load_time = sum(stat.load_time for stat in self.load_stats.values())
            self.metrics.average_load_time = (
                self.metrics.total_load_time / self.metrics.loaded_plugins 
                if self.metrics.loaded_plugins > 0 else 0.0
            )
            
            # 识别慢加载插件
            slow_plugins = [
                stat.plugin_id for stat in self.load_stats.values()
                if stat.success and stat.load_time > self.slow_load_threshold
            ]
            
            # 识别高内存使用插件
            high_memory_plugins = [
                stat.plugin_id for stat in self.load_stats.values()
                if stat.success and stat.memory_usage > self.memory_threshold
            ]
            
            return {
                'metrics': asdict(self.metrics),
                'load_stats': {k: asdict(v) for k, v in self.load_stats.items()},
                'preloaded_plugins': list(self._preload_plugins),
                'slow_plugins': slow_plugins,
                'high_memory_plugins': high_memory_plugins,
                'cache_status': {
                    'module_cache_size': len(self._module_cache),
                    'definition_cache_size': len(self._definition_cache),
                    'dependency_cache_size': len(self._dependency_cache)
                },
                'recommendations': self._generate_recommendations()
            }
    
    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        try:
            # 基于统计数据生成建议
            if self.metrics.cache_hit_rate < 0.8:
                recommendations.append("考虑增加缓存大小以提高命中率")
            
            if self.metrics.average_load_time > self.slow_load_threshold:
                recommendations.append("考虑优化插件加载逻辑或启用更多预加载")
            
            slow_plugins = [
                stat.plugin_id for stat in self.load_stats.values()
                if stat.success and stat.load_time > self.slow_load_threshold
            ]
            if slow_plugins:
                recommendations.append(f"优化慢加载插件: {', '.join(slow_plugins[:3])}")
            
            if len(self._preload_plugins) < 3:
                recommendations.append("考虑增加关键插件的预加载")
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"生成建议失败: {e}")
            return ["无法生成优化建议"]
    
    def clear_cache(self):
        """清理缓存"""
        with self._lock:
            self._module_cache.clear()
            self._definition_cache.clear()
            self._dependency_cache.clear()
            
            self.logger.info("插件缓存已清理")
    
    def enable_preload(self, enabled: bool = True):
        """启用/禁用预加载"""
        self._preload_enabled = enabled
        self.logger.info(f"插件预加载已{'启用' if enabled else '禁用'}")


# 全局性能优化器实例
global_plugin_optimizer = PluginPerformanceOptimizer()
