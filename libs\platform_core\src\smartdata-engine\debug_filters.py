#!/usr/bin/env python3
"""
调试过滤器问题
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from template.thread_safe_template_integration import ThreadSafeTemplateIntegration

def debug_filters():
    """调试过滤器问题"""
    print("=== 调试过滤器问题 ===")
    
    # 创建线程安全的模板引擎
    engine = ThreadSafeTemplateIntegration(
        enable_async=True,
        enable_debug=True,
        isolation_level='thread',
        cleanup_interval=60,
        max_scope_lifetime=300
    )
    
    # 测试数据
    test_data = {
        "numbers": [1, 2, 3, 4, 5]
    }
    
    # 测试1：基础 flatten
    print("\n测试1：基础 flatten")
    try:
        template = "{{ [numbers, [6, 7]] | flatten | join(', ') }}"
        result = engine.render_template_sync(template, test_data, 'flatten_test')
        print(f"✅ flatten 测试成功: {result}")
    except Exception as e:
        print(f"❌ flatten 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试2：基础 sum
    print("\n测试2：基础 sum")
    try:
        template = "{{ numbers | sum }}"
        result = engine.render_template_sync(template, test_data, 'sum_test')
        print(f"✅ sum 测试成功: {result}")
    except Exception as e:
        print(f"❌ sum 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试3：unique
    print("\n测试3：unique")
    try:
        template = "{{ [1, 2, 2, 3, 3] | unique | join(', ') }}"
        result = engine.render_template_sync(template, test_data, 'unique_test')
        print(f"✅ unique 测试成功: {result}")
    except Exception as e:
        print(f"❌ unique 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 关闭引擎
    engine.shutdown()

if __name__ == "__main__":
    debug_filters()
