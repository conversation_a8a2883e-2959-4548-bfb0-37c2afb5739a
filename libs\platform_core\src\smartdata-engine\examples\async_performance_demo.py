"""
异步性能演示

展示异步架构的性能优势，对比同步和异步操作的性能差异
"""

import asyncio
import time
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../'))

from core.async_data_proxy import Async<PERSON>ataProxy
from core.async_template_scope import AsyncTemplateScope
from core.adapters.database.async_sqlite import AsyncSQLiteAdapter
from core.enterprise_data_architecture import DataRegistry


class MockAsyncConnectionPool:
    """模拟异步连接池"""

    def __init__(self, connection_source):
        self.connection_source = connection_source

    def acquire(self):
        return MockAsyncConnectionContext(self.connection_source)

    async def close(self):
        pass


class MockAsyncConnectionContext:
    """模拟异步连接上下文"""

    def __init__(self, connection_source):
        self.connection_source = connection_source
        self.connection = None

    async def __aenter__(self):
        await asyncio.sleep(0.001)  # 模拟获取连接时间
        self.connection = f"mock_connection_{self.connection_source}"
        return self.connection

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await asyncio.sleep(0.001)  # 模拟释放连接时间


class MockAsyncAdapter:
    """模拟异步适配器用于演示"""

    def __init__(self, delay_ms: float = 100):
        self.delay_ms = delay_ms / 1000.0
        self.operation_count = 0

    async def create_async_connection(self, source):
        await asyncio.sleep(0.01)
        return f"mock_connection_{source}"

    async def close_async_connection(self, connection):
        await asyncio.sleep(0.01)

    async def create_async_pool(self, connection_source, min_size=5, max_size=20):
        await asyncio.sleep(0.01)
        return MockAsyncConnectionPool(connection_source)

    async def async_query(self, connection, sql, params=None):
        await asyncio.sleep(self.delay_ms)
        self.operation_count += 1
        return [{'id': i, 'name': f'user_{i}', 'email': f'user_{i}@example.com'} for i in range(10)]

    async def async_execute(self, connection, sql, params=None):
        await asyncio.sleep(self.delay_ms)
        self.operation_count += 1
        return 1


async def demo_single_vs_parallel():
    """演示单个查询 vs 并行查询的性能差异"""
    print("🔄 演示1: 单个查询 vs 并行查询性能对比")
    print("=" * 60)
    
    adapter = MockAsyncAdapter(delay_ms=200)  # 模拟200ms延迟
    proxy = AsyncDataProxy(':memory:', adapter)
    
    # 准备5个查询
    queries = [
        {'sql': 'SELECT * FROM users', 'params': None},
        {'sql': 'SELECT * FROM orders', 'params': None},
        {'sql': 'SELECT * FROM products', 'params': None},
        {'sql': 'SELECT * FROM categories', 'params': None},
        {'sql': 'SELECT * FROM reviews', 'params': None}
    ]
    
    # 串行执行
    print("📊 串行执行5个查询...")
    start_time = time.time()
    sequential_results = []
    for query in queries:
        result = await proxy.query(query['sql'], query['params'])
        sequential_results.append(result)
    sequential_time = (time.time() - start_time) * 1000
    
    # 并行执行
    print("⚡ 并行执行5个查询...")
    start_time = time.time()
    parallel_results = await proxy.parallel_queries(queries)
    parallel_time = (time.time() - start_time) * 1000
    
    # 结果对比
    speedup = sequential_time / parallel_time
    
    print(f"\n📈 性能对比结果:")
    print(f"  串行执行时间: {sequential_time:.0f}ms")
    print(f"  并行执行时间: {parallel_time:.0f}ms")
    print(f"  性能提升: {speedup:.1f}x")
    print(f"  时间节省: {sequential_time - parallel_time:.0f}ms ({((sequential_time - parallel_time) / sequential_time * 100):.1f}%)")
    
    await proxy.close()
    print()


async def demo_template_scope_performance():
    """演示模板作用域的并行数据获取"""
    print("🎨 演示2: 模板作用域并行数据获取")
    print("=" * 60)
    
    # 使用真实的SQLite适配器
    registry = DataRegistry()
    registry.register_adapter(AsyncSQLiteAdapter)
    
    async with AsyncTemplateScope("performance_demo", registry) as scope:
        # 注册3个内存数据库
        print("📊 注册3个数据库连接...")
        db1 = await scope.register_async_data_source('users_db', ':memory:')
        db2 = await scope.register_async_data_source('orders_db', ':memory:')
        db3 = await scope.register_async_data_source('products_db', ':memory:')
        
        # 创建测试数据
        print("🔧 创建测试数据...")
        await db1.execute("CREATE TABLE users (id INTEGER, name TEXT, email TEXT)")
        await db2.execute("CREATE TABLE orders (id INTEGER, user_id INTEGER, amount REAL)")
        await db3.execute("CREATE TABLE products (id INTEGER, name TEXT, price REAL)")
        
        # 插入测试数据
        for i in range(100):
            await db1.execute("INSERT INTO users VALUES (?, ?, ?)", 
                            {'1': i, '2': f'User_{i}', '3': f'user_{i}@example.com'})
            await db2.execute("INSERT INTO orders VALUES (?, ?, ?)", 
                            {'1': i, '2': i % 10, '3': 100.0 + i})
            await db3.execute("INSERT INTO products VALUES (?, ?, ?)", 
                            {'1': i, '2': f'Product_{i}', '3': 10.0 + i})
        
        # 模拟模板数据获取
        template_config = {
            'users_db': {'sql': 'SELECT COUNT(*) as user_count FROM users'},
            'orders_db': {'sql': 'SELECT SUM(amount) as total_revenue FROM orders'},
            'products_db': {'sql': 'SELECT AVG(price) as avg_price FROM products'}
        }
        
        print("⚡ 并行获取模板数据...")
        start_time = time.time()
        template_data = await scope.render_template_data(template_config)
        render_time = (time.time() - start_time) * 1000
        
        print(f"\n📊 模板数据获取结果:")
        print(f"  用户总数: {template_data.get('users_db', [{}])[0].get('user_count', 'N/A')}")
        print(f"  总收入: ${template_data.get('orders_db', [{}])[0].get('total_revenue', 'N/A'):.2f}")
        print(f"  平均价格: ${template_data.get('products_db', [{}])[0].get('avg_price', 'N/A'):.2f}")
        print(f"  数据获取时间: {render_time:.0f}ms")
        
        # 获取性能统计
        perf_stats = scope.get_performance_stats()
        print(f"  总操作数: {perf_stats['scope_info']['operation_count']}")
        print(f"  平均执行时间: {perf_stats['scope_info']['avg_execution_time']:.2f}ms")
    
    print()


async def demo_stream_query_performance():
    """演示流式查询的性能优势"""
    print("🌊 演示3: 流式查询处理大数据集")
    print("=" * 60)
    
    # 使用真实的SQLite适配器
    adapter = AsyncSQLiteAdapter()
    proxy = AsyncDataProxy(':memory:', adapter)
    
    # 创建大数据表
    print("🔧 创建大数据表...")
    await proxy.execute("CREATE TABLE large_dataset (id INTEGER PRIMARY KEY, data TEXT, value REAL)")
    
    # 批量插入数据
    print("📊 插入10000条测试数据...")
    insert_operations = [
        {
            'type': 'execute',
            'sql': 'INSERT INTO large_dataset (data, value) VALUES (?, ?)',
            'params': {'1': f'data_row_{i}', '2': i * 1.5}
        }
        for i in range(10000)
    ]
    
    start_time = time.time()
    await proxy.batch(insert_operations)
    insert_time = (time.time() - start_time) * 1000
    print(f"  批量插入耗时: {insert_time:.0f}ms")
    
    # 对比普通查询和流式查询
    print("\n📊 对比普通查询 vs 流式查询:")
    
    # 普通查询（一次性加载所有数据）
    print("  🔄 普通查询（一次性加载）...")
    start_time = time.time()
    normal_result = await proxy.query("SELECT * FROM large_dataset")
    normal_time = (time.time() - start_time) * 1000
    print(f"    加载 {len(normal_result.data)} 条记录，耗时: {normal_time:.0f}ms")
    
    # 流式查询（逐块处理）
    print("  ⚡ 流式查询（逐块处理）...")
    start_time = time.time()
    processed_count = 0
    chunk_count = 0
    
    async for result_chunk in proxy.stream_query("SELECT * FROM large_dataset", chunk_size=1000):
        if result_chunk.success:
            processed_count += len(result_chunk.data)
            chunk_count += 1
            
            # 模拟处理时间
            await asyncio.sleep(0.001)
            
            if chunk_count <= 3:  # 只显示前3个块的信息
                print(f"    处理第{chunk_count}块: {len(result_chunk.data)}条记录")
    
    stream_time = (time.time() - start_time) * 1000
    print(f"    流式处理 {processed_count} 条记录，耗时: {stream_time:.0f}ms")
    
    print(f"\n📈 流式查询优势:")
    print(f"  内存使用: 显著降低（分块处理 vs 一次性加载）")
    print(f"  响应时间: 更快开始处理数据")
    print(f"  可扩展性: 可处理任意大小的数据集")
    
    await proxy.close()
    print()


async def demo_concurrent_connections():
    """演示并发连接的性能"""
    print("🔗 演示4: 并发连接性能测试")
    print("=" * 60)
    
    adapter = MockAsyncAdapter(delay_ms=50)
    
    # 创建多个并发连接
    connection_count = 50
    print(f"🔧 创建 {connection_count} 个并发连接...")
    
    proxies = []
    for i in range(connection_count):
        proxy = AsyncDataProxy(f':memory:db_{i}', adapter)
        proxies.append(proxy)
    
    # 并发执行查询
    print("⚡ 并发执行查询...")
    start_time = time.time()
    
    async def single_query(proxy, query_id):
        return await proxy.query(f"SELECT * FROM table_{query_id}")
    
    tasks = [single_query(proxy, i) for i, proxy in enumerate(proxies)]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    concurrent_time = (time.time() - start_time) * 1000
    
    # 统计结果
    successful_results = [r for r in results if not isinstance(r, Exception)]
    failed_results = [r for r in results if isinstance(r, Exception)]
    
    print(f"\n📊 并发测试结果:")
    print(f"  总连接数: {connection_count}")
    print(f"  成功查询: {len(successful_results)}")
    print(f"  失败查询: {len(failed_results)}")
    print(f"  总耗时: {concurrent_time:.0f}ms")
    print(f"  平均每个查询: {concurrent_time / connection_count:.1f}ms")
    print(f"  QPS (每秒查询数): {connection_count / (concurrent_time / 1000):.0f}")
    
    # 清理资源
    for proxy in proxies:
        await proxy.close()
    
    print()


async def main():
    """主演示函数"""
    print("🚀 异步架构性能演示")
    print("=" * 80)
    print("本演示将展示异步架构相比同步架构的性能优势")
    print()
    
    try:
        await demo_single_vs_parallel()
        await demo_template_scope_performance()
        await demo_stream_query_performance()
        await demo_concurrent_connections()
        
        print("🎉 演示完成！")
        print("=" * 80)
        print("📈 异步架构的主要优势:")
        print("  • 并行处理: 多个操作同时执行，显著提升性能")
        print("  • 流式处理: 处理大数据集时内存效率更高")
        print("  • 高并发: 支持大量并发连接和操作")
        print("  • 资源效率: 更好的CPU和内存利用率")
        print("  • 响应性: 更快的响应时间和更好的用户体验")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    # 运行异步演示
    asyncio.run(main())
