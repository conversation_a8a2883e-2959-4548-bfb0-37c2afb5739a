"""
智能远程文件加载器

提供统一的远程文件加载接口，集成企业级功能
"""

import logging
import asyncio
from typing import Any, Dict, List, Optional, Union, AsyncIterator

try:
    from ...core.smart_data_object import SmartDataObject
except ImportError:
    # 绝对导入作为后备
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from core.smart_data_object import SmartDataObject
from .remote_processor import EnterpriseRemoteFileProcessor
from .intelligent_cache import global_cache_manager
from .connection_pool import global_connection_pool


class SmartRemoteFileLoader:
    """智能远程文件加载器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.SmartRemoteFileLoader")
        self.processor = EnterpriseRemoteFileProcessor()
        
        # 默认配置
        self.default_config = {
            'timeout': 30.0,
            'max_retries': 3,
            'enable_cache': True,
            'cache_ttl': 3600,
            'enable_progress': False,
            'concurrent_downloads': 5
        }
    
    def load(self, data: Any, options: Dict[str, Any] = None) -> SmartDataObject:
        """同步加载远程文件"""
        try:
            # 在新的事件循环中运行异步方法
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self.load_async(data, options))
            finally:
                loop.close()
        except Exception as e:
            self.logger.error(f"同步加载失败: {e}")
            return SmartDataObject({
                'success': False,
                'error': str(e),
                'loader': 'SmartRemoteFileLoader'
            })
    
    async def load_async(self, data: Any, options: Dict[str, Any] = None) -> SmartDataObject:
        """异步加载远程文件"""
        try:
            # 合并配置
            merged_options = {**self.default_config, **(options or {})}
            
            # 使用企业级处理器处理
            result = await self.processor.process(data, merged_options)
            
            # 添加加载器信息
            if hasattr(result, 'data') and isinstance(result.data, dict):
                result.data['loader'] = 'SmartRemoteFileLoader'
            
            return result
            
        except Exception as e:
            self.logger.error(f"异步加载失败: {e}")
            return SmartDataObject({
                'success': False,
                'error': str(e),
                'loader': 'SmartRemoteFileLoader'
            })
    
    async def download_file(self, url: str, options: Dict[str, Any] = None) -> SmartDataObject:
        """下载单个文件"""
        return await self.load_async(url, options)
    
    async def download_files(self, urls: List[str], options: Dict[str, Any] = None) -> SmartDataObject:
        """批量下载文件"""
        return await self.load_async(urls, options)
    
    async def download_with_progress(self, url: str, options: Dict[str, Any] = None) -> SmartDataObject:
        """带进度的下载"""
        progress_options = {**(options or {}), 'enable_progress': True}
        return await self.load_async(url, progress_options)
    
    async def resume_download(self, url: str, local_path: str, options: Dict[str, Any] = None) -> SmartDataObject:
        """断点续传下载"""
        resume_options = {
            **(options or {}), 
            'enable_resume': True,
            'local_path': local_path
        }
        return await self.load_async(url, resume_options)
    
    async def upload_file(self, url: str, data: bytes, options: Dict[str, Any] = None) -> SmartDataObject:
        """上传文件"""
        try:
            from urllib.parse import urlparse
            parsed_url = urlparse(url)
            protocol = parsed_url.scheme.lower()
            
            # 获取协议处理器
            handler = self.processor._get_protocol_handler(protocol)
            
            # 上传文件
            success = await handler.upload(url, data, {
                'auth': options.get('auth') if options else None,
                'timeout': options.get('timeout', 30.0) if options else 30.0,
                'headers': options.get('headers') if options else None
            })
            
            return SmartDataObject({
                'success': success,
                'url': url,
                'uploaded_size': len(data),
                'operation': 'upload',
                'loader': 'SmartRemoteFileLoader'
            })
            
        except Exception as e:
            self.logger.error(f"上传失败 {url}: {e}")
            return SmartDataObject({
                'success': False,
                'url': url,
                'error': str(e),
                'operation': 'upload',
                'loader': 'SmartRemoteFileLoader'
            })
    
    async def list_remote_files(self, url: str, options: Dict[str, Any] = None) -> SmartDataObject:
        """列出远程文件"""
        try:
            from urllib.parse import urlparse
            parsed_url = urlparse(url)
            protocol = parsed_url.scheme.lower()
            
            # 获取协议处理器
            handler = self.processor._get_protocol_handler(protocol)
            
            # 列出文件
            files = await handler.list_files(url, {
                'auth': options.get('auth') if options else None,
                'timeout': options.get('timeout', 30.0) if options else 30.0
            })
            
            return SmartDataObject({
                'success': True,
                'url': url,
                'files': files,
                'file_count': len(files),
                'operation': 'list',
                'loader': 'SmartRemoteFileLoader'
            })
            
        except Exception as e:
            self.logger.error(f"列表失败 {url}: {e}")
            return SmartDataObject({
                'success': False,
                'url': url,
                'error': str(e),
                'operation': 'list',
                'loader': 'SmartRemoteFileLoader'
            })
    
    def configure(self, config: Dict[str, Any]):
        """配置加载器"""
        self.default_config.update(config)
        self.logger.info(f"配置已更新: {config}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return global_cache_manager.get_stats()
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接池统计"""
        return global_connection_pool.get_stats()
    
    async def clear_cache(self) -> bool:
        """清空缓存"""
        return await global_cache_manager.clear()
    
    async def cleanup_cache(self) -> int:
        """清理过期缓存"""
        return await global_cache_manager.cleanup_expired()
    
    def get_supported_protocols(self) -> List[str]:
        """获取支持的协议"""
        return self.processor.supported_protocols
    
    def get_loader_info(self) -> Dict[str, Any]:
        """获取加载器信息"""
        return {
            'name': 'SmartRemoteFileLoader',
            'version': '2.0.0',
            'description': '智能远程文件加载器，提供企业级远程文件处理能力',
            'supported_protocols': self.get_supported_protocols(),
            'capabilities': [
                'download',
                'upload',
                'list_files',
                'batch_download',
                'progress_download',
                'resume_download',
                'intelligent_caching',
                'connection_pooling',
                'concurrent_processing'
            ],
            'default_config': self.default_config
        }
    
    async def close(self):
        """关闭加载器"""
        await self.processor.close()
        self.logger.info("智能远程文件加载器已关闭")


# 全局智能远程文件加载器实例
global_remote_loader = SmartRemoteFileLoader()


# 便捷函数
async def download_file_async(url: str, options: Dict[str, Any] = None) -> SmartDataObject:
    """异步下载文件"""
    return await global_remote_loader.download_file(url, options)


def download_file(url: str, options: Dict[str, Any] = None) -> SmartDataObject:
    """同步下载文件"""
    return global_remote_loader.load(url, options)


async def download_files_async(urls: List[str], options: Dict[str, Any] = None) -> SmartDataObject:
    """异步批量下载文件"""
    return await global_remote_loader.download_files(urls, options)


def download_files(urls: List[str], options: Dict[str, Any] = None) -> SmartDataObject:
    """同步批量下载文件"""
    return global_remote_loader.load(urls, options)


async def upload_file_async(url: str, data: bytes, options: Dict[str, Any] = None) -> SmartDataObject:
    """异步上传文件"""
    return await global_remote_loader.upload_file(url, data, options)


async def list_remote_files_async(url: str, options: Dict[str, Any] = None) -> SmartDataObject:
    """异步列出远程文件"""
    return await global_remote_loader.list_remote_files(url, options)


def get_cache_stats() -> Dict[str, Any]:
    """获取缓存统计"""
    return global_remote_loader.get_cache_stats()


def get_connection_stats() -> Dict[str, Any]:
    """获取连接池统计"""
    return global_remote_loader.get_connection_stats()


async def clear_cache_async() -> bool:
    """异步清空缓存"""
    return await global_remote_loader.clear_cache()


async def cleanup_cache_async() -> int:
    """异步清理过期缓存"""
    return await global_remote_loader.cleanup_cache()
