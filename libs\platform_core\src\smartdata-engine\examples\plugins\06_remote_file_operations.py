#!/usr/bin/env python3
"""
SmartData模板引擎远程文件处理插件使用示例

展示远程文件处理插件的完整功能：
1. 云存储操作 - AWS S3, Azure Blob, Google Cloud Storage
2. FTP/SFTP操作 - 文件传输、目录管理
3. HTTP/HTTPS文件 - 下载、上传、API集成
4. 企业级功能 - 加密、压缩、版本控制
5. 批量操作 - 并发处理、进度监控
6. 模板引擎集成 - 远程文件内容模板化
"""

import sys
import os
from datetime import datetime
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.template_ext import create_template_engine

def remote_file_operations_examples():
    """远程文件操作完整示例"""
    print("=== SmartData模板引擎远程文件处理插件示例 ===")
    
    # 创建模板引擎
    engine = create_template_engine()
    
    # 1. 云存储操作示例
    print("\n☁️ 1. 云存储操作示例")
    
    # 定义云存储配置
    cloud_configs = {
        'aws_s3': {
            'provider': 'aws_s3',
            'access_key': 'your_access_key',
            'secret_key': 'your_secret_key',
            'region': 'us-west-2',
            'bucket': 'my-data-bucket'
        },
        'azure_blob': {
            'provider': 'azure_blob',
            'account_name': 'mystorageaccount',
            'account_key': 'your_account_key',
            'container': 'my-container'
        },
        'gcs': {
            'provider': 'google_cloud_storage',
            'project_id': 'my-project',
            'credentials_path': '/path/to/credentials.json',
            'bucket': 'my-gcs-bucket'
        }
    }
    
    cloud_storage_template = """
{%- set operations = [
    {
        'type': 'upload',
        'local_path': '/local/data/report.pdf',
        'remote_path': 'reports/2024/07/report.pdf',
        'metadata': {'department': 'finance', 'confidential': 'true'}
    },
    {
        'type': 'download',
        'remote_path': 'templates/invoice_template.html',
        'local_path': '/local/templates/invoice.html'
    },
    {
        'type': 'list',
        'remote_path': 'logs/2024/07/',
        'recursive': True,
        'filter': '*.log'
    },
    {
        'type': 'sync',
        'local_path': '/local/backup/',
        'remote_path': 'backups/daily/',
        'direction': 'upload'
    }
] -%}

云存储操作:
=========

{%- for provider, config in cloud_configs.items() %}
{{ provider.upper() }} 存储操作:
{%- set cloud_result = sd.remote_file.cloud_storage(
    operations=operations,
    config=config
) -%}

{%- if cloud_result.success -%}
✅ {{ provider }} 操作成功

📊 操作统计:
- 总操作数: {{ cloud_result.data.total_operations }}
- 成功操作: {{ cloud_result.data.successful_operations }}
- 失败操作: {{ cloud_result.data.failed_operations }}
- 传输数据量: {{ cloud_result.data.total_bytes | filesizeformat }}
- 平均速度: {{ cloud_result.data.avg_speed }}MB/s

📁 操作详情:
{%- for operation in cloud_result.data.operation_details %}
{{ operation.type.upper() }}: {{ operation.path }}
  - 状态: {{ operation.status }}
  - 大小: {{ operation.size | filesizeformat }}
  - 耗时: {{ operation.duration }}ms
  - 速度: {{ operation.speed }}MB/s
  {%- if operation.metadata %}
  - 元数据: {{ operation.metadata | tojson }}
  {%- endif %}
{%- endfor %}

🔒 安全特性:
- 传输加密: {{ cloud_result.data.security.encryption_in_transit }}
- 存储加密: {{ cloud_result.data.security.encryption_at_rest }}
- 访问控制: {{ cloud_result.data.security.access_control }}
- 审计日志: {{ cloud_result.data.security.audit_logging }}

💰 成本分析:
- 存储成本: ${{ cloud_result.data.cost.storage_cost }}
- 传输成本: ${{ cloud_result.data.cost.transfer_cost }}
- API调用成本: ${{ cloud_result.data.cost.api_cost }}
- 总成本: ${{ cloud_result.data.cost.total_cost }}
{%- else -%}
❌ {{ provider }} 操作失败: {{ cloud_result.error }}
{%- endif -%}

---
{%- endfor %}
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(cloud_storage_template, {
        'cloud_configs': cloud_configs
    })
    print(result)
    
    # 2. FTP/SFTP操作示例
    print("\n📡 2. FTP/SFTP操作示例")
    
    ftp_template = """
{%- set ftp_configs = {
    'ftp': {
        'protocol': 'ftp',
        'host': 'ftp.example.com',
        'port': 21,
        'username': 'ftpuser',
        'password': 'ftppass',
        'passive_mode': True
    },
    'sftp': {
        'protocol': 'sftp',
        'host': 'sftp.example.com',
        'port': 22,
        'username': 'sftpuser',
        'private_key_path': '/path/to/private_key',
        'host_key_verification': True
    }
} -%}

FTP/SFTP文件传输:
===============

{%- for protocol, config in ftp_configs.items() %}
{{ protocol.upper() }} 连接:
{%- set ftp_result = sd.remote_file.ftp_operations(
    operations=[
        {
            'type': 'connect',
            'test_connection': True
        },
        {
            'type': 'upload',
            'local_path': '/local/data/export.csv',
            'remote_path': '/remote/data/export.csv',
            'create_dirs': True
        },
        {
            'type': 'download',
            'remote_path': '/remote/configs/app.conf',
            'local_path': '/local/configs/app.conf'
        },
        {
            'type': 'list_directory',
            'remote_path': '/remote/logs/',
            'detailed': True
        },
        {
            'type': 'sync_directory',
            'local_path': '/local/backup/',
            'remote_path': '/remote/backup/',
            'direction': 'bidirectional',
            'delete_extra': False
        }
    ],
    config=config
) -%}

{%- if ftp_result.success -%}
✅ {{ protocol.upper() }} 连接成功

🔗 连接信息:
- 服务器: {{ ftp_result.data.connection.host }}:{{ ftp_result.data.connection.port }}
- 协议版本: {{ ftp_result.data.connection.protocol_version }}
- 连接状态: {{ ftp_result.data.connection.status }}
- 连接时间: {{ ftp_result.data.connection.connect_time }}ms

📁 目录列表:
{%- for file in ftp_result.data.directory_listing %}
{{ file.name }}
  - 类型: {{ file.type }}
  - 大小: {{ file.size | filesizeformat }}
  - 修改时间: {{ file.modified_time }}
  - 权限: {{ file.permissions }}
{%- endfor %}

📤 文件传输:
{%- for transfer in ftp_result.data.file_transfers %}
{{ transfer.operation }}: {{ transfer.file_path }}
  - 状态: {{ transfer.status }}
  - 传输大小: {{ transfer.transferred_bytes | filesizeformat }}
  - 传输速度: {{ transfer.speed }}KB/s
  - 耗时: {{ transfer.duration }}s
  - 完整性校验: {{ transfer.checksum_verified }}
{%- endfor %}

🔄 同步结果:
{%- for sync in ftp_result.data.sync_operations %}
同步: {{ sync.local_path }} ↔ {{ sync.remote_path }}
  - 上传文件: {{ sync.uploaded_files }}
  - 下载文件: {{ sync.downloaded_files }}
  - 跳过文件: {{ sync.skipped_files }}
  - 删除文件: {{ sync.deleted_files }}
  - 同步状态: {{ sync.status }}
{%- endfor %}

⚡ 性能统计:
- 总传输量: {{ ftp_result.data.performance.total_bytes | filesizeformat }}
- 平均速度: {{ ftp_result.data.performance.avg_speed }}KB/s
- 连接复用: {{ ftp_result.data.performance.connection_reuse }}
- 并发传输: {{ ftp_result.data.performance.concurrent_transfers }}
{%- else -%}
❌ {{ protocol.upper() }} 操作失败: {{ ftp_result.error }}
{%- endif -%}

---
{%- endfor %}
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(ftp_template)
    print(result)
    
    # 3. HTTP/HTTPS文件操作示例
    print("\n🌐 3. HTTP/HTTPS文件操作示例")
    
    http_template = """
{%- set http_config = {
    'timeout': 30,
    'max_retries': 3,
    'verify_ssl': True,
    'follow_redirects': True,
    'user_agent': 'SmartData-Engine/1.0'
} -%}

HTTP/HTTPS文件操作:
=================

{%- set http_result = sd.remote_file.http_operations(
    operations=[
        {
            'type': 'download',
            'url': 'https://api.example.com/data/export.json',
            'local_path': '/local/data/api_export.json',
            'headers': {'Authorization': 'Bearer your_token'},
            'verify_checksum': True
        },
        {
            'type': 'upload',
            'url': 'https://upload.example.com/files',
            'local_path': '/local/reports/monthly_report.pdf',
            'method': 'POST',
            'form_field': 'file',
            'additional_data': {'category': 'reports', 'month': '2024-07'}
        },
        {
            'type': 'stream_download',
            'url': 'https://data.example.com/large_dataset.csv',
            'local_path': '/local/data/large_dataset.csv',
            'chunk_size': 8192,
            'progress_callback': True
        },
        {
            'type': 'api_call',
            'url': 'https://api.example.com/files/metadata',
            'method': 'GET',
            'headers': {'Authorization': 'Bearer your_token'},
            'params': {'path': '/data/', 'recursive': 'true'}
        }
    ],
    config=http_config
) -%}

{%- if http_result.success -%}
✅ HTTP操作成功

📊 请求统计:
- 总请求数: {{ http_result.data.total_requests }}
- 成功请求: {{ http_result.data.successful_requests }}
- 失败请求: {{ http_result.data.failed_requests }}
- 重试次数: {{ http_result.data.retry_count }}
- 平均响应时间: {{ http_result.data.avg_response_time }}ms

📥 下载操作:
{%- for download in http_result.data.downloads %}
{{ download.url }}
  - 状态码: {{ download.status_code }}
  - 文件大小: {{ download.file_size | filesizeformat }}
  - 下载速度: {{ download.download_speed }}MB/s
  - 内容类型: {{ download.content_type }}
  - 校验和: {{ download.checksum }}
  - 本地路径: {{ download.local_path }}
{%- endfor %}

📤 上传操作:
{%- for upload in http_result.data.uploads %}
{{ upload.url }}
  - 状态码: {{ upload.status_code }}
  - 上传大小: {{ upload.upload_size | filesizeformat }}
  - 上传速度: {{ upload.upload_speed }}MB/s
  - 响应数据: {{ upload.response_data | truncate(100) }}
{%- endfor %}

🌊 流式下载:
{%- for stream in http_result.data.stream_downloads %}
{{ stream.url }}
  - 总大小: {{ stream.total_size | filesizeformat }}
  - 已下载: {{ stream.downloaded_size | filesizeformat }}
  - 进度: {{ stream.progress }}%
  - 当前速度: {{ stream.current_speed }}MB/s
  - 预计剩余时间: {{ stream.eta }}s
{%- endfor %}

🔌 API调用:
{%- for api in http_result.data.api_calls %}
{{ api.method }} {{ api.url }}
  - 状态码: {{ api.status_code }}
  - 响应时间: {{ api.response_time }}ms
  - 响应大小: {{ api.response_size }} 字节
  - 响应头: {{ api.response_headers | tojson }}
  - 响应数据: {{ api.response_data | truncate(200) }}
{%- endfor %}

🔒 安全信息:
- SSL验证: {{ http_result.data.security.ssl_verified }}
- 证书信息: {{ http_result.data.security.certificate_info }}
- 加密协议: {{ http_result.data.security.tls_version }}
- 认证方式: {{ http_result.data.security.auth_method }}

📈 性能分析:
- DNS解析时间: {{ http_result.data.performance.dns_time }}ms
- 连接建立时间: {{ http_result.data.performance.connect_time }}ms
- SSL握手时间: {{ http_result.data.performance.ssl_time }}ms
- 首字节时间: {{ http_result.data.performance.ttfb }}ms
- 总传输时间: {{ http_result.data.performance.total_time }}ms
{%- else -%}
❌ HTTP操作失败: {{ http_result.error }}
{%- endif -%}
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(http_template)
    print(result)
    
    # 4. 企业级功能示例
    print("\n🏢 4. 企业级功能示例")
    
    enterprise_template = """
{%- set enterprise_config = {
    'enable_encryption': True,
    'enable_compression': True,
    'enable_versioning': True,
    'enable_audit_logging': True,
    'enable_bandwidth_throttling': True,
    'max_bandwidth': '10MB/s'
} -%}

企业级远程文件功能:
================

{%- set enterprise_result = sd.remote_file.enterprise_features(
    operations=[
        {
            'type': 'encrypted_transfer',
            'source': '/local/sensitive/financial_data.xlsx',
            'destination': 's3://secure-bucket/encrypted/financial_data.xlsx.enc',
            'encryption_algorithm': 'AES-256-GCM',
            'key_management': 'aws_kms'
        },
        {
            'type': 'compressed_backup',
            'source_directory': '/local/project/',
            'destination': 'sftp://backup.example.com/backups/project_backup.tar.gz',
            'compression_algorithm': 'gzip',
            'compression_level': 6
        },
        {
            'type': 'versioned_sync',
            'local_path': '/local/documents/',
            'remote_path': 's3://versioned-bucket/documents/',
            'version_control': True,
            'conflict_resolution': 'timestamp'
        },
        {
            'type': 'batch_processing',
            'file_list': [
                '/local/data/file1.csv',
                '/local/data/file2.csv',
                '/local/data/file3.csv'
            ],
            'destination_pattern': 's3://data-bucket/processed/{filename}',
            'parallel_uploads': 5,
            'progress_monitoring': True
        }
    ],
    config=enterprise_config
) -%}

{%- if enterprise_result.success -%}
✅ 企业级功能执行成功

🔐 加密传输:
{%- for encryption in enterprise_result.data.encrypted_transfers %}
文件: {{ encryption.filename }}
  - 加密算法: {{ encryption.algorithm }}
  - 密钥管理: {{ encryption.key_management }}
  - 原始大小: {{ encryption.original_size | filesizeformat }}
  - 加密大小: {{ encryption.encrypted_size | filesizeformat }}
  - 加密时间: {{ encryption.encryption_time }}ms
  - 传输状态: {{ encryption.transfer_status }}
{%- endfor %}

🗜️ 压缩备份:
{%- for backup in enterprise_result.data.compressed_backups %}
备份: {{ backup.source_path }}
  - 压缩算法: {{ backup.compression_algorithm }}
  - 压缩级别: {{ backup.compression_level }}
  - 原始大小: {{ backup.original_size | filesizeformat }}
  - 压缩大小: {{ backup.compressed_size | filesizeformat }}
  - 压缩比: {{ backup.compression_ratio }}%
  - 备份时间: {{ backup.backup_duration }}s
{%- endfor %}

📚 版本控制:
{%- for version in enterprise_result.data.versioned_files %}
文件: {{ version.filename }}
  - 当前版本: {{ version.current_version }}
  - 历史版本数: {{ version.version_count }}
  - 最后修改: {{ version.last_modified }}
  - 冲突解决: {{ version.conflict_resolution }}
  - 同步状态: {{ version.sync_status }}
{%- endfor %}

⚡ 批量处理:
{%- for batch in enterprise_result.data.batch_operations %}
批次: {{ batch.batch_id }}
  - 文件数量: {{ batch.file_count }}
  - 并发数: {{ batch.parallel_count }}
  - 完成文件: {{ batch.completed_files }}
  - 失败文件: {{ batch.failed_files }}
  - 总进度: {{ batch.progress }}%
  - 平均速度: {{ batch.avg_speed }}MB/s
  
  文件详情:
  {%- for file in batch.file_details %}
  - {{ file.filename }}: {{ file.status }} ({{ file.progress }}%)
  {%- endfor %}
{%- endfor %}

📊 性能监控:
- 带宽使用: {{ enterprise_result.data.performance.bandwidth_usage }}MB/s
- 带宽限制: {{ enterprise_result.data.performance.bandwidth_limit }}MB/s
- CPU使用率: {{ enterprise_result.data.performance.cpu_usage }}%
- 内存使用: {{ enterprise_result.data.performance.memory_usage }}MB
- 网络延迟: {{ enterprise_result.data.performance.network_latency }}ms

📋 审计日志:
{%- for log in enterprise_result.data.audit_logs %}
- {{ log.timestamp }}: {{ log.operation }}
  用户: {{ log.user }}
  文件: {{ log.file_path }}
  状态: {{ log.status }}
  详情: {{ log.details }}
{%- endfor %}

🔒 安全合规:
- 数据分类: {{ enterprise_result.data.compliance.data_classification }}
- 合规标准: {{ enterprise_result.data.compliance.standards | join(', ') }}
- 访问控制: {{ enterprise_result.data.compliance.access_control }}
- 数据驻留: {{ enterprise_result.data.compliance.data_residency }}
- 审计就绪: {{ enterprise_result.data.compliance.audit_ready }}

💰 成本优化:
- 存储成本: ${{ enterprise_result.data.cost_optimization.storage_cost }}
- 传输成本: ${{ enterprise_result.data.cost_optimization.transfer_cost }}
- 计算成本: ${{ enterprise_result.data.cost_optimization.compute_cost }}
- 优化建议: {{ enterprise_result.data.cost_optimization.recommendations | join(', ') }}
{%- else -%}
❌ 企业级功能执行失败: {{ enterprise_result.error }}
{%- endif -%}
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(enterprise_template)
    print(result)
    
    print("\n🎉 远程文件处理插件示例完成！")
    
    print("\n📊 功能总结:")
    print("☁️ 云存储: aws_s3, azure_blob, google_cloud_storage, alibaba_oss")
    print("📡 文件传输: ftp, sftp, scp, rsync")
    print("🌐 HTTP操作: download, upload, stream, api_integration")
    print("🏢 企业功能: encryption, compression, versioning, audit_logging")
    print("⚡ 批量处理: parallel_operations, progress_monitoring, error_handling")
    
    print("\n💡 使用要点:")
    print("✅ 统一接口 - sd.remote_file() 调用所有远程文件操作")
    print("✅ 多协议支持 - 支持主流云存储和传输协议")
    print("✅ 企业级安全 - 加密、审计、合规性支持")
    print("✅ 高性能 - 并发传输、断点续传、带宽控制")
    print("✅ 智能优化 - 自动压缩、成本优化、性能调优")

if __name__ == "__main__":
    remote_file_operations_examples()
