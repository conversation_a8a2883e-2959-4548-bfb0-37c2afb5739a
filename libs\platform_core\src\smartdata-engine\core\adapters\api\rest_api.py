"""
REST API数据适配器

支持HTTP/HTTPS REST API数据源的同步和异步访问
"""

from typing import Any, Dict, List, Optional, Union, AsyncIterator
import logging
import json
import time
import xml.etree.ElementTree as ET
from urllib.parse import urljoin, urlparse

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../'))

from core.unified_adapter import UnifiedDataAdapter
from core.adapters.base import ConnectionInfo

# 尝试导入HTTP客户端库
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    requests = None

try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False
    aiohttp = None


class RestAPIAdapter(UnifiedDataAdapter):
    """
    REST API数据适配器
    
    支持REST API特有功能：
    - HTTP/HTTPS请求
    - 认证支持（Basic, Bearer Token, API Key）
    - 请求重试和超时
    - 响应缓存
    - 分页数据处理
    - 同步和异步支持
    """
    
    def __init__(self):
        super().__init__()
        if not REQUESTS_AVAILABLE:
            self.logger.warning("requests未安装，同步REST API功能受限")
        if not AIOHTTP_AVAILABLE:
            self.logger.warning("aiohttp未安装，异步REST API功能受限")
        
        # 默认配置
        self.default_timeout = 30
        self.default_retries = 3
        self.default_headers = {
            'User-Agent': 'SmartData-Engine/1.0',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    
    def supported_types(self) -> List[str]:
        """返回支持的数据类型列表"""
        return [
            'rest_api',
            'http_api',
            'https_api',
            'api_endpoint',
            'web_api',
            'json_api',
            'xml_api',
            'graphql',
            'graphql_api',
            'soap_api'
        ]
    
    def _is_supported_connection_string(self, connection_string: str) -> bool:
        """检查是否支持的连接字符串"""
        return connection_string.startswith(('http://', 'https://'))
    
    def _is_supported_connection_object(self, connection: Any) -> bool:
        """检查是否支持的连接对象"""
        # 检查是否是API配置对象
        return (isinstance(connection, dict) and 
                ('base_url' in connection or 'url' in connection))
    
    def can_handle(self, data_source: Any) -> bool:
        """检查是否能处理指定的数据源"""
        if isinstance(data_source, str):
            return self._is_supported_connection_string(data_source)
        else:
            return self._is_supported_connection_object(data_source)
    
    def _build_operations(self) -> Dict[str, callable]:
        """构建API特有操作列表"""
        operations = {
            'get': self._sync_get,
            'post': self._sync_post,
            'put': self._sync_put,
            'delete': self._sync_delete,
            'patch': self._sync_patch,
            'graphql': self._sync_graphql,
        }
        
        # 添加异步操作
        async_ops = self.get_async_operations()
        for name, func in async_ops.items():
            operations[name] = func
        
        return operations
    
    def _parse_api_config(self, connection_source: Any) -> Dict[str, Any]:
        """解析API配置"""
        if isinstance(connection_source, str):
            # 简单URL字符串
            return {
                'base_url': connection_source,
                'headers': self.default_headers.copy(),
                'timeout': self.default_timeout,
                'retries': self.default_retries,
                'auth': None
            }
        elif isinstance(connection_source, dict):
            # 详细配置对象
            config = {
                'base_url': connection_source.get('base_url') or connection_source.get('url'),
                'headers': {**self.default_headers, **connection_source.get('headers', {})},
                'timeout': connection_source.get('timeout', self.default_timeout),
                'retries': connection_source.get('retries', self.default_retries),
                'auth': connection_source.get('auth')
            }
            
            # 处理认证
            if 'api_key' in connection_source:
                config['headers']['Authorization'] = f"Bearer {connection_source['api_key']}"
            elif 'token' in connection_source:
                config['headers']['Authorization'] = f"Bearer {connection_source['token']}"
            
            return config
        else:
            raise ValueError(f"不支持的API配置类型: {type(connection_source)}")
    
    # ========================================================================
    # 同步方法实现
    # ========================================================================
    
    def _sync_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """同步查询实现 - 映射为GET请求"""
        # 将SQL查询映射为REST API调用
        # 这里简化处理，实际应用中需要更复杂的映射逻辑
        endpoint = sql.strip()
        return self._sync_get(connection, endpoint, params)
    
    def _sync_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """同步执行实现 - 映射为POST请求"""
        endpoint = sql.strip()
        result = self._sync_post(connection, endpoint, params)
        return 1 if result else 0
    
    def _sync_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """同步事务实现 - 批量API调用"""
        results = []
        total_affected = 0
        
        for operation in operations:
            method = operation.get('method', 'GET').upper()
            endpoint = operation['endpoint']
            data = operation.get('data')
            
            if method == 'GET':
                result = self._sync_get(connection, endpoint, data)
                results.append(result)
            elif method == 'POST':
                result = self._sync_post(connection, endpoint, data)
                results.append(result)
                total_affected += 1
            elif method == 'PUT':
                result = self._sync_put(connection, endpoint, data)
                results.append(result)
                total_affected += 1
            elif method == 'DELETE':
                result = self._sync_delete(connection, endpoint, data)
                results.append(result)
                total_affected += 1
        
        return {
            'results': results,
            'total_operations': len(operations),
            'total_affected': total_affected,
            'success': True
        }
    
    def _sync_get(self, connection: Any, endpoint: str, params: Dict = None) -> List[Dict]:
        """同步GET请求"""
        if not REQUESTS_AVAILABLE:
            raise ImportError("requests未安装，无法执行同步HTTP请求")
        
        config = self._parse_api_config(connection)
        url = urljoin(config['base_url'], endpoint)
        
        try:
            response = requests.get(
                url,
                params=params,
                headers=config['headers'],
                timeout=config['timeout'],
                auth=config['auth']
            )
            response.raise_for_status()
            
            # 尝试解析响应内容
            content_type = response.headers.get('content-type', '').lower()

            if 'application/json' in content_type or 'text/json' in content_type:
                # JSON响应
                try:
                    data = response.json()
                    if isinstance(data, list):
                        return data
                    elif isinstance(data, dict):
                        return [data]
                    else:
                        return [{'data': data}]
                except json.JSONDecodeError:
                    return [{'text': response.text}]
            elif 'application/xml' in content_type or 'text/xml' in content_type:
                # XML响应
                try:
                    return self._parse_xml_response(response.text)
                except Exception:
                    return [{'xml': response.text}]
            else:
                # 其他格式
                return [{'text': response.text}]
                
        except Exception as e:
            self.logger.error(f"同步GET请求失败 {url}: {e}")
            raise
    
    def _sync_post(self, connection: Any, endpoint: str, data: Dict = None) -> Dict:
        """同步POST请求"""
        if not REQUESTS_AVAILABLE:
            raise ImportError("requests未安装，无法执行同步HTTP请求")
        
        config = self._parse_api_config(connection)
        url = urljoin(config['base_url'], endpoint)
        
        try:
            response = requests.post(
                url,
                json=data,
                headers=config['headers'],
                timeout=config['timeout'],
                auth=config['auth']
            )
            response.raise_for_status()
            
            try:
                return response.json()
            except json.JSONDecodeError:
                return {'text': response.text, 'status_code': response.status_code}
                
        except Exception as e:
            self.logger.error(f"同步POST请求失败 {url}: {e}")
            raise
    
    def _sync_put(self, connection: Any, endpoint: str, data: Dict = None) -> Dict:
        """同步PUT请求"""
        if not REQUESTS_AVAILABLE:
            raise ImportError("requests未安装，无法执行同步HTTP请求")
        
        config = self._parse_api_config(connection)
        url = urljoin(config['base_url'], endpoint)
        
        try:
            response = requests.put(
                url,
                json=data,
                headers=config['headers'],
                timeout=config['timeout'],
                auth=config['auth']
            )
            response.raise_for_status()
            
            try:
                return response.json()
            except json.JSONDecodeError:
                return {'text': response.text, 'status_code': response.status_code}
                
        except Exception as e:
            self.logger.error(f"同步PUT请求失败 {url}: {e}")
            raise
    
    def _sync_delete(self, connection: Any, endpoint: str, params: Dict = None) -> Dict:
        """同步DELETE请求"""
        if not REQUESTS_AVAILABLE:
            raise ImportError("requests未安装，无法执行同步HTTP请求")
        
        config = self._parse_api_config(connection)
        url = urljoin(config['base_url'], endpoint)
        
        try:
            response = requests.delete(
                url,
                params=params,
                headers=config['headers'],
                timeout=config['timeout'],
                auth=config['auth']
            )
            response.raise_for_status()
            
            try:
                return response.json()
            except json.JSONDecodeError:
                return {'text': response.text, 'status_code': response.status_code}
                
        except Exception as e:
            self.logger.error(f"同步DELETE请求失败 {url}: {e}")
            raise
    
    def _sync_patch(self, connection: Any, endpoint: str, data: Dict = None) -> Dict:
        """同步PATCH请求"""
        if not REQUESTS_AVAILABLE:
            raise ImportError("requests未安装，无法执行同步HTTP请求")
        
        config = self._parse_api_config(connection)
        url = urljoin(config['base_url'], endpoint)
        
        try:
            response = requests.patch(
                url,
                json=data,
                headers=config['headers'],
                timeout=config['timeout'],
                auth=config['auth']
            )
            response.raise_for_status()
            
            try:
                return response.json()
            except json.JSONDecodeError:
                return {'text': response.text, 'status_code': response.status_code}
                
        except Exception as e:
            self.logger.error(f"同步PATCH请求失败 {url}: {e}")
            raise

    def _parse_xml_response(self, xml_text: str) -> List[Dict]:
        """解析XML响应为字典列表"""
        try:
            root = ET.fromstring(xml_text)

            def xml_to_dict(element):
                """递归将XML元素转换为字典"""
                result = {}

                # 添加属性
                if element.attrib:
                    result.update(element.attrib)

                # 处理子元素
                children = list(element)
                if children:
                    child_dict = {}
                    for child in children:
                        child_data = xml_to_dict(child)
                        if child.tag in child_dict:
                            # 如果标签已存在，转换为列表
                            if not isinstance(child_dict[child.tag], list):
                                child_dict[child.tag] = [child_dict[child.tag]]
                            child_dict[child.tag].append(child_data)
                        else:
                            child_dict[child.tag] = child_data
                    result.update(child_dict)
                elif element.text and element.text.strip():
                    # 如果有文本内容
                    result['text'] = element.text.strip()

                return result

            # 转换根元素
            data = xml_to_dict(root)

            # 如果根元素包含列表结构，尝试提取
            if len(data) == 1:
                key, value = next(iter(data.items()))
                if isinstance(value, list):
                    return value
                elif isinstance(value, dict):
                    return [value]

            return [data]

        except ET.ParseError as e:
            self.logger.error(f"XML解析失败: {e}")
            return [{'xml_error': str(e), 'raw_xml': xml_text}]

    def _sync_graphql(self, connection: Any, query: str, variables: Dict = None) -> Dict:
        """同步GraphQL查询"""
        if not REQUESTS_AVAILABLE:
            raise ImportError("requests未安装，无法执行同步GraphQL请求")

        config = self._parse_api_config(connection)
        url = urljoin(config['base_url'], '/graphql')

        # 构建GraphQL请求体
        payload = {'query': query}
        if variables:
            payload['variables'] = variables

        try:
            response = requests.post(
                url,
                json=payload,
                headers=config['headers'],
                timeout=config['timeout'],
                auth=config['auth']
            )
            response.raise_for_status()

            try:
                result = response.json()
                # GraphQL响应通常包含data和errors字段
                if 'data' in result:
                    return result['data']
                elif 'errors' in result:
                    raise Exception(f"GraphQL错误: {result['errors']}")
                else:
                    return result
            except json.JSONDecodeError:
                return {'text': response.text, 'status_code': response.status_code}

        except Exception as e:
            self.logger.error(f"同步GraphQL请求失败 {url}: {e}")
            raise

    # ========================================================================
    # 异步方法实现
    # ========================================================================

    async def _create_async_connection(self, connection_source: Any) -> Any:
        """创建异步HTTP会话"""
        if not AIOHTTP_AVAILABLE:
            raise ImportError("aiohttp未安装，无法创建异步HTTP会话")

        config = self._parse_api_config(connection_source)

        # 创建连接器和会话
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=30)
        timeout = aiohttp.ClientTimeout(total=config['timeout'])

        session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=config['headers']
        )

        # 将配置附加到会话对象
        session._config = config

        self.logger.info(f"成功创建异步HTTP会话: {config['base_url']}")
        return session

    async def _close_async_connection(self, connection: Any) -> None:
        """关闭异步HTTP会话"""
        try:
            await connection.close()
            self.logger.debug("异步HTTP会话已关闭")
        except Exception as e:
            self.logger.error(f"关闭异步HTTP会话失败: {e}")

    async def _create_async_pool(self, connection_source: Any, min_size: int = 5, max_size: int = 20) -> Any:
        """创建异步连接池（HTTP会话池）"""
        # HTTP客户端通常不需要连接池，直接返回会话
        return await self._create_async_connection(connection_source)

    async def _async_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """异步查询实现 - 映射为GET请求"""
        endpoint = sql.strip()
        return await self._async_get(connection, endpoint, params)

    async def _async_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """异步执行实现 - 映射为POST请求"""
        endpoint = sql.strip()
        result = await self._async_post(connection, endpoint, params)
        return 1 if result else 0

    async def _async_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步事务实现 - 批量API调用"""
        results = []
        total_affected = 0

        for operation in operations:
            method = operation.get('method', 'GET').upper()
            endpoint = operation['endpoint']
            data = operation.get('data')

            if method == 'GET':
                result = await self._async_get(connection, endpoint, data)
                results.append(result)
            elif method == 'POST':
                result = await self._async_post(connection, endpoint, data)
                results.append(result)
                total_affected += 1
            elif method == 'PUT':
                result = await self._async_put(connection, endpoint, data)
                results.append(result)
                total_affected += 1
            elif method == 'DELETE':
                result = await self._async_delete(connection, endpoint, data)
                results.append(result)
                total_affected += 1

        return {
            'results': results,
            'total_operations': len(operations),
            'total_affected': total_affected,
            'success': True
        }

    async def _async_batch(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步批量操作实现"""
        return await self._async_transaction(connection, operations)

    async def _async_stream_query(self, connection: Any, sql: str, params: Dict = None) -> AsyncIterator[Dict]:
        """异步流式查询实现 - 分页API调用"""
        endpoint = sql.strip()
        page = 1
        page_size = params.get('page_size', 100) if params else 100

        while True:
            # 构建分页参数
            page_params = {'page': page, 'page_size': page_size}
            if params:
                page_params.update(params)

            try:
                results = await self._async_get(connection, endpoint, page_params)

                if not results:
                    break

                for item in results:
                    yield item

                # 如果返回的结果少于页面大小，说明已经是最后一页
                if len(results) < page_size:
                    break

                page += 1

            except Exception as e:
                self.logger.error(f"异步流式查询失败: {e}")
                break

    async def _async_get(self, connection: Any, endpoint: str, params: Dict = None) -> List[Dict]:
        """异步GET请求"""
        config = connection._config
        url = urljoin(config['base_url'], endpoint)

        try:
            async with connection.get(url, params=params) as response:
                response.raise_for_status()

                # 尝试解析JSON响应
                try:
                    data = await response.json()
                    if isinstance(data, list):
                        return data
                    elif isinstance(data, dict):
                        return [data]
                    else:
                        return [{'data': data}]
                except json.JSONDecodeError:
                    text = await response.text()
                    return [{'text': text}]

        except Exception as e:
            self.logger.error(f"异步GET请求失败 {url}: {e}")
            raise

    async def _async_post(self, connection: Any, endpoint: str, data: Dict = None) -> Dict:
        """异步POST请求"""
        config = connection._config
        url = urljoin(config['base_url'], endpoint)

        try:
            async with connection.post(url, json=data) as response:
                response.raise_for_status()

                try:
                    return await response.json()
                except json.JSONDecodeError:
                    text = await response.text()
                    return {'text': text, 'status_code': response.status}

        except Exception as e:
            self.logger.error(f"异步POST请求失败 {url}: {e}")
            raise

    async def _async_put(self, connection: Any, endpoint: str, data: Dict = None) -> Dict:
        """异步PUT请求"""
        config = connection._config
        url = urljoin(config['base_url'], endpoint)

        try:
            async with connection.put(url, json=data) as response:
                response.raise_for_status()

                try:
                    return await response.json()
                except json.JSONDecodeError:
                    text = await response.text()
                    return {'text': text, 'status_code': response.status}

        except Exception as e:
            self.logger.error(f"异步PUT请求失败 {url}: {e}")
            raise

    async def _async_delete(self, connection: Any, endpoint: str, params: Dict = None) -> Dict:
        """异步DELETE请求"""
        config = connection._config
        url = urljoin(config['base_url'], endpoint)

        try:
            async with connection.delete(url, params=params) as response:
                response.raise_for_status()

                try:
                    return await response.json()
                except json.JSONDecodeError:
                    text = await response.text()
                    return {'text': text, 'status_code': response.status}

        except Exception as e:
            self.logger.error(f"异步DELETE请求失败 {url}: {e}")
            raise

    async def _async_patch(self, connection: Any, endpoint: str, data: Dict = None) -> Dict:
        """异步PATCH请求"""
        config = connection._config
        url = urljoin(config['base_url'], endpoint)

        try:
            async with connection.patch(url, json=data) as response:
                response.raise_for_status()

                try:
                    return await response.json()
                except json.JSONDecodeError:
                    text = await response.text()
                    return {'text': text, 'status_code': response.status}

        except Exception as e:
            self.logger.error(f"异步PATCH请求失败 {url}: {e}")
            raise

    async def _async_graphql(self, connection: Any, query: str, variables: Dict = None) -> Dict:
        """异步GraphQL查询"""
        config = connection._config
        url = urljoin(config['base_url'], '/graphql')

        # 构建GraphQL请求体
        payload = {'query': query}
        if variables:
            payload['variables'] = variables

        try:
            async with connection.post(url, json=payload) as response:
                response.raise_for_status()

                try:
                    result = await response.json()
                    # GraphQL响应通常包含data和errors字段
                    if 'data' in result:
                        return result['data']
                    elif 'errors' in result:
                        raise Exception(f"GraphQL错误: {result['errors']}")
                    else:
                        return result
                except json.JSONDecodeError:
                    text = await response.text()
                    return {'text': text, 'status_code': response.status}

        except Exception as e:
            self.logger.error(f"异步GraphQL请求失败 {url}: {e}")
            raise

    # ========================================================================
    # API特有功能
    # ========================================================================

    def get_async_operations(self) -> Dict[str, Any]:
        """获取支持的异步操作列表"""
        operations = super().get_async_operations()

        # 添加API特有异步操作
        operations.update({
            'async_get': self._async_get,
            'async_post': self._async_post,
            'async_put': self._async_put,
            'async_delete': self._async_delete,
            'async_patch': self._async_patch,
            'async_graphql': self._async_graphql,
        })

        return operations
