#!/usr/bin/env python3
"""
Remote_file插件重构后集成测试

基于插件标准规范的完整集成测试，验证重构后的功能完整性：
- 测试统一接口的兼容性
- 验证企业级功能的保留
- 使用真实远程服务器测试
"""

import pytest
import asyncio
import logging
import sys
import os

# 添加路径以便导入
current_dir = os.path.dirname(__file__)
project_root = os.path.abspath(os.path.join(current_dir, '../../../../..'))
sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 测试配置 - 真实远程服务器
TEST_REMOTE_CONFIG = {
    'host': '***********',
    'username': 'bossbm1',
    'password': 'fmbs3_adm!',
    'protocol': 'sftp',
    'port': 22
}

TEST_FILE_PATH = '/bossdat2/uidp/outgoing/11_17071698_116105000000000000109_20250728102401_00001.txt'
TEST_SFTP_URL = f"sftp://{TEST_REMOTE_CONFIG['username']}:{TEST_REMOTE_CONFIG['password']}@{TEST_REMOTE_CONFIG['host']}{TEST_FILE_PATH}"


class TestRefactoredRemoteFilePlugin:
    """重构后Remote_file插件集成测试"""
    
    def test_plugin_standards_compliance(self):
        """测试插件标准符合性"""
        try:
            # 测试插件定义
            from plugins.remote_file import get_plugin_definitions
            
            definitions = get_plugin_definitions()
            assert isinstance(definitions, list)
            assert len(definitions) > 0
            
            plugin_def = definitions[0]
            
            # 检查必需字段
            required_fields = ['id', 'name', 'description', 'version', 'type']
            for field in required_fields:
                assert field in plugin_def, f"缺少必需字段: {field}"
            
            logger.info(f"✅ 插件标准符合性检查通过: {plugin_def['name']}")
            
        except ImportError as e:
            logger.error(f"❌ 插件导入失败: {e}")
            pytest.fail(f"插件导入失败: {e}")
    
    def test_unified_protocol_interface(self):
        """测试统一协议接口"""
        try:
            from plugins.remote_file.protocols import (
                ProtocolFactory,
                EnterpriseProtocolFactory,
                HttpHandler,
                EnterpriseHttpHandler,
                FtpHandler,
                EnterpriseFtpHandler,
                SftpHandler,
                EnterpriseSftpHandler
            )
            
            # 测试主要工厂
            main_factory = ProtocolFactory()
            supported_protocols = main_factory.get_supported_protocols()
            assert 'sftp' in supported_protocols
            assert 'http' in supported_protocols
            assert 'ftp' in supported_protocols
            
            # 测试企业级兼容性
            assert ProtocolFactory == EnterpriseProtocolFactory
            assert HttpHandler == EnterpriseHttpHandler
            assert FtpHandler == EnterpriseFtpHandler
            assert SftpHandler == EnterpriseSftpHandler
            
            logger.info("✅ 统一协议接口测试通过")
            
        except ImportError as e:
            logger.error(f"❌ 协议接口导入失败: {e}")
            pytest.fail(f"协议接口导入失败: {e}")
    
    def test_unified_processor_interface(self):
        """测试统一处理器接口"""
        try:
            from plugins.remote_file.remote_processor import (
                RemoteFileProcessor,
                EnterpriseRemoteFileProcessor
            )
            
            # 测试主要处理器
            main_processor = RemoteFileProcessor()
            assert main_processor.processor_id == 'remote_file_processor'
            
            # 测试企业级兼容处理器
            enterprise_processor = EnterpriseRemoteFileProcessor()
            assert enterprise_processor.processor_id == 'enterprise_remote_file_processor'
            
            # 验证企业级处理器是主处理器的子类
            assert isinstance(enterprise_processor, RemoteFileProcessor)
            
            logger.info("✅ 统一处理器接口测试通过")
            
        except ImportError as e:
            logger.error(f"❌ 处理器导入失败: {e}")
            pytest.fail(f"处理器导入失败: {e}")
    
    def test_unified_connection_pool_interface(self):
        """测试统一连接池接口"""
        try:
            from plugins.remote_file.connection_pool import (
                RemoteConnectionPool,
                EnterpriseConnectionPool,
                global_connection_pool
            )
            
            # 测试主要连接池
            main_pool = RemoteConnectionPool()
            assert hasattr(main_pool, 'get_connection')
            assert hasattr(main_pool, 'release_connection')
            
            # 测试企业级兼容性
            assert RemoteConnectionPool == EnterpriseConnectionPool
            
            # 测试全局实例
            assert isinstance(global_connection_pool, RemoteConnectionPool)
            
            logger.info("✅ 统一连接池接口测试通过")
            
        except ImportError as e:
            logger.error(f"❌ 连接池导入失败: {e}")
            pytest.fail(f"连接池导入失败: {e}")
    
    @pytest.mark.asyncio
    async def test_real_sftp_functionality(self):
        """测试真实SFTP功能"""
        try:
            from plugins.remote_file.protocols import ProtocolFactory, AuthConfig
            
            # 创建SFTP处理器
            handler = ProtocolFactory.create_handler('sftp')
            
            # 创建认证配置
            auth_config = AuthConfig(
                auth_type='basic',
                username=TEST_REMOTE_CONFIG['username'],
                password=TEST_REMOTE_CONFIG['password']
            )
            
            # 测试文件下载
            download_result = await handler.download(
                TEST_SFTP_URL,
                {
                    'auth': auth_config,
                    'timeout': 30.0,
                    'max_size': 1024 * 1024  # 1MB限制
                }
            )
            
            assert download_result.success is True
            assert download_result.data is not None
            assert download_result.size > 0
            
            logger.info(f"✅ 真实SFTP功能测试通过，下载了 {download_result.size} 字节")
            
        except Exception as e:
            logger.warning(f"⚠️ SFTP功能测试跳过（服务器不可用）: {e}")
            pytest.skip(f"SFTP服务器不可用: {e}")
    
    @pytest.mark.asyncio
    async def test_enterprise_processor_functionality(self):
        """测试企业级处理器功能"""
        try:
            from plugins.remote_file.remote_processor import EnterpriseRemoteFileProcessor
            
            # 创建企业级处理器
            processor = EnterpriseRemoteFileProcessor()
            
            # 测试文件处理
            result = await processor.process(
                TEST_SFTP_URL,
                {
                    'auth': {
                        'auth_type': 'basic',
                        'username': TEST_REMOTE_CONFIG['username'],
                        'password': TEST_REMOTE_CONFIG['password']
                    },
                    'timeout': 30.0,
                    'max_size': 1024 * 1024
                }
            )
            
            assert hasattr(result, 'data')
            if result.data.get('success'):
                assert result.data.get('size', 0) > 0
                logger.info(f"✅ 企业级处理器功能测试通过")
            else:
                logger.warning(f"⚠️ 企业级处理器测试跳过: {result.data.get('error')}")
                pytest.skip(f"企业级处理器测试失败: {result.data.get('error')}")
            
        except Exception as e:
            logger.warning(f"⚠️ 企业级处理器测试跳过: {e}")
            pytest.skip(f"企业级处理器测试失败: {e}")
    
    def test_smart_data_object_integration(self):
        """测试SmartDataObject集成"""
        try:
            from core.smart_data_object import SmartDataLoader
            
            # 创建智能数据加载器
            loader = SmartDataLoader()
            
            # 测试远程文件方法
            result = loader.remote_file(
                TEST_SFTP_URL,
                auth={
                    'username': TEST_REMOTE_CONFIG['username'],
                    'password': TEST_REMOTE_CONFIG['password']
                },
                timeout=30.0,
                max_size=1024 * 1024
            )
            
            assert hasattr(result, 'data')
            
            logger.info("✅ SmartDataObject集成测试通过")
            
        except Exception as e:
            logger.warning(f"⚠️ SmartDataObject集成测试跳过: {e}")
            pytest.skip(f"SmartDataObject集成失败: {e}")
    
    def test_smart_remote_loader_integration(self):
        """测试智能远程加载器集成"""
        try:
            from plugins.remote_file.smart_remote_loader import global_remote_loader
            
            # 测试全局加载器
            result = global_remote_loader.load(
                TEST_SFTP_URL,
                {
                    'auth': {
                        'auth_type': 'basic',
                        'username': TEST_REMOTE_CONFIG['username'],
                        'password': TEST_REMOTE_CONFIG['password']
                    },
                    'timeout': 30.0,
                    'max_size': 1024 * 1024
                }
            )
            
            assert hasattr(result, 'data')
            
            logger.info("✅ 智能远程加载器集成测试通过")
            
        except Exception as e:
            logger.warning(f"⚠️ 智能远程加载器测试跳过: {e}")
            pytest.skip(f"智能远程加载器测试失败: {e}")
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        try:
            # 测试所有原有的导入路径是否仍然有效
            from plugins.remote_file import (
                RemoteFileProcessor,
                EnterpriseRemoteFileProcessor,
                ProtocolFactory,
                EnterpriseProtocolFactory,
                RemoteConnectionPool,
                EnterpriseConnectionPool,
                AuthConfig,
                DownloadProgress
            )
            
            # 测试企业级接口仍然可用
            enterprise_processor = EnterpriseRemoteFileProcessor()
            assert enterprise_processor.processor_id == 'enterprise_remote_file_processor'
            
            enterprise_factory = EnterpriseProtocolFactory()
            supported_protocols = enterprise_factory.get_supported_protocols()
            assert len(supported_protocols) > 0
            
            logger.info("✅ 向后兼容性测试通过")
            
        except ImportError as e:
            logger.error(f"❌ 向后兼容性测试失败: {e}")
            pytest.fail(f"向后兼容性破坏: {e}")
    
    def test_no_redundant_files(self):
        """测试冗余文件已清理"""
        import os
        
        plugin_dir = os.path.dirname(os.path.dirname(__file__))
        
        # 检查enterprise文件是否已删除
        enterprise_files = [
            'enterprise_protocols.py',
            'enterprise_remote_processor.py',
            'enterprise_connection_pool.py'
        ]
        
        for file_name in enterprise_files:
            file_path = os.path.join(plugin_dir, file_name)
            assert not os.path.exists(file_path), f"冗余文件仍然存在: {file_name}"
        
        logger.info("✅ 冗余文件清理验证通过")
    
    def test_performance_features(self):
        """测试性能特性保留"""
        try:
            from plugins.remote_file.protocols import ProtocolFactory
            
            # 测试协议支持
            factory = ProtocolFactory()
            supported_protocols = factory.get_supported_protocols()
            
            # 检查企业级协议是否保留
            expected_protocols = ['http', 'https', 'ftp', 'ftps', 'sftp']
            for protocol in expected_protocols:
                assert protocol in supported_protocols, f"缺少协议支持: {protocol}"
            
            logger.info("✅ 性能特性保留验证通过")
            
        except Exception as e:
            logger.error(f"❌ 性能特性验证失败: {e}")
            pytest.fail(f"性能特性验证失败: {e}")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short"])
