"""
邮件工具

提供邮件相关的工具函数
"""

import logging
import re
from typing import Any, Dict, List, Optional, Tuple


class EmailValidator:
    """邮件验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.EmailValidator")
        
        # 基本邮件格式正则表达式
        self.email_pattern = re.compile(
            r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        )
        
        # 常见的无效域名
        self.invalid_domains = {
            'example.com', 'test.com', 'localhost', 'invalid.com'
        }
    
    def validate_format(self, email: str) -> bool:
        """验证邮件格式"""
        if not email or not isinstance(email, str):
            return False
        
        return bool(self.email_pattern.match(email.strip()))
    
    def validate_domain(self, email: str) -> bool:
        """验证域名"""
        try:
            if '@' not in email:
                return False
            
            domain = email.split('@')[1].lower()
            
            # 检查是否在无效域名列表中
            if domain in self.invalid_domains:
                return False
            
            # 基本域名格式检查
            if '.' not in domain:
                return False
            
            return True
            
        except Exception:
            return False
    
    def normalize_email(self, email: str) -> str:
        """标准化邮件地址"""
        try:
            email = email.strip().lower()
            
            if '@' not in email:
                return email
            
            local, domain = email.split('@', 1)
            
            # 移除local部分的点号（Gmail风格）
            if domain in ['gmail.com', 'googlemail.com']:
                local = local.replace('.', '')
                # 移除+号后的内容
                if '+' in local:
                    local = local.split('+')[0]
            
            return f"{local}@{domain}"
            
        except Exception:
            return email
    
    def extract_domain(self, email: str) -> Optional[str]:
        """提取域名"""
        try:
            if '@' not in email:
                return None
            
            return email.split('@')[1].lower()
            
        except Exception:
            return None
    
    def extract_local(self, email: str) -> Optional[str]:
        """提取本地部分"""
        try:
            if '@' not in email:
                return None
            
            return email.split('@')[0]
            
        except Exception:
            return None


class EmailFormatter:
    """邮件格式化器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.EmailFormatter")
    
    def format_address(self, email: str, name: Optional[str] = None) -> str:
        """格式化邮件地址"""
        try:
            if name:
                # 如果名称包含特殊字符，需要引号
                if any(char in name for char in [',', ';', '<', '>', '"']):
                    name = f'"{name}"'
                return f"{name} <{email}>"
            else:
                return email
                
        except Exception as e:
            self.logger.error(f"邮件地址格式化失败: {e}")
            return email
    
    def parse_address(self, address: str) -> Tuple[str, Optional[str]]:
        """解析邮件地址"""
        try:
            address = address.strip()
            
            # 检查是否包含名称
            if '<' in address and '>' in address:
                # <AUTHOR> <EMAIL>
                name_part = address.split('<')[0].strip()
                email_part = address.split('<')[1].split('>')[0].strip()
                
                # 移除名称部分的引号
                if name_part.startswith('"') and name_part.endswith('"'):
                    name_part = name_part[1:-1]
                
                return email_part, name_part if name_part else None
            else:
                # 只有邮件地址
                return address, None
                
        except Exception as e:
            self.logger.error(f"邮件地址解析失败: {e}")
            return address, None
    
    def format_address_list(self, addresses: List[str]) -> str:
        """格式化邮件地址列表"""
        try:
            return ', '.join(addresses)
        except Exception as e:
            self.logger.error(f"邮件地址列表格式化失败: {e}")
            return ''


class EmailContentProcessor:
    """邮件内容处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.EmailContentProcessor")
    
    def extract_text_from_html(self, html_content: str) -> str:
        """从HTML中提取纯文本"""
        try:
            # 简单的HTML标签移除
            import re
            
            # 移除HTML标签
            text = re.sub(r'<[^>]+>', '', html_content)
            
            # 解码HTML实体
            text = text.replace('&amp;', '&')
            text = text.replace('&lt;', '<')
            text = text.replace('&gt;', '>')
            text = text.replace('&quot;', '"')
            text = text.replace('&#39;', "'")
            text = text.replace('&nbsp;', ' ')
            
            # 清理多余的空白
            text = re.sub(r'\s+', ' ', text)
            text = text.strip()
            
            return text
            
        except Exception as e:
            self.logger.error(f"HTML文本提取失败: {e}")
            return html_content
    
    def convert_text_to_html(self, text_content: str) -> str:
        """将纯文本转换为HTML"""
        try:
            # 转义HTML特殊字符
            html = text_content.replace('&', '&amp;')
            html = html.replace('<', '&lt;')
            html = html.replace('>', '&gt;')
            html = html.replace('"', '&quot;')
            html = html.replace("'", '&#39;')
            
            # 转换换行为<br>
            html = html.replace('\n', '<br>\n')
            
            # 包装在基本HTML结构中
            html = f"<html><body>{html}</body></html>"
            
            return html
            
        except Exception as e:
            self.logger.error(f"文本转HTML失败: {e}")
            return text_content
    
    def sanitize_content(self, content: str) -> str:
        """清理邮件内容"""
        try:
            # 移除潜在的恶意内容
            import re
            
            # 移除脚本标签
            content = re.sub(r'<script[^>]*>.*?</script>', '', content, flags=re.IGNORECASE | re.DOTALL)
            
            # 移除样式标签
            content = re.sub(r'<style[^>]*>.*?</style>', '', content, flags=re.IGNORECASE | re.DOTALL)
            
            # 移除危险的属性
            dangerous_attrs = ['onclick', 'onload', 'onerror', 'onmouseover']
            for attr in dangerous_attrs:
                content = re.sub(f'{attr}="[^"]*"', '', content, flags=re.IGNORECASE)
                content = re.sub(f"{attr}='[^']*'", '', content, flags=re.IGNORECASE)
            
            return content
            
        except Exception as e:
            self.logger.error(f"内容清理失败: {e}")
            return content


class EmailHeaderParser:
    """邮件头解析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.EmailHeaderParser")
    
    def parse_headers(self, headers: str) -> Dict[str, str]:
        """解析邮件头"""
        try:
            header_dict = {}
            
            lines = headers.split('\n')
            current_header = None
            current_value = ""
            
            for line in lines:
                line = line.rstrip('\r')
                
                if line.startswith(' ') or line.startswith('\t'):
                    # 续行
                    if current_header:
                        current_value += ' ' + line.strip()
                else:
                    # 保存上一个头
                    if current_header:
                        header_dict[current_header.lower()] = current_value.strip()
                    
                    # 解析新头
                    if ':' in line:
                        current_header, current_value = line.split(':', 1)
                        current_header = current_header.strip()
                        current_value = current_value.strip()
                    else:
                        current_header = None
                        current_value = ""
            
            # 保存最后一个头
            if current_header:
                header_dict[current_header.lower()] = current_value.strip()
            
            return header_dict
            
        except Exception as e:
            self.logger.error(f"邮件头解析失败: {e}")
            return {}
    
    def extract_message_id(self, headers: Dict[str, str]) -> Optional[str]:
        """提取消息ID"""
        return headers.get('message-id')
    
    def extract_date(self, headers: Dict[str, str]) -> Optional[str]:
        """提取日期"""
        return headers.get('date')
    
    def extract_subject(self, headers: Dict[str, str]) -> Optional[str]:
        """提取主题"""
        return headers.get('subject')


class EmailAttachmentHandler:
    """邮件附件处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.EmailAttachmentHandler")
        
        # 允许的附件类型
        self.allowed_types = {
            '.txt', '.pdf', '.doc', '.docx', '.xls', '.xlsx', 
            '.ppt', '.pptx', '.jpg', '.jpeg', '.png', '.gif'
        }
        
        # 最大附件大小 (10MB)
        self.max_size = 10 * 1024 * 1024
    
    def validate_attachment(self, filename: str, size: int) -> Dict[str, Any]:
        """验证附件"""
        try:
            import os
            
            # 检查文件扩展名
            ext = os.path.splitext(filename)[1].lower()
            is_allowed_type = ext in self.allowed_types
            
            # 检查文件大小
            is_valid_size = size <= self.max_size
            
            return {
                'valid': is_allowed_type and is_valid_size,
                'filename': filename,
                'size': size,
                'extension': ext,
                'is_allowed_type': is_allowed_type,
                'is_valid_size': is_valid_size,
                'max_size': self.max_size
            }
            
        except Exception as e:
            self.logger.error(f"附件验证失败: {e}")
            return {
                'valid': False,
                'error': str(e)
            }
    
    def get_mime_type(self, filename: str) -> str:
        """获取MIME类型"""
        try:
            import mimetypes
            
            mime_type, _ = mimetypes.guess_type(filename)
            return mime_type or 'application/octet-stream'
            
        except Exception as e:
            self.logger.error(f"MIME类型获取失败: {e}")
            return 'application/octet-stream'
