"""
插件层

这个模块包含了所有的数据处理插件：
- HTTP插件 (http/)
- 文件加载插件 (file_loader/)
- 远程文件插件 (remote_file/)
- 流式传输插件 (stream/)
- AI接口插件 (ai/)
- 本地LLM插件 (local_llm/)
- Kafka流插件 (kafka/)
- 数据库插件 (database/)
等等...

同时提供插件注册和管理功能。
"""

from .plugin_registry import PluginRegistry, register_builtin_plugins

# 导入各个插件模块
from . import http_plugin  # 重命名后的http插件
from . import file_loader
from . import remote_file
from . import email_plugin  # 重命名后的email插件
# from . import stream  # 暂时跳过，有导入问题
# from . import ai  # 暂时跳过，ai_processor模块缺失
# from . import local_llm  # 暂时跳过，local_llm_processor模块缺失
# from . import kafka  # 暂时跳过，可能有导入问题
# from . import database  # 暂时跳过，可能有导入问题

__all__ = [
    'PluginRegistry',
    'register_builtin_plugins',
    'http',
    'file_loader',
    'remote_file',
    # 'stream',  # 暂时跳过
    'ai',
    'local_llm',
    'kafka',
    'database'
]
