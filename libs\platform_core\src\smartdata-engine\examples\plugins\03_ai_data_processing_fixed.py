#!/usr/bin/env python3
"""
SmartData模板引擎AI数据处理插件使用示例 (修复版)

展示AI数据处理的概念和模拟实现：
1. 数据预处理和特征工程
2. 机器学习模型训练和预测
3. 自然语言处理
4. 计算机视觉
5. 推荐系统
"""

import sys
import os
import time
import json
import random
import math
from datetime import datetime
from typing import Dict, Any, List, Tuple
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.template_ext import create_template_engine

class MockAIProcessor:
    """模拟AI处理器"""
    
    def __init__(self):
        self.models = {}
        self.metrics = {
            'predictions_made': 0,
            'models_trained': 0,
            'accuracy_scores': []
        }
    
    def preprocess_data(self, data: List[Dict[str, Any]], config: Dict[str, Any]) -> Dict[str, Any]:
        """数据预处理"""
        try:
            processed_data = []
            for item in data:
                # 模拟数据清洗和特征工程
                processed_item = {
                    'id': item.get('id', f'item_{len(processed_data)}'),
                    'features': self._extract_features(item),
                    'label': item.get('label'),
                    'processed_at': datetime.now().isoformat()
                }
                processed_data.append(processed_item)
            
            return {
                'success': True,
                'original_count': len(data),
                'processed_count': len(processed_data),
                'data': processed_data,
                'processing_time': random.uniform(10, 50)  # ms
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _extract_features(self, item: Dict[str, Any]) -> List[float]:
        """特征提取"""
        features = []
        for key, value in item.items():
            if isinstance(value, (int, float)):
                features.append(float(value))
            elif isinstance(value, str):
                features.append(float(len(value)))  # 字符串长度作为特征
        return features[:10]  # 限制特征数量
    
    def train_model(self, data: List[Dict[str, Any]], model_type: str = 'classification') -> Dict[str, Any]:
        """训练模型"""
        try:
            model_id = f'model_{model_type}_{int(time.time())}'
            
            # 模拟训练过程
            training_time = random.uniform(100, 500)  # ms
            accuracy = random.uniform(0.75, 0.95)
            
            # 保存模型
            self.models[model_id] = {
                'type': model_type,
                'accuracy': accuracy,
                'training_data_size': len(data),
                'created_at': datetime.now().isoformat(),
                'features_count': len(data[0].get('features', [])) if data else 0
            }
            
            self.metrics['models_trained'] += 1
            self.metrics['accuracy_scores'].append(accuracy)
            
            return {
                'success': True,
                'model_id': model_id,
                'accuracy': accuracy,
                'training_time': training_time,
                'data_size': len(data)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def predict(self, model_id: str, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """模型预测"""
        try:
            if model_id not in self.models:
                return {'success': False, 'error': f'Model {model_id} not found'}
            
            model = self.models[model_id]
            predictions = []
            
            for item in data:
                # 模拟预测
                if model['type'] == 'classification':
                    prediction = random.choice(['A', 'B', 'C'])
                    confidence = random.uniform(0.6, 0.95)
                elif model['type'] == 'regression':
                    prediction = random.uniform(0, 100)
                    confidence = random.uniform(0.7, 0.9)
                else:
                    prediction = random.random()
                    confidence = random.uniform(0.5, 0.8)
                
                predictions.append({
                    'id': item.get('id', f'pred_{len(predictions)}'),
                    'prediction': prediction,
                    'confidence': confidence
                })
            
            self.metrics['predictions_made'] += len(predictions)
            
            return {
                'success': True,
                'model_id': model_id,
                'predictions': predictions,
                'prediction_time': random.uniform(5, 20)  # ms
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def analyze_text(self, text: str, analysis_type: str = 'sentiment') -> Dict[str, Any]:
        """文本分析"""
        try:
            if analysis_type == 'sentiment':
                # 模拟情感分析
                sentiments = ['positive', 'negative', 'neutral']
                sentiment = random.choice(sentiments)
                score = random.uniform(0.6, 0.95)
                
                return {
                    'success': True,
                    'text_length': len(text),
                    'sentiment': sentiment,
                    'score': score,
                    'analysis_type': analysis_type
                }
            
            elif analysis_type == 'keywords':
                # 模拟关键词提取
                words = text.split()
                keywords = random.sample(words, min(5, len(words)))
                
                return {
                    'success': True,
                    'text_length': len(text),
                    'keywords': keywords,
                    'word_count': len(words),
                    'analysis_type': analysis_type
                }
            
            else:
                return {'success': False, 'error': f'Unsupported analysis type: {analysis_type}'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}

def ai_data_processing_examples():
    """AI数据处理完整示例"""
    print("=== SmartData模板引擎AI数据处理插件示例 (修复版) ===")
    
    # 创建模板引擎和模拟AI处理器
    engine = create_template_engine()
    ai_processor = MockAIProcessor()
    
    # 1. 数据预处理示例
    print("\n🔧 1. 数据预处理示例")
    
    # 模拟原始数据
    raw_data = [
        {'id': 1, 'name': 'Alice', 'age': 25, 'score': 85.5, 'category': 'A'},
        {'id': 2, 'name': 'Bob', 'age': 30, 'score': 92.0, 'category': 'B'},
        {'id': 3, 'name': 'Charlie', 'age': 35, 'score': 78.5, 'category': 'A'},
        {'id': 4, 'name': 'Diana', 'age': 28, 'score': 88.0, 'category': 'C'}
    ]
    
    preprocess_config = {
        'normalize': True,
        'remove_outliers': True,
        'feature_selection': True
    }
    
    preprocess_result = ai_processor.preprocess_data(raw_data, preprocess_config)
    
    preprocess_template = """
数据预处理结果:
==============

📊 处理统计:
{%- if result.success -%}
- 原始数据量: {{ result.original_count }}
- 处理后数据量: {{ result.processed_count }}
- 处理时间: {{ result.processing_time | round(2) }}ms
- 数据完整性: {{ (result.processed_count / result.original_count * 100) | round(1) }}%

🔧 特征工程:
{%- for item in result.data[:3] %}
{{ loop.index }}. ID{{ item.id }}:
   - 特征数量: {{ item.features | length }}
   - 特征值: {{ item.features | map('round', 2) | list }}
   - 处理时间: {{ item.processed_at }}
{%- endfor %}
{%- if result.data | length > 3 %}
... 还有 {{ result.data | length - 3 }} 条数据
{%- endif -%}
{%- else -%}
❌ 预处理失败: {{ result.error }}
{%- endif -%}

💡 预处理步骤:
- 数据清洗: 去除异常值
- 特征提取: 数值化处理
- 标准化: 特征缩放
- 特征选择: 降维优化
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(preprocess_template, result=preprocess_result)
    print(result)
    
    # 2. 机器学习模型训练示例
    print("\n🤖 2. 机器学习模型训练示例")
    
    # 使用预处理后的数据训练模型
    if preprocess_result['success']:
        training_data = preprocess_result['data']
        
        # 训练分类模型
        classification_result = ai_processor.train_model(training_data, 'classification')
        
        # 训练回归模型
        regression_result = ai_processor.train_model(training_data, 'regression')
        
        training_results = [
            {'type': 'classification', 'result': classification_result},
            {'type': 'regression', 'result': regression_result}
        ]
    else:
        training_results = []
    
    training_template = """
机器学习模型训练:
================

🤖 训练结果:
{%- for training in trainings %}
{{ loop.index }}. {{ training.type | title }} 模型:
   {%- if training.result.success -%}
   ✅ 训练成功
   - 模型ID: {{ training.result.model_id }}
   - 准确率: {{ (training.result.accuracy * 100) | round(2) }}%
   - 训练时间: {{ training.result.training_time | round(2) }}ms
   - 数据量: {{ training.result.data_size }} 条
   {%- else -%}
   ❌ 训练失败: {{ training.result.error }}
   {%- endif -%}
{%- endfor %}

📊 模型性能:
- 平均准确率: {{ (trainings | selectattr('result.success') | map(attribute='result.accuracy') | list | sum / trainings | length * 100) | round(2) if trainings else 0 }}%
- 总训练时间: {{ (trainings | selectattr('result.success') | map(attribute='result.training_time') | list | sum) | round(2) if trainings else 0 }}ms

💡 模型选择建议:
- 分类任务: 随机森林、SVM、神经网络
- 回归任务: 线性回归、决策树、XGBoost
- 深度学习: CNN、RNN、Transformer
- 集成方法: Bagging、Boosting、Stacking
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(training_template, trainings=training_results)
    print(result)
    
    # 3. 模型预测示例
    print("\n🔮 3. 模型预测示例")
    
    # 使用训练好的模型进行预测
    prediction_results = []
    if training_results and training_results[0]['result']['success']:
        model_id = training_results[0]['result']['model_id']
        
        # 准备预测数据
        test_data = [
            {'id': 'test_1', 'features': [1.2, 2.3, 3.4, 4.5]},
            {'id': 'test_2', 'features': [2.1, 3.2, 4.3, 5.4]},
            {'id': 'test_3', 'features': [3.0, 4.1, 5.2, 6.3]}
        ]
        
        prediction_result = ai_processor.predict(model_id, test_data)
        prediction_results.append(prediction_result)
    
    prediction_template = """
模型预测结果:
============

🔮 预测统计:
{%- for pred_result in predictions %}
{%- if pred_result.success -%}
- 模型ID: {{ pred_result.model_id }}
- 预测数量: {{ pred_result.predictions | length }}
- 预测时间: {{ pred_result.prediction_time | round(2) }}ms

📋 预测详情:
{%- for pred in pred_result.predictions %}
{{ loop.index }}. {{ pred.id }}:
   - 预测值: {{ pred.prediction }}
   - 置信度: {{ (pred.confidence * 100) | round(2) }}%
{%- endfor %}
{%- else -%}
❌ 预测失败: {{ pred_result.error }}
{%- endif -%}
{%- endfor %}

📊 预测质量:
- 平均置信度: {{ (predictions[0].predictions | map(attribute='confidence') | list | sum / predictions[0].predictions | length * 100) | round(2) if predictions and predictions[0].success else 0 }}%
- 高置信度预测: {{ (predictions[0].predictions | selectattr('confidence', '>', 0.8) | list | length) if predictions and predictions[0].success else 0 }} 个

💡 预测优化建议:
- 增加训练数据量
- 特征工程优化
- 模型集成方法
- 超参数调优
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(prediction_template, predictions=prediction_results)
    print(result)
    
    # 4. 自然语言处理示例
    print("\n📝 4. 自然语言处理示例")
    
    # 文本分析
    sample_texts = [
        "这个产品非常好用，我很满意！",
        "服务态度不好，很失望。",
        "产品质量一般，价格合理。"
    ]
    
    nlp_results = []
    for i, text in enumerate(sample_texts):
        sentiment_result = ai_processor.analyze_text(text, 'sentiment')
        keyword_result = ai_processor.analyze_text(text, 'keywords')
        
        nlp_results.append({
            'text': text,
            'sentiment': sentiment_result,
            'keywords': keyword_result
        })
    
    nlp_template = """
自然语言处理结果:
================

📝 文本分析:
{%- for item in results %}
{{ loop.index }}. 文本: "{{ item.text }}"
   
   🎭 情感分析:
   {%- if item.sentiment.success -%}
   - 情感: {{ item.sentiment.sentiment }}
   - 置信度: {{ (item.sentiment.score * 100) | round(2) }}%
   {%- else -%}
   - ❌ 分析失败: {{ item.sentiment.error }}
   {%- endif -%}
   
   🔑 关键词提取:
   {%- if item.keywords.success -%}
   - 关键词: {{ item.keywords.keywords | join(', ') }}
   - 词数: {{ item.keywords.word_count }}
   {%- else -%}
   - ❌ 提取失败: {{ item.keywords.error }}
   {%- endif -%}
{%- endfor %}

📊 分析统计:
- 正面情感: {{ results | selectattr('sentiment.sentiment', 'equalto', 'positive') | list | length }} 条
- 负面情感: {{ results | selectattr('sentiment.sentiment', 'equalto', 'negative') | list | length }} 条
- 中性情感: {{ results | selectattr('sentiment.sentiment', 'equalto', 'neutral') | list | length }} 条

💡 NLP应用场景:
- 情感分析: 用户评论分析
- 关键词提取: 内容标签生成
- 文本分类: 邮件分类、新闻分类
- 命名实体识别: 信息抽取
- 机器翻译: 多语言支持
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(nlp_template, results=nlp_results)
    print(result)
    
    # 5. AI系统监控示例
    print("\n📊 5. AI系统监控示例")
    
    monitoring_template = """
AI系统监控面板:
==============

🤖 模型统计:
- 已训练模型: {{ metrics.models_trained }} 个
- 总预测次数: {{ metrics.predictions_made }} 次
- 平均准确率: {{ (metrics.accuracy_scores | sum / metrics.accuracy_scores | length * 100) | round(2) if metrics.accuracy_scores else 0 }}%

📈 性能指标:
- 模型训练成功率: 100%
- 预测响应时间: < 20ms
- 系统可用性: 99.9%
- 内存使用率: 65%

🔧 系统配置:
- GPU加速: 启用
- 分布式训练: 支持
- 模型版本管理: 启用
- 自动调优: 启用

💡 优化建议:
- 定期重训练模型
- 监控数据漂移
- 实施A/B测试
- 优化特征工程
- 增强模型解释性

🚀 AI能力矩阵:
| 功能领域 | 成熟度 | 准确率 | 应用场景 |
|----------|--------|--------|----------|
| 分类预测 | 高     | 90%+   | 用户分群 |
| 回归分析 | 高     | 85%+   | 销量预测 |
| 文本分析 | 中     | 80%+   | 情感分析 |
| 图像识别 | 中     | 85%+   | 质量检测 |
| 推荐系统 | 高     | 88%+   | 个性推荐 |
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(monitoring_template, metrics=ai_processor.metrics)
    print(result)
    
    print("\n📊 功能总结:")
    print("🔧 数据预处理: 清洗、特征工程、标准化")
    print("🤖 机器学习: 分类、回归、聚类、深度学习")
    print("📝 自然语言处理: 情感分析、关键词提取、文本分类")
    print("👁️ 计算机视觉: 图像识别、目标检测、图像生成")
    print("🎯 推荐系统: 协同过滤、内容推荐、混合推荐")
    
    print("\n💡 使用要点:")
    print("✅ 数据质量 - 高质量数据是AI成功的基础")
    print("✅ 模型选择 - 根据业务场景选择合适模型")
    print("✅ 持续优化 - 定期评估和改进模型性能")
    print("✅ 可解释性 - 确保AI决策的透明度和可信度")

if __name__ == "__main__":
    try:
        ai_data_processing_examples()
        print("\n🎉 AI数据处理插件示例运行完成！")
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
        print("\n💡 这是一个概念演示，实际AI功能需要相应的机器学习库")
