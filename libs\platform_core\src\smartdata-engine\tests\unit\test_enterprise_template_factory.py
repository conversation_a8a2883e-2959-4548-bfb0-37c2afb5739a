"""
企业级模板引擎工厂单元测试
"""

import unittest
import asyncio
import tempfile
import os
from unittest.mock import Mock, patch

import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from template.enterprise_template_factory import (
    EnterpriseTemplateFactory,
    EnterpriseTemplateConfig,
    EnterpriseTemplateEngine,
    TemplateEngineMode,
    create_enterprise_template_engine
)


class TestEnterpriseTemplateConfig(unittest.TestCase):
    """企业级模板引擎配置测试"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = EnterpriseTemplateConfig()
        
        self.assertEqual(config.mode, TemplateEngineMode.ENTERPRISE)
        self.assertTrue(config.enable_async)
        self.assertFalse(config.enable_debug)
        self.assertTrue(config.enable_legacy_support)
        self.assertTrue(config.auto_discover_plugins)
        self.assertTrue(config.enable_caching)
        self.assertEqual(config.cache_size, 1000)
        self.assertTrue(config.enable_connection_pooling)
        self.assertEqual(config.max_connections_per_adapter, 10)
        self.assertTrue(config.enable_security)
        self.assertEqual(config.max_template_size, 1024 * 1024)
        self.assertEqual(config.max_execution_time, 30)
        self.assertIsNone(config.allowed_functions)
        self.assertTrue(config.enable_performance_monitoring)
        self.assertTrue(config.enable_metrics_collection)
        self.assertEqual(len(config.custom_adapters), 0)
        self.assertEqual(len(config.extensions), 0)
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = EnterpriseTemplateConfig(
            mode=TemplateEngineMode.DEBUG,
            enable_async=False,
            enable_debug=True,
            cache_size=2000,
            max_template_size=512 * 1024,
            allowed_functions=['len', 'str']
        )
        
        self.assertEqual(config.mode, TemplateEngineMode.DEBUG)
        self.assertFalse(config.enable_async)
        self.assertTrue(config.enable_debug)
        self.assertEqual(config.cache_size, 2000)
        self.assertEqual(config.max_template_size, 512 * 1024)
        self.assertEqual(config.allowed_functions, ['len', 'str'])


class TestEnterpriseTemplateEngine(unittest.TestCase):
    """企业级模板引擎测试"""
    
    def setUp(self):
        """测试前准备"""
        self.config = EnterpriseTemplateConfig(
            enable_async=False,  # 禁用异步以避免事件循环问题
            enable_debug=False,  # 减少日志输出
            enable_legacy_support=False  # 简化测试
        )
        self.engine = EnterpriseTemplateEngine(self.config)
    
    def tearDown(self):
        """测试后清理"""
        self.engine.cleanup()
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.engine.config, self.config)
        self.assertIsNotNone(self.engine.integration)
        self.assertEqual(self.engine.render_count, 0)
        self.assertEqual(self.engine.total_render_time, 0.0)
        self.assertEqual(self.engine.error_count, 0)
    
    def test_render_basic_template(self):
        """测试基础模板渲染"""
        template = "Hello {{ name }}!"
        context = {'name': 'World'}
        
        result = self.engine.render(template, context)
        
        self.assertIsInstance(result, str)
        self.assertEqual(self.engine.render_count, 1)
        self.assertGreater(self.engine.total_render_time, 0)
    
    def test_render_with_data_source(self):
        """测试带数据源的模板渲染"""
        template = "Items: {{ items|length }}"
        context = {'items': [1, 2, 3]}
        
        result = self.engine.render(template, context)
        
        self.assertIsInstance(result, str)
        self.assertEqual(self.engine.render_count, 1)
    
    def test_template_size_validation(self):
        """测试模板大小验证"""
        # 创建一个启用安全检查的引擎
        secure_config = EnterpriseTemplateConfig(
            enable_security=True,
            max_template_size=100,  # 很小的限制
            enable_legacy_support=False
        )
        secure_engine = EnterpriseTemplateEngine(secure_config)
        
        try:
            large_template = "x" * 200  # 超过限制
            
            with self.assertRaises(ValueError):
                secure_engine.render(large_template, {})
        finally:
            secure_engine.cleanup()
    
    def test_template_size_validation_disabled(self):
        """测试禁用模板大小验证"""
        # 创建一个禁用安全检查的引擎
        insecure_config = EnterpriseTemplateConfig(
            enable_security=False,
            enable_legacy_support=False
        )
        insecure_engine = EnterpriseTemplateEngine(insecure_config)
        
        try:
            large_template = "Hello {{ name }}!" * 1000
            context = {'name': 'World'}
            
            # 应该不会抛出异常
            result = insecure_engine.render(large_template, context)
            self.assertIsInstance(result, str)
        finally:
            insecure_engine.cleanup()
    
    def test_get_performance_stats(self):
        """测试性能统计"""
        # 执行几次渲染
        template = "Hello {{ name }}!"
        for i in range(3):
            self.engine.render(template, {'name': f'User{i}'})
        
        stats = self.engine.get_performance_stats()
        
        self.assertIn('render_count', stats)
        self.assertIn('total_render_time', stats)
        self.assertIn('average_render_time', stats)
        self.assertIn('error_count', stats)
        self.assertIn('error_rate', stats)
        self.assertIn('config_mode', stats)
        
        self.assertEqual(stats['render_count'], 3)
        self.assertGreater(stats['total_render_time'], 0)
        self.assertGreater(stats['average_render_time'], 0)
        self.assertEqual(stats['error_count'], 0)
        self.assertEqual(stats['error_rate'], 0)
        self.assertEqual(stats['config_mode'], TemplateEngineMode.ENTERPRISE.value)
    
    def test_get_supported_data_types(self):
        """测试获取支持的数据类型"""
        data_types = self.engine.get_supported_data_types()
        
        self.assertIsInstance(data_types, list)
        self.assertGreater(len(data_types), 0)
        
        # 检查一些基本类型
        expected_types = ['sqlite', 'csv_file', 'json_file', 'data_list']
        for expected_type in expected_types:
            self.assertIn(expected_type, data_types)
    
    def test_register_adapter_dynamically(self):
        """测试动态注册适配器"""
        # 创建一个模拟适配器类
        class MockAdapter:
            def supported_types(self):
                return ['mock_type']
        
        initial_types = self.engine.get_supported_data_types()
        self.assertNotIn('mock_type', initial_types)
        
        # 动态注册
        self.engine.register_adapter(MockAdapter)
        
        updated_types = self.engine.get_supported_data_types()
        self.assertIn('mock_type', updated_types)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效模板
        invalid_template = "Hello {{ undefined_variable.invalid_method() }}!"
        
        with self.assertRaises(Exception):
            self.engine.render(invalid_template, {})
        
        # 检查错误计数
        self.assertEqual(self.engine.error_count, 1)
        
        # 检查错误率
        stats = self.engine.get_performance_stats()
        self.assertGreater(stats['error_rate'], 0)
    
    def test_cleanup(self):
        """测试资源清理"""
        # 创建一些模板作用域
        self.engine.render("Hello {{ name }}!", {'name': 'Test1'})
        self.engine.render("Hello {{ name }}!", {'name': 'Test2'})
        
        # 清理资源
        self.engine.cleanup()
        
        # 验证清理后的状态
        stats = self.engine.get_performance_stats()
        self.assertEqual(stats['active_scopes'], 0)


class TestEnterpriseTemplateFactory(unittest.TestCase):
    """企业级模板引擎工厂测试"""
    
    def test_create_engine_default(self):
        """测试创建默认引擎"""
        engine = EnterpriseTemplateFactory.create_engine()
        
        self.assertIsInstance(engine, EnterpriseTemplateEngine)
        self.assertEqual(engine.config.mode, TemplateEngineMode.ENTERPRISE)
        
        engine.cleanup()
    
    def test_create_engine_with_config(self):
        """测试使用配置创建引擎"""
        config = EnterpriseTemplateConfig(
            mode=TemplateEngineMode.DEBUG,
            enable_debug=True
        )
        engine = EnterpriseTemplateFactory.create_engine(config)
        
        self.assertIsInstance(engine, EnterpriseTemplateEngine)
        self.assertEqual(engine.config.mode, TemplateEngineMode.DEBUG)
        self.assertTrue(engine.config.enable_debug)
        
        engine.cleanup()
    
    def test_create_high_performance_engine(self):
        """测试创建高性能引擎"""
        engine = EnterpriseTemplateFactory.create_high_performance_engine()
        
        self.assertIsInstance(engine, EnterpriseTemplateEngine)
        self.assertEqual(engine.config.mode, TemplateEngineMode.PERFORMANCE)
        self.assertTrue(engine.config.enable_async)
        self.assertTrue(engine.config.enable_caching)
        self.assertEqual(engine.config.cache_size, 2000)
        self.assertEqual(engine.config.max_connections_per_adapter, 20)
        
        engine.cleanup()
    
    def test_create_debug_engine(self):
        """测试创建调试引擎"""
        engine = EnterpriseTemplateFactory.create_debug_engine()
        
        self.assertIsInstance(engine, EnterpriseTemplateEngine)
        self.assertEqual(engine.config.mode, TemplateEngineMode.DEBUG)
        self.assertTrue(engine.config.enable_debug)
        self.assertTrue(engine.config.enable_performance_monitoring)
        self.assertTrue(engine.config.enable_metrics_collection)
        
        engine.cleanup()
    
    def test_create_secure_engine(self):
        """测试创建安全引擎"""
        engine = EnterpriseTemplateFactory.create_secure_engine()
        
        self.assertIsInstance(engine, EnterpriseTemplateEngine)
        self.assertEqual(engine.config.mode, TemplateEngineMode.ENTERPRISE)
        self.assertTrue(engine.config.enable_security)
        self.assertEqual(engine.config.max_template_size, 512 * 1024)
        self.assertEqual(engine.config.max_execution_time, 15)
        self.assertIsNotNone(engine.config.allowed_functions)
        
        engine.cleanup()
    
    def test_create_hybrid_engine(self):
        """测试创建混合引擎"""
        engine = EnterpriseTemplateFactory.create_hybrid_engine()
        
        self.assertIsInstance(engine, EnterpriseTemplateEngine)
        self.assertEqual(engine.config.mode, TemplateEngineMode.HYBRID)
        self.assertTrue(engine.config.enable_legacy_support)
        self.assertTrue(engine.config.auto_discover_plugins)
        self.assertTrue(engine.config.enable_async)
        
        engine.cleanup()
    
    def test_create_legacy_compatible_engine(self):
        """测试创建兼容引擎"""
        engine = EnterpriseTemplateFactory.create_legacy_compatible_engine()
        
        self.assertIsInstance(engine, EnterpriseTemplateEngine)
        self.assertEqual(engine.config.mode, TemplateEngineMode.LEGACY)
        self.assertTrue(engine.config.enable_legacy_support)
        self.assertTrue(engine.config.auto_discover_plugins)
        self.assertFalse(engine.config.enable_async)  # 保持与现有系统一致
        
        engine.cleanup()
    
    def test_convenience_function(self):
        """测试便利函数"""
        engine = create_enterprise_template_engine(
            mode=TemplateEngineMode.PERFORMANCE,
            enable_debug=True
        )
        
        self.assertIsInstance(engine, EnterpriseTemplateEngine)
        self.assertEqual(engine.config.mode, TemplateEngineMode.PERFORMANCE)
        self.assertTrue(engine.config.enable_debug)
        
        engine.cleanup()


class TestAsyncEnterpriseTemplateEngine(unittest.TestCase):
    """异步企业级模板引擎测试"""
    
    def setUp(self):
        """测试前准备"""
        self.config = EnterpriseTemplateConfig(
            enable_async=True,  # 异步测试需要启用
            enable_debug=False,
            enable_legacy_support=False
        )
        # 在异步测试中不在setUp中创建引擎，而是在测试方法中创建
    
    def tearDown(self):
        """测试后清理"""
        # 异步测试中引擎在测试方法中创建和清理
        pass
    
    def test_async_render_basic(self):
        """测试基础异步渲染"""
        async def async_test():
            engine = EnterpriseTemplateEngine(self.config)
            try:
                template = "Hello {{ name }}!"
                context = {'name': 'Async World'}

                result = await engine.render_async(template, context)

                self.assertIsInstance(result, str)
                self.assertEqual(engine.render_count, 1)
                self.assertGreater(engine.total_render_time, 0)
            finally:
                engine.cleanup()

        asyncio.run(async_test())
    
    def test_async_render_with_data_source(self):
        """测试带数据源的异步渲染"""
        async def async_test():
            engine = EnterpriseTemplateEngine(self.config)
            try:
                template = "Count: {{ items|length }}"
                context = {'items': [1, 2, 3, 4, 5]}

                result = await engine.render_async(template, context)

                self.assertIsInstance(result, str)
                self.assertEqual(engine.render_count, 1)
            finally:
                engine.cleanup()

        asyncio.run(async_test())
    
    def test_async_error_handling(self):
        """测试异步错误处理"""
        async def async_test():
            engine = EnterpriseTemplateEngine(self.config)
            try:
                invalid_template = "Hello {{ undefined.method() }}!"

                with self.assertRaises(Exception):
                    await engine.render_async(invalid_template, {})

                self.assertEqual(engine.error_count, 1)
            finally:
                engine.cleanup()

        asyncio.run(async_test())


if __name__ == '__main__':
    unittest.main()
