# AI插件

## 功能描述
提供完整的AI服务集成能力，支持OpenAI、<PERSON>、Ollama等多种AI服务提供者。基于智能路由和缓存机制，提供高性能的AI任务处理。

## 支持的格式/类型
- **AI配置字典**: 包含service_type、model、provider等的配置对象
- **文本提示字符串**: 简单的文本提示，自动识别为文本生成任务
- **对话消息列表**: 包含role和content的对话消息数组
- **AI任务字典**: 包含具体任务类型和参数的配置

## 使用示例

### 模板中使用
```jinja2
{# 文本生成 #}
{% set result = sd.ai({
    'service_type': 'text_generation',
    'prompt': '请写一篇关于人工智能的文章',
    'model': 'gpt-3.5-turbo',
    'max_tokens': 1000
}) %}

{# 对话 #}
{% set conversation = sd.ai({
    'service_type': 'conversation',
    'messages': [
        {'role': 'user', 'content': '你好，请介绍一下自己'},
        {'role': 'assistant', 'content': '你好！我是AI助手'},
        {'role': 'user', 'content': '你能帮我做什么？'}
    ],
    'model': 'gpt-4'
}) %}

{# 文本分析 #}
{% set analysis = sd.ai({
    'service_type': 'text_analysis',
    'text': '这是一段需要分析的文本内容',
    'provider': 'claude'
}) %}

{# 文本摘要 #}
{% set summary = sd.ai({
    'service_type': 'summarization',
    'text': '这是一篇很长的文章内容...',
    'model': 'claude-3-sonnet'
}) %}

{# 翻译 #}
{% set translation = sd.ai({
    'service_type': 'translation',
    'text': 'Hello, how are you?',
    'target_language': '中文',
    'provider': 'openai'
}) %}

{# 简单文本生成 #}
{% set simple_result = sd.ai('请解释什么是机器学习') %}

{# 错误处理 #}
{% if result.success %}
    生成的文本: {{ result.generated_text }}
    使用的模型: {{ result.model }}
    Token使用: {{ result.usage.total_tokens }}
{% else %}
    生成失败: {{ result.error }}
{% endif %}
```

### Python中使用
```python
from plugins.ai.smart_ai_loader import global_ai_loader

# 文本生成
result = global_ai_loader.load({
    'service_type': 'text_generation',
    'prompt': '请写一首诗',
    'model': 'gpt-3.5-turbo'
})

# 异步处理
result = await global_ai_loader.load_async({
    'service_type': 'conversation',
    'messages': [{'role': 'user', 'content': '你好'}]
})

# 批量处理
requests = [
    {'service_type': 'text_generation', 'prompt': '提示1'},
    {'service_type': 'text_generation', 'prompt': '提示2'}
]
results = await global_ai_loader.batch_process_async(requests)
```

## 配置选项
| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| service_type | string | 'text_generation' | AI服务类型 |
| provider | string | 'openai' | AI服务提供者 |
| model | string | 自动选择 | 使用的AI模型 |
| max_tokens | integer | 1000 | 最大生成token数 |
| temperature | number | 0.7 | 生成温度 |
| cache | boolean | true | 是否启用缓存 |
| timeout | number | 30.0 | 请求超时时间 |

## 支持的AI服务提供者

### OpenAI
- ✅ **GPT模型**: GPT-4、GPT-3.5-turbo等
- ✅ **文本嵌入**: text-embedding-ada-002等
- ✅ **多种任务**: 文本生成、对话、分析、翻译等
- ✅ **流式响应**: 支持流式文本生成

### Claude (Anthropic)
- ✅ **Claude模型**: Claude-3-opus、Claude-3-sonnet等
- ✅ **长文本处理**: 支持长文本分析和处理
- ✅ **安全对话**: 内置安全机制
- ✅ **多语言支持**: 支持多种语言处理

### Ollama (本地模型)
- ✅ **本地部署**: 支持本地模型部署
- ✅ **开源模型**: Llama、Mistral等开源模型
- ✅ **隐私保护**: 数据不离开本地环境
- ✅ **自定义模型**: 支持自定义模型加载

## 支持的AI服务类型

### 文本生成 (text_generation)
```jinja2
{% set result = sd.ai({
    'service_type': 'text_generation',
    'prompt': '请写一个故事',
    'max_tokens': 500
}) %}
```

### 对话 (conversation)
```jinja2
{% set result = sd.ai({
    'service_type': 'conversation',
    'messages': [
        {'role': 'user', 'content': '你好'},
        {'role': 'assistant', 'content': '你好！有什么可以帮助你的吗？'},
        {'role': 'user', 'content': '请介绍一下Python'}
    ]
}) %}
```

### 文本分析 (text_analysis)
```jinja2
{% set result = sd.ai({
    'service_type': 'text_analysis',
    'text': '这是一段需要分析的文本'
}) %}
```

### 文本摘要 (summarization)
```jinja2
{% set result = sd.ai({
    'service_type': 'summarization',
    'text': '长文本内容...'
}) %}
```

### 翻译 (translation)
```jinja2
{% set result = sd.ai({
    'service_type': 'translation',
    'text': 'Hello world',
    'target_language': '中文'
}) %}
```

### 分类 (classification)
```jinja2
{% set result = sd.ai({
    'service_type': 'classification',
    'text': '这是一条新闻内容',
    'categories': ['科技', '体育', '娱乐', '政治']
}) %}
```

### 文本嵌入 (embedding)
```jinja2
{% set result = sd.ai({
    'service_type': 'embedding',
    'text': '需要向量化的文本',
    'model': 'text-embedding-ada-002'
}) %}
```

## 响应对象结构
```python
{
    'success': bool,           # 操作是否成功
    'provider': str,           # 使用的提供者
    'service_type': str,       # 服务类型
    'model': str,             # 使用的模型
    'generated_text': str,     # 生成的文本（文本生成）
    'response': dict,          # 对话响应（对话）
    'result': str,            # 分析结果（分析类任务）
    'embedding': list,         # 嵌入向量（嵌入）
    'usage': {                # Token使用情况
        'prompt_tokens': int,
        'completion_tokens': int,
        'total_tokens': int
    },
    'error': str,             # 错误信息（如果有）
    'metadata': {
        'timestamp': str,      # 时间戳
        'cache_hit': bool,     # 是否命中缓存
        'processing_time': float # 处理时间
    }
}
```

## 智能特性

### 自动提供者选择
- ✅ **模型识别**: 根据模型名称自动选择提供者
- ✅ **服务匹配**: 根据服务类型选择最佳提供者
- ✅ **负载均衡**: 智能分配请求到不同提供者
- ✅ **故障转移**: 提供者不可用时自动切换

### 智能缓存
- ✅ **结果缓存**: 相同请求结果缓存
- ✅ **缓存策略**: 可配置的缓存策略
- ✅ **缓存清理**: 自动和手动缓存清理
- ✅ **缓存统计**: 缓存命中率统计

### 批量处理
- ✅ **并发处理**: 支持批量并发AI请求
- ✅ **请求合并**: 智能合并相似请求
- ✅ **结果聚合**: 批量结果聚合和处理
- ✅ **错误隔离**: 单个请求失败不影响其他

## 性能优化
- ✅ **异步处理**: 基于asyncio的高性能异步处理
- ✅ **连接池**: AI服务连接池管理
- ✅ **请求限流**: 智能请求频率控制
- ✅ **超时控制**: 可配置的请求超时
- ✅ **重试机制**: 智能重试和退避策略

## 错误处理
```jinja2
{% set result = sd.ai(config) %}
{% if result.success %}
    {# 处理成功结果 #}
    结果: {{ result.generated_text or result.result }}
    提供者: {{ result.provider }}
    模型: {{ result.model }}
{% else %}
    {# 处理错误 #}
    错误信息: {{ result.error }}
    服务类型: {{ result.service_type }}
    提供者: {{ result.provider }}
{% endif %}
```

## 配置管理
```python
# 配置OpenAI
global_ai_loader.configure_provider('openai', {
    'api_key': 'your-api-key',
    'base_url': 'https://api.openai.com/v1'
})

# 配置Claude
global_ai_loader.configure_provider('claude', {
    'api_key': 'your-claude-key'
})

# 配置Ollama
global_ai_loader.configure_provider('ollama', {
    'base_url': 'http://localhost:11434'
})
```

## 版本历史
- **v2.0.0**: 完整功能实现，符合插件标准规范
  - 支持OpenAI、Claude、Ollama三大提供者
  - 智能路由和缓存机制
  - 完整的异步支持
  - 标准化接口和错误处理
- **v1.0.0**: 初始版本

## 插件标准符合性
- ✅ **插件定义**: 完整的PLUGIN_DEFINITIONS
- ✅ **主处理器**: 标准的AiDataProcessor
- ✅ **智能加载器**: SmartAILoader
- ✅ **异步支持**: 完整的异步/同步双模式
- ✅ **模板集成**: 标准的SmartDataLoader集成
- ✅ **测试覆盖**: 完整的测试套件
- ✅ **文档质量**: 详细的使用文档

**符合性等级: A级 (企业级质量)** 🏆
