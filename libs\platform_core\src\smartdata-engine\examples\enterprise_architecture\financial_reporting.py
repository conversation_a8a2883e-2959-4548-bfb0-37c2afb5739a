#!/usr/bin/env python3
"""
企业级模板引擎新架构 - 财务报表生成示例

基于新架构的完整财务报表生成解决方案
展示企业级功能在实际业务场景中的应用
"""

import sys
import os
import time
import asyncio
import tempfile
import json
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.enterprise_template_factory import EnterpriseTemplateFactory
from template.enterprise_template_integration import EnterpriseTemplateIntegration

def financial_reporting_example():
    """基于新架构的财务报表生成示例"""
    print("=== 企业级财务报表生成系统 (新架构) ===")
    
    # 🏢 创建企业级模板引擎
    print("\n🏢 初始化企业级报表引擎")
    engine = EnterpriseTemplateFactory.create_high_performance_engine()
    
    # 创建模板集成器
    integration = EnterpriseTemplateIntegration(
        enable_async=False,  # 简化示例
        enable_legacy_support=False,
        enable_debug=False
    )
    
    print("✅ 企业级报表引擎初始化完成")
    print(f"📊 支持数据源: {len(engine.get_supported_data_types())}种")
    
    # 📊 示例1：月度财务报表
    print("\n📊 示例1：月度财务报表生成")
    
    # 创建报表作用域
    scope = integration.create_template_scope('monthly_report')
    
    # 财务数据
    financial_data = {
        'company': '智慧科技股份有限公司',
        'period': '2024年7月',
        'report_date': time.strftime('%Y年%m月%d日'),
        'revenue_items': [
            {'category': '主营业务收入', 'amount': 2800000, 'budget': 2500000, 'last_year': 2200000},
            {'category': '其他业务收入', 'amount': 420000, 'budget': 400000, 'last_year': 380000},
            {'category': '投资收益', 'amount': 150000, 'budget': 100000, 'last_year': 120000}
        ],
        'expense_items': [
            {'category': '主营业务成本', 'amount': 1680000, 'budget': 1750000, 'last_year': 1540000},
            {'category': '销售费用', 'amount': 280000, 'budget': 300000, 'last_year': 260000},
            {'category': '管理费用', 'amount': 350000, 'budget': 380000, 'last_year': 320000},
            {'category': '财务费用', 'amount': 45000, 'budget': 50000, 'last_year': 42000}
        ],
        'assets': {
            'current_assets': 8500000,
            'fixed_assets': 12000000,
            'intangible_assets': 2500000,
            'total_assets': 23000000
        },
        'liabilities': {
            'current_liabilities': 3200000,
            'long_term_liabilities': 5800000,
            'total_liabilities': 9000000
        }
    }
    
    # 注册财务数据源
    financial_proxy = integration.register_data_source(scope, 'financial', financial_data)
    
    # 月度财务报表模板
    monthly_template = """
{{ financial.company }}
{{ financial.period }}财务报表
报表生成日期: {{ financial.report_date }}
=====================================

一、收入分析
-----------
{%- set total_revenue = financial.revenue_items | sum(attribute='amount') %}
{%- set total_revenue_budget = financial.revenue_items | sum(attribute='budget') %}
{%- set total_revenue_last_year = financial.revenue_items | sum(attribute='last_year') %}

{%- for item in financial.revenue_items %}
{{ item.category }}:
  本期实际: ¥{{ "{:,}".format(item.amount) }}
  本期预算: ¥{{ "{:,}".format(item.budget) }}
  上年同期: ¥{{ "{:,}".format(item.last_year) }}
  预算完成率: {{ "%.1f"|format((item.amount / item.budget * 100)) }}%
  同比增长率: {{ "%.1f"|format(((item.amount - item.last_year) / item.last_year * 100)) }}%
{%- endfor %}

收入汇总:
  本期总收入: ¥{{ "{:,}".format(total_revenue) }}
  预算总收入: ¥{{ "{:,}".format(total_revenue_budget) }}
  上年同期: ¥{{ "{:,}".format(total_revenue_last_year) }}
  预算完成率: {{ "%.1f"|format((total_revenue / total_revenue_budget * 100)) }}%
  同比增长率: {{ "%.1f"|format(((total_revenue - total_revenue_last_year) / total_revenue_last_year * 100)) }}%

二、支出分析
-----------
{%- set total_expenses = financial.expense_items | sum(attribute='amount') %}
{%- set total_expenses_budget = financial.expense_items | sum(attribute='budget') %}
{%- set total_expenses_last_year = financial.expense_items | sum(attribute='last_year') %}

{%- for item in financial.expense_items %}
{{ item.category }}:
  本期实际: ¥{{ "{:,}".format(item.amount) }}
  本期预算: ¥{{ "{:,}".format(item.budget) }}
  上年同期: ¥{{ "{:,}".format(item.last_year) }}
  预算执行率: {{ "%.1f"|format((item.amount / item.budget * 100)) }}%
  同比变动率: {{ "%.1f"|format(((item.amount - item.last_year) / item.last_year * 100)) }}%
{%- endfor %}

支出汇总:
  本期总支出: ¥{{ "{:,}".format(total_expenses) }}
  预算总支出: ¥{{ "{:,}".format(total_expenses_budget) }}
  上年同期: ¥{{ "{:,}".format(total_expenses_last_year) }}
  预算执行率: {{ "%.1f"|format((total_expenses / total_expenses_budget * 100)) }}%
  同比变动率: {{ "%.1f"|format(((total_expenses - total_expenses_last_year) / total_expenses_last_year * 100)) }}%

三、盈利分析
-----------
{%- set net_profit = total_revenue - total_expenses %}
{%- set net_profit_budget = total_revenue_budget - total_expenses_budget %}
{%- set net_profit_last_year = total_revenue_last_year - total_expenses_last_year %}

净利润: ¥{{ "{:,}".format(net_profit) }}
预算净利润: ¥{{ "{:,}".format(net_profit_budget) }}
上年同期净利润: ¥{{ "{:,}".format(net_profit_last_year) }}
净利润率: {{ "%.2f"|format((net_profit / total_revenue * 100)) }}%
预算完成率: {{ "%.1f"|format((net_profit / net_profit_budget * 100)) }}%
同比增长率: {{ "%.1f"|format(((net_profit - net_profit_last_year) / net_profit_last_year * 100)) }}%

盈利能力评价: 
{%- set profit_margin = net_profit / total_revenue * 100 %}
{%- if profit_margin > 20 %}
优秀 (净利润率 > 20%)
{%- elif profit_margin > 15 %}
良好 (净利润率 15-20%)
{%- elif profit_margin > 10 %}
一般 (净利润率 10-15%)
{%- elif profit_margin > 5 %}
偏低 (净利润率 5-10%)
{%- else %}
较差 (净利润率 < 5%)
{%- endif %}

四、资产负债分析
--------------
{%- set equity = financial.assets.total_assets - financial.liabilities.total_liabilities %}
{%- set debt_ratio = financial.liabilities.total_liabilities / financial.assets.total_assets * 100 %}
{%- set current_ratio = financial.assets.current_assets / financial.liabilities.current_liabilities %}

资产结构:
  流动资产: ¥{{ "{:,}".format(financial.assets.current_assets) }} ({{ "%.1f"|format((financial.assets.current_assets / financial.assets.total_assets * 100)) }}%)
  固定资产: ¥{{ "{:,}".format(financial.assets.fixed_assets) }} ({{ "%.1f"|format((financial.assets.fixed_assets / financial.assets.total_assets * 100)) }}%)
  无形资产: ¥{{ "{:,}".format(financial.assets.intangible_assets) }} ({{ "%.1f"|format((financial.assets.intangible_assets / financial.assets.total_assets * 100)) }}%)
  资产总计: ¥{{ "{:,}".format(financial.assets.total_assets) }}

负债结构:
  流动负债: ¥{{ "{:,}".format(financial.liabilities.current_liabilities) }}
  长期负债: ¥{{ "{:,}".format(financial.liabilities.long_term_liabilities) }}
  负债总计: ¥{{ "{:,}".format(financial.liabilities.total_liabilities) }}

所有者权益: ¥{{ "{:,}".format(equity) }}

财务比率:
  资产负债率: {{ "%.2f"|format(debt_ratio) }}%
  流动比率: {{ "%.2f"|format(current_ratio) }}
  权益比率: {{ "%.2f"|format((equity / financial.assets.total_assets * 100)) }}%

五、财务健康度评估
----------------
{%- set health_score = 0 %}
{%- if profit_margin > 15 %}
  {%- set health_score = health_score + 25 %}
{%- elif profit_margin > 10 %}
  {%- set health_score = health_score + 20 %}
{%- elif profit_margin > 5 %}
  {%- set health_score = health_score + 15 %}
{%- endif %}

{%- if debt_ratio < 50 %}
  {%- set health_score = health_score + 25 %}
{%- elif debt_ratio < 60 %}
  {%- set health_score = health_score + 20 %}
{%- elif debt_ratio < 70 %}
  {%- set health_score = health_score + 15 %}
{%- endif %}

{%- if current_ratio > 2.0 %}
  {%- set health_score = health_score + 25 %}
{%- elif current_ratio > 1.5 %}
  {%- set health_score = health_score + 20 %}
{%- elif current_ratio > 1.0 %}
  {%- set health_score = health_score + 15 %}
{%- endif %}

{%- if (total_revenue / total_revenue_budget * 100) > 100 %}
  {%- set health_score = health_score + 25 %}
{%- elif (total_revenue / total_revenue_budget * 100) > 95 %}
  {%- set health_score = health_score + 20 %}
{%- elif (total_revenue / total_revenue_budget * 100) > 90 %}
  {%- set health_score = health_score + 15 %}
{%- endif %}

财务健康度评分: {{ health_score }}/100

评级等级: 
{%- if health_score >= 90 %}
AAA级 (优秀)
{%- elif health_score >= 80 %}
AA级 (良好)
{%- elif health_score >= 70 %}
A级 (中等偏上)
{%- elif health_score >= 60 %}
BBB级 (中等)
{%- elif health_score >= 50 %}
BB级 (中等偏下)
{%- else %}
B级 (需要改善)
{%- endif %}

风险等级: 
{%- if health_score >= 80 %}
低风险
{%- elif health_score >= 60 %}
中等风险
{%- else %}
高风险
{%- endif %}

六、经营建议
-----------
{%- if profit_margin < 10 %}
• 建议优化成本结构，提高盈利能力
{%- endif %}
{%- if debt_ratio > 60 %}
• 建议降低负债率，改善财务结构
{%- endif %}
{%- if current_ratio < 1.5 %}
• 建议增加流动资产，提高短期偿债能力
{%- endif %}
{%- if (total_revenue / total_revenue_budget * 100) < 95 %}
• 建议加强销售管理，提高收入完成率
{%- endif %}
{%- if ((total_revenue - total_revenue_last_year) / total_revenue_last_year * 100) < 10 %}
• 建议制定增长策略，提高市场竞争力
{%- endif %}

报表生成完成 - {{ time.strftime('%Y-%m-%d %H:%M:%S') }}
    """.strip()
    
    # 渲染月度报表
    context = {
        'financial': financial_data,
        'time': time
    }
    
    result = engine.render(monthly_template, context)
    print("月度财务报表:")
    print(result)
    
    # 清理作用域
    integration.cleanup_template_scope('monthly_report')
    
    # 📈 获取性能统计
    stats = engine.get_performance_stats()
    print(f"\n📈 报表生成性能:")
    print(f"  渲染次数: {stats['render_count']}")
    print(f"  平均渲染时间: {stats['average_render_time']:.2f}ms")
    print(f"  总渲染时间: {stats['total_render_time']:.2f}ms")
    print(f"  错误率: {stats['error_rate']:.1f}%")
    
    engine.cleanup()
    
    print("\n🎉 企业级财务报表生成完成！")
    print("\n💼 新架构优势体现:")
    print("1. ✅ 高性能引擎：快速处理复杂财务计算")
    print("2. ✅ 企业级功能：生命周期管理、性能监控")
    print("3. ✅ 数据源管理：统一的数据访问接口")
    print("4. ✅ 模板作用域：隔离的数据处理环境")
    print("5. ✅ 智能计算：自动化财务指标计算")
    print("6. ✅ 风险评估：基于规则的智能分析")
    
    print("\n📊 报表特性:")
    print("💰 收入分析 - 预算对比、同比分析")
    print("💸 支出控制 - 成本结构、预算执行")
    print("📈 盈利能力 - 多维度盈利分析")
    print("🏦 资产负债 - 财务结构、偿债能力")
    print("🎯 健康评估 - 综合评分、风险等级")
    print("📋 经营建议 - 基于数据的管理建议")

if __name__ == "__main__":
    financial_reporting_example()
