"""
数据库适配器模块

提供各种数据库的适配器实现，支持插件化扩展
"""

from .base import DatabaseAdapterBase
from .postgresql import PostgreSQLAdapter
from .mysql import MySQLAdapter
from .sqlite import SQLiteAdapter
from .async_postgresql import AsyncPostgreSQLAdapter
from .async_mysql import AsyncMySQLAdapter
from .async_sqlite import AsyncSQLiteAdapter

__all__ = [
    'DatabaseAdapterBase',
    # 同步适配器
    'PostgreSQLAdapter',
    'MySQLAdapter',
    'SQLiteAdapter',
    # 异步适配器
    'AsyncPostgreSQLAdapter',
    'AsyncMySQLAdapter',
    'AsyncSQLiteAdapter',
]
