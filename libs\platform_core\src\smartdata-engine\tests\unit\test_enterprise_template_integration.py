"""
企业级模板集成器单元测试
"""

import unittest
import asyncio
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock

import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from template.enterprise_template_integration import EnterpriseTemplateIntegration, EnhancedSmartDataLoader
from core.enterprise_data_architecture import DataRegistry
from core.data_contract import DataResult


class TestEnterpriseTemplateIntegration(unittest.TestCase):
    """企业级模板集成器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.integration = EnterpriseTemplateIntegration(
            enable_async=False,  # 禁用异步以避免事件循环问题
            enable_legacy_support=False,  # 简化测试
            enable_debug=False  # 减少日志输出
        )
    
    def tearDown(self):
        """测试后清理"""
        # 清理所有模板作用域
        for template_id in list(self.integration.template_scopes.keys()):
            self.integration.cleanup_template_scope(template_id)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsInstance(self.integration.data_registry, DataRegistry)
        self.assertFalse(self.integration.enable_async)
        self.assertFalse(self.integration.enable_legacy_support)
        self.assertFalse(self.integration.enable_debug)
        self.assertEqual(len(self.integration.template_scopes), 0)
    
    def test_create_template_scope_sync(self):
        """测试创建同步模板作用域"""
        scope = self.integration.create_template_scope('test_sync', is_async=False)
        
        self.assertIn('test_sync', self.integration.template_scopes)
        self.assertEqual(scope.template_id, 'test_sync')
        self.assertIsNotNone(scope.data_registry)
    
    def test_create_template_scope_async(self):
        """测试创建异步模板作用域"""
        scope = self.integration.create_template_scope('test_async', is_async=True)
        
        self.assertIn('test_async', self.integration.template_scopes)
        self.assertEqual(scope.template_id, 'test_async')
        self.assertIsNotNone(scope.data_registry)
    
    def test_cleanup_template_scope(self):
        """测试清理模板作用域"""
        scope = self.integration.create_template_scope('test_cleanup')
        self.assertIn('test_cleanup', self.integration.template_scopes)
        
        self.integration.cleanup_template_scope('test_cleanup')
        self.assertNotIn('test_cleanup', self.integration.template_scopes)
    
    def test_register_data_source_memory(self):
        """测试注册内存数据源"""
        scope = self.integration.create_template_scope('test_memory')
        test_data = [{'id': 1, 'name': 'test'}]
        
        proxy = self.integration.register_data_source(scope, 'test_data', test_data)
        
        self.assertIsNotNone(proxy)
        self.assertIn('test_data', scope.data_proxies)
    
    def test_register_data_source_sqlite(self):
        """测试注册SQLite数据源"""
        scope = self.integration.create_template_scope('test_sqlite')
        
        proxy = self.integration.register_data_source(scope, 'test_db', ':memory:')
        
        self.assertIsNotNone(proxy)
        self.assertIn('test_db', scope.data_proxies)
    
    def test_create_enhanced_smart_loader(self):
        """测试创建增强智能数据加载器"""
        scope = self.integration.create_template_scope('test_loader')
        
        loader = self.integration.create_enhanced_smart_loader(scope)
        
        self.assertIsInstance(loader, EnhancedSmartDataLoader)
        self.assertEqual(loader.scope, scope)
        self.assertEqual(loader.integration, self.integration)
    
    def test_render_template_sync_basic(self):
        """测试基础同步模板渲染"""
        template = "Hello {{ name }}!"
        context = {'name': 'World'}
        
        result = self.integration.render_template_sync(template, context)
        
        # 由于没有安装Jinja2，这里主要测试流程不出错
        self.assertIsInstance(result, str)
    
    def test_render_template_sync_with_data_source(self):
        """测试带数据源的同步模板渲染"""
        template = "Data: {{ data }}"
        context = {'data': [{'id': 1, 'name': 'test'}]}
        
        result = self.integration.render_template_sync(template, context)
        
        self.assertIsInstance(result, str)
    
    def test_is_data_source_detection(self):
        """测试数据源检测"""
        # 内存数据
        self.assertTrue(self.integration._is_data_source([{'id': 1}]))
        
        # SQLite连接字符串
        self.assertTrue(self.integration._is_data_source(':memory:'))
        
        # 普通字符串
        self.assertFalse(self.integration._is_data_source('hello'))
        
        # 普通数字
        self.assertFalse(self.integration._is_data_source(123))
    
    def test_get_performance_stats(self):
        """测试性能统计"""
        stats = self.integration.get_performance_stats()
        
        self.assertIn('registered_adapters', stats)
        self.assertIn('active_scopes', stats)
        self.assertIn('async_enabled', stats)
        self.assertIn('legacy_support', stats)
        
        self.assertIsInstance(stats['registered_adapters'], int)
        self.assertIsInstance(stats['active_scopes'], int)
        self.assertIsInstance(stats['async_enabled'], bool)
        self.assertIsInstance(stats['legacy_support'], bool)
    
    def test_multiple_template_scopes(self):
        """测试多个模板作用域"""
        scope1 = self.integration.create_template_scope('scope1')
        scope2 = self.integration.create_template_scope('scope2')
        
        self.assertEqual(len(self.integration.template_scopes), 2)
        self.assertIn('scope1', self.integration.template_scopes)
        self.assertIn('scope2', self.integration.template_scopes)
        
        # 清理一个作用域
        self.integration.cleanup_template_scope('scope1')
        self.assertEqual(len(self.integration.template_scopes), 1)
        self.assertNotIn('scope1', self.integration.template_scopes)
        self.assertIn('scope2', self.integration.template_scopes)


class TestEnhancedSmartDataLoader(unittest.TestCase):
    """增强智能数据加载器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.integration = EnterpriseTemplateIntegration(
            enable_async=True,
            enable_legacy_support=False,
            enable_debug=False
        )
        self.scope = self.integration.create_template_scope('test_loader')
        self.loader = EnhancedSmartDataLoader(
            integration=self.integration,
            scope=self.scope,
            legacy_loader=None
        )
    
    def tearDown(self):
        """测试后清理"""
        self.integration.cleanup_template_scope('test_loader')
    
    def test_database_method(self):
        """测试database方法"""
        proxy = self.loader.database(':memory:')
        
        self.assertIsNotNone(proxy)
        # 检查是否注册到作用域
        self.assertTrue(len(self.scope.data_proxies) > 0)
    
    def test_memory_method(self):
        """测试memory方法"""
        test_data = [{'id': 1, 'name': 'test'}]
        proxy = self.loader.memory(test_data)
        
        self.assertIsNotNone(proxy)
        self.assertTrue(len(self.scope.data_proxies) > 0)
    
    def test_api_method_with_string(self):
        """测试api方法（字符串参数）"""
        proxy = self.loader.api('https://api.example.com')
        
        self.assertIsNotNone(proxy)
        self.assertTrue(len(self.scope.data_proxies) > 0)
    
    def test_api_method_with_config(self):
        """测试api方法（配置对象参数）"""
        config = {'base_url': 'https://api.example.com', 'timeout': 30}
        proxy = self.loader.api(config)
        
        self.assertIsNotNone(proxy)
        self.assertTrue(len(self.scope.data_proxies) > 0)
    
    def test_file_method_with_string(self):
        """测试file方法（字符串参数）"""
        with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as f:
            f.write(b'id,name\n1,test\n')
            temp_file = f.name
        
        try:
            proxy = self.loader.file(temp_file)
            self.assertIsNotNone(proxy)
            self.assertTrue(len(self.scope.data_proxies) > 0)
        finally:
            os.unlink(temp_file)
    
    def test_file_method_with_config(self):
        """测试file方法（配置对象参数）"""
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            f.write(b'[{"id": 1, "name": "test"}]')
            temp_file = f.name
        
        try:
            config = {'file_path': temp_file, 'encoding': 'utf-8'}
            proxy = self.loader.file(config)
            self.assertIsNotNone(proxy)
            self.assertTrue(len(self.scope.data_proxies) > 0)
        finally:
            os.unlink(temp_file)
    
    def test_loader_method(self):
        """测试通用loader方法"""
        test_data = [{'id': 1, 'name': 'test'}]
        proxy = self.loader.loader(test_data)
        
        self.assertIsNotNone(proxy)
        self.assertTrue(len(self.scope.data_proxies) > 0)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效数据源
        result = self.loader.database('invalid_connection')
        
        # 应该返回错误结果而不是抛出异常
        self.assertIsNotNone(result)


class TestAsyncEnterpriseTemplateIntegration(unittest.TestCase):
    """异步企业级模板集成器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.integration = EnterpriseTemplateIntegration(
            enable_async=True,
            enable_legacy_support=False,
            enable_debug=False
        )
    
    def tearDown(self):
        """测试后清理"""
        # 清理所有模板作用域
        for template_id in list(self.integration.template_scopes.keys()):
            self.integration.cleanup_template_scope(template_id)
    
    def test_async_render_template_basic(self):
        """测试基础异步模板渲染"""
        async def async_test():
            template = "Hello {{ name }}!"
            context = {'name': 'Async World'}
            
            result = await self.integration.render_template_async(template, context)
            self.assertIsInstance(result, str)
        
        # 运行异步测试
        asyncio.run(async_test())
    
    def test_async_render_template_with_data_source(self):
        """测试带数据源的异步模板渲染"""
        async def async_test():
            template = "Data count: {{ data|length }}"
            context = {'data': [{'id': 1}, {'id': 2}]}
            
            result = await self.integration.render_template_async(template, context)
            self.assertIsInstance(result, str)
        
        asyncio.run(async_test())


if __name__ == '__main__':
    unittest.main()
