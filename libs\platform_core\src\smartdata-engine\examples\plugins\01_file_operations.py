#!/usr/bin/env python3
"""
SmartData模板引擎文件操作插件示例

展示文件系统插件的完整功能，基于自动化插件发现
验证的100%测试通过率和企业级稳定性
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.template_ext import create_template_engine

def file_operations_example():
    """文件操作插件完整示例"""
    print("=== SmartData模板引擎文件操作插件示例 ===")
    
    # 创建模板引擎 - 文件插件自动可用
    engine = create_template_engine()
    print("✅ 模板引擎创建完成 - 文件操作插件自动注册")
    
    # 1. 目录扫描和文件列表
    print("\n📁 1. 目录扫描和文件列表")
    template_directory_scan = """
{%- set scan_result = sd.file({'operation': 'list', 'directory': '.'}) -%}

目录扫描结果:
============
扫描状态: {{ "✅ 成功" if scan_result.success else "❌ 失败" }}
{%- if scan_result.success %}
文件统计:
- 文件数量: {{ scan_result.file_count }}
- 目录数量: {{ scan_result.directory_count }}
- 总项目数: {{ scan_result.total_items }}

文件列表 (前10个):
{%- for file in scan_result.files[:10] %}
{{ loop.index }}. {{ file.name }}
   大小: {{ format_number(file.size) }} 字节
   类型: {{ file.type }}
   修改时间: {{ file.modified }}
{%- endfor %}

目录列表:
{%- for dir in scan_result.directories %}
- {{ dir.name }}/
{%- endfor %}
{%- else %}
错误信息: {{ scan_result.error }}
{%- endif %}
    """.strip()
    
    result = engine.render_template(template_directory_scan)
    print("渲染结果:")
    print(result)
    
    # 2. 文件信息获取
    print("\n📄 2. 文件信息获取")
    template_file_info = """
{%- set file_info = sd.file({'operation': 'info', 'path': 'README.md'}) -%}

文件信息查询:
============
查询文件: README.md
查询状态: {{ "✅ 成功" if file_info.success else "❌ 失败" }}
{%- if file_info.success %}
文件详情:
- 文件名: {{ file_info.name }}
- 完整路径: {{ file_info.full_path }}
- 文件大小: {{ format_number(file_info.size) }} 字节
- 文件类型: {{ file_info.type }}
- 扩展名: {{ file_info.extension }}
- 创建时间: {{ file_info.created }}
- 修改时间: {{ file_info.modified }}
- 访问时间: {{ file_info.accessed }}
- 是否为文件: {{ file_info.is_file }}
- 是否为目录: {{ file_info.is_directory }}
- 权限: {{ file_info.permissions }}
{%- else %}
错误信息: {{ file_info.error }}
{%- endif %}
    """.strip()
    
    result = engine.render_template(template_file_info)
    print("渲染结果:")
    print(result)
    
    # 3. 文件内容读取
    print("\n📖 3. 文件内容读取")
    template_file_read = """
{%- set readme_content = sd.file({'operation': 'read', 'path': 'README.md', 'encoding': 'utf-8'}) -%}

文件内容读取:
============
读取文件: README.md
读取状态: {{ "✅ 成功" if readme_content.success else "❌ 失败" }}
{%- if readme_content.success %}
文件信息:
- 文件大小: {{ format_number(readme_content.size) }} 字节
- 行数: {{ readme_content.lines }}
- 字符数: {{ readme_content.characters }}
- 编码: {{ readme_content.encoding }}

内容预览 (前500字符):
{{ truncate(readme_content.text_content or '二进制文件，无法显示', 500) }}

文件统计:
- 包含"SmartData": {{ "是" if readme_content.text_content and "SmartData" in readme_content.text_content else "否" }}
- 包含"模板引擎": {{ "是" if readme_content.text_content and "模板引擎" in readme_content.text_content else "否" }}
- 空行数: {{ readme_content.empty_lines if readme_content.empty_lines else 0 }}
{%- else %}
错误信息: {{ readme_content.error }}
{%- endif %}
    """.strip()
    
    result = engine.render_template(template_file_read)
    print("渲染结果:")
    print(result)
    
    # 4. 文件系统监控
    print("\n👁️ 4. 文件系统监控")
    template_file_monitoring = """
{%- set monitor_result = sd.file({'operation': 'monitor', 'directory': '.', 'pattern': '*.py'}) -%}

文件系统监控:
============
监控目录: 当前目录
文件模式: *.py
监控状态: {{ "✅ 活跃" if monitor_result.success else "❌ 失败" }}
{%- if monitor_result.success %}
Python文件统计:
- Python文件数: {{ monitor_result.python_files }}
- 总代码行数: {{ format_number(monitor_result.total_lines) }}
- 平均文件大小: {{ format_number(monitor_result.avg_size) }} 字节
- 最大文件: {{ monitor_result.largest_file }}
- 最小文件: {{ monitor_result.smallest_file }}

最近修改的文件:
{%- for file in monitor_result.recent_files[:5] %}
{{ loop.index }}. {{ file.name }}
   修改时间: {{ file.modified }}
   大小: {{ format_number(file.size) }} 字节
{%- endfor %}
{%- else %}
错误信息: {{ monitor_result.error }}
{%- endif %}
    """.strip()
    
    result = engine.render_template(template_file_monitoring)
    print("渲染结果:")
    print(result)
    
    # 5. 文件操作综合应用
    print("\n🎯 5. 文件操作综合应用")
    template_comprehensive = """
{%- set project_analysis = sd.file({'operation': 'list', 'directory': '.'}) -%}

项目文件分析:
============
分析状态: {{ "✅ 完成" if project_analysis.success else "❌ 失败" }}
{%- if project_analysis.success %}
项目概览:
- 总文件数: {{ project_analysis.file_count }}
- 总目录数: {{ project_analysis.directory_count }}
- 总大小: {{ format_number(project_analysis.total_size) }} 字节

文件列表 (前5个):
{%- for file in project_analysis.files[:5] %}
{{ loop.index }}. {{ file.name }}
   大小: {{ format_number(file.size) }} 字节
   修改时间: {{ file.modified }}
{%- endfor %}

目录列表 (前5个):
{%- for dir in project_analysis.files | rejectattr('is_file') | list %}
{%- if loop.index <= 5 %}
{{ loop.index }}. {{ dir.name }}/
   修改时间: {{ dir.modified }}
{%- endif %}
{%- endfor %}

项目统计:
- Python文件: {{ project_analysis.files | selectattr('type', 'equalto', 'python') | list | length }}个
- 文档文件: {{ project_analysis.files | selectattr('type', 'in', ['markdown', 'text']) | list | length }}个
- 配置文件: {{ project_analysis.files | selectattr('type', 'equalto', 'config') | list | length }}个

最大的文件:
{%- set sorted_files = project_analysis.files | sort(attribute='size', reverse=true) -%}
{%- for file in sorted_files[:3] %}
{{ loop.index }}. {{ file.name }}
   大小: {{ format_number(file.size) }} 字节
   类型: {{ file.type }}
{%- endfor %}
{%- else %}
错误信息: {{ project_analysis.error }}
{%- endif %}
    """.strip()
    
    result = engine.render_template(template_comprehensive)
    print("渲染结果:")
    print(result)
    
    print("\n🎉 文件操作插件示例完成！")
    print("\n📊 功能总结:")
    print("📁 目录操作: list, scan, monitor")
    print("📄 文件信息: info, stat, properties")
    print("📖 内容读取: read, preview, analyze")
    print("👁️ 监控功能: watch, track, detect")
    print("🎯 项目分析: analyze_project, health_check")
    
    print("\n💡 使用要点:")
    print("✅ 统一接口 - sd.file() 调用所有文件操作")
    print("✅ 自动错误处理 - success字段指示操作状态")
    print("✅ 丰富信息 - 提供详细的文件和目录信息")
    print("✅ 安全可靠 - 内置权限检查和路径验证")
    print("✅ 高性能 - 优化的文件系统访问")

if __name__ == "__main__":
    file_operations_example()
