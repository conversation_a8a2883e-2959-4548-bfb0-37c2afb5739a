"""
TemplateScope模板作用域管理器测试

测试TemplateScope类的所有功能，确保模板作用域管理的正确性
"""

import pytest
import time
import threading
from typing import Dict, Any, Callable, List, Optional
from unittest.mock import Mock, MagicMock

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from core.enterprise_data_architecture import (
    TemplateScope, DataRegistry, DataProxy, LifecycleManager,
    UnsupportedDataTypeError, AdapterCreationError
)


class MockAdapter:
    """模拟适配器"""
    
    def __init__(self, adapter_name: str = "MockAdapter"):
        self.adapter_name = adapter_name
    
    def supported_types(self) -> List[str]:
        return ['mock_type']
    
    def can_handle(self, data_source: Any) -> bool:
        return isinstance(data_source, dict) and data_source.get('type') == 'mock_type'
    
    def create_proxy(self, data_source: Any, lifecycle_manager):
        return DataProxy(data_source, self, lifecycle_manager)
    
    def get_operations(self) -> Dict[str, Callable]:
        return {
            'query': lambda source, sql: [{'id': 1, 'name': 'test'}],
            'execute': lambda source, cmd: {'affected_rows': 1}
        }
    
    def get_metadata(self) -> Dict[str, Any]:
        return {'adapter_name': self.adapter_name, 'version': '1.0.0'}


class TestTemplateScope:
    """TemplateScope类测试套件"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.template_id = "test_template_123"
        self.registry = DataRegistry()
        
        # 注册模拟适配器
        self.registry.register_adapter(MockAdapter)
        
        # 注册类型检测器
        def mock_detector(data_source):
            if isinstance(data_source, dict) and data_source.get('type') == 'mock_type':
                return 'mock_type'
            return None
        
        self.registry.register_type_detector(mock_detector)
        
        # 创建模板作用域
        self.scope = TemplateScope(self.template_id, self.registry)
    
    def test_scope_initialization(self):
        """测试作用域初始化"""
        assert self.scope.template_id == self.template_id
        assert self.scope.data_registry is self.registry
        assert isinstance(self.scope.lifecycle_manager, LifecycleManager)
        assert isinstance(self.scope.data_proxies, dict)
        assert len(self.scope.data_proxies) == 0
        assert hasattr(self.scope, 'logger')
        assert hasattr(self.scope, '_lock')
    
    def test_get_scope_id(self):
        """测试获取作用域ID"""
        assert self.scope.get_scope_id() == self.template_id
    
    def test_register_single_data_source(self):
        """测试注册单个数据源"""
        data_source = {'type': 'mock_type', 'name': 'test_db'}
        
        # 注册数据源
        proxy = self.scope.register_data_source('db', data_source)
        
        # 验证注册成功
        assert isinstance(proxy, DataProxy)
        assert proxy.source == data_source
        assert isinstance(proxy.adapter, MockAdapter)
        
        # 验证作用域状态
        assert len(self.scope.data_proxies) == 1
        assert 'db' in self.scope.data_proxies
        assert self.scope.data_proxies['db'] is proxy
    
    def test_register_multiple_data_sources(self):
        """测试注册多个数据源"""
        data_sources = [
            {'type': 'mock_type', 'name': 'db1'},
            {'type': 'mock_type', 'name': 'db2'},
            {'type': 'mock_type', 'name': 'api1'}
        ]
        
        proxies = []
        for i, source in enumerate(data_sources):
            proxy = self.scope.register_data_source(f'source_{i}', source)
            proxies.append(proxy)
        
        # 验证所有数据源已注册
        assert len(self.scope.data_proxies) == 3
        for i in range(3):
            assert f'source_{i}' in self.scope.data_proxies
            assert self.scope.data_proxies[f'source_{i}'] is proxies[i]
    
    def test_get_data_proxy(self):
        """测试获取数据代理"""
        data_source = {'type': 'mock_type', 'name': 'test_db'}
        
        # 注册数据源
        original_proxy = self.scope.register_data_source('db', data_source)
        
        # 获取数据代理
        retrieved_proxy = self.scope.get_data_proxy('db')
        
        # 验证获取成功
        assert retrieved_proxy is original_proxy
        
        # 测试获取不存在的数据源
        nonexistent_proxy = self.scope.get_data_proxy('nonexistent')
        assert nonexistent_proxy is None
    
    def test_list_data_sources(self):
        """测试列出数据源"""
        # 初始状态
        assert self.scope.list_data_sources() == []
        
        # 注册数据源
        sources = ['db1', 'db2', 'api1']
        for name in sources:
            self.scope.register_data_source(name, {'type': 'mock_type', 'name': name})
        
        # 验证列表
        data_source_names = self.scope.list_data_sources()
        assert set(data_source_names) == set(sources)
        assert len(data_source_names) == 3
    
    def test_remove_data_source(self):
        """测试移除数据源"""
        data_source = {'type': 'mock_type', 'name': 'test_db'}
        
        # 注册数据源
        self.scope.register_data_source('db', data_source)
        assert len(self.scope.data_proxies) == 1
        
        # 移除数据源
        success = self.scope.remove_data_source('db')
        
        # 验证移除成功
        assert success is True
        assert len(self.scope.data_proxies) == 0
        assert 'db' not in self.scope.data_proxies
        
        # 尝试移除不存在的数据源
        success = self.scope.remove_data_source('nonexistent')
        assert success is False
    
    def test_get_context_data(self):
        """测试获取上下文数据"""
        # 注册多个数据源
        sources = {
            'db': {'type': 'mock_type', 'name': 'database'},
            'api': {'type': 'mock_type', 'name': 'api_service'}
        }
        
        proxies = {}
        for name, source in sources.items():
            proxies[name] = self.scope.register_data_source(name, source)
        
        # 获取上下文数据
        context = self.scope.get_context_data()
        
        # 验证上下文数据
        assert isinstance(context, dict)
        assert set(context.keys()) == set(sources.keys())
        for name in sources.keys():
            assert context[name] is proxies[name]
    
    def test_get_scope_info(self):
        """测试获取作用域信息"""
        # 注册数据源
        self.scope.register_data_source('db', {'type': 'mock_type', 'name': 'test_db'})
        
        # 获取作用域信息
        info = self.scope.get_scope_info()
        
        # 验证信息结构
        assert isinstance(info, dict)
        assert info['template_id'] == self.template_id
        assert 'db' in info['data_sources']
        assert info['resource_count'] >= 1  # 至少有一个资源
        assert 'mock_type' in info['registry_types']
    
    def test_cleanup_scope(self):
        """测试清理作用域"""
        # 注册多个数据源
        for i in range(3):
            self.scope.register_data_source(f'source_{i}', {'type': 'mock_type', 'name': f'test_{i}'})
        
        # 验证资源已注册
        assert len(self.scope.data_proxies) == 3
        assert self.scope.lifecycle_manager.get_resource_count() >= 3
        
        # 清理作用域
        self.scope.cleanup()
        
        # 验证清理结果
        assert len(self.scope.data_proxies) == 0
        assert self.scope.lifecycle_manager.get_resource_count() == 0
    
    def test_context_manager(self):
        """测试上下文管理器"""
        with TemplateScope("context_test", self.registry) as scope:
            # 在上下文中注册数据源
            scope.register_data_source('db', {'type': 'mock_type', 'name': 'test_db'})
            
            # 验证数据源已注册
            assert len(scope.data_proxies) == 1
            assert scope.lifecycle_manager.get_resource_count() >= 1
        
        # 退出上下文后，资源应该被自动清理
        assert len(scope.data_proxies) == 0
        assert scope.lifecycle_manager.get_resource_count() == 0
    
    def test_unsupported_data_type(self):
        """测试不支持的数据类型"""
        unsupported_source = {'type': 'unsupported_type', 'name': 'test'}
        
        # 尝试注册不支持的数据类型
        with pytest.raises(UnsupportedDataTypeError):
            self.scope.register_data_source('unsupported', unsupported_source)
    
    def test_thread_safety(self):
        """测试线程安全"""
        import threading
        import time
        
        results = []
        errors = []
        
        def register_sources():
            try:
                for i in range(5):
                    source_name = f"thread_{threading.current_thread().ident}_{i}"
                    source_data = {'type': 'mock_type', 'name': source_name}
                    self.scope.register_data_source(source_name, source_data)
                results.append('registered')
            except Exception as e:
                errors.append(str(e))
        
        def cleanup_scope():
            try:
                time.sleep(0.01)  # 稍微延迟
                self.scope.cleanup()
                results.append('cleaned')
            except Exception as e:
                errors.append(str(e))
        
        # 创建多个线程同时操作
        threads = []
        for _ in range(3):
            threads.append(threading.Thread(target=register_sources))
        threads.append(threading.Thread(target=cleanup_scope))
        
        # 启动所有线程
        for thread in threads:
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证没有错误发生
        assert len(errors) == 0, f"Thread safety test failed with errors: {errors}"
        assert len(results) > 0
    
    def test_scope_isolation(self):
        """测试作用域隔离"""
        # 创建两个不同的作用域
        scope1 = TemplateScope("template_1", self.registry)
        scope2 = TemplateScope("template_2", self.registry)
        
        try:
            # 在不同作用域中注册同名数据源
            scope1.register_data_source('db', {'type': 'mock_type', 'name': 'db1'})
            scope2.register_data_source('db', {'type': 'mock_type', 'name': 'db2'})
            
            # 验证作用域隔离
            proxy1 = scope1.get_data_proxy('db')
            proxy2 = scope2.get_data_proxy('db')
            
            assert proxy1 is not proxy2
            assert proxy1.source['name'] == 'db1'
            assert proxy2.source['name'] == 'db2'
            
            # 验证清理隔离
            scope1.cleanup()
            assert len(scope1.data_proxies) == 0
            assert len(scope2.data_proxies) == 1  # scope2不受影响
            
        finally:
            scope1.cleanup()
            scope2.cleanup()


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, '-v'])
