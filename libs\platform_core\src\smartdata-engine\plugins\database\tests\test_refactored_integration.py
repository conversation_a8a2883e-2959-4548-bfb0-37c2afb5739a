#!/usr/bin/env python3
"""
Database插件重构后集成测试

基于插件标准规范的完整集成测试，验证重构后的功能完整性：
- 测试统一接口的兼容性
- 验证企业级功能的保留
- 使用真实PostgreSQL数据库测试
"""

import pytest
import asyncio
import logging
import sys
import os

# 添加路径以便导入
current_dir = os.path.dirname(__file__)
project_root = os.path.abspath(os.path.join(current_dir, '../../../../..'))
sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 测试配置 - 真实PostgreSQL数据库
TEST_DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'username': 'admin',
    'password': 'admin123',
    'database': 'nacos_db',
    'database_type': 'postgresql'
}

TEST_CONNECTION_STRING = "postgresql://admin:admin123@localhost:5432/nacos_db"


class TestRefactoredDatabasePlugin:
    """重构后Database插件集成测试"""
    
    def test_plugin_standards_compliance(self):
        """测试插件标准符合性"""
        try:
            # 测试插件定义
            from plugins.database import get_plugin_definitions, PLUGIN_DEFINITIONS
            
            definitions = get_plugin_definitions()
            assert isinstance(definitions, list)
            assert len(definitions) > 0
            
            plugin_def = definitions[0]
            
            # 检查必需字段
            required_fields = ['id', 'name', 'description', 'version', 'type', 'category', 'priority']
            for field in required_fields:
                assert field in plugin_def, f"缺少必需字段: {field}"
            
            logger.info(f"✅ 插件标准符合性检查通过: {plugin_def['name']}")
            
        except ImportError as e:
            logger.error(f"❌ 插件导入失败: {e}")
            pytest.fail(f"插件导入失败: {e}")
    
    def test_unified_connector_interface(self):
        """测试统一连接器接口"""
        try:
            from plugins.database.connectors import (
                ConnectorFactory, 
                EnterpriseConnectorFactory,
                ConnectionResult,
                QueryResult
            )
            
            # 测试主要工厂
            main_factory = ConnectorFactory()
            supported_types = main_factory.get_supported_types()
            assert 'postgresql' in supported_types
            
            # 测试企业级兼容工厂
            enterprise_factory = EnterpriseConnectorFactory()
            enterprise_dbs = enterprise_factory.get_supported_databases()
            assert 'postgresql' in enterprise_dbs
            
            # 验证兼容性
            assert set(supported_types) == set(enterprise_dbs)
            
            logger.info("✅ 统一连接器接口测试通过")
            
        except ImportError as e:
            logger.error(f"❌ 连接器导入失败: {e}")
            pytest.fail(f"连接器导入失败: {e}")
    
    def test_unified_processor_interface(self):
        """测试统一处理器接口"""
        try:
            from plugins.database.database_processor import (
                DatabaseProcessor,
                EnterpriseDatabaseProcessor
            )
            
            # 测试主要处理器
            main_processor = DatabaseProcessor()
            main_info = main_processor.get_processor_info()
            
            # 测试企业级兼容处理器
            enterprise_processor = EnterpriseDatabaseProcessor()
            enterprise_info = enterprise_processor.get_processor_info()
            
            # 验证企业级处理器是主处理器的子类
            assert isinstance(enterprise_processor, DatabaseProcessor)
            assert enterprise_info['id'] == 'enterprise_database_processor'
            
            logger.info("✅ 统一处理器接口测试通过")
            
        except ImportError as e:
            logger.error(f"❌ 处理器导入失败: {e}")
            pytest.fail(f"处理器导入失败: {e}")
    
    @pytest.mark.asyncio
    async def test_real_database_functionality(self):
        """测试真实数据库功能"""
        try:
            from plugins.database.connectors import ConnectorFactory, ConnectionConfig
            
            # 创建连接器
            connector = ConnectorFactory.create_connector('postgresql', enable_debug=True)
            # 创建连接配置（移除不支持的字段）
            config_dict = TEST_DB_CONFIG.copy()
            config_dict.pop('database_type', None)  # 移除不支持的字段
            config = ConnectionConfig(**config_dict)
            
            # 测试连接
            pool = await connector.create_pool(config)
            
            # 测试基础查询
            result = await connector.execute_query(pool, "SELECT 1 as test_value", {})
            assert result.success is True
            assert result.data[0]['test_value'] == 1
            
            # 测试数据库信息查询
            db_info_result = await connector.execute_query(pool, "SELECT version() as db_version", {})
            assert db_info_result.success is True
            assert 'PostgreSQL' in db_info_result.data[0]['db_version']
            
            # 关闭连接
            await connector.close_pool(pool)
            
            logger.info("✅ 真实数据库功能测试通过")
            
        except Exception as e:
            logger.warning(f"⚠️ 数据库功能测试跳过（数据库不可用）: {e}")
            pytest.skip(f"数据库不可用: {e}")
    
    @pytest.mark.asyncio
    async def test_enterprise_wrapper_functionality(self):
        """测试企业级包装器功能"""
        try:
            from plugins.database.connectors import EnterpriseConnectorFactory
            
            # 创建企业级连接器
            connector = EnterpriseConnectorFactory.create_connector('postgresql', TEST_DB_CONFIG)
            
            # 测试连接
            conn_result = await connector.connect()
            if not conn_result.success:
                pytest.skip(f"数据库连接失败: {conn_result.error}")
            
            # 测试查询
            query_result = await connector.execute_query("SELECT 'Hello from Enterprise' as message")
            assert query_result.success is True
            assert query_result.data[0]['message'] == 'Hello from Enterprise'
            
            # 测试兼容属性
            assert query_result.row_count == query_result.affected_rows
            
            # 断开连接
            await connector.disconnect()
            
            logger.info("✅ 企业级包装器功能测试通过")
            
        except Exception as e:
            logger.warning(f"⚠️ 企业级包装器测试跳过: {e}")
            pytest.skip(f"企业级包装器测试失败: {e}")
    
    def test_smart_data_object_integration(self):
        """测试SmartDataObject集成"""
        try:
            from core.smart_data_object import SmartDataLoader
            
            # 创建智能数据加载器
            loader = SmartDataLoader()
            
            # 测试数据库方法
            query_data = {
                'type': 'database_query',
                'connection_string': TEST_CONNECTION_STRING,
                'query': 'SELECT 1 as test_value',
                'config': TEST_DB_CONFIG
            }
            
            result = loader.database(query_data)
            assert hasattr(result, 'data')
            
            logger.info("✅ SmartDataObject集成测试通过")
            
        except Exception as e:
            logger.warning(f"⚠️ SmartDataObject集成测试跳过: {e}")
            pytest.skip(f"SmartDataObject集成失败: {e}")
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        try:
            # 测试所有原有的导入路径是否仍然有效
            from plugins.database import (
                DatabaseProcessor,
                EnterpriseDatabaseProcessor,
                ConnectorFactory,
                EnterpriseConnectorFactory,
                ConnectionResult,
                QueryResult,
                ConnectionConfig
            )
            
            # 测试企业级接口仍然可用
            enterprise_processor = EnterpriseDatabaseProcessor()
            assert enterprise_processor.processor_id == 'enterprise_database_processor'
            
            enterprise_factory = EnterpriseConnectorFactory()
            supported_dbs = enterprise_factory.get_supported_databases()
            assert len(supported_dbs) > 0
            
            logger.info("✅ 向后兼容性测试通过")
            
        except ImportError as e:
            logger.error(f"❌ 向后兼容性测试失败: {e}")
            pytest.fail(f"向后兼容性破坏: {e}")
    
    def test_no_redundant_files(self):
        """测试冗余文件已清理"""
        import os
        
        plugin_dir = os.path.dirname(os.path.dirname(__file__))
        
        # 检查enterprise文件是否已删除
        enterprise_files = [
            'enterprise_connectors.py',
            'enterprise_database_processor.py'
        ]
        
        for file_name in enterprise_files:
            file_path = os.path.join(plugin_dir, file_name)
            assert not os.path.exists(file_path), f"冗余文件仍然存在: {file_name}"
        
        logger.info("✅ 冗余文件清理验证通过")
    
    def test_performance_features(self):
        """测试性能特性保留"""
        try:
            from plugins.database.database_processor import DatabaseProcessor
            
            processor = DatabaseProcessor()
            info = processor.get_processor_info()
            
            # 检查企业级功能是否保留
            expected_capabilities = [
                'multi_database_support',
                'connection_pooling',
                'query_optimization',
                'intelligent_caching',
                'transaction_management',
                'security_authentication',
                'performance_monitoring',
                'failover_support',
                'batch_processing',
                'async_processing'
            ]
            
            capabilities = info.get('capabilities', [])
            for capability in expected_capabilities:
                assert capability in capabilities, f"缺少企业级功能: {capability}"
            
            logger.info("✅ 性能特性保留验证通过")
            
        except Exception as e:
            logger.error(f"❌ 性能特性验证失败: {e}")
            pytest.fail(f"性能特性验证失败: {e}")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short"])
