#!/usr/bin/env python3
"""
SmartData模板引擎自定义语法示例

展示如何自定义Jinja2的语法定界符，支持多种模板格式
解决语法不统一和与其他模板系统兼容的问题
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.template_ext import create_template_engine

def custom_syntax_examples():
    """自定义语法完整示例"""
    print("=== SmartData模板引擎自定义语法示例 ===")
    
    # 1. 默认Jinja2语法
    print("\n📝 1. 默认Jinja2语法")
    default_engine = create_template_engine()
    
    default_template = """
默认Jinja2语法:
{%- set user = {'name': '张三', 'age': 28} -%}
用户信息:
- 姓名: {{ user.name }}
- 年龄: {{ user.age }}
- 当前时间: {{ now() }}
{# 这是注释 #}
    """.strip()
    
    result = default_engine.render_template(default_template)
    print("模板:")
    print(default_template)
    print("\n渲染结果:")
    print(result)
    
    # 2. JSP/ASP风格语法
    print("\n🌐 2. JSP/ASP风格语法")
    jsp_syntax = {
        'block_start_string': '<%',
        'block_end_string': '%>',
        'variable_start_string': '<%=',
        'variable_end_string': '%>',
        'comment_start_string': '<%--',
        'comment_end_string': '--%>'
    }
    
    jsp_engine = create_template_engine(custom_syntax=jsp_syntax)
    
    jsp_template = """
JSP/ASP风格语法:
<%- set user = {'name': '李四', 'department': '技术部'} -%>
员工信息:
- 姓名: <%= user.name %>
- 部门: <%= user.department %>
- 当前时间: <%= now() %>
<%-- 这是JSP风格的注释 --%>
    """.strip()
    
    result = jsp_engine.render_template(jsp_template)
    print("模板:")
    print(jsp_template)
    print("\n渲染结果:")
    print(result)
    
    # 3. Shell/Bash变量风格语法
    print("\n🐚 3. Shell/Bash变量风格语法")
    shell_syntax = {
        'block_start_string': '#{',
        'block_end_string': '}',
        'variable_start_string': '${',
        'variable_end_string': '}',
        'comment_start_string': '#[',
        'comment_end_string': ']'
    }
    
    shell_engine = create_template_engine(custom_syntax=shell_syntax)
    
    shell_template = """
Shell/Bash风格语法:
#{- set config = {'host': 'localhost', 'port': 8080} -}
服务器配置:
- 主机: ${config.host}
- 端口: ${config.port}
- 时间戳: ${timestamp()}
#[ 这是Shell风格的注释 ]
    """.strip()
    
    result = shell_engine.render_template(shell_template)
    print("模板:")
    print(shell_template)
    print("\n渲染结果:")
    print(result)
    
    # 4. Mustache风格语法
    print("\n👨 4. Mustache风格语法")
    mustache_syntax = {
        'block_start_string': '{{#',
        'block_end_string': '}}',
        'variable_start_string': '{{',
        'variable_end_string': '}}',
        'comment_start_string': '{{!',
        'comment_end_string': '}}'
    }
    
    mustache_engine = create_template_engine(custom_syntax=mustache_syntax)
    
    mustache_template = """
Mustache风格语法:
{{#- set products = [{'name': '笔记本', 'price': 5000}, {'name': '鼠标', 'price': 100}] -}}
产品列表:
{{# for product in products }}
- {{product.name}}: ¥{{format_number(product.price)}}
{{# endfor }}
总价: ¥{{format_number(products | sum(attribute='price'))}}
{{! 这是Mustache风格的注释 }}
    """.strip()
    
    result = mustache_engine.render_template(mustache_template)
    print("模板:")
    print(mustache_template)
    print("\n渲染结果:")
    print(result)
    
    # 5. 自定义企业标准语法
    print("\n🏢 5. 自定义企业标准语法")
    enterprise_syntax = {
        'block_start_string': '[%',
        'block_end_string': '%]',
        'variable_start_string': '[=',
        'variable_end_string': '=]',
        'comment_start_string': '[#',
        'comment_end_string': '#]'
    }
    
    enterprise_engine = create_template_engine(custom_syntax=enterprise_syntax)
    
    enterprise_template = """
企业标准语法:
[%- set financial_data = {
    'revenue': 1000000,
    'expenses': 750000,
    'profit': 250000
} -%]

财务报表:
=======
营业收入: ¥[=format_number(financial_data.revenue)=]
营业支出: ¥[=format_number(financial_data.expenses)=]
净利润: ¥[=format_number(financial_data.profit)=]
利润率: [=calculate_percentage(financial_data.profit, financial_data.revenue)=]%

[%- if financial_data.profit > 0 -%]
盈利状况: ✅ 盈利
[%- else -%]
盈利状况: ❌ 亏损
[%- endif -%]

生成时间: [=now()=]
[# 企业标准模板注释 #]
    """.strip()
    
    result = enterprise_engine.render_template(enterprise_template)
    print("模板:")
    print(enterprise_template)
    print("\n渲染结果:")
    print(result)
    
    # 6. JMeter兼容语法示例
    print("\n🔧 6. JMeter兼容语法示例")
    jmeter_syntax = {
        'block_start_string': '${__',
        'block_end_string': '__}',
        'variable_start_string': '${',
        'variable_end_string': '}',
        'comment_start_string': '${#',
        'comment_end_string': '#}'
    }
    
    jmeter_engine = create_template_engine(custom_syntax=jmeter_syntax)
    
    jmeter_template = """
JMeter兼容语法:
${__- set test_data = {
    'host': 'api.example.com',
    'port': 443,
    'protocol': 'https'
} -__}

API测试配置:
- 主机: ${test_data.host}
- 端口: ${test_data.port}
- 协议: ${test_data.protocol}
- 完整URL: ${test_data.protocol}://${test_data.host}:${test_data.port}
- 时间戳: ${timestamp()}

${__- for i in range(3) -__}
测试用例${i + 1}: ${test_data.protocol}://${test_data.host}/api/test${i + 1}
${__- endfor -__}

${# JMeter兼容注释 #}
    """.strip()
    
    result = jmeter_engine.render_template(jmeter_template)
    print("模板:")
    print(jmeter_template)
    print("\n渲染结果:")
    print(result)
    
    # 7. 语法对比总结
    print("\n📊 7. 语法对比总结")
    
    comparison_data = [
        {
            'name': '默认Jinja2',
            'block': '{% %}',
            'variable': '{{ }}',
            'comment': '{# #}',
            'use_case': 'Web模板，Python生态'
        },
        {
            'name': 'JSP/ASP风格',
            'block': '<% %>',
            'variable': '<%= %>',
            'comment': '<%-- --%>',
            'use_case': 'Java/C#开发者熟悉'
        },
        {
            'name': 'Shell风格',
            'block': '#{  }',
            'variable': '${ }',
            'comment': '#[ ]',
            'use_case': '运维脚本，配置文件'
        },
        {
            'name': 'Mustache风格',
            'block': '{{# }}',
            'variable': '{{ }}',
            'comment': '{{! }}',
            'use_case': '前端模板，多语言支持'
        },
        {
            'name': '企业标准',
            'block': '[% %]',
            'variable': '[= =]',
            'comment': '[# #]',
            'use_case': '企业内部标准化'
        },
        {
            'name': 'JMeter兼容',
            'block': '${__ __}',
            'variable': '${ }',
            'comment': '${# #}',
            'use_case': '性能测试，API测试'
        }
    ]
    
    print("语法对比表:")
    print("=" * 80)
    print(f"{'语法名称':<12} {'块语法':<12} {'变量语法':<12} {'注释语法':<12} {'适用场景'}")
    print("-" * 80)
    
    for syntax in comparison_data:
        print(f"{syntax['name']:<12} {syntax['block']:<12} {syntax['variable']:<12} {syntax['comment']:<12} {syntax['use_case']}")
    
    print("\n🎉 自定义语法示例完成！")
    print("\n💡 关键优势:")
    print("✅ 语法灵活性 - 支持多种模板格式")
    print("✅ 团队统一 - 可以制定企业内部标准")
    print("✅ 工具兼容 - 与JMeter等工具无缝集成")
    print("✅ 迁移友好 - 支持从其他模板系统迁移")
    print("✅ 学习成本低 - 使用团队熟悉的语法")
    
    print("\n🔧 使用建议:")
    print("1. 团队协作：统一使用一种语法风格")
    print("2. 工具集成：选择与现有工具兼容的语法")
    print("3. 文档规范：在项目文档中明确语法标准")
    print("4. 渐进迁移：可以逐步从旧语法迁移到新语法")

if __name__ == "__main__":
    custom_syntax_examples()
