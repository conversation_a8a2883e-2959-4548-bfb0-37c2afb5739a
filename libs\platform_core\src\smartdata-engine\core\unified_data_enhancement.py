#!/usr/bin/env python3
"""
统一数据增强系统

将旧模板引擎的强大功能集成到新架构中，提供：
1. 统一的XPath/JSONPath支持
2. 可编辑的数据操作
3. 多格式数据处理
4. Python dict的路径访问增强
"""

import json
import logging
from typing import Any, Dict, List, Optional, Union, Callable
from abc import ABC, abstractmethod

# 导入旧模板引擎的强大组件
try:
    from ..template.components.jsonpath_resolver import JSONPathResolver
    from ..template.components.enhanced_data_types_v2 import EnhancedPathResult, NodeExistenceType
    from ..template.components.smart_data_factory import SmartDataFactory
    from ..template.components.smart_json_data import SmartJSONData
    from ..template.components.smart_xml_data import SmartXMLData
    LEGACY_COMPONENTS_AVAILABLE = True
except ImportError:
    LEGACY_COMPONENTS_AVAILABLE = False


class IEnhancedData(ABC):
    """增强数据接口"""
    
    @abstractmethod
    def get(self, path: str, default: Any = None) -> Any:
        """通过路径获取数据"""
        pass
    
    @abstractmethod
    def set(self, path: str, value: Any) -> bool:
        """通过路径设置数据"""
        pass
    
    @abstractmethod
    def delete(self, path: str) -> bool:
        """通过路径删除数据"""
        pass
    
    @abstractmethod
    def exists(self, path: str) -> bool:
        """检查路径是否存在"""
        pass
    
    @abstractmethod
    def find(self, pattern: str) -> List[str]:
        """查找匹配的路径"""
        pass


class EnhancedDict(dict, IEnhancedData):
    """
    增强的字典类型
    
    在Python原生dict基础上增加XPath路径访问功能
    """
    
    def __init__(self, data: Dict = None):
        super().__init__(data or {})
        self._jsonpath_resolver = JSONPathResolver() if LEGACY_COMPONENTS_AVAILABLE else None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def get(self, path: str, default: Any = None) -> Any:
        """
        通过路径获取数据
        
        支持多种路径格式：
        - JSONPath: $.user.name
        - 点分路径: user.name
        - 数组索引: users[0].name
        """
        if not path:
            return default
        
        # 如果是简单的key，直接使用dict的get方法
        if '.' not in path and '[' not in path and not path.startswith('$'):
            return super().get(path, default)
        
        # 使用JSONPath解析器
        if self._jsonpath_resolver:
            try:
                results = self._jsonpath_resolver.resolve(path, self)
                return results[0] if results else default
            except Exception as e:
                self.logger.debug(f"JSONPath解析失败: {path}, 错误: {e}")
                return default
        
        # 简单的点分路径解析（备用方案）
        return self._simple_path_get(path, default)
    
    def _simple_path_get(self, path: str, default: Any = None) -> Any:
        """简单的点分路径获取"""
        try:
            current = self
            parts = path.split('.')
            
            for part in parts:
                if '[' in part and ']' in part:
                    # 处理数组索引 like: users[0]
                    key, index_part = part.split('[', 1)
                    index = int(index_part.rstrip(']'))
                    current = current[key][index]
                else:
                    current = current[part]
            
            return current
        except (KeyError, IndexError, TypeError, ValueError):
            return default
    
    def set(self, path: str, value: Any) -> bool:
        """通过路径设置数据"""
        if not path:
            return False
        
        # 简单key直接设置
        if '.' not in path and '[' not in path:
            self[path] = value
            return True
        
        # 复杂路径设置
        try:
            parts = path.split('.')
            current = self
            
            # 导航到父级
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
            
            # 设置最终值
            final_key = parts[-1]
            current[final_key] = value
            return True
            
        except Exception as e:
            self.logger.error(f"路径设置失败: {path}, 错误: {e}")
            return False
    
    def delete(self, path: str) -> bool:
        """通过路径删除数据"""
        if not path:
            return False
        
        # 简单key直接删除
        if '.' not in path and '[' not in path:
            if path in self:
                del self[path]
                return True
            return False
        
        # 复杂路径删除
        try:
            parts = path.split('.')
            current = self
            
            # 导航到父级
            for part in parts[:-1]:
                current = current[part]
            
            # 删除最终key
            final_key = parts[-1]
            if final_key in current:
                del current[final_key]
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"路径删除失败: {path}, 错误: {e}")
            return False
    
    def exists(self, path: str) -> bool:
        """检查路径是否存在"""
        return self.get(path, object()) is not object()
    
    def find(self, pattern: str) -> List[str]:
        """查找匹配的路径"""
        # 简单实现，可以后续增强
        paths = []
        self._collect_paths(self, '', paths)

        # 简单的模式匹配
        import re
        # 转义特殊字符，只保留*作为通配符
        escaped_pattern = re.escape(pattern).replace('\\*', '.*')
        try:
            compiled_pattern = re.compile(escaped_pattern)
            return [path for path in paths if compiled_pattern.search(path)]
        except re.PatternError:
            # 如果正则表达式有问题，使用简单的字符串匹配
            if '*' in pattern:
                # 简单的通配符匹配
                prefix = pattern.split('*')[0]
                return [path for path in paths if prefix in path]
            else:
                # 精确匹配
                return [path for path in paths if pattern in path]
    
    def _collect_paths(self, obj: Any, prefix: str, paths: List[str]):
        """收集所有路径"""
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{prefix}.{key}" if prefix else key
                paths.append(current_path)
                self._collect_paths(value, current_path, paths)
        elif isinstance(obj, list):
            for i, value in enumerate(obj):
                current_path = f"{prefix}[{i}]"
                paths.append(current_path)
                self._collect_paths(value, current_path, paths)


class EnhancedList(list, IEnhancedData):
    """增强的列表类型"""
    
    def __init__(self, data: List = None):
        super().__init__(data or [])
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def get(self, path: str, default: Any = None) -> Any:
        """通过路径获取数据"""
        try:
            if path.isdigit():
                index = int(path)
                return self[index] if 0 <= index < len(self) else default
            
            # 支持负数索引
            if path.startswith('-') and path[1:].isdigit():
                index = int(path)
                return self[index] if -len(self) <= index < 0 else default
            
            # 支持切片 like: 0:5
            if ':' in path:
                parts = path.split(':')
                start = int(parts[0]) if parts[0] else None
                end = int(parts[1]) if len(parts) > 1 and parts[1] else None
                return self[start:end]
            
            return default
        except (ValueError, IndexError):
            return default
    
    def set(self, path: str, value: Any) -> bool:
        """通过路径设置数据"""
        try:
            if path.isdigit():
                index = int(path)
                if 0 <= index < len(self):
                    self[index] = value
                    return True
            return False
        except (ValueError, IndexError):
            return False
    
    def delete(self, path: str) -> bool:
        """通过路径删除数据"""
        try:
            if path.isdigit():
                index = int(path)
                if 0 <= index < len(self):
                    del self[index]
                    return True
            return False
        except (ValueError, IndexError):
            return False
    
    def exists(self, path: str) -> bool:
        """检查路径是否存在"""
        try:
            if path.isdigit():
                index = int(path)
                return 0 <= index < len(self)
            return False
        except ValueError:
            return False
    
    def find(self, pattern: str) -> List[str]:
        """查找匹配的路径"""
        # 简单实现
        return [str(i) for i in range(len(self))]


class UnifiedDataEnhancer:
    """
    统一数据增强器
    
    负责将普通数据转换为增强数据类型
    """
    
    def __init__(self, enable_legacy_features: bool = True):
        self.enable_legacy_features = enable_legacy_features and LEGACY_COMPONENTS_AVAILABLE
        self.smart_factory = SmartDataFactory() if self.enable_legacy_features else None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def enhance(self, data: Any) -> Any:
        """
        增强数据
        
        根据数据类型选择合适的增强策略
        """
        if data is None:
            return data
        
        # 基本类型不需要增强
        if isinstance(data, (str, int, float, bool)):
            return data
        
        # 字典类型增强
        if isinstance(data, dict):
            return self._enhance_dict(data)
        
        # 列表类型增强
        if isinstance(data, list):
            return self._enhance_list(data)
        
        # 其他类型尝试使用旧模板引擎的智能处理
        if self.enable_legacy_features and self.smart_factory:
            try:
                smart_modifier = self.smart_factory.create_modifier(data)
                if smart_modifier:
                    return smart_modifier
            except Exception as e:
                self.logger.debug(f"智能修改器创建失败: {e}")
        
        # 默认返回原数据
        return data
    
    def _enhance_dict(self, data: dict) -> EnhancedDict:
        """增强字典数据"""
        enhanced = EnhancedDict()
        
        for key, value in data.items():
            # 递归增强嵌套数据
            enhanced[key] = self.enhance(value)
        
        return enhanced
    
    def _enhance_list(self, data: list) -> EnhancedList:
        """增强列表数据"""
        enhanced = EnhancedList()
        
        for item in data:
            # 递归增强嵌套数据
            enhanced.append(self.enhance(item))
        
        return enhanced
    
    def is_enhanced(self, data: Any) -> bool:
        """检查数据是否已经增强"""
        return isinstance(data, (EnhancedDict, EnhancedList, IEnhancedData))


# 全局增强器实例
_global_enhancer = UnifiedDataEnhancer()


def enhance_data(data: Any) -> Any:
    """全局数据增强函数"""
    return _global_enhancer.enhance(data)


def is_enhanced_data(data: Any) -> bool:
    """检查是否为增强数据"""
    return _global_enhancer.is_enhanced(data)
