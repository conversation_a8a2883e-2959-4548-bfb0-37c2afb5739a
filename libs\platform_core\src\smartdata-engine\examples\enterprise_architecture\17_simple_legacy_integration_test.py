#!/usr/bin/env python3
"""
简化的旧功能集成测试

验证新架构成功集成旧模板引擎功能
"""

import sys
import os
import json
import tempfile

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.thread_safe_template_integration import ThreadSafeTemplateIntegration


def simple_legacy_integration_test():
    """简化的旧功能集成测试"""
    print("=== 简化的旧功能集成测试 ===")
    print("验证新架构集成旧模板引擎功能")
    print("=" * 60)
    
    # 创建线程安全的模板引擎
    engine = ThreadSafeTemplateIntegration(
        enable_async=True,
        enable_debug=True,
        isolation_level='thread',
        cleanup_interval=60,
        max_scope_lifetime=300
    )
    
    test_results = []
    
    # 📁 测试1：文件处理修复验证
    print("\n📁 测试1：文件处理修复验证")
    print("-" * 40)
    
    try:
        # 创建测试数据
        test_data = {
            "company": "智慧科技",
            "employees": [
                {"name": "张三", "age": 28, "salary": 8000},
                {"name": "李四", "age": 32, "salary": 12000}
            ]
        }
        
        temp_file = tempfile.mktemp(suffix='.json')
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        file_path = temp_file.replace('\\', '/')
        
        # 使用字符串拼接避免f-string问题
        template = """
文件处理测试
===========
{%- set data = sd.file('""" + file_path + """').parse() -%}
公司: {{ data.company }}
员工数: {{ data.employees | length }}
第一个员工: {{ data.employees[0].name }}
总薪资: {{ data.employees | sum(attribute='salary') }}
        """.strip()
        
        result = engine.render_template_sync(template, {}, 'file_test')
        
        if '智慧科技' in result and '张三' in result and '20000' in result:
            test_results.append("✅ 文件处理")
            print("✅ 文件处理正常")
            print(f"结果: {result}")
        else:
            test_results.append("❌ 文件处理")
            print("❌ 文件处理失败")
            print(f"结果: {result}")
        
        # 清理
        if os.path.exists(temp_file):
            os.unlink(temp_file)
            
    except Exception as e:
        test_results.append("❌ 文件处理")
        print(f"❌ 文件处理异常: {e}")
        import traceback
        traceback.print_exc()
    
    # 🔄 测试2：企业级过滤器
    print("\n🔄 测试2：企业级过滤器")
    print("-" * 40)
    
    try:
        filter_data = {
            "numbers": [1, 2, 3, 4, 5],
            "employees": [
                {"name": "张三", "salary": 8000, "active": True},
                {"name": "李四", "salary": 12000, "active": True},
                {"name": "王五", "salary": 6000, "active": False}
            ]
        }
        
        template = """
企业级过滤器测试
===============

1. 基础过滤器:
数字总和: {{ numbers | sum }}
数字展平: {{ [numbers, [6, 7]] | flatten | join(', ') }}
去重测试: {{ [1, 2, 2, 3, 3] | unique | join(', ') }}

2. 员工数据:
活跃员工: {{ employees | selectattr('active') | map(attribute='name') | list | join(', ') }}
高薪员工: {{ employees | selectattr('salary', '>', 7000) | map(attribute='name') | list | join(', ') }}
总薪资: {{ employees | sum(attribute='salary') }}

3. 分组测试:
按状态分组: {{ employees | group_by('active') | length }}个组
        """.strip()
        
        result = engine.render_template_sync(template, filter_data, 'filter_test')
        
        if '张三' in result and '26000' in result and '1, 2, 3, 4, 5, 6, 7' in result:
            test_results.append("✅ 企业级过滤器")
            print("✅ 企业级过滤器正常")
        else:
            test_results.append("❌ 企业级过滤器")
            print("❌ 企业级过滤器失败")
            print(f"结果: {result}")
            
    except Exception as e:
        test_results.append("❌ 企业级过滤器")
        print(f"❌ 企业级过滤器异常: {e}")
        import traceback
        traceback.print_exc()
    
    # 🔗 测试3：链式调用
    print("\n🔗 测试3：链式调用")
    print("-" * 40)
    
    try:
        chain_data = {
            "orders": [
                {
                    "id": 1,
                    "items": [
                        {"product": "笔记本", "price": 5000, "quantity": 2},
                        {"product": "鼠标", "price": 100, "quantity": 1}
                    ]
                },
                {
                    "id": 2,
                    "items": [
                        {"product": "键盘", "price": 300, "quantity": 1}
                    ]
                }
            ]
        }
        
        template = """
链式调用测试
===========

1. 多级链式调用:
所有产品: {{ orders | map(attribute='items') | list | flatten | map(attribute='product') | unique | list | join(', ') }}

2. 复杂计算:
{%- set all_items = orders | map(attribute='items') | list | flatten -%}
{%- set item_totals = [] -%}
{%- for item in all_items -%}
{%- set _ = item_totals.append(item.price * item.quantity) -%}
{%- endfor -%}
订单总价值: {{ item_totals | sum }}

3. 条件链式:
高价商品: {{ orders | map(attribute='items') | list | flatten | selectattr('price', '>', 200) | map(attribute='product') | list | join(', ') }}
        """.strip()
        
        result = engine.render_template_sync(template, chain_data, 'chain_test')
        
        if '笔记本' in result and '键盘' in result:
            test_results.append("✅ 链式调用")
            print("✅ 链式调用正常")
        else:
            test_results.append("❌ 链式调用")
            print("❌ 链式调用失败")
            print(f"结果: {result}")
            
    except Exception as e:
        test_results.append("❌ 链式调用")
        print(f"❌ 链式调用异常: {e}")
        import traceback
        traceback.print_exc()
    
    # 📊 生成测试报告
    print("\n📊 简化集成测试报告")
    print("=" * 60)
    
    successful_tests = len([r for r in test_results if r.startswith('✅')])
    total_tests = len(test_results)
    success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print("测试结果:")
    for result in test_results:
        print(f"  {result}")
    
    print(f"\n总体结果:")
    print(f"  成功测试: {successful_tests}/{total_tests}")
    print(f"  成功率: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("\n🎉 完美！旧模板引擎功能完全集成成功")
        print("\n✅ 成功集成的功能:")
        print("  📁 文件处理 - 完全修复")
        print("  🔄 企业级过滤器 - 完全集成")
        print("  🔗 链式调用 - 完全集成")
        
        print("\n🚀 新架构成功继承了旧模板引擎的强大功能！")
        
    elif success_rate >= 66:
        print("\n✅ 良好！大部分旧功能成功集成")
        print("  核心功能已迁移，可以投入使用")
        
    else:
        print("\n⚠️ 需要进一步优化功能集成")
    
    # 关闭引擎
    print("\n🔧 正在关闭模板引擎...")
    engine.shutdown()
    print("✅ 模板引擎已安全关闭")
    
    return success_rate


if __name__ == "__main__":
    success_rate = simple_legacy_integration_test()
    
    if success_rate == 100:
        print(f"\n🏆 集成测试: {success_rate:.1f}% - 完美成功！")
        print("🎯 新架构完全继承了旧模板引擎的强大功能！")
    elif success_rate >= 66:
        print(f"\n📊 集成测试: {success_rate:.1f}% - 良好")
        print("🎯 核心功能成功迁移，基本满足需求")
    else:
        print(f"\n🔧 集成测试: {success_rate:.1f}% - 需要优化")
