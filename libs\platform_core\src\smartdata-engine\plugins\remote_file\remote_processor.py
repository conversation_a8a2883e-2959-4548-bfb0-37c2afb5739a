"""
统一远程文件数据处理器

提供完整的远程文件处理能力，包括：
- 多协议支持（HTTP/HTTPS、FTP/SFTP、S3、WebDAV等）
- 智能缓存管理
- 断点续传和并发下载
- 进度监控和安全验证
- 连接池管理和故障恢复
"""

import logging
import asyncio
import time
import hashlib
from typing import Any, Dict, List, Optional, AsyncIterator, Union
from urllib.parse import urlparse
from dataclasses import dataclass

try:
    from ...core.smart_data_object import SmartDataObject
    from ...core.base_processor import BaseProcessor
except ImportError:
    # 绝对导入作为后备
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from core.smart_data_object import SmartDataObject
    from core.base_processor import BaseProcessor
from .protocols import ProtocolFactory, AuthConfig, DownloadProgress
from .intelligent_cache import global_cache_manager
from .connection_pool import global_connection_pool


@dataclass
class DownloadConfig:
    """下载配置"""
    url: str
    auth: Optional[AuthConfig] = None
    timeout: float = 30.0
    max_retries: int = 3
    chunk_size: int = 8192
    enable_cache: bool = True
    cache_ttl: Optional[float] = None
    enable_resume: bool = True
    enable_progress: bool = False
    concurrent_chunks: int = 1
    headers: Optional[Dict[str, str]] = None
    local_path: Optional[str] = None


class RemoteFileProcessor(BaseProcessor):
    """统一远程文件数据处理器"""

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(f"{__name__}.RemoteFileProcessor")

        # 处理器信息
        self.processor_id = "remote_file_processor"
        self.version = "2.0.0"
        self.priority = 65

        # 支持的协议
        self.supported_protocols = ProtocolFactory.get_supported_protocols()
        
        # 默认配置
        self.default_config = {
            'timeout': 30.0,
            'max_retries': 3,
            'chunk_size': 8192,
            'enable_cache': True,
            'cache_ttl': 3600,
            'enable_resume': True,
            'max_file_size': 1024 * 1024 * 1024,  # 1GB
            'concurrent_downloads': 5
        }
        
        # 协议处理器缓存
        self._protocol_handlers = {}
    
    def can_process(self, data: Any) -> bool:
        """检查是否可以处理数据"""
        if isinstance(data, str):
            # 检查是否为URL
            try:
                parsed = urlparse(data)
                return parsed.scheme.lower() in self.supported_protocols
            except:
                return False
        
        elif isinstance(data, dict):
            # 检查是否包含URL字段
            url = data.get('url') or data.get('uri') or data.get('path')
            if url:
                try:
                    parsed = urlparse(url)
                    return parsed.scheme.lower() in self.supported_protocols
                except:
                    return False
        
        elif isinstance(data, list):
            # 检查是否为URL列表
            return all(self.can_process(item) for item in data)
        
        return False
    
    async def process(self, data: Any, options: Dict[str, Any] = None) -> SmartDataObject:
        """处理远程文件数据"""
        try:
            options = options or {}
            
            if isinstance(data, str):
                # 单个URL
                return await self._process_single_url(data, options)
            
            elif isinstance(data, dict):
                # 配置对象
                return await self._process_config_object(data, options)
            
            elif isinstance(data, list):
                # 批量URL
                return await self._process_batch_urls(data, options)
            
            else:
                return SmartDataObject({
                    'success': False,
                    'error': f'不支持的数据类型: {type(data)}',
                    'processor': self.processor_id
                })
                
        except Exception as e:
            self.logger.error(f"处理远程文件失败: {e}")
            return SmartDataObject({
                'success': False,
                'error': str(e),
                'processor': self.processor_id
            })
    
    async def _process_single_url(self, url: str, options: Dict[str, Any]) -> SmartDataObject:
        """处理单个URL"""
        # 构建下载配置
        config = self._build_download_config(url, options)
        
        # 检查缓存
        if config.enable_cache:
            cache_key = self._generate_cache_key(url, options)
            cached_data = await global_cache_manager.get(cache_key)
            if cached_data is not None:
                self.logger.info(f"从缓存获取: {url}")
                return SmartDataObject({
                    'success': True,
                    'url': url,
                    'data': cached_data,
                    'size': len(cached_data) if isinstance(cached_data, (bytes, str)) else 0,
                    'cached': True,
                    'processor': self.processor_id
                })
        
        # 下载文件
        if config.enable_progress:
            return await self._download_with_progress(config)
        else:
            return await self._download_simple(config)
    
    async def _process_config_object(self, config_data: Dict[str, Any], options: Dict[str, Any]) -> SmartDataObject:
        """处理配置对象"""
        url = config_data.get('url') or config_data.get('uri') or config_data.get('path')
        if not url:
            return SmartDataObject({
                'success': False,
                'error': '配置对象中缺少URL',
                'processor': self.processor_id
            })
        
        # 合并配置
        merged_options = {**options, **config_data}
        return await self._process_single_url(url, merged_options)
    
    async def _process_batch_urls(self, urls: List[str], options: Dict[str, Any]) -> SmartDataObject:
        """批量处理URL"""
        max_concurrent = options.get('concurrent_downloads', self.default_config['concurrent_downloads'])
        
        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def download_with_semaphore(url):
            async with semaphore:
                return await self._process_single_url(url, options)
        
        # 并发下载
        tasks = [download_with_semaphore(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        successful_downloads = []
        failed_downloads = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed_downloads.append({
                    'url': urls[i],
                    'error': str(result)
                })
            elif hasattr(result, 'success') and result.success:
                successful_downloads.append(result)
            else:
                failed_downloads.append({
                    'url': urls[i],
                    'error': getattr(result, 'error', '未知错误')
                })
        
        return SmartDataObject({
            'success': len(failed_downloads) == 0,
            'total_urls': len(urls),
            'successful_downloads': len(successful_downloads),
            'failed_downloads': len(failed_downloads),
            'results': successful_downloads,
            'errors': failed_downloads,
            'processor': self.processor_id
        })
    
    async def _download_simple(self, config: DownloadConfig) -> SmartDataObject:
        """简单下载"""
        try:
            # 解析URL获取协议
            parsed_url = urlparse(config.url)
            protocol = parsed_url.scheme.lower()
            
            # 获取协议处理器
            handler = self._get_protocol_handler(protocol)
            
            # 下载数据
            start_time = time.time()
            data = await handler.download(config.url, {
                'auth': config.auth,
                'timeout': config.timeout,
                'headers': config.headers,
                'chunk_size': config.chunk_size
            })
            download_time = time.time() - start_time
            
            # 缓存数据
            if config.enable_cache:
                cache_key = self._generate_cache_key(config.url, {})
                await global_cache_manager.set(
                    cache_key, 
                    data, 
                    ttl=config.cache_ttl
                )
            
            return SmartDataObject({
                'success': True,
                'url': config.url,
                'data': data,
                'size': len(data) if isinstance(data, (bytes, str)) else 0,
                'download_time': download_time,
                'speed': len(data) / download_time if download_time > 0 else 0,
                'cached': False,
                'processor': self.processor_id
            })
            
        except Exception as e:
            self.logger.error(f"下载失败 {config.url}: {e}")
            return SmartDataObject({
                'success': False,
                'url': config.url,
                'error': str(e),
                'processor': self.processor_id
            })
    
    async def _download_with_progress(self, config: DownloadConfig) -> SmartDataObject:
        """带进度的下载"""
        try:
            # 解析URL获取协议
            parsed_url = urlparse(config.url)
            protocol = parsed_url.scheme.lower()
            
            # 获取协议处理器
            handler = self._get_protocol_handler(protocol)
            
            # 下载数据并收集进度
            progress_data = []
            data_chunks = []
            
            async for progress in handler.download_with_progress(config.url, {
                'auth': config.auth,
                'timeout': config.timeout,
                'headers': config.headers
            }):
                progress_data.append({
                    'downloaded_size': progress.downloaded_size,
                    'total_size': progress.total_size,
                    'percentage': progress.percentage,
                    'speed': progress.speed,
                    'eta': progress.eta,
                    'status': progress.status
                })
                
                if progress.status == 'completed':
                    # 这里需要从handler获取实际数据
                    # 简化处理，重新下载
                    data = await handler.download(config.url, {
                        'auth': config.auth,
                        'timeout': config.timeout,
                        'headers': config.headers
                    })
                    break
            
            # 缓存数据
            if config.enable_cache and 'data' in locals():
                cache_key = self._generate_cache_key(config.url, {})
                await global_cache_manager.set(
                    cache_key, 
                    data, 
                    ttl=config.cache_ttl
                )
            
            return SmartDataObject({
                'success': True,
                'url': config.url,
                'data': data if 'data' in locals() else b'',
                'size': len(data) if 'data' in locals() and isinstance(data, (bytes, str)) else 0,
                'progress_history': progress_data,
                'final_progress': progress_data[-1] if progress_data else None,
                'cached': False,
                'processor': self.processor_id
            })
            
        except Exception as e:
            self.logger.error(f"进度下载失败 {config.url}: {e}")
            return SmartDataObject({
                'success': False,
                'url': config.url,
                'error': str(e),
                'processor': self.processor_id
            })
    
    def _build_download_config(self, url: str, options: Dict[str, Any]) -> DownloadConfig:
        """构建下载配置"""
        # 认证配置
        auth_config = None
        if 'auth' in options:
            auth_data = options['auth']
            if isinstance(auth_data, dict):
                auth_config = AuthConfig(**auth_data)
        
        return DownloadConfig(
            url=url,
            auth=auth_config,
            timeout=options.get('timeout', self.default_config['timeout']),
            max_retries=options.get('max_retries', self.default_config['max_retries']),
            chunk_size=options.get('chunk_size', self.default_config['chunk_size']),
            enable_cache=options.get('enable_cache', self.default_config['enable_cache']),
            cache_ttl=options.get('cache_ttl', self.default_config['cache_ttl']),
            enable_resume=options.get('enable_resume', self.default_config['enable_resume']),
            enable_progress=options.get('enable_progress', False),
            concurrent_chunks=options.get('concurrent_chunks', 1),
            headers=options.get('headers'),
            local_path=options.get('local_path')
        )
    
    def _get_protocol_handler(self, protocol: str):
        """获取协议处理器"""
        if protocol not in self._protocol_handlers:
            self._protocol_handlers[protocol] = ProtocolFactory.create_handler(
                protocol, 
                self.default_config
            )
        return self._protocol_handlers[protocol]
    
    def _generate_cache_key(self, url: str, options: Dict[str, Any]) -> str:
        """生成缓存键"""
        # 包含URL和关键选项的哈希
        key_data = f"{url}_{options.get('auth', '')}_{options.get('headers', '')}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get_processor_info(self) -> Dict[str, Any]:
        """获取处理器信息"""
        return {
            'id': self.processor_id,
            'name': '企业级远程文件处理器',
            'version': self.version,
            'description': '提供企业级远程文件下载和处理能力',
            'supported_protocols': self.supported_protocols,
            'capabilities': [
                'multi_protocol_support',
                'intelligent_caching',
                'resume_download',
                'progress_monitoring',
                'concurrent_download',
                'connection_pooling',
                'security_authentication',
                'batch_processing'
            ],
            'priority': self.priority
        }
    
    def get_supported_services(self) -> List[str]:
        """获取支持的服务"""
        return [
            'download',
            'upload',
            'list_files',
            'batch_download',
            'progress_download',
            'resume_download'
        ]
    
    async def close(self):
        """关闭处理器"""
        # 关闭协议处理器
        for handler in self._protocol_handlers.values():
            if hasattr(handler, 'close'):
                await handler.close()
        
        self._protocol_handlers.clear()
        self.logger.info("远程文件处理器已关闭")


# 企业级兼容接口
class EnterpriseRemoteFileProcessor(RemoteFileProcessor):
    """企业级远程文件处理器 - 兼容接口"""

    def __init__(self):
        super().__init__()
        # 更新处理器信息以匹配企业级接口
        self.processor_id = "enterprise_remote_file_processor"
        self.version = "2.0.0"
        self.priority = 65
