"""
智能数据处理模块

提供文本分析、图像识别、语音处理、数据分类等智能数据处理能力
"""

import logging
import asyncio
from typing import Any, Dict, List, Optional, Union
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

from core.smart_data_object import SmartDataObject
from .ai_factory import AIServiceFactory, AIServiceType


class ProcessingType(Enum):
    """处理类型"""
    TEXT_ANALYSIS = "text_analysis"
    NLP_PROCESSING = "nlp_processing"
    IMAGE_RECOGNITION = "image_recognition"
    IMAGE_PROCESSING = "image_processing"
    SPEECH_RECOGNITION = "speech_recognition"
    SPEECH_SYNTHESIS = "speech_synthesis"
    DATA_CLASSIFICATION = "data_classification"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    ENTITY_EXTRACTION = "entity_extraction"
    KEYWORD_EXTRACTION = "keyword_extraction"
    LANGUAGE_DETECTION = "language_detection"


@dataclass
class ProcessingConfig:
    """处理配置"""
    processing_type: ProcessingType
    provider: str = "openai"
    model: str = "gpt-3.5-turbo"
    confidence_threshold: float = 0.8
    max_results: int = 10
    language: str = "zh"
    custom_params: Dict = None


class BaseIntelligentProcessor(ABC):
    """智能处理器基类"""
    
    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{config.processing_type.value}")
        self.ai_factory = AIServiceFactory()
    
    @abstractmethod
    async def process(self, data: Any, options: Dict = None) -> SmartDataObject:
        """处理数据"""
        pass
    
    def _validate_confidence(self, confidence: float) -> bool:
        """验证置信度"""
        return confidence >= self.config.confidence_threshold


class TextAnalysisProcessor(BaseIntelligentProcessor):
    """文本分析处理器"""
    
    def __init__(self, config: ProcessingConfig = None):
        if config is None:
            config = ProcessingConfig(
                processing_type=ProcessingType.TEXT_ANALYSIS,
                provider="openai",
                model="gpt-3.5-turbo"
            )
        super().__init__(config)
    
    async def process(self, data: Any, options: Dict = None) -> SmartDataObject:
        """文本分析处理"""
        try:
            text = self._extract_text(data)
            if not text:
                return SmartDataObject({
                    "success": False,
                    "error": "无法提取文本内容",
                    "processing_type": "text_analysis"
                })
            
            # 构建分析提示
            analysis_prompt = self._build_analysis_prompt(text, options)
            
            # 调用AI服务
            provider = self.ai_factory.create_provider(self.config.provider)
            response = await provider.process(
                AIServiceType.TEXT_ANALYSIS,
                analysis_prompt,
                {"model": self.config.model}
            )
            
            # 解析分析结果
            analysis_result = self._parse_analysis_result(response, text)
            
            return SmartDataObject({
                "success": True,
                "processing_type": "text_analysis",
                "original_text": text,
                "analysis": analysis_result,
                "language": self._detect_language(text),
                "word_count": len(text.split()),
                "char_count": len(text)
            })
            
        except Exception as e:
            self.logger.error(f"文本分析失败: {e}")
            return SmartDataObject({
                "success": False,
                "error": str(e),
                "processing_type": "text_analysis"
            })
    
    def _extract_text(self, data: Any) -> str:
        """提取文本"""
        if isinstance(data, str):
            return data
        elif isinstance(data, dict):
            return data.get("text", data.get("content", str(data)))
        else:
            return str(data)
    
    def _build_analysis_prompt(self, text: str, options: Dict = None) -> str:
        """构建分析提示"""
        prompt = "请对以下文本进行详细分析：\n\n"
        prompt += f"文本内容: {text}\n\n"
        prompt += "请提供以下分析结果：\n"
        prompt += "1. 主题和关键词\n"
        prompt += "2. 情感倾向\n"
        prompt += "3. 文本结构\n"
        prompt += "4. 语言特点\n"
        prompt += "5. 重要信息提取\n"
        
        if options:
            if "focus" in options:
                prompt += f"\n特别关注: {options['focus']}\n"
            if "analysis_type" in options:
                prompt += f"分析类型: {options['analysis_type']}\n"
        
        return prompt
    
    def _parse_analysis_result(self, response: Any, original_text: str) -> Dict:
        """解析分析结果"""
        if hasattr(response, 'result'):
            analysis_text = response.result
        elif isinstance(response, dict) and 'result' in response:
            analysis_text = response['result']
        else:
            analysis_text = str(response)
        
        return {
            "summary": analysis_text,
            "keywords": self._extract_keywords(original_text),
            "sentiment": self._analyze_sentiment(original_text),
            "entities": self._extract_entities(original_text),
            "topics": self._extract_topics(original_text)
        }
    
    def _detect_language(self, text: str) -> str:
        """检测语言"""
        # 简单的语言检测逻辑
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        if chinese_chars > len(text) * 0.3:
            return "zh"
        else:
            return "en"
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取
        words = text.split()
        # 过滤常见停用词
        stop_words = {"的", "是", "在", "有", "和", "与", "或", "但", "而", "了", "着", "过"}
        keywords = [word for word in words if len(word) > 1 and word not in stop_words]
        return keywords[:10]  # 返回前10个关键词
    
    def _analyze_sentiment(self, text: str) -> Dict:
        """情感分析"""
        positive_words = ["好", "棒", "优秀", "满意", "喜欢", "开心", "高兴", "赞"]
        negative_words = ["差", "坏", "糟糕", "不满", "讨厌", "生气", "难过", "批评"]
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        total_sentiment_words = positive_count + negative_count
        if total_sentiment_words == 0:
            return {"sentiment": "neutral", "confidence": 0.5}
        
        if positive_count > negative_count:
            confidence = positive_count / total_sentiment_words
            return {"sentiment": "positive", "confidence": confidence}
        elif negative_count > positive_count:
            confidence = negative_count / total_sentiment_words
            return {"sentiment": "negative", "confidence": confidence}
        else:
            return {"sentiment": "neutral", "confidence": 0.5}
    
    def _extract_entities(self, text: str) -> List[Dict]:
        """实体提取"""
        # 简单的实体提取逻辑
        entities = []
        
        # 检测数字
        import re
        numbers = re.findall(r'\d+', text)
        for num in numbers:
            entities.append({"text": num, "type": "NUMBER", "confidence": 0.9})
        
        # 检测日期模式
        date_patterns = re.findall(r'\d{4}年\d{1,2}月\d{1,2}日', text)
        for date in date_patterns:
            entities.append({"text": date, "type": "DATE", "confidence": 0.8})
        
        return entities[:self.config.max_results]
    
    def _extract_topics(self, text: str) -> List[str]:
        """主题提取"""
        # 简单的主题提取
        topics = []
        
        # 基于关键词的主题识别
        tech_keywords = ["技术", "科技", "AI", "人工智能", "算法", "数据"]
        business_keywords = ["商业", "市场", "销售", "客户", "产品", "服务"]
        
        text_lower = text.lower()
        
        if any(keyword in text_lower for keyword in tech_keywords):
            topics.append("技术")
        if any(keyword in text_lower for keyword in business_keywords):
            topics.append("商业")
        
        return topics


class NLPProcessor(BaseIntelligentProcessor):
    """自然语言处理器"""
    
    def __init__(self, config: ProcessingConfig = None):
        if config is None:
            config = ProcessingConfig(
                processing_type=ProcessingType.NLP_PROCESSING,
                provider="openai",
                model="gpt-3.5-turbo"
            )
        super().__init__(config)
    
    async def process(self, data: Any, options: Dict = None) -> SmartDataObject:
        """NLP处理"""
        try:
            text = self._extract_text(data)
            processing_tasks = options.get("tasks", ["tokenization", "pos_tagging", "ner"]) if options else ["tokenization"]
            
            results = {}
            
            for task in processing_tasks:
                if task == "tokenization":
                    results["tokens"] = self._tokenize(text)
                elif task == "pos_tagging":
                    results["pos_tags"] = self._pos_tagging(text)
                elif task == "ner":
                    results["named_entities"] = self._named_entity_recognition(text)
                elif task == "dependency_parsing":
                    results["dependencies"] = self._dependency_parsing(text)
                elif task == "sentiment":
                    results["sentiment"] = self._sentiment_analysis(text)
            
            return SmartDataObject({
                "success": True,
                "processing_type": "nlp_processing",
                "original_text": text,
                "results": results,
                "tasks_completed": processing_tasks
            })
            
        except Exception as e:
            self.logger.error(f"NLP处理失败: {e}")
            return SmartDataObject({
                "success": False,
                "error": str(e),
                "processing_type": "nlp_processing"
            })
    
    def _extract_text(self, data: Any) -> str:
        """提取文本"""
        if isinstance(data, str):
            return data
        elif isinstance(data, dict):
            return data.get("text", data.get("content", str(data)))
        else:
            return str(data)
    
    def _tokenize(self, text: str) -> List[str]:
        """分词"""
        # 简单的分词逻辑
        import re
        # 中文分词（简单版本）
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        english_pattern = re.compile(r'[a-zA-Z]+')
        
        tokens = []
        tokens.extend(chinese_pattern.findall(text))
        tokens.extend(english_pattern.findall(text))
        
        return tokens
    
    def _pos_tagging(self, text: str) -> List[Dict]:
        """词性标注"""
        tokens = self._tokenize(text)
        # 简单的词性标注
        pos_tags = []
        for token in tokens:
            if token.isdigit():
                pos = "NUM"
            elif any(char in token for char in "的了着过"):
                pos = "PART"
            elif len(token) == 1:
                pos = "CHAR"
            else:
                pos = "WORD"
            
            pos_tags.append({"token": token, "pos": pos})
        
        return pos_tags
    
    def _named_entity_recognition(self, text: str) -> List[Dict]:
        """命名实体识别"""
        entities = []
        
        # 简单的实体识别
        import re
        
        # 识别数字
        numbers = re.finditer(r'\d+', text)
        for match in numbers:
            entities.append({
                "text": match.group(),
                "label": "NUMBER",
                "start": match.start(),
                "end": match.end(),
                "confidence": 0.9
            })
        
        # 识别日期
        dates = re.finditer(r'\d{4}年\d{1,2}月\d{1,2}日', text)
        for match in dates:
            entities.append({
                "text": match.group(),
                "label": "DATE",
                "start": match.start(),
                "end": match.end(),
                "confidence": 0.8
            })
        
        return entities
    
    def _dependency_parsing(self, text: str) -> List[Dict]:
        """依存句法分析"""
        # 简单的依存分析
        tokens = self._tokenize(text)
        dependencies = []
        
        for i, token in enumerate(tokens):
            dependencies.append({
                "token": token,
                "index": i,
                "head": max(0, i-1),  # 简单假设依存于前一个词
                "relation": "dep"
            })
        
        return dependencies
    
    def _sentiment_analysis(self, text: str) -> Dict:
        """情感分析"""
        positive_words = ["好", "棒", "优秀", "满意", "喜欢", "开心", "高兴"]
        negative_words = ["差", "坏", "糟糕", "不满", "讨厌", "生气", "难过"]
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count > negative_count:
            return {"sentiment": "positive", "score": 0.8, "confidence": 0.9}
        elif negative_count > positive_count:
            return {"sentiment": "negative", "score": 0.2, "confidence": 0.9}
        else:
            return {"sentiment": "neutral", "score": 0.5, "confidence": 0.7}


class DataClassificationProcessor(BaseIntelligentProcessor):
    """数据分类处理器"""
    
    def __init__(self, config: ProcessingConfig = None):
        if config is None:
            config = ProcessingConfig(
                processing_type=ProcessingType.DATA_CLASSIFICATION,
                provider="openai",
                model="gpt-3.5-turbo"
            )
        super().__init__(config)
    
    async def process(self, data: Any, options: Dict = None) -> SmartDataObject:
        """数据分类处理"""
        try:
            # 获取分类类别
            categories = options.get("categories", ["正面", "负面", "中性"]) if options else ["正面", "负面", "中性"]
            
            # 构建分类提示
            classification_prompt = self._build_classification_prompt(data, categories, options)
            
            # 调用AI服务
            provider = self.ai_factory.create_provider(self.config.provider)
            response = await provider.process(
                AIServiceType.CLASSIFICATION,
                classification_prompt,
                {"model": self.config.model}
            )
            
            # 解析分类结果
            classification_result = self._parse_classification_result(response, categories)
            
            return SmartDataObject({
                "success": True,
                "processing_type": "data_classification",
                "original_data": str(data)[:200],
                "categories": categories,
                "classification": classification_result,
                "confidence": classification_result.get("confidence", 0.0)
            })
            
        except Exception as e:
            self.logger.error(f"数据分类失败: {e}")
            return SmartDataObject({
                "success": False,
                "error": str(e),
                "processing_type": "data_classification"
            })
    
    def _build_classification_prompt(self, data: Any, categories: List[str], options: Dict = None) -> str:
        """构建分类提示"""
        prompt = "请对以下数据进行分类：\n\n"
        prompt += f"数据内容: {str(data)}\n\n"
        prompt += f"可选类别: {', '.join(categories)}\n\n"
        prompt += "请选择最合适的类别，并说明理由。\n"
        
        if options and "classification_criteria" in options:
            prompt += f"分类标准: {options['classification_criteria']}\n"
        
        return prompt
    
    def _parse_classification_result(self, response: Any, categories: List[str]) -> Dict:
        """解析分类结果"""
        if hasattr(response, 'result'):
            result_text = response.result
        elif isinstance(response, dict) and 'result' in response:
            result_text = response['result']
        else:
            result_text = str(response)
        
        # 简单的分类结果解析
        result_lower = result_text.lower()
        
        # 查找匹配的类别
        matched_category = None
        confidence = 0.5
        
        for category in categories:
            if category.lower() in result_lower:
                matched_category = category
                confidence = 0.8
                break
        
        if not matched_category:
            matched_category = categories[0]  # 默认第一个类别
            confidence = 0.3
        
        return {
            "category": matched_category,
            "confidence": confidence,
            "explanation": result_text,
            "all_categories": categories
        }


class IntelligentDataProcessorOrchestrator:
    """智能数据处理编排器"""
    
    def __init__(self):
        self.processors = {}
        self.logger = logging.getLogger(f"{__name__}.IntelligentDataProcessorOrchestrator")
        
        # 注册默认处理器
        self.register_processor("text_analysis", TextAnalysisProcessor())
        self.register_processor("nlp", NLPProcessor())
        self.register_processor("classification", DataClassificationProcessor())
    
    def register_processor(self, name: str, processor: BaseIntelligentProcessor):
        """注册处理器"""
        self.processors[name] = processor
        self.logger.info(f"注册智能处理器: {name}")
    
    def get_processor(self, name: str) -> Optional[BaseIntelligentProcessor]:
        """获取处理器"""
        return self.processors.get(name)
    
    def list_processors(self) -> List[str]:
        """列出所有处理器"""
        return list(self.processors.keys())
    
    async def process_with_processor(self, processor_name: str, data: Any, options: Dict = None) -> SmartDataObject:
        """使用指定处理器处理数据"""
        processor = self.get_processor(processor_name)
        if not processor:
            return SmartDataObject({
                "success": False,
                "error": f"处理器 '{processor_name}' 未找到",
                "available_processors": self.list_processors()
            })
        
        return await processor.process(data, options)
    
    async def auto_process(self, data: Any, options: Dict = None) -> SmartDataObject:
        """自动选择处理器处理数据"""
        # 根据数据类型自动选择处理器
        if isinstance(data, str) and len(data) > 10:
            return await self.process_with_processor("text_analysis", data, options)
        elif isinstance(data, dict) and "categories" in (options or {}):
            return await self.process_with_processor("classification", data, options)
        else:
            return await self.process_with_processor("text_analysis", data, options)


# 全局智能数据处理编排器实例
global_intelligent_processor = IntelligentDataProcessorOrchestrator()
