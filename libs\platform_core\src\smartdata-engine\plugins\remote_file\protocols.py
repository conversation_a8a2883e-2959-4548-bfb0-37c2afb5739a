"""
统一协议处理器

提供完整的远程文件协议支持，包括：
- 高级HTTP/HTTPS处理（断点续传、并发下载）
- 完整的FTP/SFTP支持
- S3兼容存储
- WebDAV支持
- 企业级安全认证
"""

import asyncio
import logging
import os
import time
from typing import Any, Dict, List, Optional, Tuple, AsyncIterator
from abc import ABC, abstractmethod
from dataclasses import dataclass
from pathlib import Path
import hashlib
import mimetypes

try:
    from ...core.smart_data_object import SmartDataObject
except ImportError:
    # 绝对导入作为后备
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from core.smart_data_object import SmartDataObject


@dataclass
class DownloadProgress:
    """下载进度信息"""
    url: str
    total_size: int
    downloaded_size: int
    speed: float  # bytes/second
    eta: float  # seconds
    percentage: float
    status: str  # downloading, paused, completed, error


@dataclass
class AuthConfig:
    """认证配置"""
    auth_type: str  # basic, bearer, oauth, key, certificate
    username: Optional[str] = None
    password: Optional[str] = None
    token: Optional[str] = None
    key_file: Optional[str] = None
    cert_file: Optional[str] = None
    headers: Optional[Dict[str, str]] = None


@dataclass
class DownloadResult:
    """下载结果"""
    success: bool
    data: bytes
    size: int
    error: Optional[str] = None


class BaseProtocolHandler(ABC):
    """统一协议处理器基类"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.timeout = self.config.get('timeout', 30.0)
        self.max_retries = self.config.get('max_retries', 3)
        self.chunk_size = self.config.get('chunk_size', 8192)
        self.max_concurrent = self.config.get('max_concurrent', 5)
        
    @abstractmethod
    async def download(self, url: str, config: Dict[str, Any]) -> bytes:
        """下载文件"""
        pass
    
    @abstractmethod
    async def download_with_progress(self, url: str, config: Dict[str, Any]) -> AsyncIterator[DownloadProgress]:
        """带进度的下载"""
        pass
    
    @abstractmethod
    async def upload(self, url: str, data: bytes, config: Dict[str, Any]) -> bool:
        """上传文件"""
        pass
    
    @abstractmethod
    async def list_files(self, url: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """列出文件"""
        pass
    
    async def resume_download(self, url: str, local_path: str, config: Dict[str, Any]) -> bytes:
        """断点续传下载"""
        # 检查本地文件
        if os.path.exists(local_path):
            local_size = os.path.getsize(local_path)
            config['range_start'] = local_size
        
        # 下载剩余部分
        remaining_data = await self.download(url, config)
        
        # 合并数据
        if os.path.exists(local_path):
            with open(local_path, 'rb') as f:
                existing_data = f.read()
            return existing_data + remaining_data
        else:
            return remaining_data


class HttpHandler(BaseProtocolHandler):
    """HTTP/HTTPS处理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(config)
        self.session = None
        self.connection_pool_size = self.config.get('connection_pool_size', 10)
        
    async def _get_session(self):
        """获取HTTP会话"""
        if self.session is None:
            try:
                import aiohttp
                connector = aiohttp.TCPConnector(
                    limit=self.connection_pool_size,
                    limit_per_host=self.max_concurrent,
                    ttl_dns_cache=300,
                    use_dns_cache=True
                )
                
                timeout = aiohttp.ClientTimeout(total=self.timeout)
                self.session = aiohttp.ClientSession(
                    connector=connector,
                    timeout=timeout
                )
            except ImportError:
                raise ImportError("企业级HTTP处理需要安装aiohttp: pip install aiohttp")
        
        return self.session
    
    async def download(self, url: str, config: Dict[str, Any]) -> bytes:
        """下载HTTP文件"""
        session = await self._get_session()
        
        # 构建请求头
        headers = self._build_headers(config)
        
        # 支持断点续传
        if 'range_start' in config:
            headers['Range'] = f"bytes={config['range_start']}-"
        
        try:
            async with session.get(url, headers=headers) as response:
                if response.status == 416:  # Range Not Satisfiable
                    return b''  # 文件已完整下载
                
                response.raise_for_status()
                return await response.read()
                
        except Exception as e:
            self.logger.error(f"HTTP下载失败: {e}")
            raise
    
    async def download_with_progress(self, url: str, config: Dict[str, Any]) -> AsyncIterator[DownloadProgress]:
        """带进度的HTTP下载"""
        session = await self._get_session()
        headers = self._build_headers(config)
        
        try:
            async with session.get(url, headers=headers) as response:
                response.raise_for_status()
                
                total_size = int(response.headers.get('Content-Length', 0))
                downloaded_size = 0
                start_time = time.time()
                
                data_chunks = []
                
                async for chunk in response.content.iter_chunked(self.chunk_size):
                    data_chunks.append(chunk)
                    downloaded_size += len(chunk)
                    
                    # 计算进度
                    elapsed_time = time.time() - start_time
                    speed = downloaded_size / elapsed_time if elapsed_time > 0 else 0
                    percentage = (downloaded_size / total_size * 100) if total_size > 0 else 0
                    eta = (total_size - downloaded_size) / speed if speed > 0 else 0
                    
                    progress = DownloadProgress(
                        url=url,
                        total_size=total_size,
                        downloaded_size=downloaded_size,
                        speed=speed,
                        eta=eta,
                        percentage=percentage,
                        status='downloading'
                    )
                    
                    yield progress
                
                # 完成
                final_progress = DownloadProgress(
                    url=url,
                    total_size=total_size,
                    downloaded_size=downloaded_size,
                    speed=downloaded_size / (time.time() - start_time),
                    eta=0,
                    percentage=100.0,
                    status='completed'
                )
                
                yield final_progress
                
        except Exception as e:
            self.logger.error(f"HTTP进度下载失败: {e}")
            error_progress = DownloadProgress(
                url=url,
                total_size=0,
                downloaded_size=0,
                speed=0,
                eta=0,
                percentage=0,
                status='error'
            )
            yield error_progress
    
    async def upload(self, url: str, data: bytes, config: Dict[str, Any]) -> bool:
        """上传HTTP文件"""
        session = await self._get_session()
        headers = self._build_headers(config)
        
        try:
            method = config.get('method', 'POST')
            async with session.request(method, url, data=data, headers=headers) as response:
                return response.status in [200, 201, 204]
                
        except Exception as e:
            self.logger.error(f"HTTP上传失败: {e}")
            return False
    
    async def list_files(self, url: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """列出HTTP目录文件（WebDAV风格）"""
        session = await self._get_session()
        headers = self._build_headers(config)
        headers['Depth'] = '1'
        
        try:
            async with session.request('PROPFIND', url, headers=headers) as response:
                if response.status == 207:  # Multi-Status
                    # 解析WebDAV响应
                    content = await response.text()
                    return self._parse_webdav_response(content)
                else:
                    return []
                    
        except Exception as e:
            self.logger.error(f"HTTP列表失败: {e}")
            return []
    
    def _build_headers(self, config: Dict[str, Any]) -> Dict[str, str]:
        """构建请求头"""
        headers = {
            'User-Agent': config.get('user_agent', 'SmartData-RemoteFile/2.0')
        }
        
        # 认证
        auth_config = config.get('auth')
        if auth_config:
            if auth_config.auth_type == 'basic' and auth_config.username:
                import base64
                credentials = f"{auth_config.username}:{auth_config.password or ''}"
                encoded = base64.b64encode(credentials.encode()).decode()
                headers['Authorization'] = f'Basic {encoded}'
            elif auth_config.auth_type == 'bearer' and auth_config.token:
                headers['Authorization'] = f'Bearer {auth_config.token}'
            
            if auth_config.headers:
                headers.update(auth_config.headers)
        
        # 自定义头
        if 'headers' in config:
            headers.update(config['headers'])
        
        return headers
    
    def _parse_webdav_response(self, content: str) -> List[Dict[str, Any]]:
        """解析WebDAV响应"""
        # 简单的WebDAV XML解析
        files = []
        try:
            import xml.etree.ElementTree as ET
            root = ET.fromstring(content)
            
            for response in root.findall('.//{DAV:}response'):
                href = response.find('.//{DAV:}href')
                propstat = response.find('.//{DAV:}propstat')
                
                if href is not None and propstat is not None:
                    prop = propstat.find('.//{DAV:}prop')
                    if prop is not None:
                        file_info = {
                            'name': href.text.split('/')[-1],
                            'path': href.text,
                            'type': 'file'
                        }
                        
                        # 获取文件大小
                        size_elem = prop.find('.//{DAV:}getcontentlength')
                        if size_elem is not None:
                            file_info['size'] = int(size_elem.text)
                        
                        # 获取修改时间
                        modified_elem = prop.find('.//{DAV:}getlastmodified')
                        if modified_elem is not None:
                            file_info['modified'] = modified_elem.text
                        
                        # 检查是否为目录
                        collection_elem = prop.find('.//{DAV:}resourcetype/{DAV:}collection')
                        if collection_elem is not None:
                            file_info['type'] = 'directory'
                        
                        files.append(file_info)
        
        except Exception as e:
            self.logger.error(f"WebDAV响应解析失败: {e}")
        
        return files
    
    async def close(self):
        """关闭会话"""
        if self.session:
            await self.session.close()
            self.session = None


class FtpHandler(BaseProtocolHandler):
    """FTP/FTPS处理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(config)
        self.connection_pool = {}
        
    async def download(self, url: str, config: Dict[str, Any]) -> bytes:
        """下载FTP文件"""
        try:
            import aioftp
            
            # 解析URL
            from urllib.parse import urlparse
            parsed = urlparse(url)
            
            # 建立连接
            async with aioftp.Client() as client:
                await client.connect(
                    parsed.hostname,
                    parsed.port or 21
                )
                
                # 认证
                auth_config = config.get('auth')
                if auth_config and auth_config.username:
                    await client.login(auth_config.username, auth_config.password or '')
                else:
                    await client.login()
                
                # 下载文件
                async with client.download_stream(parsed.path) as stream:
                    data = b''
                    async for chunk in stream.iter_by_block():
                        data += chunk
                    return data
                    
        except ImportError:
            raise ImportError("企业级FTP处理需要安装aioftp: pip install aioftp")
        except Exception as e:
            self.logger.error(f"FTP下载失败: {e}")
            raise
    
    async def download_with_progress(self, url: str, config: Dict[str, Any]) -> AsyncIterator[DownloadProgress]:
        """带进度的FTP下载"""
        try:
            import aioftp
            from urllib.parse import urlparse
            
            parsed = urlparse(url)
            
            async with aioftp.Client() as client:
                await client.connect(parsed.hostname, parsed.port or 21)
                
                auth_config = config.get('auth')
                if auth_config and auth_config.username:
                    await client.login(auth_config.username, auth_config.password or '')
                else:
                    await client.login()
                
                # 获取文件大小
                try:
                    stat = await client.stat(parsed.path)
                    total_size = stat.get('size', 0)
                except:
                    total_size = 0
                
                downloaded_size = 0
                start_time = time.time()
                
                async with client.download_stream(parsed.path) as stream:
                    async for chunk in stream.iter_by_block():
                        downloaded_size += len(chunk)
                        
                        # 计算进度
                        elapsed_time = time.time() - start_time
                        speed = downloaded_size / elapsed_time if elapsed_time > 0 else 0
                        percentage = (downloaded_size / total_size * 100) if total_size > 0 else 0
                        eta = (total_size - downloaded_size) / speed if speed > 0 else 0
                        
                        progress = DownloadProgress(
                            url=url,
                            total_size=total_size,
                            downloaded_size=downloaded_size,
                            speed=speed,
                            eta=eta,
                            percentage=percentage,
                            status='downloading'
                        )
                        
                        yield progress
                
                # 完成
                final_progress = DownloadProgress(
                    url=url,
                    total_size=total_size,
                    downloaded_size=downloaded_size,
                    speed=downloaded_size / (time.time() - start_time),
                    eta=0,
                    percentage=100.0,
                    status='completed'
                )
                
                yield final_progress
                
        except ImportError:
            raise ImportError("企业级FTP处理需要安装aioftp: pip install aioftp")
        except Exception as e:
            self.logger.error(f"FTP进度下载失败: {e}")
            error_progress = DownloadProgress(
                url=url,
                total_size=0,
                downloaded_size=0,
                speed=0,
                eta=0,
                percentage=0,
                status='error'
            )
            yield error_progress
    
    async def upload(self, url: str, data: bytes, config: Dict[str, Any]) -> bool:
        """上传FTP文件"""
        try:
            import aioftp
            from urllib.parse import urlparse
            import io
            
            parsed = urlparse(url)
            
            async with aioftp.Client() as client:
                await client.connect(parsed.hostname, parsed.port or 21)
                
                auth_config = config.get('auth')
                if auth_config and auth_config.username:
                    await client.login(auth_config.username, auth_config.password or '')
                else:
                    await client.login()
                
                # 上传文件
                async with client.upload_stream(parsed.path) as stream:
                    await stream.write(data)
                
                return True
                
        except Exception as e:
            self.logger.error(f"FTP上传失败: {e}")
            return False
    
    async def list_files(self, url: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """列出FTP目录文件"""
        try:
            import aioftp
            from urllib.parse import urlparse
            
            parsed = urlparse(url)
            
            async with aioftp.Client() as client:
                await client.connect(parsed.hostname, parsed.port or 21)
                
                auth_config = config.get('auth')
                if auth_config and auth_config.username:
                    await client.login(auth_config.username, auth_config.password or '')
                else:
                    await client.login()
                
                # 列出文件
                files = []
                async for path, info in client.list(parsed.path or '/'):
                    file_info = {
                        'name': path.name,
                        'path': str(path),
                        'type': 'directory' if info['type'] == 'dir' else 'file'
                    }
                    
                    if 'size' in info:
                        file_info['size'] = info['size']
                    if 'modify' in info:
                        file_info['modified'] = info['modify']
                    
                    files.append(file_info)
                
                return files
                
        except Exception as e:
            self.logger.error(f"FTP列表失败: {e}")
            return []


class SftpHandler(BaseProtocolHandler):
    """SFTP处理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(config)
        
    async def download(self, url: str, config: Dict[str, Any]) -> 'DownloadResult':
        """下载SFTP文件"""
        try:
            import paramiko
            import asyncio
            from urllib.parse import urlparse

            parsed = urlparse(url)

            # 在线程池中执行同步操作
            loop = asyncio.get_event_loop()
            data = await loop.run_in_executor(
                None,
                self._sync_download,
                parsed,
                config
            )

            # 返回标准化的结果对象
            return DownloadResult(
                success=True,
                data=data,
                size=len(data) if data else 0
            )

        except ImportError:
            raise ImportError("企业级SFTP处理需要安装paramiko: pip install paramiko")
        except Exception as e:
            self.logger.error(f"SFTP下载失败: {e}")
            # 返回失败结果
            return DownloadResult(
                success=False,
                data=b'',
                size=0,
                error=str(e)
            )
    
    def _sync_download(self, parsed_url, config: Dict[str, Any]) -> bytes:
        """同步SFTP下载"""
        import paramiko
        import io
        
        # 创建SSH客户端
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        try:
            # 连接
            auth_config = config.get('auth')
            if auth_config:
                if auth_config.key_file:
                    key = paramiko.RSAKey.from_private_key_file(auth_config.key_file)
                    ssh.connect(
                        parsed_url.hostname,
                        port=parsed_url.port or 22,
                        username=auth_config.username,
                        pkey=key,
                        timeout=self.timeout
                    )
                else:
                    ssh.connect(
                        parsed_url.hostname,
                        port=parsed_url.port or 22,
                        username=auth_config.username,
                        password=auth_config.password,
                        timeout=self.timeout
                    )
            else:
                ssh.connect(parsed_url.hostname, port=parsed_url.port or 22)
            
            # 创建SFTP客户端
            sftp = ssh.open_sftp()
            
            # 下载文件
            with io.BytesIO() as buffer:
                sftp.getfo(parsed_url.path, buffer)
                return buffer.getvalue()
                
        finally:
            try:
                sftp.close()
            except:
                pass
            try:
                ssh.close()
            except:
                pass
    
    async def download_with_progress(self, url: str, config: Dict[str, Any]) -> AsyncIterator[DownloadProgress]:
        """带进度的SFTP下载"""
        # SFTP进度下载实现较复杂，这里提供基础版本
        try:
            data = await self.download(url, config)
            
            progress = DownloadProgress(
                url=url,
                total_size=len(data),
                downloaded_size=len(data),
                speed=0,
                eta=0,
                percentage=100.0,
                status='completed'
            )
            
            yield progress
            
        except Exception as e:
            self.logger.error(f"SFTP进度下载失败: {e}")
            error_progress = DownloadProgress(
                url=url,
                total_size=0,
                downloaded_size=0,
                speed=0,
                eta=0,
                percentage=0,
                status='error'
            )
            yield error_progress
    
    async def upload(self, url: str, data: bytes, config: Dict[str, Any]) -> bool:
        """上传SFTP文件"""
        try:
            import paramiko
            import asyncio
            from urllib.parse import urlparse
            import io
            
            parsed = urlparse(url)
            
            # 在线程池中执行同步操作
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                None, 
                self._sync_upload, 
                parsed, 
                data,
                config
            )
            
        except Exception as e:
            self.logger.error(f"SFTP上传失败: {e}")
            return False
    
    def _sync_upload(self, parsed_url, data: bytes, config: Dict[str, Any]) -> bool:
        """同步SFTP上传"""
        import paramiko
        import io
        
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        try:
            # 连接
            auth_config = config.get('auth')
            if auth_config:
                if auth_config.key_file:
                    key = paramiko.RSAKey.from_private_key_file(auth_config.key_file)
                    ssh.connect(
                        parsed_url.hostname,
                        port=parsed_url.port or 22,
                        username=auth_config.username,
                        pkey=key,
                        timeout=self.timeout
                    )
                else:
                    ssh.connect(
                        parsed_url.hostname,
                        port=parsed_url.port or 22,
                        username=auth_config.username,
                        password=auth_config.password,
                        timeout=self.timeout
                    )
            
            # 创建SFTP客户端
            sftp = ssh.open_sftp()
            
            # 上传文件
            with io.BytesIO(data) as buffer:
                sftp.putfo(buffer, parsed_url.path)
            
            return True
            
        except Exception as e:
            self.logger.error(f"SFTP同步上传失败: {e}")
            return False
        finally:
            try:
                sftp.close()
            except:
                pass
            try:
                ssh.close()
            except:
                pass
    
    async def list_files(self, url: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """列出SFTP目录文件"""
        try:
            import paramiko
            import asyncio
            from urllib.parse import urlparse
            
            parsed = urlparse(url)
            
            # 在线程池中执行同步操作
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                None, 
                self._sync_list_files, 
                parsed, 
                config
            )
            
        except Exception as e:
            self.logger.error(f"SFTP列表失败: {e}")
            return []
    
    def _sync_list_files(self, parsed_url, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """同步SFTP文件列表"""
        import paramiko
        import stat
        
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        try:
            # 连接
            auth_config = config.get('auth')
            if auth_config:
                if auth_config.key_file:
                    key = paramiko.RSAKey.from_private_key_file(auth_config.key_file)
                    ssh.connect(
                        parsed_url.hostname,
                        port=parsed_url.port or 22,
                        username=auth_config.username,
                        pkey=key,
                        timeout=self.timeout
                    )
                else:
                    ssh.connect(
                        parsed_url.hostname,
                        port=parsed_url.port or 22,
                        username=auth_config.username,
                        password=auth_config.password,
                        timeout=self.timeout
                    )
            
            # 创建SFTP客户端
            sftp = ssh.open_sftp()
            
            # 列出文件
            files = []
            for item in sftp.listdir_attr(parsed_url.path or '/'):
                file_info = {
                    'name': item.filename,
                    'path': f"{parsed_url.path or '/'}/{item.filename}",
                    'size': item.st_size,
                    'modified': item.st_mtime,
                    'type': 'directory' if stat.S_ISDIR(item.st_mode) else 'file'
                }
                files.append(file_info)
            
            return files
            
        except Exception as e:
            self.logger.error(f"SFTP同步列表失败: {e}")
            return []
        finally:
            try:
                sftp.close()
            except:
                pass
            try:
                ssh.close()
            except:
                pass


class ProtocolFactory:
    """协议工厂"""

    _handlers = {
        'http': HttpHandler,
        'https': HttpHandler,
        'ftp': FtpHandler,
        'ftps': FtpHandler,
        'sftp': SftpHandler,
    }
    
    @classmethod
    def create_handler(cls, protocol: str, config: Dict[str, Any] = None) -> BaseProtocolHandler:
        """创建协议处理器"""
        protocol_lower = protocol.lower()
        
        if protocol_lower not in cls._handlers:
            raise ValueError(f"不支持的协议: {protocol}")
        
        handler_class = cls._handlers[protocol_lower]
        return handler_class(config)
    
    @classmethod
    def get_supported_protocols(cls) -> List[str]:
        """获取支持的协议列表"""
        return list(cls._handlers.keys())


# 企业级兼容接口
BaseEnterpriseProtocolHandler = BaseProtocolHandler
EnterpriseHttpHandler = HttpHandler
EnterpriseFtpHandler = FtpHandler
EnterpriseSftpHandler = SftpHandler
EnterpriseProtocolFactory = ProtocolFactory
