"""
MySQL数据库适配器

提供MySQL数据库的完整支持，包括：
- 连接管理
- SQL执行
- 事务处理
- 存储过程和函数调用
- 批量操作
"""

from typing import Any, Dict, List, Optional, Union
import logging

from .base import DatabaseAdapterBase, ConnectionInfo

# 尝试导入MySQL驱动
try:
    import pymysql
    from pymysql.cursors import DictCursor
    PYMYSQL_AVAILABLE = True
except ImportError:
    PYMYSQL_AVAILABLE = False
    pymysql = None
    DictCursor = None


class MySQLAdapter(DatabaseAdapterBase):
    """
    MySQL数据库适配器

    支持MySQL特有的功能：
    - 存储过程和函数
    - 批量插入
    - 事务管理
    - 连接池
    - 字符集处理
    """

    def __init__(self):
        super().__init__()
        if not PYMYSQL_AVAILABLE:
            self.logger.warning("pymysql未安装，MySQL适配器功能受限")

    def supported_types(self) -> List[str]:
        """返回支持的数据类型列表"""
        return [
            'mysql',
            'mysql_connection',
            'mysql_connection_string',
            'mysql_url',
            'mariadb',
            'mariadb_connection',
            'mariadb_connection_string',
            'mariadb_url'
        ]

    def _is_supported_connection_string(self, connection_string: str) -> bool:
        """检查是否支持的连接字符串"""
        return connection_string.startswith(('mysql://', 'mariadb://'))

    def _is_supported_connection_object(self, connection: Any) -> bool:
        """检查是否支持的连接对象"""
        if not PYMYSQL_AVAILABLE:
            return False

        # 检查是否是pymysql连接对象
        if (hasattr(connection, 'cursor') and
            hasattr(connection, 'commit') and
            hasattr(connection, 'rollback') and
            'pymysql' in str(type(connection))):
            return True

        # 检查是否是MySQL配置字典
        if isinstance(connection, dict):
            # 必须包含基本连接信息
            has_host = 'host' in connection
            has_database = 'database' in connection or 'db' in connection
            # MySQL默认端口或明确指定为MySQL
            is_mysql = (connection.get('port') == 3306 or
                       'mysql' in str(connection).lower() or
                       'mariadb' in str(connection).lower())
            return has_host and has_database and is_mysql

        return False

    def can_handle(self, data_source: Any) -> bool:
        """检查是否能处理指定的数据源"""
        if isinstance(data_source, str):
            return self._is_supported_connection_string(data_source)
        else:
            return self._is_supported_connection_object(data_source)

    def _get_default_port(self) -> int:
        """获取默认端口"""
        return 3306

    def _create_connection(self, connection_source: Union[str, ConnectionInfo]) -> Any:
        """创建MySQL连接"""
        if not PYMYSQL_AVAILABLE:
            raise ImportError("pymysql未安装，无法创建MySQL连接")

        if isinstance(connection_source, str):
            # 解析连接字符串
            conn_info = self._parse_connection_string(connection_source)
        else:
            conn_info = connection_source

        try:
            # 创建连接参数
            connection_params = {
                'host': conn_info.host,
                'port': conn_info.port,
                'user': conn_info.username,
                'password': conn_info.password,
                'database': conn_info.database,
                'charset': 'utf8mb4',
                'autocommit': False,
                'cursorclass': DictCursor
            }

            # 添加额外选项
            if conn_info.options:
                for key, value in conn_info.options.items():
                    if key in ['charset', 'connect_timeout', 'read_timeout', 'write_timeout']:
                        connection_params[key] = value

            # 创建连接
            connection = pymysql.connect(**connection_params)

            self.logger.info(f"成功创建MySQL连接: {conn_info.host}:{conn_info.port}/{conn_info.database}")
            return connection

        except Exception as e:
            self.logger.error(f"创建MySQL连接失败: {e}")
            raise

    def _execute_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """执行查询 - 返回字典列表"""
        try:
            with connection.cursor() as cursor:
                # 执行查询
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)

                # 获取结果
                results = cursor.fetchall()

                # pymysql的DictCursor已经返回字典列表
                return results

        except Exception as e:
            self.logger.error(f"MySQL查询执行失败: {e}")
            raise

    def _execute_command(self, connection: Any, sql: str, params: Dict = None) -> int:
        """执行命令 - 返回影响行数"""
        try:
            with connection.cursor() as cursor:
                # 执行命令
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)

                # 提交事务（如果不在事务中）
                if connection.get_autocommit():
                    connection.commit()

                # 返回影响行数
                return cursor.rowcount

        except Exception as e:
            self.logger.error(f"MySQL命令执行失败: {e}")
            raise

    def _get_database_info(self, connection: Any) -> Dict[str, Any]:
        """获取MySQL数据库信息"""
        try:
            with connection.cursor() as cursor:
                # 获取版本信息
                cursor.execute("SELECT VERSION() as version")
                version_info = cursor.fetchone()

                # 获取数据库名称
                cursor.execute("SELECT DATABASE() as database_name")
                database_info = cursor.fetchone()

                # 获取用户信息
                cursor.execute("SELECT USER() as current_user")
                user_info = cursor.fetchone()

                # 获取连接信息
                cursor.execute("""
                    SELECT
                        @@hostname as hostname,
                        @@port as port,
                        @@character_set_database as charset,
                        @@collation_database as collation
                """)
                connection_info = cursor.fetchone()

                return {
                    'database_type': 'MySQL',
                    'version': version_info['version'] if version_info else 'Unknown',
                    'database_name': database_info['database_name'] if database_info else 'Unknown',
                    'current_user': user_info['current_user'] if user_info else 'Unknown',
                    'hostname': connection_info['hostname'] if connection_info else 'Unknown',
                    'port': connection_info['port'] if connection_info else 'Unknown',
                    'charset': connection_info['charset'] if connection_info else 'Unknown',
                    'collation': connection_info['collation'] if connection_info else 'Unknown'
                }

        except Exception as e:
            self.logger.error(f"获取MySQL数据库信息失败: {e}")
            return {
                'database_type': 'MySQL',
                'error': str(e)
            }

    # ========================================================================
    # MySQL特有功能
    # ========================================================================

    def _build_operations(self) -> Dict[str, callable]:
        """构建MySQL特有操作列表"""
        operations = super()._build_operations()

        # 添加MySQL特有操作
        operations.update({
            'show_tables': self._show_tables_wrapper,
            'show_databases': self._show_databases_wrapper,
            'show_columns': self._show_columns_wrapper,
            'show_indexes': self._show_indexes_wrapper,
            'show_status': self._show_status_wrapper,
            'show_variables': self._show_variables_wrapper,
            'optimize_table': self._optimize_table_wrapper,
            'repair_table': self._repair_table_wrapper,
            'check_table': self._check_table_wrapper,
            'load_data': self._load_data_wrapper,
        })

        return operations

    def _show_tables_wrapper(self, connection: Any, database: str = None):
        """SHOW TABLES操作包装器"""
        return self._show_tables(connection, database)

    def _show_databases_wrapper(self, connection: Any):
        """SHOW DATABASES操作包装器"""
        return self._show_databases(connection)

    def _show_columns_wrapper(self, connection: Any, table: str, database: str = None):
        """SHOW COLUMNS操作包装器"""
        return self._show_columns(connection, table, database)

    def _show_indexes_wrapper(self, connection: Any, table: str, database: str = None):
        """SHOW INDEXES操作包装器"""
        return self._show_indexes(connection, table, database)

    def _show_status_wrapper(self, connection: Any, pattern: str = None):
        """SHOW STATUS操作包装器"""
        return self._show_status(connection, pattern)

    def _show_variables_wrapper(self, connection: Any, pattern: str = None):
        """SHOW VARIABLES操作包装器"""
        return self._show_variables(connection, pattern)

    def _optimize_table_wrapper(self, connection: Any, table: str):
        """OPTIMIZE TABLE操作包装器"""
        return self._optimize_table(connection, table)

    def _repair_table_wrapper(self, connection: Any, table: str):
        """REPAIR TABLE操作包装器"""
        return self._repair_table(connection, table)

    def _check_table_wrapper(self, connection: Any, table: str):
        """CHECK TABLE操作包装器"""
        return self._check_table(connection, table)

    def _load_data_wrapper(self, connection: Any, table: str, file_path: str,
                          fields_terminated_by: str = '\t', lines_terminated_by: str = '\n'):
        """LOAD DATA操作包装器"""
        return self._load_data(connection, table, file_path, fields_terminated_by, lines_terminated_by)

    def _show_tables(self, connection: Any, database: str = None) -> List[Dict]:
        """显示表列表"""
        try:
            with connection.cursor() as cursor:
                if database:
                    cursor.execute(f"SHOW TABLES FROM {database}")
                else:
                    cursor.execute("SHOW TABLES")

                results = cursor.fetchall()

                # 转换为统一格式
                table_list = []
                for row in results:
                    # SHOW TABLES返回的字段名可能不同
                    table_name = list(row.values())[0]
                    table_list.append({'table_name': table_name})

                return table_list

        except Exception as e:
            self.logger.error(f"SHOW TABLES操作失败: {e}")
            raise

    def _show_databases(self, connection: Any) -> List[Dict]:
        """显示数据库列表"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SHOW DATABASES")
                results = cursor.fetchall()

                # 转换为统一格式
                database_list = []
                for row in results:
                    database_name = list(row.values())[0]
                    database_list.append({'database_name': database_name})

                return database_list

        except Exception as e:
            self.logger.error(f"SHOW DATABASES操作失败: {e}")
            raise

    def _show_columns(self, connection: Any, table: str, database: str = None) -> List[Dict]:
        """显示表列信息"""
        try:
            with connection.cursor() as cursor:
                if database:
                    cursor.execute(f"SHOW COLUMNS FROM {database}.{table}")
                else:
                    cursor.execute(f"SHOW COLUMNS FROM {table}")

                return cursor.fetchall()

        except Exception as e:
            self.logger.error(f"SHOW COLUMNS操作失败: {e}")
            raise

    def _show_indexes(self, connection: Any, table: str, database: str = None) -> List[Dict]:
        """显示表索引信息"""
        try:
            with connection.cursor() as cursor:
                if database:
                    cursor.execute(f"SHOW INDEXES FROM {database}.{table}")
                else:
                    cursor.execute(f"SHOW INDEXES FROM {table}")

                return cursor.fetchall()

        except Exception as e:
            self.logger.error(f"SHOW INDEXES操作失败: {e}")
            raise

    def _show_status(self, connection: Any, pattern: str = None) -> List[Dict]:
        """显示服务器状态"""
        try:
            with connection.cursor() as cursor:
                if pattern:
                    cursor.execute(f"SHOW STATUS LIKE '{pattern}'")
                else:
                    cursor.execute("SHOW STATUS")

                return cursor.fetchall()

        except Exception as e:
            self.logger.error(f"SHOW STATUS操作失败: {e}")
            raise

    def _show_variables(self, connection: Any, pattern: str = None) -> List[Dict]:
        """显示服务器变量"""
        try:
            with connection.cursor() as cursor:
                if pattern:
                    cursor.execute(f"SHOW VARIABLES LIKE '{pattern}'")
                else:
                    cursor.execute("SHOW VARIABLES")

                return cursor.fetchall()

        except Exception as e:
            self.logger.error(f"SHOW VARIABLES操作失败: {e}")
            raise

    def _optimize_table(self, connection: Any, table: str) -> Dict:
        """优化表"""
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"OPTIMIZE TABLE {table}")
                results = cursor.fetchall()

                return {
                    'success': True,
                    'operation': 'OPTIMIZE TABLE',
                    'table': table,
                    'results': results
                }

        except Exception as e:
            self.logger.error(f"OPTIMIZE TABLE操作失败: {e}")
            raise

    def _repair_table(self, connection: Any, table: str) -> Dict:
        """修复表"""
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"REPAIR TABLE {table}")
                results = cursor.fetchall()

                return {
                    'success': True,
                    'operation': 'REPAIR TABLE',
                    'table': table,
                    'results': results
                }

        except Exception as e:
            self.logger.error(f"REPAIR TABLE操作失败: {e}")
            raise

    def _check_table(self, connection: Any, table: str) -> Dict:
        """检查表"""
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"CHECK TABLE {table}")
                results = cursor.fetchall()

                return {
                    'success': True,
                    'operation': 'CHECK TABLE',
                    'table': table,
                    'results': results
                }

        except Exception as e:
            self.logger.error(f"CHECK TABLE操作失败: {e}")
            raise

    def _load_data(self, connection: Any, table: str, file_path: str,
                  fields_terminated_by: str = '\t', lines_terminated_by: str = '\n') -> Dict:
        """从文件加载数据"""
        try:
            with connection.cursor() as cursor:
                load_sql = f"""
                LOAD DATA LOCAL INFILE '{file_path}'
                INTO TABLE {table}
                FIELDS TERMINATED BY '{fields_terminated_by}'
                LINES TERMINATED BY '{lines_terminated_by}'
                """

                cursor.execute(load_sql)
                connection.commit()

                return {
                    'success': True,
                    'operation': 'LOAD DATA',
                    'table': table,
                    'file_path': file_path,
                    'affected_rows': cursor.rowcount
                }

        except Exception as e:
            self.logger.error(f"LOAD DATA操作失败: {e}")
            raise

    # ========================================================================
    # 重写基类方法以适配MySQL特性
    # ========================================================================

    def _call_procedure(self, connection: Any, procedure_name: str, params: Dict = None) -> Any:
        """调用存储过程（MySQL版本）"""
        try:
            with connection.cursor() as cursor:
                if params:
                    param_list = list(params.values())
                    cursor.callproc(procedure_name, param_list)
                else:
                    cursor.callproc(procedure_name)

                # 获取结果
                results = []
                for result in cursor.stored_results():
                    results.append(result.fetchall())

                return results if len(results) > 1 else (results[0] if results else [])

        except Exception as e:
            self.logger.error(f"调用存储过程失败: {e}")
            raise

    def _call_function(self, connection: Any, function_name: str, params: Dict = None) -> Any:
        """调用函数（MySQL版本）"""
        try:
            with connection.cursor() as cursor:
                if params:
                    param_list = list(params.values())
                    placeholders = ', '.join(['%s'] * len(param_list))
                    sql = f"SELECT {function_name}({placeholders})"
                    cursor.execute(sql, param_list)
                else:
                    sql = f"SELECT {function_name}()"
                    cursor.execute(sql)

                result = cursor.fetchone()
                return result

        except Exception as e:
            self.logger.error(f"调用函数失败: {e}")
            raise
