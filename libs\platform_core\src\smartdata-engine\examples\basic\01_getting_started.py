#!/usr/bin/env python3
"""
SmartData模板引擎快速入门示例

展示如何使用自动化功能快速开始，无需手工配置
基于验证的100%测试通过率和自动化机制
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.template_ext import create_template_engine

def getting_started_example():
    """快速入门示例"""
    print("=== SmartData模板引擎快速入门 ===")
    
    # 🚀 核心特性：一行代码创建完整的企业级模板引擎
    # ✅ 自动发现和注册所有插件
    # ✅ 自动注册所有内置函数
    # ✅ 自动加载企业级过滤器
    print("\n🔧 创建模板引擎...")
    engine = create_template_engine()
    print("✅ 模板引擎创建完成 - 所有功能自动可用")
    
    # 示例1：基础模板渲染
    print("\n📝 示例1：基础模板渲染")
    template1 = """
欢迎使用SmartData模板引擎！
当前时间: {{ now() }}
系统状态: 运行正常
    """.strip()
    
    result1 = engine.render_template(template1)
    print("模板:")
    print(template1)
    print("\n渲染结果:")
    print(result1)
    
    # 示例2：使用内置函数（自动可用，无需注册）
    print("\n🔢 示例2：内置函数使用")
    template2 = """
内置函数演示:
=============
数学计算:
- 求和: {{ sum([10, 20, 30, 40, 50]) }}
- 最大值: {{ max([85, 92, 78, 96, 88]) }}
- 平均值: {{ sum([85, 92, 78, 96, 88]) / 5 }}
- 格式化: {{ format_number(1234567.89) }}

字符串处理:
- 长度: {{ len("SmartData模板引擎") }}
- 类型: {{ type("Hello").__name__ }}
- 截断: {{ truncate("这是一个很长的文本内容示例", 10) }}

时间处理:
- 当前时间: {{ now() }}
- 时间戳: {{ timestamp() }}
    """.strip()
    
    result2 = engine.render_template(template2)
    print("模板:")
    print(template2)
    print("\n渲染结果:")
    print(result2)
    
    # 示例3：条件和循环
    print("\n🔄 示例3：条件和循环")
    template3 = """
{%- set users = [
    {'name': '张三', 'age': 28, 'department': '技术部', 'active': true},
    {'name': '李四', 'age': 32, 'department': '销售部', 'active': true},
    {'name': '王五', 'age': 25, 'department': '技术部', 'active': false},
    {'name': '赵六', 'age': 35, 'department': '管理部', 'active': true}
] -%}

用户列表:
=========
{%- for user in users %}
{{ loop.index }}. {{ user.name }}
   年龄: {{ user.age }}岁
   部门: {{ user.department }}
   状态: {{ "激活" if user.active else "未激活" }}
   {%- if user.age >= 30 %}
   标签: 资深员工
   {%- endif %}
{%- endfor %}

统计信息:
- 总用户数: {{ users | length }}
- 激活用户: {{ users | selectattr('active') | list | length }}
- 平均年龄: {{ (users | sum(attribute='age')) / (users | length) }}
- 技术部人数: {{ users | selectattr('department', 'equalto', '技术部') | list | length }}
    """.strip()
    
    result3 = engine.render_template(template3)
    print("模板:")
    print(template3)
    print("\n渲染结果:")
    print(result3)
    
    # 示例4：数据处理和格式化
    print("\n📊 示例4：数据处理和格式化")
    template4 = """
{%- set sales_data = [
    {'month': '1月', 'sales': 120000, 'target': 100000},
    {'month': '2月', 'sales': 135000, 'target': 110000},
    {'month': '3月', 'sales': 98000, 'target': 105000}
] -%}

销售报告:
=========
{%- for record in sales_data %}
{{ record.month }}:
  销售额: ¥{{ format_number(record.sales) }}
  目标: ¥{{ format_number(record.target) }}
  完成率: {{ calculate_percentage(record.sales, record.target) }}%
  状态: {{ "✅ 达成" if record.sales >= record.target else "❌ 未达成" }}
{%- endfor %}

总体统计:
- 总销售额: ¥{{ format_number(sales_data | sum(attribute='sales')) }}
- 总目标: ¥{{ format_number(sales_data | sum(attribute='target')) }}
- 整体完成率: {{ calculate_percentage(sales_data | sum(attribute='sales'), sales_data | sum(attribute='target')) }}%
- 最佳月份: {{ sales_data | sort(attribute='sales', reverse=true) | first | attr('month') }}
    """.strip()
    
    result4 = engine.render_template(template4)
    print("模板:")
    print(template4)
    print("\n渲染结果:")
    print(result4)
    
    # 示例5：错误处理和安全访问
    print("\n🛡️ 示例5：错误处理和安全访问")
    template5 = """
{%- set test_data = {
    'user': {'name': '张三', 'profile': {'email': '<EMAIL>'}},
    'empty_list': [],
    'null_value': none
} -%}

安全数据访问:
============
- 用户名: {{ safe_get(test_data, 'user.name', '未知用户') }}
- 邮箱: {{ deep_get(test_data, 'user.profile.email', '无邮箱') }}
- 不存在的字段: {{ safe_get(test_data, 'nonexistent', '默认值') }}
- 空列表长度: {{ test_data.empty_list | length }}
- 空值处理: {{ test_data.null_value or '空值已处理' }}

安全数学运算:
- 正常除法: {{ 100 / 4 }}
- 安全除法: {{ 100 | safe_divide(0, '除零保护') }}
- 类型安全: {{ int('123') if '123'.isdigit() else '无效数字' }}
    """.strip()
    
    result5 = engine.render_template(template5)
    print("模板:")
    print(template5)
    print("\n渲染结果:")
    print(result5)
    
    print("\n🎉 快速入门完成！")
    print("\n💡 关键要点:")
    print("1. ✅ 一行代码创建引擎：create_template_engine()")
    print("2. ✅ 内置函数自动可用：now(), format_number(), sum()等")
    print("3. ✅ 企业级过滤器自动加载：safe_divide, calculate_percentage等")
    print("4. ✅ 无需手工配置：插件和函数自动注册")
    print("5. ✅ 安全可靠：自动错误处理和边界检查")
    
    print("\n📖 下一步:")
    print("- 查看 02_builtin_functions.py 了解所有内置函数")
    print("- 查看 03_enterprise_filters.py 了解企业级过滤器")
    print("- 查看 ../plugins/ 目录了解插件使用")
    print("- 查看 ../business_scenarios/ 目录了解业务场景")

if __name__ == "__main__":
    getting_started_example()
