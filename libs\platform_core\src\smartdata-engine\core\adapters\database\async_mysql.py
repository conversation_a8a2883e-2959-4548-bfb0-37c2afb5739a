"""
异步MySQL数据库适配器

提供高性能的异步MySQL数据库操作，支持连接池和流式查询
"""

from typing import Any, Dict, List, Optional, Union, AsyncIterator
import logging

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../'))

from core.unified_adapter import UnifiedDataAdapter
from core.adapters.base import ConnectionInfo

# 尝试导入异步MySQL驱动
try:
    import aiomysql
    AIOMYSQL_AVAILABLE = True
except ImportError:
    AIOMYSQL_AVAILABLE = False
    aiomysql = None


class AsyncMySQLAdapter(UnifiedDataAdapter):
    """
    异步MySQL数据库适配器
    
    支持MySQL特有的异步功能：
    - 异步连接和连接池
    - 流式查询处理大结果集
    - 异步事务管理
    - 批量操作优化
    - MySQL特有功能支持
    """
    
    def __init__(self):
        super().__init__()
        if not AIOMYSQL_AVAILABLE:
            self.logger.warning("aiomysql未安装，异步MySQL功能受限")
    
    def supported_types(self) -> List[str]:
        """返回支持的数据类型列表"""
        return [
            'mysql',
            'mysql_connection',
            'mysql_connection_string',
            'mysql_url',
            'mariadb',
            'mariadb_connection',
            'mariadb_connection_string',
            'mariadb_url'
        ]
    
    def _is_supported_connection_string(self, connection_string: str) -> bool:
        """检查是否支持的连接字符串"""
        return connection_string.startswith(('mysql://', 'mysql+aiomysql://', 'mariadb://'))
    
    def _is_supported_connection_object(self, connection: Any) -> bool:
        """检查是否支持的连接对象"""
        if not AIOMYSQL_AVAILABLE:
            return False
        
        # 检查是否是aiomysql连接对象
        return (hasattr(connection, 'cursor') and 
                hasattr(connection, 'commit') and
                'aiomysql' in str(type(connection)))
    
    def _get_default_port(self) -> int:
        """获取默认端口"""
        return 3306
    
    def can_handle(self, data_source: Any) -> bool:
        """检查是否能处理指定的数据源"""
        if isinstance(data_source, str):
            return self._is_supported_connection_string(data_source)
        else:
            return self._is_supported_connection_object(data_source)
    
    def _build_operations(self) -> Dict[str, callable]:
        """构建MySQL特有操作列表"""
        operations = {}
        
        # 添加异步操作
        async_ops = self.get_async_operations()
        for name, func in async_ops.items():
            operations[name] = func
        
        return operations
    
    # ========================================================================
    # 同步方法实现（兼容性）
    # ========================================================================
    
    def _sync_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """同步查询实现 - 使用PyMySQL"""
        # 这里可以使用PyMySQL作为同步实现
        # 为了简化，这里抛出异常提示使用异步版本
        raise NotImplementedError("请使用异步版本的MySQL适配器")
    
    def _sync_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """同步执行实现"""
        raise NotImplementedError("请使用异步版本的MySQL适配器")
    
    def _sync_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """同步事务实现"""
        raise NotImplementedError("请使用异步版本的MySQL适配器")
    
    # ========================================================================
    # 异步方法实现
    # ========================================================================
    
    async def _create_async_connection(self, connection_source: Any) -> Any:
        """创建异步MySQL连接"""
        if not AIOMYSQL_AVAILABLE:
            raise ImportError("aiomysql未安装，无法创建异步MySQL连接")
        
        if isinstance(connection_source, str):
            # 解析连接字符串
            if connection_source.startswith('mysql://'):
                # mysql://user:password@host:port/database
                connection_string = connection_source[8:]  # 移除 'mysql://'
                parts = connection_string.split('/')
                if len(parts) < 2:
                    raise ValueError("无效的MySQL连接字符串格式")
                
                database = parts[1].split('?')[0]  # 移除查询参数
                auth_host = parts[0]
                
                if '@' in auth_host:
                    auth, host_port = auth_host.split('@')
                    if ':' in auth:
                        username, password = auth.split(':', 1)
                    else:
                        username, password = auth, ''
                else:
                    username, password = 'root', ''
                    host_port = auth_host
                
                if ':' in host_port:
                    host, port = host_port.split(':')
                    port = int(port)
                else:
                    host, port = host_port, 3306
                
                conn_params = {
                    'host': host,
                    'port': port,
                    'user': username,
                    'password': password,
                    'db': database,
                    'charset': 'utf8mb4',
                    'autocommit': False
                }
            else:
                raise ValueError(f"不支持的连接字符串格式: {connection_source}")
        else:
            # 从ConnectionInfo构建连接参数
            conn_info = connection_source
            conn_params = {
                'host': conn_info.host,
                'port': conn_info.port or 3306,
                'user': conn_info.username,
                'password': conn_info.password,
                'db': conn_info.database,
                'charset': 'utf8mb4',
                'autocommit': False
            }
            
            # 添加额外选项
            if conn_info.options:
                conn_params.update(conn_info.options)
        
        try:
            # 创建异步连接
            connection = await aiomysql.connect(**conn_params)
            
            self.logger.info(f"成功创建异步MySQL连接: {conn_params['host']}:{conn_params['port']}/{conn_params['db']}")
            return connection
            
        except Exception as e:
            self.logger.error(f"创建异步MySQL连接失败: {e}")
            raise
    
    async def _close_async_connection(self, connection: Any) -> None:
        """关闭异步连接"""
        try:
            connection.close()
            await connection.wait_closed()
            self.logger.debug("异步MySQL连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭异步MySQL连接失败: {e}")
    
    async def _create_async_pool(self, connection_source: Any, min_size: int = 5, max_size: int = 20) -> Any:
        """创建异步连接池"""
        if not AIOMYSQL_AVAILABLE:
            raise ImportError("aiomysql未安装，无法创建异步连接池")
        
        # 解析连接参数（复用_create_async_connection的逻辑）
        if isinstance(connection_source, str):
            if connection_source.startswith('mysql://'):
                connection_string = connection_source[8:]
                parts = connection_string.split('/')
                database = parts[1].split('?')[0]
                auth_host = parts[0]
                
                if '@' in auth_host:
                    auth, host_port = auth_host.split('@')
                    if ':' in auth:
                        username, password = auth.split(':', 1)
                    else:
                        username, password = auth, ''
                else:
                    username, password = 'root', ''
                    host_port = auth_host
                
                if ':' in host_port:
                    host, port = host_port.split(':')
                    port = int(port)
                else:
                    host, port = host_port, 3306
                
                pool_params = {
                    'host': host,
                    'port': port,
                    'user': username,
                    'password': password,
                    'db': database,
                    'charset': 'utf8mb4',
                    'autocommit': False,
                    'minsize': min_size,
                    'maxsize': max_size
                }
        else:
            conn_info = connection_source
            pool_params = {
                'host': conn_info.host,
                'port': conn_info.port or 3306,
                'user': conn_info.username,
                'password': conn_info.password,
                'db': conn_info.database,
                'charset': 'utf8mb4',
                'autocommit': False,
                'minsize': min_size,
                'maxsize': max_size
            }
        
        try:
            # 创建连接池
            pool = await aiomysql.create_pool(**pool_params)
            
            self.logger.info(f"成功创建异步MySQL连接池: min={min_size}, max={max_size}")
            return pool
            
        except Exception as e:
            self.logger.error(f"创建异步MySQL连接池失败: {e}")
            raise
    
    async def _async_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """异步查询实现"""
        try:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                if params:
                    # 使用位置参数
                    param_values = list(params.values())
                    await cursor.execute(sql, param_values)
                else:
                    await cursor.execute(sql)
                
                rows = await cursor.fetchall()
                return list(rows)
                
        except Exception as e:
            self.logger.error(f"异步MySQL查询失败: {e}")
            raise
    
    async def _async_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """异步执行实现"""
        try:
            async with connection.cursor() as cursor:
                if params:
                    param_values = list(params.values())
                    await cursor.execute(sql, param_values)
                else:
                    await cursor.execute(sql)
                
                # 提交事务
                await connection.commit()
                
                # 返回影响行数
                return cursor.rowcount
                
        except Exception as e:
            self.logger.error(f"异步MySQL执行失败: {e}")
            raise
    
    async def _async_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步事务实现"""
        try:
            # 开始事务
            await connection.begin()
            
            results = []
            total_affected = 0
            
            try:
                for operation in operations:
                    op_type = operation.get('type', 'execute')
                    sql = operation['sql']
                    params = operation.get('params')
                    
                    if op_type == 'query':
                        result = await self._async_query(connection, sql, params)
                        results.append(result)
                    else:
                        affected = await self._async_execute(connection, sql, params)
                        results.append(affected)
                        total_affected += affected
                
                # 提交事务
                await connection.commit()
                
                return {
                    'results': results,
                    'total_operations': len(operations),
                    'total_affected': total_affected,
                    'success': True
                }
                
            except Exception as e:
                # 回滚事务
                await connection.rollback()
                raise
                
        except Exception as e:
            self.logger.error(f"异步MySQL事务失败: {e}")
            raise
    
    async def _async_batch(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步批量操作实现"""
        results = []
        total_affected = 0
        
        for operation in operations:
            op_type = operation.get('type', 'execute')
            sql = operation['sql']
            params = operation.get('params')
            
            if op_type == 'query':
                result = await self._async_query(connection, sql, params)
                results.append(result)
            else:
                affected = await self._async_execute(connection, sql, params)
                results.append(affected)
                total_affected += affected
        
        return {
            'results': results,
            'total_operations': len(operations),
            'total_affected': total_affected,
            'success': True
        }
    
    async def _async_stream_query(self, connection: Any, sql: str, params: Dict = None) -> AsyncIterator[Dict]:
        """异步流式查询实现"""
        try:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                if params:
                    param_values = list(params.values())
                    await cursor.execute(sql, param_values)
                else:
                    await cursor.execute(sql)
                
                # 逐行获取数据
                while True:
                    row = await cursor.fetchone()
                    if row is None:
                        break
                    yield dict(row)
                        
        except Exception as e:
            self.logger.error(f"异步MySQL流式查询失败: {e}")
            raise
    
    # ========================================================================
    # MySQL特有异步功能
    # ========================================================================
    
    def get_async_operations(self) -> Dict[str, Any]:
        """获取支持的异步操作列表"""
        operations = super().get_async_operations()
        
        # 添加MySQL特有异步操作
        operations.update({
            'async_show_tables': self._async_show_tables,
            'async_show_databases': self._async_show_databases,
            'async_describe_table': self._async_describe_table,
            'async_optimize_table': self._async_optimize_table,
            'async_analyze_table': self._async_analyze_table,
        })
        
        return operations
    
    async def _async_show_tables(self, connection: Any, database: str = None) -> List[Dict]:
        """异步显示表列表"""
        try:
            if database:
                sql = f"SHOW TABLES FROM {database}"
            else:
                sql = "SHOW TABLES"
            
            return await self._async_query(connection, sql)
            
        except Exception as e:
            self.logger.error(f"异步SHOW TABLES操作失败: {e}")
            raise
    
    async def _async_show_databases(self, connection: Any) -> List[Dict]:
        """异步显示数据库列表"""
        try:
            return await self._async_query(connection, "SHOW DATABASES")
            
        except Exception as e:
            self.logger.error(f"异步SHOW DATABASES操作失败: {e}")
            raise
    
    async def _async_describe_table(self, connection: Any, table: str) -> List[Dict]:
        """异步描述表结构"""
        try:
            return await self._async_query(connection, f"DESCRIBE {table}")
            
        except Exception as e:
            self.logger.error(f"异步DESCRIBE TABLE操作失败: {e}")
            raise
    
    async def _async_optimize_table(self, connection: Any, table: str) -> Dict:
        """异步优化表"""
        try:
            result = await self._async_query(connection, f"OPTIMIZE TABLE {table}")
            
            return {
                'success': True,
                'operation': 'async_optimize_table',
                'table': table,
                'result': result
            }
            
        except Exception as e:
            self.logger.error(f"异步OPTIMIZE TABLE操作失败: {e}")
            raise
    
    async def _async_analyze_table(self, connection: Any, table: str) -> Dict:
        """异步分析表"""
        try:
            result = await self._async_query(connection, f"ANALYZE TABLE {table}")
            
            return {
                'success': True,
                'operation': 'async_analyze_table',
                'table': table,
                'result': result
            }
            
        except Exception as e:
            self.logger.error(f"异步ANALYZE TABLE操作失败: {e}")
            raise
    
    async def get_async_database_info(self, connection: Any) -> Dict[str, Any]:
        """获取异步数据库信息"""
        try:
            # 获取版本信息
            version_result = await self._async_query(connection, "SELECT VERSION() as version")
            
            # 获取数据库名称
            db_result = await self._async_query(connection, "SELECT DATABASE() as database_name")
            
            # 获取用户信息
            user_result = await self._async_query(connection, "SELECT USER() as current_user")
            
            return {
                'database_type': 'MySQL',
                'version': version_result[0]['version'] if version_result else 'Unknown',
                'database_name': db_result[0]['database_name'] if db_result else 'Unknown',
                'current_user': user_result[0]['current_user'] if user_result else 'Unknown',
                'async_capable': True
            }
            
        except Exception as e:
            self.logger.error(f"获取异步MySQL数据库信息失败: {e}")
            return {
                'database_type': 'MySQL',
                'error': str(e),
                'async_capable': True
            }
