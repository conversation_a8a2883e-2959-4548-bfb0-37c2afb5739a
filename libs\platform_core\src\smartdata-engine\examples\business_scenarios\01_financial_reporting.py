#!/usr/bin/env python3
"""
SmartData模板引擎财务报表生成示例

展示企业级财务报表自动化生成，集成多个插件和高级功能
基于验证的100%测试通过率和自动化机制
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.template_ext import create_template_engine

def financial_reporting_example():
    """财务报表生成完整示例"""
    print("=== SmartData模板引擎财务报表生成示例 ===")
    
    # 创建模板引擎 - 所有功能自动可用
    engine = create_template_engine()
    print("✅ 模板引擎创建完成 - 企业级功能自动注册")
    
    # 1. 月度财务报表
    print("\n📊 1. 月度财务报表生成")
    template_monthly_report = """
{%- set financial_data = {
    'company': '智慧科技有限公司',
    'period': '2024年1月',
    'revenue': [
        {'category': '产品销售', 'amount': 2500000, 'budget': 2200000},
        {'category': '服务收入', 'amount': 800000, 'budget': 750000},
        {'category': '授权费用', 'amount': 300000, 'budget': 350000}
    ],
    'expenses': [
        {'category': '人力成本', 'amount': 1200000, 'budget': 1150000},
        {'category': '运营费用', 'amount': 450000, 'budget': 500000},
        {'category': '研发投入', 'amount': 600000, 'budget': 580000},
        {'category': '市场推广', 'amount': 200000, 'budget': 220000}
    ],
    'assets': {
        'current_assets': 5200000,
        'fixed_assets': 3800000,
        'intangible_assets': 1200000
    },
    'liabilities': {
        'current_liabilities': 1800000,
        'long_term_liabilities': 2200000
    }
} -%}

{%- set total_revenue = financial_data.revenue | sum(attribute='amount') -%}
{%- set total_revenue_budget = financial_data.revenue | sum(attribute='budget') -%}
{%- set total_expenses = financial_data.expenses | sum(attribute='amount') -%}
{%- set total_expenses_budget = financial_data.expenses | sum(attribute='budget') -%}
{%- set net_profit = total_revenue - total_expenses -%}
{%- set total_assets = financial_data.assets.current_assets + financial_data.assets.fixed_assets + financial_data.assets.intangible_assets -%}
{%- set total_liabilities = financial_data.liabilities.current_liabilities + financial_data.liabilities.long_term_liabilities -%}
{%- set equity = total_assets - total_liabilities -%}

{{ financial_data.company }}
{{ financial_data.period }}财务报表
生成时间: {{ now() }}
=====================================

一、收入分析
-----------
{%- for item in financial_data.revenue %}
{{ item.category }}:
  实际收入: ¥{{ format_number(item.amount) }}
  预算收入: ¥{{ format_number(item.budget) }}
  完成率: {{ calculate_percentage(item.amount, item.budget) }}%
  差异: ¥{{ format_number(item.amount - item.budget) }}
{%- endfor %}

收入汇总:
  总收入: ¥{{ format_number(total_revenue) }}
  预算总收入: ¥{{ format_number(total_revenue_budget) }}
  整体完成率: {{ calculate_percentage(total_revenue, total_revenue_budget) }}%
  超额收入: ¥{{ format_number(total_revenue - total_revenue_budget) }}

二、支出分析
-----------
{%- for item in financial_data.expenses %}
{{ item.category }}:
  实际支出: ¥{{ format_number(item.amount) }}
  预算支出: ¥{{ format_number(item.budget) }}
  预算执行率: {{ calculate_percentage(item.amount, item.budget) }}%
  节约/超支: ¥{{ format_number(item.budget - item.amount) }}
{%- endfor %}

支出汇总:
  总支出: ¥{{ format_number(total_expenses) }}
  预算总支出: ¥{{ format_number(total_expenses_budget) }}
  预算执行率: {{ calculate_percentage(total_expenses, total_expenses_budget) }}%
  预算节约: ¥{{ format_number(total_expenses_budget - total_expenses) }}

三、盈利分析
-----------
净利润: ¥{{ format_number(net_profit) }}
利润率: {{ calculate_percentage(net_profit, total_revenue) }}%
盈利状况: {{ "盈利" if net_profit > 0 else "亏损" }}
{%- if net_profit > 0 %}
盈利能力: {{ "优秀" if calculate_percentage(net_profit, total_revenue) > 15 else "良好" if calculate_percentage(net_profit, total_revenue) > 10 else "一般" }}
{%- endif %}

四、资产负债分析
--------------
资产结构:
  流动资产: ¥{{ format_number(financial_data.assets.current_assets) }} ({{ calculate_percentage(financial_data.assets.current_assets, total_assets) }}%)
  固定资产: ¥{{ format_number(financial_data.assets.fixed_assets) }} ({{ calculate_percentage(financial_data.assets.fixed_assets, total_assets) }}%)
  无形资产: ¥{{ format_number(financial_data.assets.intangible_assets) }} ({{ calculate_percentage(financial_data.assets.intangible_assets, total_assets) }}%)
  总资产: ¥{{ format_number(total_assets) }}

负债结构:
  流动负债: ¥{{ format_number(financial_data.liabilities.current_liabilities) }}
  长期负债: ¥{{ format_number(financial_data.liabilities.long_term_liabilities) }}
  总负债: ¥{{ format_number(total_liabilities) }}

所有者权益: ¥{{ format_number(equity) }}
资产负债率: {{ calculate_percentage(total_liabilities, total_assets) }}%
流动比率: {{ (financial_data.assets.current_assets / financial_data.liabilities.current_liabilities) | round(2) }}

五、财务健康度评估
----------------
{%- set health_score = 0 -%}
{%- if calculate_percentage(net_profit, total_revenue) > 10 -%}
  {%- set health_score = health_score + 25 -%}
{%- endif -%}
{%- if calculate_percentage(total_liabilities, total_assets) < 60 -%}
  {%- set health_score = health_score + 25 -%}
{%- endif -%}
{%- if (financial_data.assets.current_assets / financial_data.liabilities.current_liabilities) > 1.5 -%}
  {%- set health_score = health_score + 25 -%}
{%- endif -%}
{%- if calculate_percentage(total_revenue, total_revenue_budget) > 95 -%}
  {%- set health_score = health_score + 25 -%}
{%- endif -%}

财务健康度评分: {{ health_score }}/100
评级: {{ "A级" if health_score >= 80 else "B级" if health_score >= 60 else "C级" if health_score >= 40 else "D级" }}
风险等级: {{ "低风险" if health_score >= 80 else "中等风险" if health_score >= 60 else "高风险" }}

六、经营建议
-----------
{%- if calculate_percentage(net_profit, total_revenue) < 10 %}
• 建议优化成本结构，提高盈利能力
{%- endif %}
{%- if calculate_percentage(total_liabilities, total_assets) > 60 %}
• 建议降低负债率，改善财务结构
{%- endif %}
{%- if (financial_data.assets.current_assets / financial_data.liabilities.current_liabilities) < 1.5 %}
• 建议增加流动资产，提高偿债能力
{%- endif %}
{%- if calculate_percentage(total_revenue, total_revenue_budget) < 95 %}
• 建议加强销售管理，提高收入完成率
{%- endif %}

报表生成完成 - {{ now() }}
    """.strip()
    
    result = engine.render_template(template_monthly_report)
    print("渲染结果:")
    print(result)
    
    # 2. 多期财务对比分析
    print("\n📈 2. 多期财务对比分析")
    template_comparison = """
{%- set quarterly_data = [
    {
        'quarter': '2024 Q1',
        'revenue': 3600000,
        'expenses': 2450000,
        'assets': 10200000,
        'liabilities': 4000000
    },
    {
        'quarter': '2024 Q2',
        'revenue': 4200000,
        'expenses': 2800000,
        'assets': 11500000,
        'liabilities': 4200000
    },
    {
        'quarter': '2024 Q3',
        'revenue': 4800000,
        'expenses': 3100000,
        'assets': 12800000,
        'liabilities': 4500000
    }
] -%}

多期财务对比分析报告
生成时间: {{ now() }}
==================

季度财务表现:
{%- for quarter in quarterly_data %}
{%- set profit = quarter.revenue - quarter.expenses -%}
{%- set equity = quarter.assets - quarter.liabilities -%}
{{ quarter.quarter }}:
  营业收入: ¥{{ format_number(quarter.revenue) }}
  营业支出: ¥{{ format_number(quarter.expenses) }}
  净利润: ¥{{ format_number(profit) }}
  利润率: {{ calculate_percentage(profit, quarter.revenue) }}%
  总资产: ¥{{ format_number(quarter.assets) }}
  净资产: ¥{{ format_number(equity) }}
  资产负债率: {{ calculate_percentage(quarter.liabilities, quarter.assets) }}%
{%- endfor %}

增长趋势分析:
{%- set q1_revenue = quarterly_data[0].revenue -%}
{%- set q3_revenue = quarterly_data[2].revenue -%}
{%- set revenue_growth = calculate_percentage(q3_revenue - q1_revenue, q1_revenue) -%}
收入增长率: {{ revenue_growth }}%
{%- if revenue_growth > 20 %}
增长评价: 高速增长 🚀
{%- elif revenue_growth > 10 %}
增长评价: 稳健增长 📈
{%- elif revenue_growth > 0 %}
增长评价: 缓慢增长 📊
{%- else %}
增长评价: 收入下降 📉
{%- endif %}

平均季度表现:
- 平均收入: ¥{{ format_number(quarterly_data | avg_by('revenue')) }}
- 平均支出: ¥{{ format_number(quarterly_data | avg_by('expenses')) }}
- 平均利润: ¥{{ format_number((quarterly_data | sum(attribute='revenue') - quarterly_data | sum(attribute='expenses')) / 3) }}
- 平均资产: ¥{{ format_number(quarterly_data | avg_by('assets')) }}

最佳表现季度: {{ quarterly_data | sort_by('revenue', reverse=true) | first | attr('quarter') }}
    """.strip()
    
    result = engine.render_template(template_comparison)
    print("渲染结果:")
    print(result)
    
    print("\n🎉 财务报表生成示例完成！")
    print("\n📊 功能特点:")
    print("💰 收入分析 - 多维度收入结构分析")
    print("💸 支出控制 - 预算执行和成本分析")
    print("📈 盈利能力 - 利润率和盈利质量评估")
    print("🏦 资产负债 - 财务结构和偿债能力")
    print("🎯 健康评估 - 综合财务健康度评分")
    print("📋 经营建议 - 基于数据的管理建议")
    
    print("\n💡 技术亮点:")
    print("✅ 自动化计算 - 复杂财务指标自动计算")
    print("✅ 智能分析 - 基于规则的智能评估")
    print("✅ 动态报表 - 数据驱动的报表生成")
    print("✅ 多期对比 - 趋势分析和增长评估")
    print("✅ 风险预警 - 财务风险自动识别")

if __name__ == "__main__":
    financial_reporting_example()
