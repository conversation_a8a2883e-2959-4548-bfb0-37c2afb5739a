#!/usr/bin/env python3
"""
测试查询流程
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

def test_query_flow():
    """测试查询流程"""
    print("=== 测试查询流程 ===")
    
    try:
        # 直接测试DatabaseConnector
        from core.smart_data_object import SmartDataLoader
        
        loader = SmartDataLoader()
        
        # 测试database方法
        db_connector = loader.database("sqlite:///:memory:")
        print(f"✅ 数据库连接器类型: {type(db_connector)}")
        print(f"✅ 数据库连接器: {db_connector}")
        
        # 测试query方法
        if hasattr(db_connector, 'query'):
            query_result = db_connector.query("SELECT 'Hello' as message")
            print(f"✅ 查询结果类型: {type(query_result)}")
            print(f"✅ 查询结果: {query_result}")
            
            # 检查SmartDataObject的属性
            if hasattr(query_result, 'data'):
                print(f"✅ 查询结果数据: {query_result.data}")
            if hasattr(query_result, 'raw_data'):
                print(f"✅ 查询结果原始数据: {query_result.raw_data}")
            
            # 尝试访问success属性
            try:
                success = query_result.success
                print(f"✅ success属性: {success}")
            except AttributeError as e:
                print(f"❌ 无法访问success属性: {e}")
            
            # 尝试访问data属性
            try:
                data = query_result.data
                print(f"✅ data属性: {data}")
            except AttributeError as e:
                print(f"❌ 无法访问data属性: {e}")
                
        else:
            print("❌ 数据库连接器没有query方法")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_query_flow()
