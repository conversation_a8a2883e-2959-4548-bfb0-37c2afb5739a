#!/usr/bin/env python3
"""
平滑迁移演示

展示从旧模板引擎到线程安全版本的三种迁移模式
"""

import sys
import os
import tempfile
import json
from typing import Dict, Any

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from migration.smooth_migration_tool import (
    SmoothMigrationTool, MigrationConfig, MigrationMode
)


def migration_demo():
    """平滑迁移演示"""
    print("=== 平滑迁移演示 ===")
    print("展示三种迁移模式：兼容、混合、完全")
    print("=" * 80)
    
    # 创建模拟的旧代码
    old_code_samples = create_old_code_samples()
    
    # 🔄 示例1：兼容模式迁移
    print("\n🔄 示例1：兼容模式迁移")
    print("-" * 60)
    print("保持旧API不变，内部使用线程安全新架构")
    
    compatibility_demo(old_code_samples)
    
    # 🔀 示例2：混合模式迁移
    print("\n🔀 示例2：混合模式迁移")
    print("-" * 60)
    print("新旧API并存，支持逐步迁移")
    
    hybrid_demo(old_code_samples)
    
    # 🚀 示例3：完全模式迁移
    print("\n🚀 示例3：完全模式迁移")
    print("-" * 60)
    print("完全使用新架构API")
    
    full_demo(old_code_samples)
    
    # 📊 示例4：迁移对比分析
    print("\n📊 示例4：迁移对比分析")
    print("-" * 60)
    
    comparison_demo()
    
    print("\n🎉 平滑迁移演示完成！")


def create_old_code_samples() -> Dict[str, str]:
    """创建旧代码示例"""
    return {
        'old_template_usage': '''
# 旧模板引擎使用示例
from template.hybrid_template_engine import HybridTemplateEngine

engine = HybridTemplateEngine(
    enable_smart_data=True,
    enable_jsonpath=True
)

template = """
用户信息
=======
姓名: {{ user.name }}
邮箱: {{ user.email }}
技能: {{ user.skills | join(', ') }}
"""

context = {
    'user': {
        'name': '张三',
        'email': '<EMAIL>',
        'skills': ['Python', 'AI', '数据分析']
    }
}

result = engine.render_template(template, context)
print(result)
        ''',
        
        'smart_data_usage': '''
# 智能数据处理示例
from template.components.smart_data_factory import SmartDataFactory

factory = SmartDataFactory()
smart_data = factory.create_modifier(complex_json_data)

# JSONPath查询
user_names = smart_data.jsonpath('$.users[*].name')
active_users = smart_data.jsonpath('$.users[?(@.active==true)]')
        ''',
        
        'template_with_smart_features': '''
# 带智能功能的模板
template = """
智能数据处理演示
===============
{% set smart_data = sd.smart_data(data) %}
{% set user_count = sd.jsonpath(data, '$.users | length') %}

用户总数: {{ user_count }}
活跃用户: {{ sd.jsonpath(data, '$.users[?(@.active==true)]') | length }}

用户列表:
{% for user in data.users %}
- {{ user.name }} ({{ user.department }})
{% endfor %}
"""
        '''
    }


def compatibility_demo(old_code_samples: Dict[str, str]):
    """兼容模式演示"""
    try:
        # 创建兼容模式迁移工具
        config = MigrationConfig(
            mode=MigrationMode.COMPATIBILITY,
            enable_thread_safety=True,
            enable_legacy_support=True,
            backup_original=True
        )
        
        migration_tool = SmoothMigrationTool(config)
        
        # 创建临时源文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(old_code_samples['old_template_usage'])
            source_file = f.name
        
        # 创建临时目标目录
        target_dir = tempfile.mkdtemp()
        
        # 执行迁移
        result = migration_tool.migrate(source_file, target_dir)
        
        print(f"✅ 兼容模式迁移: {'成功' if result.success else '失败'}")
        print(f"   迁移组件: {result.migrated_components}")
        print(f"   迁移时间: {result.migration_time}")
        
        if result.backup_path:
            print(f"   备份路径: {result.backup_path}")
        
        # 测试兼容性包装器
        test_compatibility_wrapper(target_dir)
        
        # 清理
        migration_tool.cleanup_backups()
        os.unlink(source_file)
        
    except Exception as e:
        print(f"❌ 兼容模式演示失败: {e}")


def hybrid_demo(old_code_samples: Dict[str, str]):
    """混合模式演示"""
    try:
        # 创建混合模式迁移工具
        config = MigrationConfig(
            mode=MigrationMode.HYBRID,
            enable_thread_safety=True,
            test_before_migration=False
        )
        
        migration_tool = SmoothMigrationTool(config)
        
        # 创建临时源文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(old_code_samples['smart_data_usage'])
            source_file = f.name
        
        # 创建临时目标目录
        target_dir = tempfile.mkdtemp()
        
        # 执行迁移
        result = migration_tool.migrate(source_file, target_dir)
        
        print(f"✅ 混合模式迁移: {'成功' if result.success else '失败'}")
        print(f"   迁移组件: {result.migrated_components}")
        print(f"   迁移时间: {result.migration_time}")
        
        # 测试混合桥接器
        test_hybrid_bridge(target_dir)
        
        # 清理
        migration_tool.cleanup_backups()
        os.unlink(source_file)
        
    except Exception as e:
        print(f"❌ 混合模式演示失败: {e}")


def full_demo(old_code_samples: Dict[str, str]):
    """完全模式演示"""
    try:
        # 创建完全模式迁移工具
        config = MigrationConfig(
            mode=MigrationMode.FULL,
            enable_thread_safety=True,
            test_before_migration=False
        )
        
        migration_tool = SmoothMigrationTool(config)
        
        # 创建临时源文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(old_code_samples['template_with_smart_features'])
            source_file = f.name
        
        # 创建临时目标目录
        target_dir = tempfile.mkdtemp()
        
        # 执行迁移
        result = migration_tool.migrate(source_file, target_dir)
        
        print(f"✅ 完全模式迁移: {'成功' if result.success else '失败'}")
        print(f"   迁移组件: {result.migrated_components}")
        print(f"   迁移时间: {result.migration_time}")
        
        # 测试新引擎
        test_new_engine(target_dir)
        
        # 清理
        migration_tool.cleanup_backups()
        os.unlink(source_file)
        
    except Exception as e:
        print(f"❌ 完全模式演示失败: {e}")


def test_compatibility_wrapper(target_dir: str):
    """测试兼容性包装器"""
    try:
        compatibility_file = os.path.join(target_dir, 'template_compatibility.py')
        if os.path.exists(compatibility_file):
            print("   📝 兼容性包装器已创建")
            
            # 读取并验证内容
            with open(compatibility_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'HybridTemplateEngine' in content and 'ThreadSafeTemplateIntegration' in content:
                print("   ✅ 包装器内容验证通过")
            else:
                print("   ⚠️ 包装器内容可能不完整")
        else:
            print("   ❌ 兼容性包装器文件未找到")
            
    except Exception as e:
        print(f"   ❌ 兼容性包装器测试失败: {e}")


def test_hybrid_bridge(target_dir: str):
    """测试混合桥接器"""
    try:
        bridge_file = os.path.join(target_dir, 'template_bridge.py')
        if os.path.exists(bridge_file):
            print("   📝 混合桥接器已创建")
            
            # 读取并验证内容
            with open(bridge_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'TemplateBridge' in content and 'render_auto' in content:
                print("   ✅ 桥接器内容验证通过")
            else:
                print("   ⚠️ 桥接器内容可能不完整")
        else:
            print("   ❌ 混合桥接器文件未找到")
            
    except Exception as e:
        print(f"   ❌ 混合桥接器测试失败: {e}")


def test_new_engine(target_dir: str):
    """测试新引擎"""
    try:
        engine_file = os.path.join(target_dir, 'new_template_engine.py')
        if os.path.exists(engine_file):
            print("   📝 新引擎代码已创建")
            
            # 读取并验证内容
            with open(engine_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'ThreadSafeTemplateIntegration' in content and 'render_template' in content:
                print("   ✅ 新引擎内容验证通过")
            else:
                print("   ⚠️ 新引擎内容可能不完整")
        else:
            print("   ❌ 新引擎文件未找到")
            
    except Exception as e:
        print(f"   ❌ 新引擎测试失败: {e}")


def comparison_demo():
    """迁移对比演示"""
    print("迁移模式对比:")
    print("┌─────────────┬─────────────┬─────────────┬─────────────┐")
    print("│    特性     │  兼容模式   │  混合模式   │  完全模式   │")
    print("├─────────────┼─────────────┼─────────────┼─────────────┤")
    print("│ API兼容性   │     100%    │     90%     │     0%      │")
    print("│ 迁移成本    │     低      │     中      │     高      │")
    print("│ 新功能使用  │     部分    │     完整    │     完整    │")
    print("│ 线程安全    │     ✅      │     ✅      │     ✅      │")
    print("│ 性能提升    │     中      │     高      │     最高    │")
    print("│ 风险等级    │     最低    │     中      │     高      │")
    print("└─────────────┴─────────────┴─────────────┴─────────────┘")
    
    print("\n推荐迁移路径:")
    print("1. 🔄 兼容模式 → 立即获得线程安全，零代码修改")
    print("2. 🔀 混合模式 → 逐步迁移，新旧并存")
    print("3. 🚀 完全模式 → 完全使用新架构，最佳性能")
    
    print("\n迁移建议:")
    print("• 生产环境：建议从兼容模式开始")
    print("• 新项目：直接使用完全模式")
    print("• 大型项目：采用混合模式逐步迁移")


if __name__ == "__main__":
    migration_demo()
