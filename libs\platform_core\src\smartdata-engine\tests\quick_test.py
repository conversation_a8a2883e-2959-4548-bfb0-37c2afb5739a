"""
快速测试脚本 - 验证核心功能
"""

import sys
import os
import unittest
import tempfile
import asyncio

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_enterprise_template_integration():
    """测试企业级模板集成"""
    print("🧪 测试企业级模板集成...")
    
    try:
        from template.enterprise_template_integration import EnterpriseTemplateIntegration
        
        # 创建集成器
        integration = EnterpriseTemplateIntegration(
            enable_async=False,
            enable_legacy_support=False,
            enable_debug=False
        )
        
        # 测试模板作用域创建
        scope = integration.create_template_scope('test')
        assert scope is not None
        assert 'test' in integration.template_scopes
        
        # 测试数据源注册
        test_data = [{'id': 1, 'name': 'test'}]
        proxy = integration.register_data_source(scope, 'test_data', test_data)
        assert proxy is not None
        
        # 测试基础模板渲染
        template = "Hello {{ name }}!"
        context = {'name': 'World'}
        result = integration.render_template_sync(template, context)
        assert isinstance(result, str)
        
        # 清理
        integration.cleanup_template_scope('test')
        
        print("✅ 企业级模板集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 企业级模板集成测试失败: {e}")
        return False


def test_enterprise_template_factory():
    """测试企业级模板引擎工厂"""
    print("🧪 测试企业级模板引擎工厂...")
    
    try:
        from template.enterprise_template_factory import (
            EnterpriseTemplateFactory,
            EnterpriseTemplateConfig,
            TemplateEngineMode
        )
        
        # 测试默认引擎创建
        engine = EnterpriseTemplateFactory.create_engine()
        assert engine is not None
        
        # 测试基础渲染
        template = "Hello {{ name }}!"
        context = {'name': 'Factory'}
        result = engine.render(template, context)
        assert isinstance(result, str)
        
        # 测试性能统计
        stats = engine.get_performance_stats()
        assert 'render_count' in stats
        assert stats['render_count'] == 1
        
        # 测试支持的数据类型
        data_types = engine.get_supported_data_types()
        assert isinstance(data_types, list)
        assert len(data_types) > 0
        
        # 清理
        engine.cleanup()
        
        print("✅ 企业级模板引擎工厂测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 企业级模板引擎工厂测试失败: {e}")
        return False


def test_data_adapters():
    """测试数据适配器"""
    print("🧪 测试数据适配器...")
    
    try:
        from core.enterprise_data_architecture import DataRegistry
        from core.adapters.memory.data_list_adapter import DataListAdapter
        from core.adapters.database.sqlite import SQLiteAdapter
        
        # 创建注册表
        registry = DataRegistry()
        
        # 注册适配器
        registry.register_adapter(DataListAdapter)
        registry.register_adapter(SQLiteAdapter)
        
        # 测试内存数据适配器
        test_data = [{'id': 1, 'name': 'test'}]
        adapter = registry.get_adapter(test_data)
        assert adapter is not None
        
        # 测试SQLite适配器
        sqlite_adapter = registry.get_adapter(':memory:')
        assert sqlite_adapter is not None
        
        # 测试支持的类型
        types = registry.get_supported_types()
        assert 'data_list' in types
        assert 'sqlite_connection_string' in types
        
        print("✅ 数据适配器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据适配器测试失败: {e}")
        return False


def test_file_adapters():
    """测试文件适配器"""
    print("🧪 测试文件适配器...")
    
    try:
        from core.adapters.file.csv_adapter import CSVAdapter
        from core.adapters.file.json_adapter import JSONAdapter
        
        # 测试CSV适配器
        csv_adapter = CSVAdapter()
        assert csv_adapter.can_handle('test.csv')
        assert not csv_adapter.can_handle('test.xml')  # 使用XML扩展名测试
        
        # 测试JSON适配器
        json_adapter = JSONAdapter()
        assert json_adapter.can_handle('test.json')
        assert not json_adapter.can_handle('test.csv')
        
        # 测试CSV写入和读取
        test_data = [
            {'id': 1, 'name': 'Alice'},
            {'id': 2, 'name': 'Bob'}
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            csv_file = f.name
        
        try:
            # 写入
            written = csv_adapter._sync_write_csv(csv_file, test_data)
            assert written == 2
            
            # 读取
            read_data = csv_adapter._sync_read_csv(csv_file)
            assert len(read_data) == 2
            assert read_data[0]['name'] == 'Alice'
            
        finally:
            if os.path.exists(csv_file):
                os.unlink(csv_file)
        
        print("✅ 文件适配器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 文件适配器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_async_functionality():
    """测试异步功能"""
    print("🧪 测试异步功能...")
    
    try:
        from template.enterprise_template_factory import EnterpriseTemplateConfig, EnterpriseTemplateEngine
        
        async def async_test():
            # 创建异步引擎
            config = EnterpriseTemplateConfig(
                enable_async=True,
                enable_legacy_support=False,
                enable_debug=False
            )
            engine = EnterpriseTemplateEngine(config)
            
            try:
                # 测试异步渲染
                template = "Hello {{ name }}!"
                context = {'name': 'Async'}
                result = await engine.render_async(template, context)
                assert isinstance(result, str)
                
                # 测试统计
                stats = engine.get_performance_stats()
                assert stats['render_count'] == 1
                
                return True
            finally:
                engine.cleanup()
        
        # 运行异步测试
        result = asyncio.run(async_test())
        assert result
        
        print("✅ 异步功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 异步功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 企业级模板引擎 - 快速功能验证")
    print("=" * 60)
    
    tests = [
        test_enterprise_template_integration,
        test_enterprise_template_factory,
        test_data_adapters,
        test_file_adapters,
        test_async_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有核心功能测试通过！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步调试")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
