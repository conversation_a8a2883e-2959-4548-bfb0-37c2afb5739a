"""
File插件

提供企业级文件处理能力，包括：
- 本地文件操作
- 文件压缩和解压
- 格式转换
- 文件监控
- 批量处理
- 安全检查
"""

# 插件定义 - 按照统一标准
PLUGIN_DEFINITIONS = [
    {
        'id': 'file_processor',
        'name': '文件处理器',
        'description': '企业级文件操作处理器，支持压缩、转换、监控等功能',
        'version': '1.0.0',
        'type': 'processor',
        'category': 'file',
        'priority': 65,
        'class_name': 'FileProcessor',
        'module_file': 'file_processor',
        'auto_load': True,
        'enabled': True,
        'capabilities': [
            'file_operations',
            'compression',
            'decompression',
            'format_conversion',
            'file_monitoring',
            'batch_processing',
            'security_scanning',
            'metadata_extraction',
            'file_validation',
            'backup_restore',
            'sync_operations',
            'permission_management'
        ],
        'supported_formats': [
            'text', 'json', 'xml', 'csv', 'yaml', 'ini',
            'zip', 'tar', 'gz', 'bz2', '7z',
            'pdf', 'docx', 'xlsx', 'pptx',
            'jpg', 'png', 'gif', 'bmp', 'svg',
            'mp3', 'mp4', 'avi', 'mov'
        ],
        'supported_operations': [
            'read', 'write', 'copy', 'move', 'delete',
            'compress', 'decompress', 'convert',
            'monitor', 'validate', 'backup', 'sync'
        ],
        'dependencies': [],
        'optional_dependencies': [
            'aiofiles', 'watchdog', 'pillow', 'pypdf2',
            'openpyxl', 'python-docx', 'pyyaml', 'lxml'
        ],
        'author': 'SmartData Team',
        'license': 'MIT',
        'tags': ['file', 'compression', 'conversion', 'monitoring']
    }
]


def get_plugin_definitions():
    """获取插件定义"""
    return PLUGIN_DEFINITIONS


try:
    # File处理器
    from .file_processor import FileProcessor
    
    # File操作工具
    from .file_operations import (
        FileOperationManager,
        FileCompressor,
        FileConverter,
        FileMonitor
    )
    
    # File工具
    from .file_utils import (
        FileValidator,
        MetadataExtractor,
        SecurityScanner
    )

    __all__ = [
        # 处理器
        'FileProcessor',
        
        # 操作工具
        'FileOperationManager',
        'FileCompressor',
        'FileConverter',
        'FileMonitor',
        
        # 工具
        'FileValidator',
        'MetadataExtractor',
        'SecurityScanner',
        
        # 插件定义
        'PLUGIN_DEFINITIONS',
        'get_plugin_definitions'
    ]

except ImportError as e:
    # 优雅处理导入错误
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"File插件部分功能导入失败: {e}")
    
    __all__ = ['PLUGIN_DEFINITIONS', 'get_plugin_definitions']
