#!/usr/bin/env python3
"""
SmartDataTemplateExtension - 智能数据模板扩展入口

这是模板引擎的主要入口文件，整合了：
1. 数据资源扩展 (DataResourceExtension)
2. 高级语法扩展 (AdvancedStatementExtension)
3. Lambda表达式支持
4. 智能数据对象集成
5. 插件系统集成

基于原有架构设计，提供统一的模板引擎接口。
"""

import asyncio
import logging
import time
import pickle
import threading
import hashlib
from typing import Any, Dict, List, Optional, Union, Callable
from collections import defaultdict
from pathlib import Path
from jinja2 import Environment, BaseLoader, select_autoescape, Template
from jinja2.ext import Extension

# 延迟导入避免循环依赖
SmartDataLoader = None
set_global_registry = None

# 导入企业级过滤器
try:
    from .filters import register_enterprise_filters
except ImportError:
    register_enterprise_filters = None

def _lazy_import():
    global SmartDataLoader, set_global_registry
    if SmartDataLoader is None:
        try:
            from ..core.smart_data_object import SmartDataLoader, set_global_registry
        except ImportError:
            from core.smart_data_object import SmartDataLoader, set_global_registry

# 安全导入策略 - 使用try-except处理可能的导入失败
imported_components = {}

def safe_import(module_path, class_name, alias=None):
    """安全导入组件"""
    try:
        if module_path.startswith('.'):
            # 相对导入
            module = __import__(module_path, fromlist=[class_name], level=1)
        else:
            # 绝对导入
            module = __import__(module_path, fromlist=[class_name])

        component = getattr(module, class_name)
        key = alias or class_name
        imported_components[key] = component
        return component
    except (ImportError, AttributeError) as e:
        logging.getLogger(__name__).warning(f"无法导入 {class_name} 从 {module_path}: {e}")
        return None

try:
    # 核心扩展 - 必需的
    from .data_resource_extension import DataResourceExtension, DataResourceTemplateEngine
    from .extensions import setup_template_extensions, create_enhanced_jinja_environment
    from .extensions.advanced_statement_extension import AdvancedStatementExtension
    from ..plugins.plugin_registry import PluginRegistry
    from ..core.smart_matcher import SmartDataMatcher

    # 高级组件 - 可选的
    ConditionalCompiler = safe_import('.advanced.conditional_compilation', 'ConditionalCompiler')
    MacroSystem = safe_import('.advanced.macro_system', 'MacroSystem')

    # 性能组件 - 可选的 (保留ConcurrentPerformanceManager的并发管理功能)
    AdaptiveCacheManager = safe_import('.components.adaptive_cache', 'AdaptiveCacheManager')
    PerformanceOptimizer = safe_import('.components.performance_optimizer', 'PerformanceOptimizer')
    ConcurrentPerformanceManager = safe_import('.components.concurrent_performance_optimizer', 'ConcurrentPerformanceManager')
    IntelligentPerformanceMonitor = safe_import('.components.intelligent_performance_monitor', 'IntelligentPerformanceMonitor')

    # 数据处理组件 - 可选的
    SmartDataFactory = safe_import('.components.smart_data_factory', 'SmartDataFactory')
    get_smart_data_factory = safe_import('.components.smart_data_factory', 'get_smart_data_factory')
    EnhancedDataTypeManager = safe_import('.components.enhanced_data_types', 'EnhancedDataTypeManager')
    DynamicContextEngine = safe_import('.components.dynamic_context_engine', 'DynamicContextEngine')

    # 专业评估器 - 使用更强大的高级版本
    AdvancedExpressionEvaluator = safe_import('.components.specialized_evaluators.advanced_expression_evaluator', 'AdvancedExpressionEvaluator')
    JSONPathEvaluator = safe_import('.components.specialized_evaluators.jsonpath_evaluator', 'JSONPathEvaluator')
    EvaluatorManager = safe_import('.components.specialized_evaluators.evaluator_manager', 'SpecializedEvaluatorManager')

    # 安全和内存管理 - 可选的
    EnhancedSecurityManager = safe_import('.components.enhanced_security_manager', 'EnhancedSecurityManager')
    AdvancedMemoryManager = safe_import('.components.advanced_memory_manager', 'AdvancedMemoryManager')

except ImportError:
    # 绝对导入作为后备
    import sys
    import os
    current_dir = os.path.dirname(__file__)
    parent_dir = os.path.join(current_dir, '..')
    sys.path.insert(0, current_dir)
    sys.path.insert(0, parent_dir)

    # 核心扩展 - 必需的
    from data_resource_extension import DataResourceExtension, DataResourceTemplateEngine
    from extensions import setup_template_extensions, create_enhanced_jinja_environment
    from extensions.advanced_statement_extension import AdvancedStatementExtension
    from plugins.plugin_registry import PluginRegistry
    from core.smart_matcher import SmartDataMatcher

    # 使用安全导入策略导入可选组件
    def safe_import_absolute(module_path, class_name):
        try:
            module = __import__(module_path, fromlist=[class_name])
            return getattr(module, class_name)
        except (ImportError, AttributeError) as e:
            logging.getLogger(__name__).warning(f"无法导入 {class_name} 从 {module_path}: {e}")
            return None

    # 高级组件 - 可选的
    ConditionalCompiler = safe_import_absolute('advanced.conditional_compilation', 'ConditionalCompiler')
    MacroSystem = safe_import_absolute('advanced.macro_system', 'MacroSystem')

    # 性能组件 - 可选的 (保留ConcurrentPerformanceManager的并发管理功能)
    AdaptiveCacheManager = safe_import_absolute('components.adaptive_cache', 'AdaptiveCacheManager')
    PerformanceOptimizer = safe_import_absolute('components.performance_optimizer', 'PerformanceOptimizer')
    ConcurrentPerformanceManager = safe_import_absolute('components.concurrent_performance_optimizer', 'ConcurrentPerformanceManager')
    IntelligentPerformanceMonitor = safe_import_absolute('components.intelligent_performance_monitor', 'IntelligentPerformanceMonitor')

    # 数据处理组件 - 可选的
    SmartDataFactory = safe_import_absolute('components.smart_data_factory', 'SmartDataFactory')
    get_smart_data_factory = safe_import_absolute('components.smart_data_factory', 'get_smart_data_factory')
    EnhancedDataTypeManager = safe_import_absolute('components.enhanced_data_types', 'EnhancedDataTypeManager')
    DynamicContextEngine = safe_import_absolute('components.dynamic_context_engine', 'DynamicContextEngine')

    # 专业评估器 - 使用更强大的高级版本
    AdvancedExpressionEvaluator = safe_import_absolute('components.specialized_evaluators.advanced_expression_evaluator', 'AdvancedExpressionEvaluator')
    JSONPathEvaluator = safe_import_absolute('components.specialized_evaluators.jsonpath_evaluator', 'JSONPathEvaluator')
    EvaluatorManager = safe_import_absolute('components.specialized_evaluators.evaluator_manager', 'SpecializedEvaluatorManager')

    # 安全和内存管理 - 可选的
    EnhancedSecurityManager = safe_import_absolute('components.enhanced_security_manager', 'EnhancedSecurityManager')
    AdvancedMemoryManager = safe_import_absolute('components.advanced_memory_manager', 'AdvancedMemoryManager')


class LambdaExtension(Extension):
    """Lambda表达式扩展"""
    
    tags = {'lambda_filter', 'lambda_map', 'lambda_sort'}
    
    def __init__(self, environment: Environment):
        super().__init__(environment)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 注册lambda相关的全局函数
        self._register_lambda_functions()
    
    def _register_lambda_functions(self):
        """注册lambda相关的全局函数"""
        self.environment.globals.update({
            'lambda_filter': self._lambda_filter,
            'lambda_map': self._lambda_map,
            'lambda_sort': self._lambda_sort,
            'lambda_reduce': self._lambda_reduce,
            'create_lambda': self._create_lambda
        })
    
    def _create_lambda(self, expression: str, param_name: str = 'x'):
        """创建lambda函数"""
        try:
            # 安全的lambda表达式创建
            safe_globals = {
                '__builtins__': {
                    'len': len,
                    'str': str,
                    'int': int,
                    'float': float,
                    'bool': bool,
                    'list': list,
                    'dict': dict,
                    'tuple': tuple,
                    'set': set,
                    'abs': abs,
                    'max': max,
                    'min': min,
                    'sum': sum,
                    'any': any,
                    'all': all,
                    'sorted': sorted,
                    'reversed': reversed,
                    'enumerate': enumerate,
                    'zip': zip,
                    'range': range
                }
            }
            
            # 构建lambda函数代码
            lambda_code = f"lambda {param_name}: {expression}"
            
            # 编译并返回lambda函数
            return eval(lambda_code, safe_globals)
            
        except Exception as e:
            self.logger.error(f"创建lambda函数失败: {e}")
            # 返回恒等函数作为后备
            return lambda x: x
    
    def _lambda_filter(self, iterable, expression: str, param_name: str = 'x'):
        """Lambda过滤器"""
        try:
            lambda_func = self._create_lambda(expression, param_name)
            return [item for item in iterable if lambda_func(item)]
        except Exception as e:
            self.logger.error(f"Lambda过滤失败: {e}")
            return list(iterable)
    
    def _lambda_map(self, iterable, expression: str, param_name: str = 'x'):
        """Lambda映射"""
        try:
            lambda_func = self._create_lambda(expression, param_name)
            return [lambda_func(item) for item in iterable]
        except Exception as e:
            self.logger.error(f"Lambda映射失败: {e}")
            return list(iterable)
    
    def _lambda_sort(self, iterable, expression: str, param_name: str = 'x', reverse: bool = False):
        """Lambda排序"""
        try:
            lambda_func = self._create_lambda(expression, param_name)
            return sorted(iterable, key=lambda_func, reverse=reverse)
        except Exception as e:
            self.logger.error(f"Lambda排序失败: {e}")
            return list(iterable)
    
    def _lambda_reduce(self, iterable, expression: str, initial=None):
        """Lambda归约"""
        try:
            from functools import reduce
            lambda_func = self._create_lambda(expression, 'acc, x')
            if initial is not None:
                return reduce(lambda_func, iterable, initial)
            else:
                return reduce(lambda_func, iterable)
        except Exception as e:
            self.logger.error(f"Lambda归约失败: {e}")
            return initial


class HybridTemplateEngine:
    """
    混合模板引擎 - SmartDataTemplateExtension的主要实现

    完全集成所有模板功能：
    1. 数据资源扩展
    2. 高级语法扩展
    3. Lambda表达式支持
    4. 智能数据对象
    5. 插件系统
    6. 高级组件集成：
       - 条件编译 (ConditionalCompiler)
       - 宏系统 (MacroSystem)
       - 自适应缓存 (AdaptiveCache)
       - 性能优化器 (PerformanceOptimizer)
       - 智能数据工厂 (SmartDataFactory)
       - 专业评估器 (EvaluatorManager)
       - 安全管理器 (EnhancedSecurityManager)
       - 内存管理器 (AdvancedMemoryManager)
    """

    def __init__(self, plugin_registry: PluginRegistry = None, **kwargs):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.plugin_registry = plugin_registry or PluginRegistry()
        self.debug_mode = kwargs.get('debug', False)

        # 延迟导入并设置全局注册表
        _lazy_import()
        set_global_registry(self.plugin_registry)

        # 初始化核心组件
        self._initialize_core_components(**kwargs)

        # 初始化高级组件
        self._initialize_advanced_components(**kwargs)

        # 初始化性能组件
        self._initialize_performance_components(**kwargs)

        # 初始化安全和内存管理
        self._initialize_security_memory_components(**kwargs)

        # 在所有组件初始化完成后，重新注册高级组件
        self._register_advanced_components()

        self.logger.info("混合模板引擎初始化完成")

    def _initialize_core_components(self, **kwargs):
        """初始化核心组件"""
        # 准备Jinja2环境参数
        env_kwargs = {
            'loader': BaseLoader(),
            'autoescape': select_autoescape(['html', 'xml']),
            'extension_config': {
                'enable_advanced_syntax': True,
                'enable_multiline_syntax': True,
                'enable_function_definition': True,
                'enable_class_definition': True,
                'enable_module_import': True,
                'enable_comprehensions': True,
                'safe_mode': True,
                'debug_mode': self.debug_mode
            }
        }

        # 如果提供了自定义语法，应用到环境配置
        if 'custom_syntax' in kwargs:
            custom_syntax = kwargs['custom_syntax']
            if custom_syntax:
                # 应用自定义语法定界符
                if 'block_start_string' in custom_syntax:
                    env_kwargs['block_start_string'] = custom_syntax['block_start_string']
                if 'block_end_string' in custom_syntax:
                    env_kwargs['block_end_string'] = custom_syntax['block_end_string']
                if 'variable_start_string' in custom_syntax:
                    env_kwargs['variable_start_string'] = custom_syntax['variable_start_string']
                if 'variable_end_string' in custom_syntax:
                    env_kwargs['variable_end_string'] = custom_syntax['variable_end_string']
                if 'comment_start_string' in custom_syntax:
                    env_kwargs['comment_start_string'] = custom_syntax['comment_start_string']
                if 'comment_end_string' in custom_syntax:
                    env_kwargs['comment_end_string'] = custom_syntax['comment_end_string']

                self.logger.info(f"应用自定义语法: {custom_syntax}")

        # 创建增强的Jinja2环境
        self.env = create_enhanced_jinja_environment(**env_kwargs)

        # 添加Lambda扩展
        self.env.add_extension(LambdaExtension)

        # 添加数据资源扩展
        self.env.add_extension(DataResourceExtension)

        # 创建智能数据加载器
        _lazy_import()

        # 创建混合数据加载器（保持其他插件功能不变）
        try:
            from core.unified_connectors import UnifiedSmartDataLoader, HybridSmartDataLoader
            original_loader = SmartDataLoader(self.plugin_registry)
            unified_loader = UnifiedSmartDataLoader(self.plugin_registry)

            # 创建混合加载器
            self.smart_data_loader = HybridSmartDataLoader(original_loader, unified_loader)
            self.logger.info("使用混合智能数据加载器")
        except ImportError:
            # 回退到原始加载器
            self.smart_data_loader = SmartDataLoader(self.plugin_registry)
            self.logger.warning("回退到原始智能数据加载器")

        # 注册全局对象
        self._register_global_objects()

    def _initialize_advanced_components(self, **kwargs):
        """初始化高级组件 - 使用安全初始化"""
        initialized_count = 0

        # 条件编译器
        if ConditionalCompiler:
            try:
                self.conditional_compiler = ConditionalCompiler()
                initialized_count += 1
                self.logger.debug("条件编译器初始化成功")
            except Exception as e:
                self.logger.debug(f"条件编译器初始化失败: {e}")

        # 宏系统
        if MacroSystem:
            try:
                self.macro_system = MacroSystem()
                initialized_count += 1
                self.logger.debug("宏系统初始化成功")
            except Exception as e:
                self.logger.debug(f"宏系统初始化失败: {e}")

        # 智能数据工厂
        if get_smart_data_factory:
            try:
                self.smart_data_factory = get_smart_data_factory()
                initialized_count += 1
                self.logger.debug("智能数据工厂初始化成功")
            except Exception as e:
                self.logger.debug(f"智能数据工厂初始化失败: {e}")

        # 增强数据类型管理器 - 使用正确的类名
        if EnhancedDataTypeManager:
            try:
                # 初始化增强数据类型管理器
                self.enhanced_data_type_manager = EnhancedDataTypeManager()
                initialized_count += 1
                self.logger.debug("增强数据类型管理器初始化成功")
            except (ImportError, AttributeError, ModuleNotFoundError) as e:
                self.logger.debug(f"增强数据类型管理器依赖缺失，跳过: {e}")
            except Exception as e:
                self.logger.debug(f"增强数据类型管理器初始化失败: {e}")

        # 动态上下文引擎
        if DynamicContextEngine:
            try:
                self.dynamic_context_engine = DynamicContextEngine()
                initialized_count += 1
                self.logger.debug("动态上下文引擎初始化成功")
            except Exception as e:
                self.logger.debug(f"动态上下文引擎初始化失败: {e}")

        # 专业评估器管理器 - 使用更强大的高级版本
        if EvaluatorManager:
            try:
                self.evaluator_manager = EvaluatorManager()
                initialized_count += 1
                self.logger.debug("专业评估器管理器初始化成功")
            except Exception as e:
                self.logger.debug(f"专业评估器管理器初始化失败: {e}")

        # 高级表达式评估器 - 替换基础版本
        if AdvancedExpressionEvaluator:
            try:
                self.advanced_expression_evaluator = AdvancedExpressionEvaluator(
                    enable_debug=self.debug_mode,
                    enable_security=True
                )
                initialized_count += 1
                self.logger.debug("高级表达式评估器初始化成功")
            except Exception as e:
                self.logger.debug(f"高级表达式评估器初始化失败: {e}")

        # JSONPath评估器 - 替换基础版本
        if JSONPathEvaluator:
            try:
                self.jsonpath_evaluator = JSONPathEvaluator(
                    enable_debug=self.debug_mode
                )
                initialized_count += 1
                self.logger.debug("JSONPath评估器初始化成功")
            except Exception as e:
                self.logger.debug(f"JSONPath评估器初始化失败: {e}")

        self.logger.info(f"高级组件初始化完成: {initialized_count}/8 个组件成功")

    def _initialize_performance_components(self, **kwargs):
        """初始化性能组件 - 使用安全初始化"""
        initialized_count = 0

        # 自适应缓存 - 使用安全配置，禁用外部依赖
        if AdaptiveCacheManager:
            try:
                # 最小化配置，只使用L1内存缓存
                safe_config = {
                    'l1_max_size': kwargs.get('cache_size', 1000),
                    'l1_max_memory': kwargs.get('cache_memory', 100 * 1024 * 1024),
                    'l1_strategy': 'lru',  # 使用简单的LRU策略
                    'enable_l2': False,    # 禁用磁盘缓存
                    'enable_l3': False,    # 禁用Redis分布式缓存
                    'l2_cache_dir': None,  # 不使用磁盘缓存
                    'l3_redis_url': None   # 不使用Redis
                }
                self.adaptive_cache = AdaptiveCacheManager(safe_config)
                initialized_count += 1
                self.logger.debug("自适应缓存初始化成功 (仅L1内存缓存)")
            except (ImportError, ConnectionError, ModuleNotFoundError) as e:
                self.logger.debug(f"自适应缓存外部依赖缺失，跳过: {e}")
            except Exception as e:
                self.logger.debug(f"自适应缓存初始化失败: {e}")

        # 性能优化器
        if PerformanceOptimizer:
            try:
                self.performance_optimizer = PerformanceOptimizer()
                initialized_count += 1
                self.logger.debug("性能优化器初始化成功")
            except Exception as e:
                self.logger.debug(f"性能优化器初始化失败: {e}")

        # 并发性能管理器 - 恢复，专注于并发任务管理
        if ConcurrentPerformanceManager:
            try:
                self.concurrent_performance_manager = ConcurrentPerformanceManager(
                    thread_pool_config={'max_workers': kwargs.get('max_workers', 4)},
                    enable_load_balancing=True
                )
                initialized_count += 1
                self.logger.debug("并发性能管理器初始化成功")
            except Exception as e:
                self.logger.debug(f"并发性能管理器初始化失败: {e}")

        # 智能性能监控器
        if IntelligentPerformanceMonitor:
            try:
                self.performance_monitor = IntelligentPerformanceMonitor()
                initialized_count += 1
                self.logger.debug("智能性能监控器初始化成功")
            except Exception as e:
                self.logger.debug(f"智能性能监控器初始化失败: {e}")

        self.logger.info(f"性能组件初始化完成: {initialized_count}/4 个组件成功")

    def _initialize_security_memory_components(self, **kwargs):
        """初始化安全和内存管理组件 - 使用安全初始化"""
        initialized_count = 0

        # 增强安全管理器
        if EnhancedSecurityManager:
            try:
                self.security_manager = EnhancedSecurityManager()
                initialized_count += 1
                self.logger.debug("增强安全管理器初始化成功")
            except Exception as e:
                self.logger.debug(f"增强安全管理器初始化失败: {e}")

        # 高级内存管理器
        if AdvancedMemoryManager:
            try:
                self.memory_manager = AdvancedMemoryManager()
                initialized_count += 1
                self.logger.debug("高级内存管理器初始化成功")
            except Exception as e:
                self.logger.debug(f"高级内存管理器初始化失败: {e}")

        self.logger.info(f"安全和内存管理组件初始化完成: {initialized_count}/2 个组件成功")
    
    def _register_global_objects(self):
        """注册全局对象和高级组件"""
        # 注册智能数据加载器
        self.env.globals['sd'] = self.smart_data_loader
        self.env.globals['smart_data'] = self.smart_data_loader

        # 注册插件注册表
        self.env.globals['registry'] = self.plugin_registry

        # 注册企业级过滤器
        if register_enterprise_filters:
            try:
                register_enterprise_filters(self.env)
                self.logger.debug("企业级过滤器注册成功")
            except Exception as e:
                self.logger.warning(f"企业级过滤器注册失败: {e}")

        # 注册Python内置函数
        self._register_builtin_functions()

        # 注册便利函数
        self.env.globals.update({
            'async_run': self._async_run,
            'safe_eval': self._safe_eval,
            'get_plugin': self._get_plugin,
            'list_plugins': self._list_plugins,
            'enhanced_eval': self._enhanced_eval,
            'jsonpath_query': self._jsonpath_query,
            'compile_condition': self._compile_condition,
            'create_macro': self._create_macro,
            'smart_call': self._smart_call,  # 新增：智能调用
            'to_async': self._to_async,      # 新增：转换为异步
            'to_sync': self._to_sync         # 新增：转换为同步
        })

        # 注册高级组件（如果初始化成功）
        self._register_advanced_components()

    def _register_builtin_functions(self):
        """自动注册Python内置函数和常用工具函数"""
        import datetime
        import time
        import math
        import re

        # 时间和日期函数
        def now():
            return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        def timestamp():
            return int(time.time())

        def format_date(date_str, format_str='%Y-%m-%d'):
            try:
                if isinstance(date_str, str):
                    dt = datetime.datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                else:
                    dt = date_str
                return dt.strftime(format_str)
            except:
                return date_str

        # 数学和格式化函数
        def format_number(num, decimals=2):
            try:
                return f"{float(num):,.{decimals}f}"
            except:
                return str(num)

        def calculate_percentage(value, total):
            try:
                return round((value / total) * 100, 2) if total > 0 else 0
            except:
                return 0

        def safe_get(data, key, default=None):
            try:
                if isinstance(data, dict):
                    return data.get(key, default)
                elif hasattr(data, key):
                    return getattr(data, key, default)
                else:
                    return default
            except:
                return default

        def deep_get(data, path, default=None):
            try:
                keys = path.split('.')
                current = data
                for key in keys:
                    if isinstance(current, dict):
                        current = current.get(key)
                    elif hasattr(current, key):
                        current = getattr(current, key)
                    else:
                        return default
                    if current is None:
                        return default
                return current
            except:
                return default

        # 字符串处理函数
        def truncate(text, length=50, suffix='...'):
            try:
                text = str(text)
                return text[:length] + suffix if len(text) > length else text
            except:
                return str(text)

        def slugify(text):
            try:
                text = str(text).lower()
                text = re.sub(r'[^\w\s-]', '', text)
                text = re.sub(r'[-\s]+', '-', text)
                return text.strip('-')
            except:
                return str(text)

        # 注册所有内置函数
        builtin_functions = {
            # 时间函数
            'now': now,
            'timestamp': timestamp,
            'format_date': format_date,

            # 数学和格式化
            'format_number': format_number,
            'calculate_percentage': calculate_percentage,
            'safe_get': safe_get,
            'deep_get': deep_get,

            # 字符串处理
            'truncate': truncate,
            'slugify': slugify,

            # Python内置函数
            'hasattr': hasattr,
            'len': len,
            'str': str,
            'int': int,
            'float': float,
            'bool': bool,
            'list': list,
            'dict': dict,
            'set': set,
            'tuple': tuple,
            'enumerate': enumerate,
            'zip': zip,
            'range': range,
            'sorted': sorted,
            'reversed': reversed,
            'sum': sum,
            'min': min,
            'max': max,
            'abs': abs,
            'round': round,

            # 数学函数
            'ceil': math.ceil,
            'floor': math.floor,
            'sqrt': math.sqrt,
            'pow': pow,

            # 类型检查
            'isinstance': isinstance,
            'type': type,
            'callable': callable,
        }

        self.env.globals.update(builtin_functions)

        # 注册增强的过滤器
        self._register_enhanced_filters()

        self.logger.debug(f"已自动注册 {len(builtin_functions)} 个内置函数")

    def _register_enhanced_filters(self):
        """注册增强的过滤器，支持SmartDataObject操作"""

        def enhanced_sort(data, reverse=False, attribute=None, **kwargs):
            """增强的排序过滤器，支持attribute参数"""
            # 处理关键字参数
            if 'attribute' in kwargs:
                attribute = kwargs['attribute']
            if 'reverse' in kwargs:
                reverse = kwargs['reverse']

            # 检查是否是SmartDataObject且有sort方法支持attribute参数
            if hasattr(data, 'sort') and hasattr(data, 'registry'):
                # SmartDataObject的sort方法
                return data.sort(attribute=attribute, reverse=reverse)
            elif isinstance(data, (list, tuple)):
                if attribute is not None:
                    # 按属性排序
                    def get_attr_value(item):
                        if isinstance(item, dict):
                            return item.get(attribute, 0)
                        elif hasattr(item, attribute):
                            return getattr(item, attribute, 0)
                        else:
                            return 0
                    return sorted(data, key=get_attr_value, reverse=reverse)
                else:
                    return sorted(data, reverse=reverse)
            else:
                return data

        def enhanced_selectattr(data, attribute, test='defined', *args):
            """增强的selectattr过滤器，支持SmartDataObject"""
            if hasattr(data, '__iter__'):
                result = []
                for item in data:
                    if isinstance(item, dict):
                        value = item.get(attribute)
                    elif hasattr(item, attribute):
                        value = getattr(item, attribute)
                    else:
                        continue

                    # 应用测试
                    should_include = False
                    if test == 'defined':
                        should_include = value is not None
                    elif test == 'equalto' and len(args) > 0:
                        should_include = value == args[0]
                    elif test == 'in' and len(args) > 0:
                        should_include = value in args[0]
                    elif callable(test):
                        should_include = test(value)
                    elif test is True:
                        should_include = bool(value)
                    elif test is False:
                        should_include = not bool(value)
                    else:
                        # 默认情况：如果没有明确的测试，检查值的真实性
                        should_include = bool(value)

                    if should_include:
                        result.append(item)

                return result
            else:
                return data

        def enhanced_rejectattr(data, attribute, test='defined', *args):
            """增强的rejectattr过滤器，支持SmartDataObject"""
            if hasattr(data, '__iter__'):
                result = []
                for item in data:
                    if isinstance(item, dict):
                        value = item.get(attribute)
                    elif hasattr(item, attribute):
                        value = getattr(item, attribute)
                    else:
                        result.append(item)  # 没有属性的项目被保留
                        continue

                    # 应用测试（反向）
                    should_reject = False
                    if test == 'defined':
                        should_reject = value is not None
                    elif test == 'equalto' and len(args) > 0:
                        should_reject = value == args[0]
                    elif test == 'in' and len(args) > 0:
                        should_reject = value in args[0]
                    elif callable(test):
                        should_reject = test(value)
                    elif test is True:
                        should_reject = bool(value)
                    elif test is False:
                        should_reject = not bool(value)
                    else:
                        # 默认情况：如果没有明确的测试，检查值的真实性
                        should_reject = bool(value)

                    if not should_reject:
                        result.append(item)

                return result
            else:
                return data

        def enhanced_avg_by(data, attribute=None):
            """增强的avg_by过滤器，支持无参数调用"""
            if hasattr(data, '__iter__'):
                items = list(data)
                if not items:
                    return 0

                if attribute is not None:
                    # 按属性计算平均值
                    values = []
                    for item in items:
                        if isinstance(item, dict):
                            value = item.get(attribute, 0)
                        elif hasattr(item, attribute):
                            value = getattr(item, attribute, 0)
                        else:
                            value = 0

                        if isinstance(value, (int, float)):
                            values.append(value)

                    return sum(values) / len(values) if values else 0
                else:
                    # 直接计算数字列表的平均值
                    numeric_values = [x for x in items if isinstance(x, (int, float))]
                    return sum(numeric_values) / len(numeric_values) if numeric_values else 0
            else:
                return 0

        # 注册增强过滤器，覆盖内置过滤器
        enhanced_filters = {
            'sort': enhanced_sort,
            'selectattr': enhanced_selectattr,
            'rejectattr': enhanced_rejectattr,
            'avg_by': enhanced_avg_by,
        }

        # 强制覆盖内置过滤器
        for name, filter_func in enhanced_filters.items():
            self.env.filters[name] = filter_func

        self.logger.debug(f"已注册 {len(enhanced_filters)} 个增强过滤器（覆盖内置）")

    def _register_advanced_components(self):
        """注册高级组件到模板环境"""
        # 注册条件编译器
        if hasattr(self, 'conditional_compiler'):
            self.env.globals['conditional'] = self.conditional_compiler

        # 注册宏系统
        if hasattr(self, 'macro_system'):
            self.env.globals['macro'] = self.macro_system

        # 注册智能数据工厂
        if hasattr(self, 'smart_data_factory'):
            self.env.globals['factory'] = self.smart_data_factory

        # 注册增强数据类型管理器
        if hasattr(self, 'enhanced_data_type_manager'):
            self.env.globals['enhanced_types'] = self.enhanced_data_type_manager
            if self.debug_mode:
                self.logger.debug("增强数据类型管理器已注册到全局变量")
        elif self.debug_mode:
            self.logger.debug("增强数据类型管理器未找到，跳过注册")

        # 注册动态上下文引擎
        if hasattr(self, 'dynamic_context_engine'):
            self.env.globals['context_engine'] = self.dynamic_context_engine

        # 注册专业评估器管理器
        if hasattr(self, 'evaluator_manager'):
            self.env.globals['evaluator'] = self.evaluator_manager

        # 注册高级表达式评估器
        if hasattr(self, 'advanced_expression_evaluator'):
            self.env.globals['advanced_eval'] = self.advanced_expression_evaluator

        # 注册JSONPath评估器
        if hasattr(self, 'jsonpath_evaluator'):
            self.env.globals['jsonpath'] = self.jsonpath_evaluator

        # 注册性能监控器
        if hasattr(self, 'performance_monitor'):
            self.env.globals['perf_monitor'] = self.performance_monitor

        # 注册缓存
        if hasattr(self, 'adaptive_cache'):
            self.env.globals['cache'] = self.adaptive_cache

        # 注册并发性能管理器
        if hasattr(self, 'concurrent_performance_manager'):
            self.env.globals['concurrent_manager'] = self.concurrent_performance_manager
    
    def _async_run(self, coro):
        """在模板中运行异步函数 - 增强版"""
        try:
            # 导入智能协调器
            from core.async_sync_coordinator import global_coordinator
            import inspect

            # 如果传入的是函数而不是协程，先检查是否需要调用
            if callable(coro) and not inspect.iscoroutine(coro):
                if inspect.iscoroutinefunction(coro):
                    # 异步函数，需要调用
                    coro = coro()
                else:
                    # 同步函数，使用智能协调器
                    return global_coordinator.sync_smart_call(coro)

            # 处理协程
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，在新线程中运行
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, coro)
                    return future.result(timeout=30)
            else:
                return loop.run_until_complete(coro)
        except Exception as e:
            self.logger.error(f"异步执行失败: {e}")
            return None

    def _smart_call(self, func, *args, **kwargs):
        """智能调用函数 - 自动处理异步/同步转换"""
        try:
            from core.async_sync_coordinator import global_coordinator
            return global_coordinator.smart_call(func, *args, **kwargs)
        except Exception as e:
            self.logger.error(f"智能调用失败: {e}")
            return None

    def _to_async(self, func):
        """将同步函数转换为异步函数"""
        try:
            from core.async_sync_coordinator import global_coordinator
            return global_coordinator.create_async_wrapper(func)
        except Exception as e:
            self.logger.error(f"转换为异步失败: {e}")
            return func

    def _to_sync(self, func):
        """将异步函数转换为同步函数"""
        try:
            from core.async_sync_coordinator import global_coordinator
            return global_coordinator.create_sync_wrapper(func)
        except Exception as e:
            self.logger.error(f"转换为同步失败: {e}")
            return func
    
    def _safe_eval(self, expression: str, context: Dict[str, Any] = None):
        """安全的表达式求值"""
        try:
            safe_context = context or {}
            safe_globals = {
                '__builtins__': {
                    'len': len, 'str': str, 'int': int, 'float': float,
                    'bool': bool, 'list': list, 'dict': dict, 'tuple': tuple,
                    'abs': abs, 'max': max, 'min': min, 'sum': sum
                }
            }
            return eval(expression, safe_globals, safe_context)
        except Exception as e:
            self.logger.error(f"表达式求值失败: {e}")
            return None
    
    def _get_plugin(self, plugin_id: str):
        """获取插件"""
        return self.plugin_registry.get_processor(plugin_id)
    
    def _list_plugins(self):
        """列出所有插件"""
        return list(self.plugin_registry.processors.keys())

    def _enhanced_eval(self, expression: str, context: Dict[str, Any] = None):
        """增强的表达式求值，使用高级专业评估器"""
        try:
            # 优先使用高级表达式评估器
            if hasattr(self, 'advanced_expression_evaluator'):
                return self.advanced_expression_evaluator.evaluate(expression, context or {})
            # 回退到评估器管理器
            elif hasattr(self, 'evaluator_manager'):
                return self.evaluator_manager.evaluate(expression, context or {})
            else:
                return self._safe_eval(expression, context)
        except Exception as e:
            self.logger.error(f"增强表达式求值失败: {e}")
            return None

    def _jsonpath_query(self, data: Any, path: str):
        """JSONPath查询，使用高级JSONPath评估器"""
        try:
            # 优先使用高级JSONPath评估器
            if hasattr(self, 'jsonpath_evaluator'):
                return self.jsonpath_evaluator.evaluate(path, {'data': data})
            # 回退到评估器管理器
            elif hasattr(self, 'evaluator_manager'):
                return self.evaluator_manager.evaluate(path, {'data': data})
            else:
                self.logger.warning("JSONPath评估器未初始化")
                return None
        except Exception as e:
            self.logger.error(f"JSONPath查询失败: {e}")
            return None

    def _compile_condition(self, condition: str, context: Dict[str, Any] = None):
        """编译条件表达式"""
        try:
            if hasattr(self, 'conditional_compiler'):
                return self.conditional_compiler.compile(condition, context or {})
            else:
                self.logger.warning("条件编译器未初始化")
                return False
        except Exception as e:
            self.logger.error(f"条件编译失败: {e}")
            return False

    def _create_macro(self, name: str, template: str, **kwargs):
        """创建宏"""
        try:
            if hasattr(self, 'macro_system'):
                return self.macro_system.create_macro(name, template, **kwargs)
            else:
                self.logger.warning("宏系统未初始化")
                return None
        except Exception as e:
            self.logger.error(f"宏创建失败: {e}")
            return None
    
    def render_template(self, template_string: str, context: Dict[str, Any] = None) -> str:
        """渲染模板 - 同步版本"""
        try:
            template = self.env.from_string(template_string)
            return template.render(context or {})
        except Exception as e:
            self.logger.error(f"模板渲染失败: {e}")
            raise

    async def render_template_async(self, template_string: str, context: Dict[str, Any] = None) -> str:
        """渲染模板 - 异步版本"""
        try:
            # 导入智能协调器
            from core.async_sync_coordinator import global_coordinator

            # 在异步上下文中渲染模板
            def _render():
                template = self.env.from_string(template_string)
                return template.render(context or {})

            # 使用智能协调器在异步上下文中执行
            return await global_coordinator.async_smart_call(_render)
        except Exception as e:
            self.logger.error(f"异步模板渲染失败: {e}")
            raise
    
    def render_template_file(self, template_path: str, context: Dict[str, Any] = None) -> str:
        """渲染模板文件"""
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                template_string = f.read()
            return self.render_template(template_string, context)
        except Exception as e:
            self.logger.error(f"模板文件渲染失败: {e}")
            raise
    
    def add_global(self, name: str, value: Any):
        """添加全局变量"""
        self.env.globals[name] = value
    
    def add_filter(self, name: str, func: Callable):
        """添加过滤器"""
        self.env.filters[name] = func
    
    def add_test(self, name: str, func: Callable):
        """添加测试函数"""
        self.env.tests[name] = func
    
    def get_stats(self) -> Dict[str, Any]:
        """获取完整的统计信息"""
        stats = {
            # 基础统计
            'extensions': len(self.env.extensions),
            'globals': len(self.env.globals),
            'filters': len(self.env.filters),
            'tests': len(self.env.tests),
            'plugins': len(self.plugin_registry.processors) if self.plugin_registry else 0,

            # 高级组件状态
            'advanced_components': {
                'conditional_compiler': hasattr(self, 'conditional_compiler'),
                'macro_system': hasattr(self, 'macro_system'),
                'smart_data_factory': hasattr(self, 'smart_data_factory'),
                'enhanced_data_types': hasattr(self, 'enhanced_data_types'),
                'dynamic_context_engine': hasattr(self, 'dynamic_context_engine'),
                'evaluator_manager': hasattr(self, 'evaluator_manager'),
                'expression_evaluator': hasattr(self, 'expression_evaluator'),
                'jsonpath_resolver': hasattr(self, 'jsonpath_resolver')
            },

            # 性能组件状态
            'performance_components': {
                'adaptive_cache': hasattr(self, 'adaptive_cache'),
                'performance_optimizer': hasattr(self, 'performance_optimizer'),
                'concurrent_optimizer': hasattr(self, 'concurrent_optimizer'),
                'performance_monitor': hasattr(self, 'performance_monitor')
            },

            # 安全和内存组件状态
            'security_memory_components': {
                'security_manager': hasattr(self, 'security_manager'),
                'memory_manager': hasattr(self, 'memory_manager')
            }
        }

        # 添加缓存统计（如果可用）
        if hasattr(self, 'adaptive_cache'):
            try:
                cache_stats = self.adaptive_cache.get_stats()
                stats['cache_stats'] = cache_stats
            except:
                pass

        # 添加性能监控统计（如果可用）
        if hasattr(self, 'performance_monitor'):
            try:
                perf_stats = self.performance_monitor.get_stats()
                stats['performance_stats'] = perf_stats
            except:
                pass

        return stats

    def get_component_info(self) -> Dict[str, Any]:
        """获取组件详细信息"""
        info = {
            'core_components': [
                'DataResourceExtension',
                'AdvancedStatementExtension',
                'LambdaExtension',
                'SmartDataLoader'
            ],
            'advanced_components': [],
            'performance_components': [],
            'security_memory_components': []
        }

        # 检查高级组件
        if hasattr(self, 'conditional_compiler'):
            info['advanced_components'].append('ConditionalCompiler')
        if hasattr(self, 'macro_system'):
            info['advanced_components'].append('MacroSystem')
        if hasattr(self, 'smart_data_factory'):
            info['advanced_components'].append('SmartDataFactory')
        if hasattr(self, 'enhanced_data_type_manager'):
            info['advanced_components'].append('EnhancedDataTypeManager')
        if hasattr(self, 'dynamic_context_engine'):
            info['advanced_components'].append('DynamicContextEngine')
        # 高级专业评估器
        if hasattr(self, 'evaluator_manager'):
            info['advanced_components'].append('SpecializedEvaluatorManager')
        if hasattr(self, 'advanced_expression_evaluator'):
            info['advanced_components'].append('AdvancedExpressionEvaluator')
        if hasattr(self, 'jsonpath_evaluator'):
            info['advanced_components'].append('JSONPathEvaluator')

        # 检查性能组件
        if hasattr(self, 'adaptive_cache'):
            info['performance_components'].append('AdaptiveCache')
        if hasattr(self, 'performance_optimizer'):
            info['performance_components'].append('PerformanceOptimizer')
        if hasattr(self, 'concurrent_performance_manager'):
            info['performance_components'].append('ConcurrentPerformanceManager')
        if hasattr(self, 'performance_monitor'):
            info['performance_components'].append('IntelligentPerformanceMonitor')

        # 检查安全和内存组件
        if hasattr(self, 'security_manager'):
            info['security_memory_components'].append('EnhancedSecurityManager')
        if hasattr(self, 'memory_manager'):
            info['security_memory_components'].append('AdvancedMemoryManager')

        return info

    # ==================== 性能优化功能 ====================

    def __init_performance_tracking(self):
        """初始化性能跟踪"""
        if not hasattr(self, '_performance_data'):
            self._performance_data = {
                'render_count': 0,
                'total_render_time': 0.0,
                'avg_render_time': 0.0,
                'cache_hits': 0,
                'cache_misses': 0,
                'template_cache': {},
                'compiled_templates': {},
                'optimization_stats': defaultdict(int)
            }
            self._performance_lock = threading.Lock()

    def precompile_template(self, template_string: str, template_id: str = None) -> str:
        """
        预编译模板以提高性能

        Args:
            template_string: 模板字符串
            template_id: 模板ID，用于缓存，如果为None则自动生成

        Returns:
            str: 模板ID，用于后续渲染
        """
        self.__init_performance_tracking()

        if template_id is None:
            template_id = hashlib.md5(template_string.encode()).hexdigest()

        try:
            start_time = time.time()

            # 编译模板
            compiled_template = self.env.from_string(template_string)

            # 缓存编译后的模板
            with self._performance_lock:
                self._performance_data['compiled_templates'][template_id] = {
                    'template': compiled_template,
                    'source': template_string,
                    'compile_time': time.time() - start_time,
                    'created_at': time.time(),
                    'render_count': 0,
                    'total_render_time': 0.0
                }
                self._performance_data['optimization_stats']['precompiled'] += 1

            self.logger.debug(f"模板预编译完成: {template_id}")
            return template_id

        except Exception as e:
            self.logger.error(f"模板预编译失败: {e}")
            raise

    def render_precompiled_template(self, template_id: str, context: Dict[str, Any] = None) -> str:
        """
        渲染预编译的模板

        Args:
            template_id: 预编译模板的ID
            context: 渲染上下文

        Returns:
            str: 渲染结果
        """
        self.__init_performance_tracking()

        with self._performance_lock:
            if template_id not in self._performance_data['compiled_templates']:
                raise ValueError(f"预编译模板不存在: {template_id}")

            template_info = self._performance_data['compiled_templates'][template_id]
            compiled_template = template_info['template']

        try:
            start_time = time.time()
            result = compiled_template.render(context or {})
            render_time = time.time() - start_time

            # 更新性能统计
            with self._performance_lock:
                template_info['render_count'] += 1
                template_info['total_render_time'] += render_time
                self._performance_data['render_count'] += 1
                self._performance_data['total_render_time'] += render_time
                self._performance_data['avg_render_time'] = (
                    self._performance_data['total_render_time'] /
                    self._performance_data['render_count']
                )
                self._performance_data['optimization_stats']['precompiled_renders'] += 1

            return result

        except Exception as e:
            self.logger.error(f"预编译模板渲染失败: {e}")
            raise

    def optimize_template(self, template_string: str) -> str:
        """
        智能优化模板

        Args:
            template_string: 原始模板字符串

        Returns:
            str: 优化后的模板字符串
        """
        self.__init_performance_tracking()

        try:
            optimized = template_string

            # 1. 移除多余的空白字符
            import re
            optimized = re.sub(r'\n\s*\n', '\n', optimized)  # 移除多余空行
            optimized = re.sub(r'[ \t]+', ' ', optimized)     # 压缩空格

            # 2. 优化变量访问
            optimized = re.sub(r'\{\{\s+', '{{', optimized)   # 移除变量开始的空格
            optimized = re.sub(r'\s+\}\}', '}}', optimized)   # 移除变量结束的空格

            # 3. 优化控制结构
            optimized = re.sub(r'\{\%\s+', '{%', optimized)   # 移除控制开始的空格
            optimized = re.sub(r'\s+\%\}', '%}', optimized)   # 移除控制结束的空格

            # 4. 优化注释
            optimized = re.sub(r'\{#.*?#\}', '', optimized, flags=re.DOTALL)  # 移除注释

            with self._performance_lock:
                self._performance_data['optimization_stats']['optimized'] += 1

            self.logger.debug("模板优化完成")
            return optimized

        except Exception as e:
            self.logger.error(f"模板优化失败: {e}")
            return template_string

    def get_performance_data(self) -> Dict[str, Any]:
        """
        获取性能数据

        Returns:
            Dict[str, Any]: 详细的性能统计数据
        """
        self.__init_performance_tracking()

        with self._performance_lock:
            performance_data = self._performance_data.copy()

        # 计算缓存命中率
        total_cache_requests = performance_data['cache_hits'] + performance_data['cache_misses']
        cache_hit_rate = (
            performance_data['cache_hits'] / total_cache_requests
            if total_cache_requests > 0 else 0.0
        )

        # 获取预编译模板统计
        compiled_stats = {}
        for template_id, info in performance_data['compiled_templates'].items():
            compiled_stats[template_id] = {
                'compile_time': info['compile_time'],
                'render_count': info['render_count'],
                'total_render_time': info['total_render_time'],
                'avg_render_time': (
                    info['total_render_time'] / info['render_count']
                    if info['render_count'] > 0 else 0.0
                ),
                'created_at': info['created_at']
            }

        return {
            'render_statistics': {
                'total_renders': performance_data['render_count'],
                'total_render_time': performance_data['total_render_time'],
                'average_render_time': performance_data['avg_render_time'],
                'renders_per_second': (
                    performance_data['render_count'] / performance_data['total_render_time']
                    if performance_data['total_render_time'] > 0 else 0.0
                )
            },
            'cache_statistics': {
                'cache_hits': performance_data['cache_hits'],
                'cache_misses': performance_data['cache_misses'],
                'cache_hit_rate': cache_hit_rate,
                'cached_templates': len(performance_data['template_cache'])
            },
            'compilation_statistics': {
                'precompiled_templates': len(performance_data['compiled_templates']),
                'compiled_template_details': compiled_stats
            },
            'optimization_statistics': dict(performance_data['optimization_stats']),
            'memory_usage': {
                'template_cache_size': len(performance_data['template_cache']),
                'compiled_templates_size': len(performance_data['compiled_templates'])
            }
        }

    def clear_performance_data(self):
        """清除性能数据"""
        self.__init_performance_tracking()

        with self._performance_lock:
            self._performance_data = {
                'render_count': 0,
                'total_render_time': 0.0,
                'avg_render_time': 0.0,
                'cache_hits': 0,
                'cache_misses': 0,
                'template_cache': {},
                'compiled_templates': {},
                'optimization_stats': defaultdict(int)
            }

        self.logger.info("性能数据已清除")

    def export_performance_report(self, format: str = 'dict') -> Union[Dict[str, Any], str]:
        """
        导出性能报告

        Args:
            format: 导出格式，支持 'dict', 'json'

        Returns:
            Union[Dict[str, Any], str]: 性能报告
        """
        performance_data = self.get_performance_data()

        if format == 'json':
            import json
            return json.dumps(performance_data, indent=2, ensure_ascii=False)
        else:
            return performance_data


# 向后兼容的别名
SmartDataTemplateExtension = HybridTemplateEngine


def create_template_engine(
    plugin_registry: PluginRegistry = None,
    auto_discover_plugins: bool = True,
    custom_syntax: dict = None,
    **kwargs
) -> HybridTemplateEngine:
    """
    创建模板引擎的便利函数

    Args:
        plugin_registry: 插件注册表，如果为None则创建新的
        auto_discover_plugins: 是否自动发现和注册插件，默认为True
        custom_syntax: 自定义语法配置，支持修改Jinja2的定界符
        **kwargs: 其他参数传递给HybridTemplateEngine

    Returns:
        HybridTemplateEngine: 配置好的模板引擎实例

    自定义语法示例:
        custom_syntax = {
            'block_start_string': '<%',      # 默认: '{%'
            'block_end_string': '%>',        # 默认: '%}'
            'variable_start_string': '${',   # 默认: '{{'
            'variable_end_string': '}',      # 默认: '}}'
            'comment_start_string': '<#',    # 默认: '{#'
            'comment_end_string': '#>',      # 默认: '#}'
        }
    """
    # 如果没有提供插件注册表，创建一个新的
    if plugin_registry is None:
        plugin_registry = PluginRegistry()

    # 如果启用自动发现，扫描并注册所有插件
    if auto_discover_plugins:
        try:
            discovered_plugins = plugin_registry.auto_discover_plugins()
            if discovered_plugins:
                print(f"✅ 自动发现并注册了 {len(discovered_plugins)} 个插件:")
                for plugin_id in discovered_plugins:
                    print(f"  - {plugin_id}")
            else:
                print("⚠️ 未发现任何插件")
        except Exception as e:
            print(f"⚠️ 自动插件发现失败: {e}")

    # 如果提供了自定义语法，添加到kwargs中
    if custom_syntax:
        kwargs['custom_syntax'] = custom_syntax

    return HybridTemplateEngine(plugin_registry, **kwargs)


__all__ = [
    'HybridTemplateEngine',
    'SmartDataTemplateExtension',
    'LambdaExtension',
    'create_template_engine'
]
