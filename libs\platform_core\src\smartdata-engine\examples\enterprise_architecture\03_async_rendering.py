#!/usr/bin/env python3
"""
企业级模板引擎新架构 - 异步渲染示例

展示异步架构的高性能特性
包括并发渲染、异步数据源、性能对比等
"""

import sys
import os
import asyncio
import time
import tempfile
import json
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.enterprise_template_integration import EnterpriseTemplateIntegration
from template.enterprise_template_factory import EnterpriseTemplateFactory, EnterpriseTemplateConfig

async def async_rendering_example():
    """异步渲染完整示例"""
    print("=== 企业级模板引擎异步渲染示例 ===")
    
    # 🚀 示例1：基础异步渲染
    print("\n🚀 示例1：基础异步渲染")
    
    # 创建异步模板集成器
    integration = EnterpriseTemplateIntegration(
        enable_async=True,
        enable_legacy_support=False,
        enable_debug=False
    )
    
    print("✅ 异步模板集成器创建完成")
    
    # 基础异步模板渲染
    template = """
异步模板引擎示例
===============
渲染时间: {{ render_time }}
异步支持: {{ async_enabled }}
并发能力: 高性能
处理模式: 非阻塞
    """.strip()
    
    context = {
        'render_time': time.strftime('%Y-%m-%d %H:%M:%S'),
        'async_enabled': True
    }
    
    result = await integration.render_template_async(template, context)
    print("异步渲染结果:")
    print(result)
    
    # ⚡ 示例2：性能对比测试
    print("\n⚡ 示例2：同步vs异步性能对比")
    
    # 创建同步引擎
    sync_config = EnterpriseTemplateConfig(enable_async=False, enable_debug=False)
    sync_engine = EnterpriseTemplateFactory.create_engine(sync_config)
    
    # 创建异步引擎
    async_config = EnterpriseTemplateConfig(enable_async=True, enable_debug=False)
    async_engine = EnterpriseTemplateFactory.create_engine(async_config)
    
    # 测试模板
    test_template = """
性能测试模板 #{{ test_id }}
===================
数据处理: {{ data | length }}条记录
计算结果: {{ data | sum }}
平均值: {{ (data | sum) / (data | length) }}
最大值: {{ data | max }}
最小值: {{ data | min }}
    """.strip()
    
    test_data = list(range(1, 101))  # 1-100的数字
    test_context = {'test_id': 1, 'data': test_data}
    
    # 同步渲染性能测试
    sync_start = time.time()
    for i in range(10):
        test_context['test_id'] = i + 1
        sync_result = sync_engine.render(test_template, test_context)
    sync_time = time.time() - sync_start
    
    # 异步渲染性能测试
    async def async_render_test():
        results = []
        for i in range(10):
            test_context['test_id'] = i + 1
            result = await async_engine.render_async(test_template, test_context)
            results.append(result)
        return results
    
    async_start = time.time()
    async_results = await async_render_test()
    async_time = time.time() - async_start
    
    print(f"同步渲染时间: {sync_time:.4f}秒")
    print(f"异步渲染时间: {async_time:.4f}秒")
    print(f"性能提升: {(sync_time / async_time):.2f}倍")
    
    sync_engine.cleanup()
    async_engine.cleanup()
    
    # 🔄 示例3：并发渲染
    print("\n🔄 示例3：并发渲染测试")
    
    # 创建高性能异步引擎
    high_perf_engine = EnterpriseTemplateFactory.create_high_performance_engine()
    
    # 并发渲染任务
    async def render_task(task_id, data):
        template = f"""
并发任务 #{task_id}
================
任务ID: {task_id}
数据量: {{{{ data | length }}}}条
处理时间: {{{{ process_time }}}}
状态: 完成
        """.strip()
        
        context = {
            'data': data,
            'process_time': time.strftime('%H:%M:%S.%f')[:-3]
        }
        
        return await high_perf_engine.render_async(template, context)
    
    # 创建并发任务
    tasks = []
    for i in range(5):
        task_data = list(range(i * 10, (i + 1) * 10))
        task = render_task(i + 1, task_data)
        tasks.append(task)
    
    # 并发执行
    concurrent_start = time.time()
    results = await asyncio.gather(*tasks)
    concurrent_time = time.time() - concurrent_start
    
    print(f"并发渲染完成，耗时: {concurrent_time:.4f}秒")
    print(f"任务数量: {len(results)}个")
    print("第一个任务结果:")
    print(results[0])
    
    high_perf_engine.cleanup()
    
    # 📊 示例4：异步数据源处理
    print("\n📊 示例4：异步数据源处理")
    
    # 创建异步作用域
    scope = integration.create_template_scope('async_data_demo', is_async=True)
    
    # 模拟异步数据加载
    async def load_async_data():
        # 模拟网络延迟
        await asyncio.sleep(0.1)
        return [
            {'product': '产品A', 'sales': 150000, 'region': '华东'},
            {'product': '产品B', 'sales': 120000, 'region': '华北'},
            {'product': '产品C', 'sales': 180000, 'region': '华南'}
        ]
    
    # 加载异步数据
    async_data = await load_async_data()
    
    # 注册异步数据源
    data_proxy = integration.register_data_source(scope, 'sales_data', async_data)
    
    template = """
异步数据源处理示例
=================
数据加载: 异步完成
数据量: {{ sales_data | length }}条记录

销售数据:
{%- for item in sales_data %}
{{ item.product }}: ¥{{ item.sales | format_number }} ({{ item.region }})
{%- endfor %}

统计信息:
- 总销售额: ¥{{ (sales_data | sum(attribute='sales')) | format_number }}
- 平均销售额: ¥{{ ((sales_data | sum(attribute='sales')) / (sales_data | length)) | format_number }}
- 最佳产品: {{ (sales_data | sort(attribute='sales', reverse=true) | first).product }}
    """.strip()
    
    # 自定义格式化函数
    def format_number(value):
        return f"{value:,}"
    
    context = {
        'sales_data': async_data,
        'format_number': format_number
    }
    
    result = await integration.render_template_async(template, context)
    print("异步数据源渲染结果:")
    print(result)
    
    integration.cleanup_template_scope('async_data_demo')
    
    # 🎯 示例5：异步批量处理
    print("\n🎯 示例5：异步批量处理")
    
    # 批量数据
    batch_data = [
        {'id': 1, 'name': '报表A', 'type': '财务报表'},
        {'id': 2, 'name': '报表B', 'type': '销售报表'},
        {'id': 3, 'name': '报表C', 'type': '库存报表'},
        {'id': 4, 'name': '报表D', 'type': '人事报表'}
    ]
    
    # 批量渲染函数
    async def batch_render(items):
        template = """
{{ report.type }} - {{ report.name }}
{{ "=" * (report.type | length + report.name | length + 3) }}
报表ID: {{ report.id }}
生成时间: {{ generation_time }}
状态: 已生成
        """.strip()
        
        tasks = []
        for item in items:
            context = {
                'report': item,
                'generation_time': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            task = integration.render_template_async(template, context)
            tasks.append(task)
        
        return await asyncio.gather(*tasks)
    
    # 执行批量渲染
    batch_start = time.time()
    batch_results = await batch_render(batch_data)
    batch_time = time.time() - batch_start
    
    print(f"批量渲染完成，耗时: {batch_time:.4f}秒")
    print(f"处理报表: {len(batch_results)}个")
    print("\n第一个报表结果:")
    print(batch_results[0])
    
    # 获取性能统计
    stats = integration.get_performance_stats()
    print(f"\n📈 异步性能统计:")
    print(f"  注册的适配器: {stats['registered_adapters']}种")
    print(f"  活跃作用域: {stats['active_scopes']}个")
    print(f"  异步支持: {stats['async_enabled']}")
    
    print("\n🎉 异步渲染示例完成！")
    print("\n💡 异步架构优势:")
    print("1. ✅ 高并发处理：支持大量并发渲染任务")
    print("2. ✅ 非阻塞IO：异步数据源加载不阻塞主线程")
    print("3. ✅ 性能提升：相比同步模式有显著性能提升")
    print("4. ✅ 资源优化：更好的CPU和内存利用率")
    print("5. ✅ 可扩展性：轻松处理大规模数据处理任务")
    
    print("\n📖 下一步学习:")
    print("- 04_enterprise_features.py - 探索企业级功能")
    print("- financial_reporting.py - 实战异步财务报表")

def main():
    """主函数"""
    asyncio.run(async_rendering_example())

if __name__ == "__main__":
    main()
