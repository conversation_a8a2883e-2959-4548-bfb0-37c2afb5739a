#!/usr/bin/env python3
"""
SmartData模板引擎企业级过滤器示例

展示所有自动加载的企业级过滤器，无需手工配置
基于验证的自动化功能和100%测试通过率
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.template_ext import create_template_engine

def enterprise_filters_example():
    """企业级过滤器完整示例"""
    print("=== SmartData模板引擎企业级过滤器示例 ===")
    
    # 创建模板引擎 - 所有企业级过滤器自动可用
    engine = create_template_engine()
    print("✅ 模板引擎创建完成 - 所有企业级过滤器自动加载")
    
    # 1. 数据处理过滤器
    print("\n🔄 1. 数据处理过滤器")
    template_data_processing = """
{%- set nested_data = [
    [1, 2, 3],
    [4, 5, 6],
    [7, 8, 9]
] -%}
{%- set numbers = [10, 20, 30, 40, 50] -%}

数据处理过滤器演示:
================
扁平化处理:
- 原始嵌套数据: {{ nested_data }}
- 扁平化结果: {{ nested_data | flatten }}

乘法运算:
- 原始数据: {{ numbers }}
- 乘以2: {{ numbers | multiply(2) }}
- 乘以0.5: {{ numbers | multiply(0.5) }}

数据分组:
{%- set products = [
    {'name': '笔记本', 'category': '电子产品', 'price': 5000},
    {'name': '鼠标', 'category': '配件', 'price': 100},
    {'name': '键盘', 'category': '配件', 'price': 200},
    {'name': '显示器', 'category': '电子产品', 'price': 2000}
] -%}
{%- set grouped = products | group_by('category') -%}
按类别分组:
{%- for category, items in grouped.items() %}
{{ category }}:
  {%- for item in items %}
  - {{ item.name }}: ¥{{ item.price }}
  {%- endfor %}
{%- endfor %}
    """.strip()
    
    result = engine.render_template(template_data_processing)
    print("渲染结果:")
    print(result)
    
    # 2. 安全运算过滤器
    print("\n🛡️ 2. 安全运算过滤器")
    template_safe_operations = """
{%- set test_numbers = [100, 50, 0, 25] -%}

安全运算过滤器演示:
================
安全除法:
{%- for num in test_numbers %}
- 100 ÷ {{ num }} = {{ 100 | safe_divide(num, '除零保护') }}
{%- endfor %}

安全除法在业务场景中的应用:
{%- set sales_data = [
    {'month': '1月', 'sales': 120000, 'target': 100000},
    {'month': '2月', 'sales': 135000, 'target': 0},
    {'month': '3月', 'sales': 98000, 'target': 105000}
] -%}
{%- for record in sales_data %}
{{ record.month }}:
  销售额: ¥{{ format_number(record.sales) }}
  目标: ¥{{ format_number(record.target) }}
  完成率: {{ record.sales | safe_divide(record.target, 0) | multiply(100) }}%
{%- endfor %}
    """.strip()
    
    result = engine.render_template(template_safe_operations)
    print("渲染结果:")
    print(result)
    
    # 3. 统计分析过滤器
    print("\n📊 3. 统计分析过滤器")
    template_statistics = """
{%- set employees = [
    {'name': '张三', 'department': '技术部', 'salary': 8000, 'performance': 95},
    {'name': '李四', 'department': '销售部', 'salary': 12000, 'performance': 88},
    {'name': '王五', 'department': '技术部', 'salary': 6000, 'performance': 92},
    {'name': '赵六', 'department': '销售部', 'salary': 15000, 'performance': 85},
    {'name': '钱七', 'department': '技术部', 'salary': 10000, 'performance': 90}
] -%}

统计分析过滤器演示:
================
按属性统计:
- 薪资总和: ¥{{ format_number(employees | sum_by('salary')) }}
- 平均薪资: ¥{{ format_number(employees | avg_by('salary')) }}
- 最高薪资: ¥{{ format_number(employees | max_by('salary')) }}
- 最低薪资: ¥{{ format_number(employees | min_by('salary')) }}
- 员工总数: {{ employees | count_by('department') }}

按部门统计:
{%- set tech_employees = employees | selectattr('department', 'equalto', '技术部') | list -%}
{%- set sales_employees = employees | selectattr('department', 'equalto', '销售部') | list -%}
技术部:
  人数: {{ tech_employees | length }}
  平均薪资: ¥{{ format_number(tech_employees | avg_by('salary')) }}
  平均绩效: {{ tech_employees | avg_by('performance') }}

销售部:
  人数: {{ sales_employees | length }}
  平均薪资: ¥{{ format_number(sales_employees | avg_by('salary')) }}
  平均绩效: {{ sales_employees | avg_by('performance') }}
    """.strip()
    
    result = engine.render_template(template_statistics)
    print("渲染结果:")
    print(result)
    
    # 4. 数据整理过滤器
    print("\n🔧 4. 数据整理过滤器")
    template_data_cleaning = """
{%- set raw_data = [1, 2, 2, 3, 1, 4, 3, 5, 2, 4] -%}
{%- set mixed_data = [
    {'id': 3, 'name': 'Charlie', 'score': 85},
    {'id': 1, 'name': 'Alice', 'score': 95},
    {'id': 2, 'name': 'Bob', 'score': 90},
    {'id': 4, 'name': 'David', 'score': 80}
] -%}

数据整理过滤器演示:
================
去重处理:
- 原始数据: {{ raw_data }}
- 去重结果: {{ raw_data | unique }}

排序处理:
- 按ID排序: {{ mixed_data | sort_by('id') | map(attribute='name') | list }}
- 按分数排序: {{ mixed_data | sort_by('score', reverse=true) | map(attribute='name') | list }}

分块处理:
{%- set large_list = range(1, 21) | list -%}
- 原始数据: {{ large_list }}
- 分成5块:
{%- for chunk in large_list | chunk(5) %}
  块{{ loop.index }}: {{ chunk }}
{%- endfor %}

百分比计算:
{%- set scores = [85, 92, 78, 96, 88] -%}
{%- for score in scores %}
- 分数{{ score }}: {{ score | percentage(100) }}%
{%- endfor %}
    """.strip()
    
    result = engine.render_template(template_data_cleaning)
    print("渲染结果:")
    print(result)
    
    # 5. 综合应用示例
    print("\n🎯 5. 综合应用示例")
    template_comprehensive = """
{%- set quarterly_sales = [
    {'quarter': 'Q1', 'regions': [
        {'name': '华北', 'sales': [120000, 135000, 128000]},
        {'name': '华东', 'sales': [98000, 105000, 102000]},
        {'name': '华南', 'sales': [87000, 92000, 89000]}
    ]},
    {'quarter': 'Q2', 'regions': [
        {'name': '华北', 'sales': [142000, 155000, 148000]},
        {'name': '华东', 'sales': [108000, 115000, 112000]},
        {'name': '华南', 'sales': [97000, 102000, 99000]}
    ]}
] -%}

企业级数据分析综合示例:
====================
{%- for quarter_data in quarterly_sales %}
{{ quarter_data.quarter }}季度分析:
{%- for region in quarter_data.regions %}
  {{ region.name }}地区:
    月度销售: {{ region.sales | map('format_number') | list }}
    季度总计: ¥{{ format_number(region.sales | sum) }}
    月均销售: ¥{{ format_number(region.sales | avg_by) }}
    最佳月份: ¥{{ format_number(region.sales | max) }}
    增长趋势: {{ "上升" if region.sales[-1] > region.sales[0] else "下降" }}
{%- endfor %}

  季度汇总:
    总销售额: ¥{{ format_number(quarter_data.regions | map(attribute='sales') | list | flatten | sum) }}
    地区数量: {{ quarter_data.regions | length }}
    最佳地区: {{ quarter_data.regions | sort_by('sales', reverse=true) | first | attr('name') }}
{%- endfor %}

全年对比分析:
{%- set q1_total = quarterly_sales[0].regions | map(attribute='sales') | list | flatten | sum -%}
{%- set q2_total = quarterly_sales[1].regions | map(attribute='sales') | list | flatten | sum -%}
- Q1总销售: ¥{{ format_number(q1_total) }}
- Q2总销售: ¥{{ format_number(q2_total) }}
- 环比增长: {{ ((q2_total - q1_total) | safe_divide(q1_total, 0) | multiply(100)) | round(2) }}%
- 增长金额: ¥{{ format_number(q2_total - q1_total) }}
    """.strip()
    
    result = engine.render_template(template_comprehensive)
    print("渲染结果:")
    print(result)
    
    print("\n🎉 企业级过滤器示例完成！")
    print("\n📊 过滤器分类总结:")
    print("🔄 数据处理: multiply, flatten, group_by")
    print("🛡️ 安全运算: safe_divide")
    print("📊 统计分析: sum_by, avg_by, max_by, min_by, count_by")
    print("🔧 数据整理: unique, sort_by, chunk, percentage")
    print("🎨 格式化: format_number, truncate, slugify")
    
    print("\n💡 关键优势:")
    print("✅ 自动加载 - 无需手工注册任何过滤器")
    print("✅ 企业级功能 - 专为复杂业务场景设计")
    print("✅ 安全可靠 - 内置错误处理和边界检查")
    print("✅ 高性能 - 优化的算法实现")
    print("✅ 易于使用 - 直观的管道语法")

if __name__ == "__main__":
    enterprise_filters_example()
