"""
邮件模板引擎

提供邮件模板渲染功能
"""

import logging
from typing import Any, Dict, List, Optional
from dataclasses import dataclass


@dataclass
class EmailTemplate:
    """邮件模板"""
    id: str
    name: str
    subject_template: str
    body_template: str
    html_template: Optional[str] = None
    variables: Optional[Dict[str, str]] = None
    
    def __post_init__(self):
        if self.variables is None:
            self.variables = {}


class TemplateRenderer:
    """模板渲染器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.TemplateRenderer")
    
    def render(self, template: str, data: Dict[str, Any]) -> str:
        """渲染模板"""
        try:
            # 简单的模板渲染实现
            result = template
            for key, value in data.items():
                placeholder = f"{{{{{key}}}}}"
                result = result.replace(placeholder, str(value))
            return result
        except Exception as e:
            self.logger.error(f"模板渲染失败: {e}")
            return template


class EmailTemplateEngine:
    """邮件模板引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.EmailTemplateEngine")
        self.renderer = TemplateRenderer()
        self.templates: Dict[str, EmailTemplate] = {}
        
        # 加载默认模板
        self._load_default_templates()
    
    def _load_default_templates(self):
        """加载默认模板"""
        # 系统通知模板
        self.templates['system_notification'] = EmailTemplate(
            id='system_notification',
            name='系统通知',
            subject_template='系统通知: {{title}}',
            body_template='{{content}}',
            html_template='<h2>{{title}}</h2><p>{{content}}</p>',
            variables={'title': '标题', 'content': '内容'}
        )
        
        # 欢迎邮件模板
        self.templates['welcome'] = EmailTemplate(
            id='welcome',
            name='欢迎邮件',
            subject_template='欢迎使用 {{service_name}}',
            body_template='亲爱的 {{user_name}}，欢迎使用 {{service_name}}！',
            html_template='<h1>欢迎使用 {{service_name}}</h1><p>亲爱的 {{user_name}}，感谢您的注册！</p>',
            variables={'user_name': '用户名', 'service_name': '服务名称'}
        )
    
    def add_template(self, template: EmailTemplate):
        """添加模板"""
        self.templates[template.id] = template
        self.logger.info(f"添加邮件模板: {template.name}")
    
    def get_template(self, template_id: str) -> Optional[EmailTemplate]:
        """获取模板"""
        return self.templates.get(template_id)
    
    def render_template(self, template_id: str, data: Dict[str, Any]) -> Dict[str, str]:
        """渲染模板"""
        template = self.get_template(template_id)
        if not template:
            raise ValueError(f"模板不存在: {template_id}")
        
        try:
            subject = self.renderer.render(template.subject_template, data)
            body = self.renderer.render(template.body_template, data)
            
            result = {
                'subject': subject,
                'body': body
            }
            
            if template.html_template:
                html_body = self.renderer.render(template.html_template, data)
                result['html_body'] = html_body
            
            return result
            
        except Exception as e:
            self.logger.error(f"模板渲染失败: {e}")
            raise
    
    def list_templates(self) -> List[Dict[str, str]]:
        """列出所有模板"""
        return [
            {
                'id': template.id,
                'name': template.name,
                'variables': list(template.variables.keys())
            }
            for template in self.templates.values()
        ]
