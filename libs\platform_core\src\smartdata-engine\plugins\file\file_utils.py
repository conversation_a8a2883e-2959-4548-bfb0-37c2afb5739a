"""
文件工具

提供文件验证、元数据提取、安全扫描等工具
"""

import logging
import os
import hashlib
import mimetypes
from typing import Any, Dict, List, Optional
from pathlib import Path


class FileValidator:
    """文件验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.FileValidator")
        
        # 危险文件扩展名
        self.dangerous_extensions = {
            '.exe', '.bat', '.cmd', '.com', '.scr', '.pif', '.vbs', '.js',
            '.jar', '.app', '.deb', '.pkg', '.dmg', '.msi', '.run'
        }
        
        # 允许的文件扩展名（如果设置了白名单）
        self.allowed_extensions = set()
    
    def validate_file_extension(self, file_path: str) -> Dict[str, Any]:
        """验证文件扩展名"""
        try:
            file_ext = Path(file_path).suffix.lower()
            
            is_dangerous = file_ext in self.dangerous_extensions
            is_allowed = not self.allowed_extensions or file_ext in self.allowed_extensions
            
            return {
                'valid': not is_dangerous and is_allowed,
                'extension': file_ext,
                'is_dangerous': is_dangerous,
                'is_allowed': is_allowed,
                'reason': self._get_validation_reason(file_ext, is_dangerous, is_allowed)
            }
            
        except Exception as e:
            self.logger.error(f"文件扩展名验证失败: {e}")
            return {
                'valid': False,
                'error': str(e)
            }
    
    def _get_validation_reason(self, file_ext: str, is_dangerous: bool, is_allowed: bool) -> str:
        """获取验证失败原因"""
        if is_dangerous:
            return f"危险文件类型: {file_ext}"
        elif not is_allowed and self.allowed_extensions:
            return f"不在允许列表中: {file_ext}"
        else:
            return "验证通过"
    
    def validate_file_size(self, file_path: str, max_size: int = 100 * 1024 * 1024) -> Dict[str, Any]:
        """验证文件大小"""
        try:
            if not os.path.exists(file_path):
                return {
                    'valid': False,
                    'error': '文件不存在'
                }
            
            file_size = os.path.getsize(file_path)
            is_valid = file_size <= max_size
            
            return {
                'valid': is_valid,
                'size': file_size,
                'max_size': max_size,
                'reason': f"文件大小 {file_size} 字节" + ("" if is_valid else f"，超过限制 {max_size} 字节")
            }
            
        except Exception as e:
            self.logger.error(f"文件大小验证失败: {e}")
            return {
                'valid': False,
                'error': str(e)
            }
    
    def set_allowed_extensions(self, extensions: List[str]):
        """设置允许的文件扩展名"""
        self.allowed_extensions = {ext.lower() for ext in extensions}


class MetadataExtractor:
    """元数据提取器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.MetadataExtractor")
    
    def extract_basic_metadata(self, file_path: str) -> Dict[str, Any]:
        """提取基本元数据"""
        try:
            if not os.path.exists(file_path):
                return {
                    'success': False,
                    'error': '文件不存在'
                }
            
            stat = os.stat(file_path)
            path_obj = Path(file_path)
            
            # 获取MIME类型
            mime_type, encoding = mimetypes.guess_type(file_path)
            
            metadata = {
                'success': True,
                'file_name': path_obj.name,
                'file_extension': path_obj.suffix.lower(),
                'file_size': stat.st_size,
                'created_time': stat.st_ctime,
                'modified_time': stat.st_mtime,
                'accessed_time': stat.st_atime,
                'mime_type': mime_type,
                'encoding': encoding,
                'is_file': os.path.isfile(file_path),
                'is_directory': os.path.isdir(file_path),
                'permissions': oct(stat.st_mode)[-3:]
            }
            
            # 计算文件哈希（仅对小文件）
            if stat.st_size < 10 * 1024 * 1024:  # 小于10MB
                metadata['md5_hash'] = self._calculate_file_hash(file_path, 'md5')
                metadata['sha256_hash'] = self._calculate_file_hash(file_path, 'sha256')
            
            return metadata
            
        except Exception as e:
            self.logger.error(f"元数据提取失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _calculate_file_hash(self, file_path: str, algorithm: str = 'md5') -> str:
        """计算文件哈希"""
        try:
            hash_obj = hashlib.new(algorithm)
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
            
        except Exception as e:
            self.logger.error(f"文件哈希计算失败: {e}")
            return ""
    
    def extract_text_metadata(self, file_path: str) -> Dict[str, Any]:
        """提取文本文件元数据"""
        try:
            if not os.path.exists(file_path):
                return {
                    'success': False,
                    'error': '文件不存在'
                }
            
            with open(file_path, 'rb') as f:
                raw_data = f.read(1024)  # 读取前1KB
            
            # 尝试检测编码
            try:
                text_data = raw_data.decode('utf-8')
                encoding = 'utf-8'
            except UnicodeDecodeError:
                try:
                    text_data = raw_data.decode('gbk')
                    encoding = 'gbk'
                except UnicodeDecodeError:
                    try:
                        text_data = raw_data.decode('latin-1')
                        encoding = 'latin-1'
                    except UnicodeDecodeError:
                        return {
                            'success': False,
                            'error': '无法检测文件编码'
                        }
            
            # 统计文本信息
            lines = text_data.split('\n')
            
            return {
                'success': True,
                'encoding': encoding,
                'line_count': len(lines),
                'char_count': len(text_data),
                'first_line': lines[0] if lines else '',
                'is_empty': len(text_data.strip()) == 0
            }
            
        except Exception as e:
            self.logger.error(f"文本元数据提取失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }


class SecurityScanner:
    """安全扫描器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.SecurityScanner")
        
        # 恶意文件特征
        self.malicious_signatures = [
            b'MZ',  # PE文件头
            b'\x7fELF',  # ELF文件头
            b'#!/bin/sh',  # Shell脚本
            b'#!/bin/bash',  # Bash脚本
        ]
        
        # 可疑文件名模式
        self.suspicious_patterns = [
            'autorun.inf',
            'desktop.ini',
            'thumbs.db',
            '.htaccess',
            'web.config'
        ]
    
    def scan_file(self, file_path: str) -> Dict[str, Any]:
        """扫描文件安全性"""
        try:
            if not os.path.exists(file_path):
                return {
                    'safe': False,
                    'error': '文件不存在'
                }
            
            scan_results = {
                'safe': True,
                'warnings': [],
                'threats': [],
                'file_path': file_path
            }
            
            # 检查文件名
            file_name = os.path.basename(file_path).lower()
            for pattern in self.suspicious_patterns:
                if pattern in file_name:
                    scan_results['warnings'].append(f"可疑文件名: {pattern}")
            
            # 检查文件内容（前1KB）
            try:
                with open(file_path, 'rb') as f:
                    content = f.read(1024)
                
                for signature in self.malicious_signatures:
                    if signature in content:
                        scan_results['threats'].append(f"发现可疑特征: {signature}")
                        scan_results['safe'] = False
                        
            except Exception as e:
                scan_results['warnings'].append(f"无法读取文件内容: {e}")
            
            # 检查文件权限
            try:
                stat = os.stat(file_path)
                if stat.st_mode & 0o111:  # 检查执行权限
                    scan_results['warnings'].append("文件具有执行权限")
            except Exception:
                pass
            
            return scan_results
            
        except Exception as e:
            self.logger.error(f"文件安全扫描失败: {e}")
            return {
                'safe': False,
                'error': str(e)
            }
    
    def scan_directory(self, dir_path: str, recursive: bool = False) -> Dict[str, Any]:
        """扫描目录安全性"""
        try:
            if not os.path.exists(dir_path):
                return {
                    'safe': False,
                    'error': '目录不存在'
                }
            
            scan_results = {
                'safe': True,
                'scanned_files': 0,
                'safe_files': 0,
                'unsafe_files': 0,
                'warnings': [],
                'threats': []
            }
            
            # 扫描文件
            if recursive:
                for root, dirs, files in os.walk(dir_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        file_result = self.scan_file(file_path)
                        
                        scan_results['scanned_files'] += 1
                        
                        if file_result.get('safe', True):
                            scan_results['safe_files'] += 1
                        else:
                            scan_results['unsafe_files'] += 1
                            scan_results['safe'] = False
                        
                        scan_results['warnings'].extend(file_result.get('warnings', []))
                        scan_results['threats'].extend(file_result.get('threats', []))
            else:
                for item in os.listdir(dir_path):
                    item_path = os.path.join(dir_path, item)
                    if os.path.isfile(item_path):
                        file_result = self.scan_file(item_path)
                        
                        scan_results['scanned_files'] += 1
                        
                        if file_result.get('safe', True):
                            scan_results['safe_files'] += 1
                        else:
                            scan_results['unsafe_files'] += 1
                            scan_results['safe'] = False
                        
                        scan_results['warnings'].extend(file_result.get('warnings', []))
                        scan_results['threats'].extend(file_result.get('threats', []))
            
            return scan_results
            
        except Exception as e:
            self.logger.error(f"目录安全扫描失败: {e}")
            return {
                'safe': False,
                'error': str(e)
            }
