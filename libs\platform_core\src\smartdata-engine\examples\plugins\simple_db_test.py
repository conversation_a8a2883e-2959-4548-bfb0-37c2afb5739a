#!/usr/bin/env python3
"""
简单的数据库测试
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

def test_database_api():
    """测试数据库API"""
    print("=== 简单数据库API测试 ===")
    
    try:
        # 直接测试DatabaseProcessor
        from plugins.database.database_processor import DatabaseProcessor
        
        processor = DatabaseProcessor()
        print(f"✅ DatabaseProcessor创建成功: {processor}")
        
        # 测试process方法签名
        import inspect
        sig = inspect.signature(processor.process)
        print(f"✅ process方法签名: {sig}")
        
    except Exception as e:
        print(f"❌ DatabaseProcessor测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    try:
        # 测试SmartDataLoader
        from core.smart_data_object import SmartDataLoader
        
        loader = SmartDataLoader()
        print(f"✅ SmartDataLoader创建成功: {loader}")
        
        # 测试database方法
        db_connector = loader.database("sqlite:///:memory:")
        print(f"✅ database方法调用成功: {type(db_connector)}")
        
    except Exception as e:
        print(f"❌ SmartDataLoader测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_database_api()
