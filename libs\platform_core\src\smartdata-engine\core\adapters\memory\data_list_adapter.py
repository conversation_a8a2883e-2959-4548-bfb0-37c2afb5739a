"""
内存数据列表适配器

支持Python列表和字典数据的直接操作
"""

from typing import Any, Dict, List, Optional, Union, AsyncIterator
import logging
import copy

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../'))

from core.unified_adapter import UnifiedDataAdapter


class DataListAdapter(UnifiedDataAdapter):
    """
    内存数据列表适配器
    
    支持Python内置数据类型：
    - 字典列表直接查询和操作
    - 内存中的数据过滤和排序
    - 数据转换和聚合
    - 同步和异步操作支持
    """
    
    def __init__(self):
        super().__init__()
    
    def supported_types(self) -> List[str]:
        """返回支持的数据类型列表"""
        return [
            'data_list',
            'list_object',
            'dict_list',
            'memory_data',
            'python_list',
            'iterable_object'
        ]
    
    def _is_supported_connection_string(self, connection_string: str) -> bool:
        """检查是否支持的连接字符串"""
        return False  # 不支持字符串连接
    
    def _is_supported_connection_object(self, connection: Any) -> bool:
        """检查是否支持的连接对象"""
        return isinstance(connection, (list, tuple)) or hasattr(connection, '__iter__')
    
    def can_handle(self, data_source: Any) -> bool:
        """检查是否能处理指定的数据源"""
        return self._is_supported_connection_object(data_source)
    
    def _build_operations(self) -> Dict[str, callable]:
        """构建内存数据特有操作列表"""
        operations = {
            'filter_data': self._sync_filter_data,
            'sort_data': self._sync_sort_data,
            'aggregate_data': self._sync_aggregate_data,
        }
        
        # 添加异步操作
        async_ops = self.get_async_operations()
        for name, func in async_ops.items():
            operations[name] = func
        
        return operations
    
    # ========================================================================
    # 同步方法实现
    # ========================================================================
    
    def _sync_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """同步查询实现 - 从内存数据中查询"""
        # 将SQL查询映射为内存数据操作
        data = list(connection) if hasattr(connection, '__iter__') else [connection]
        
        # 确保数据是字典列表格式
        normalized_data = []
        for item in data:
            if isinstance(item, dict):
                normalized_data.append(item)
            else:
                normalized_data.append({'value': item})
        
        # 应用参数过滤
        if params:
            if 'filter' in params:
                normalized_data = self._apply_filter(normalized_data, params['filter'])
            if 'sort' in params:
                normalized_data = self._apply_sort(normalized_data, params['sort'])
            if 'limit' in params:
                normalized_data = normalized_data[:params['limit']]
        
        return normalized_data
    
    def _sync_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """同步执行实现 - 修改内存数据"""
        # 对于内存数据，执行操作通常是修改数据
        if params and 'data' in params:
            new_data = params['data']
            if hasattr(connection, 'extend'):
                connection.extend(new_data)
                return len(new_data)
            elif hasattr(connection, 'append'):
                for item in new_data:
                    connection.append(item)
                return len(new_data)
        return 0
    
    def _sync_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """同步事务实现 - 批量内存操作"""
        results = []
        total_affected = 0
        
        # 创建数据副本用于事务
        original_data = copy.deepcopy(list(connection))
        
        try:
            for operation in operations:
                op_type = operation.get('type', 'query')
                params = operation.get('params', {})
                
                if op_type == 'query':
                    result = self._sync_query(connection, '', params)
                    results.append(result)
                elif op_type == 'execute':
                    affected = self._sync_execute(connection, '', params)
                    results.append(affected)
                    total_affected += affected
                elif op_type == 'filter':
                    result = self._sync_filter_data(connection, params.get('filter', {}))
                    results.append(result)
                elif op_type == 'sort':
                    result = self._sync_sort_data(connection, params.get('sort', []))
                    results.append(result)
            
            return {
                'results': results,
                'total_operations': len(operations),
                'total_affected': total_affected,
                'success': True
            }
            
        except Exception as e:
            # 回滚到原始数据
            connection.clear()
            connection.extend(original_data)
            raise
    
    def _sync_filter_data(self, connection: Any, filter_criteria: Dict) -> List[Dict]:
        """同步数据过滤"""
        data = list(connection)
        return self._apply_filter(data, filter_criteria)
    
    def _sync_sort_data(self, connection: Any, sort_criteria: List[Dict]) -> List[Dict]:
        """同步数据排序"""
        data = list(connection)
        return self._apply_sort(data, sort_criteria)
    
    def _sync_aggregate_data(self, connection: Any, agg_criteria: Dict) -> Dict:
        """同步数据聚合"""
        data = list(connection)
        
        result = {}
        for field, operations in agg_criteria.items():
            if isinstance(operations, str):
                operations = [operations]
            
            field_values = [item.get(field) for item in data if field in item and item[field] is not None]
            
            for op in operations:
                if op == 'count':
                    result[f'{field}_count'] = len(field_values)
                elif op == 'sum':
                    result[f'{field}_sum'] = sum(v for v in field_values if isinstance(v, (int, float)))
                elif op == 'avg':
                    numeric_values = [v for v in field_values if isinstance(v, (int, float))]
                    result[f'{field}_avg'] = sum(numeric_values) / len(numeric_values) if numeric_values else 0
                elif op == 'min':
                    result[f'{field}_min'] = min(field_values) if field_values else None
                elif op == 'max':
                    result[f'{field}_max'] = max(field_values) if field_values else None
        
        return result
    
    def _apply_filter(self, data: List[Dict], filter_criteria: Dict) -> List[Dict]:
        """应用过滤条件"""
        if not filter_criteria:
            return data
        
        filtered_data = []
        for item in data:
            match = True
            for field, condition in filter_criteria.items():
                if field not in item:
                    match = False
                    break
                
                value = item[field]
                
                if isinstance(condition, dict):
                    # 复杂条件 {'$gt': 10, '$lt': 20}
                    for op, target in condition.items():
                        if op == '$gt' and not (value > target):
                            match = False
                            break
                        elif op == '$lt' and not (value < target):
                            match = False
                            break
                        elif op == '$gte' and not (value >= target):
                            match = False
                            break
                        elif op == '$lte' and not (value <= target):
                            match = False
                            break
                        elif op == '$eq' and not (value == target):
                            match = False
                            break
                        elif op == '$ne' and not (value != target):
                            match = False
                            break
                        elif op == '$in' and value not in target:
                            match = False
                            break
                        elif op == '$nin' and value in target:
                            match = False
                            break
                else:
                    # 简单条件
                    if value != condition:
                        match = False
                        break
            
            if match:
                filtered_data.append(item)
        
        return filtered_data
    
    def _apply_sort(self, data: List[Dict], sort_criteria: List[Dict]) -> List[Dict]:
        """应用排序条件"""
        if not sort_criteria:
            return data
        
        sorted_data = data.copy()
        
        # 按照排序条件逐个排序
        for sort_spec in reversed(sort_criteria):  # 反向处理以保持优先级
            field = sort_spec.get('field')
            direction = sort_spec.get('direction', 'asc')
            
            if field:
                reverse = direction.lower() == 'desc'
                sorted_data.sort(
                    key=lambda x: x.get(field, ''),
                    reverse=reverse
                )
        
        return sorted_data

    # ========================================================================
    # 异步方法实现
    # ========================================================================

    async def _create_async_connection(self, connection_source: Any) -> Any:
        """创建异步连接（内存数据直接返回）"""
        self.logger.info(f"创建异步内存数据连接: {type(connection_source).__name__}")
        return connection_source

    async def _close_async_connection(self, connection: Any) -> None:
        """关闭异步连接（内存数据无需关闭）"""
        self.logger.debug("异步内存数据连接已关闭")

    async def _create_async_pool(self, connection_source: Any, min_size: int = 5, max_size: int = 20) -> Any:
        """创建异步连接池（内存数据不需要连接池）"""
        return await self._create_async_connection(connection_source)

    async def _async_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """异步查询实现"""
        return self._sync_query(connection, sql, params)

    async def _async_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """异步执行实现"""
        return self._sync_execute(connection, sql, params)

    async def _async_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步事务实现"""
        return self._sync_transaction(connection, operations)

    async def _async_batch(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步批量操作实现"""
        return await self._async_transaction(connection, operations)

    async def _async_stream_query(self, connection: Any, sql: str, params: Dict = None) -> AsyncIterator[Dict]:
        """异步流式查询实现"""
        data = self._sync_query(connection, sql, params)
        for item in data:
            yield item

    # ========================================================================
    # 内存数据特有功能
    # ========================================================================

    def get_async_operations(self) -> Dict[str, Any]:
        """获取支持的异步操作列表"""
        operations = super().get_async_operations()

        # 添加内存数据特有异步操作
        operations.update({
            'async_filter_data': self._async_filter_data,
            'async_sort_data': self._async_sort_data,
            'async_aggregate_data': self._async_aggregate_data,
        })

        return operations

    async def _async_filter_data(self, connection: Any, filter_criteria: Dict) -> List[Dict]:
        """异步数据过滤"""
        return self._sync_filter_data(connection, filter_criteria)

    async def _async_sort_data(self, connection: Any, sort_criteria: List[Dict]) -> List[Dict]:
        """异步数据排序"""
        return self._sync_sort_data(connection, sort_criteria)

    async def _async_aggregate_data(self, connection: Any, agg_criteria: Dict) -> Dict:
        """异步数据聚合"""
        return self._sync_aggregate_data(connection, agg_criteria)
