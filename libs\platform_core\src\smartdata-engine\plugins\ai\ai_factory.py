"""
AI服务提供者工厂

类似database插件的连接器工厂，提供智能AI服务提供者创建和管理
"""

import logging
from typing import Any, Dict, List, Optional, Type, Union
from abc import ABC, abstractmethod

from core.smart_data_object import SmartDataObject


class AIServiceType:
    """AI服务类型常量"""
    TEXT_GENERATION = "text_generation"
    TEXT_ANALYSIS = "text_analysis"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    ENTITY_EXTRACTION = "entity_extraction"
    CONVERSATION = "conversation"
    TRANSLATION = "translation"
    SUMMARIZATION = "summarization"
    CLASSIFICATION = "classification"
    EMBEDDING = "embedding"
    IMAGE_ANALYSIS = "image_analysis"
    SPEECH_TO_TEXT = "speech_to_text"
    TEXT_TO_SPEECH = "text_to_speech"


class IAIServiceProvider(ABC):
    """AI服务提供者接口"""
    
    @abstractmethod
    def can_handle(self, service_type: str, model: str = None) -> bool:
        """检查是否可以处理指定服务类型"""
        pass
    
    @abstractmethod
    async def process(self, service_type: str, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理AI请求"""
        pass
    
    @abstractmethod
    def get_provider_name(self) -> str:
        """获取提供者名称"""
        pass
    
    @abstractmethod
    def get_supported_services(self) -> List[str]:
        """获取支持的服务类型"""
        pass
    
    @abstractmethod
    def get_supported_models(self) -> List[str]:
        """获取支持的模型列表"""
        pass


class AIServiceFactory:
    """AI服务工厂 - 类似database插件的连接器工厂"""

    # 服务提供者映射
    PROVIDER_CLASSES = {
        'openai': None,  # 延迟导入
        'claude': None,  # 延迟导入
        'ollama': None,  # 延迟导入
    }
    
    @classmethod
    def detect_provider(cls, service_type: str, model: str = None) -> str:
        """检测最佳服务提供者"""
        # 基于模型名称检测提供者
        if model:
            if model.startswith('gpt-') or model.startswith('text-'):
                return 'openai'
            elif model.startswith('claude-'):
                return 'claude'
            elif model.startswith(('llama', 'mistral', 'gemma', 'qwen', 'phi')):
                return 'ollama'

        # 默认使用OpenAI
        return 'openai'
    
    @classmethod
    def create_provider(cls, provider_name: str, **config) -> Optional[IAIServiceProvider]:
        """创建AI服务提供者"""
        provider_name_lower = provider_name.lower()

        # 延迟导入各个提供者
        if provider_name_lower == 'openai':
            try:
                from .openai_provider import OpenAIProvider
                return OpenAIProvider(**config)
            except ImportError as e:
                logging.getLogger(__name__).error(f"导入OpenAIProvider失败: {e}")
                return None
        
        elif provider_name_lower == 'claude':
            try:
                from .claude_provider import ClaudeProvider
                return ClaudeProvider(**config)
            except ImportError as e:
                logging.getLogger(__name__).error(f"导入ClaudeProvider失败: {e}")
                return None
        
        elif provider_name_lower == 'ollama':
            try:
                from .ollama_provider import OllamaProvider
                return OllamaProvider(**config)
            except ImportError as e:
                logging.getLogger(__name__).error(f"导入OllamaProvider失败: {e}")
                return None

        return None
    
    @classmethod
    def get_supported_providers(cls) -> List[str]:
        """获取支持的提供者"""
        return list(cls.PROVIDER_CLASSES.keys())
    
    @classmethod
    def register_provider(cls, provider_name: str, provider_class: Type[IAIServiceProvider]):
        """注册自定义AI服务提供者"""
        cls.PROVIDER_CLASSES[provider_name] = provider_class
    
    @classmethod
    def get_provider_info(cls, provider_name: str) -> Optional[Dict[str, Any]]:
        """获取提供者信息"""
        try:
            provider = cls.create_provider(provider_name)
            if provider:
                return {
                    'name': provider.get_provider_name(),
                    'supported_services': provider.get_supported_services(),
                    'supported_models': provider.get_supported_models()
                }
        except Exception as e:
            logging.getLogger(__name__).error(f"获取提供者信息失败: {e}")
        
        return None
    
    @classmethod
    def list_all_providers(cls) -> Dict[str, Dict[str, Any]]:
        """列出所有可用的提供者及其信息"""
        providers_info = {}
        
        for provider_name in cls.get_supported_providers():
            info = cls.get_provider_info(provider_name)
            if info:
                providers_info[provider_name] = info
        
        return providers_info


# 全局工厂实例
ai_service_factory = AIServiceFactory()


def create_ai_provider(provider_name: str, **config) -> Optional[IAIServiceProvider]:
    """创建AI服务提供者的便捷函数"""
    return ai_service_factory.create_provider(provider_name, **config)


def detect_best_provider(service_type: str, model: str = None) -> str:
    """检测最佳提供者的便捷函数"""
    return ai_service_factory.detect_provider(service_type, model)


def get_all_providers() -> Dict[str, Dict[str, Any]]:
    """获取所有提供者信息的便捷函数"""
    return ai_service_factory.list_all_providers()
