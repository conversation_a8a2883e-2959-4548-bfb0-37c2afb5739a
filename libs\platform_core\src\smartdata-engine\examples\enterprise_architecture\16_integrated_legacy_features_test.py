#!/usr/bin/env python3
"""
集成旧模板引擎功能测试

验证新的线程安全架构成功集成了旧模板引擎的所有强大功能：
1. 企业级过滤器
2. 推导式扩展
3. 高级语句扩展
4. 复杂数据处理
5. 链式调用
"""

import sys
import os
import json
import tempfile
import threading
import asyncio

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.thread_safe_template_integration import ThreadSafeTemplateIntegration


def integrated_legacy_features_test():
    """集成旧模板引擎功能测试"""
    print("=== 集成旧模板引擎功能测试 ===")
    print("验证新架构成功集成旧模板引擎的强大功能")
    print("=" * 80)
    
    # 创建线程安全的模板引擎
    engine = ThreadSafeTemplateIntegration(
        enable_async=True,
        enable_debug=True,
        isolation_level='thread',
        cleanup_interval=60,
        max_scope_lifetime=300
    )
    
    test_results = []
    
    # 📁 测试1：文件处理 + 企业级过滤器
    print("\n📁 测试1：文件处理 + 企业级过滤器")
    print("-" * 60)
    
    try:
        # 创建复杂测试数据
        test_data = {
            "company": "智慧科技有限公司",
            "employees": [
                {"name": "张三", "age": 28, "salary": 8500.50, "department": "技术部", "active": True},
                {"name": "李四", "age": 32, "salary": 12000.75, "department": "技术部", "active": True},
                {"name": "王五", "age": 25, "salary": 6000.00, "department": "销售部", "active": False},
                {"name": "赵六", "age": 30, "salary": 9500.25, "department": "市场部", "active": True},
                {"name": "钱七", "age": 35, "salary": 15000.00, "department": "技术部", "active": True}
            ],
            "projects": [
                {"name": "项目A", "budget": 100000, "team": ["张三", "李四"]},
                {"name": "项目B", "budget": 150000, "team": ["钱七"]},
                {"name": "项目C", "budget": 80000, "team": ["赵六", "王五"]}
            ]
        }
        
        temp_file = tempfile.mktemp(suffix='.json')
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        file_path = temp_file.replace('\\', '/')
        
        template = f"""
企业级数据处理测试
================
{{%- set data = sd.file('{file_path}').parse() -%}}

1. 基础信息:
公司: {{{{ data.company }}}}
员工总数: {{{{ data.employees | length }}}}
项目总数: {{{{ data.projects | length }}}}

2. 企业级过滤器测试:
活跃员工: {{{{ data.employees | selectattr('active') | map(attribute='name') | list | join(', ') }}}}
技术部员工: {{{{ data.employees | selectattr('department', 'equalto', '技术部') | map(attribute='name') | list | join(', ') }}}}

3. 数值处理:
总薪资: {{{{ data.employees | sum(attribute='salary') | format_number(2) }}}}
平均薪资: {{{{ (data.employees | sum(attribute='salary') / (data.employees | length)) | format_number(2) }}}}
最高薪资: {{{{ data.employees | map(attribute='salary') | max | format_number(2) }}}}

4. 分组统计:
{{%- set dept_groups = data.employees | group_by('department') -%}}
{{%- for dept, emps in dept_groups.items() %}
{{{{ dept }}}部门: {{{{ emps | length }}}}人, 平均薪资: {{{{ (emps | sum(attribute='salary') / (emps | length)) | format_number(2) }}}}
{{%- endfor %}

5. 复杂链式调用:
高薪技术员工: {{{{ data.employees | selectattr('department', 'equalto', '技术部') | selectattr('salary', '>', 10000) | map(attribute='name') | list | join(', ') }}}}
项目预算统计: {{{{ data.projects | sum(attribute='budget') | format_number(0) }}}}
        """.strip()
        
        result = engine.render_template_sync(template, {}, 'enterprise_test')
        
        if '智慧科技有限公司' in result and '张三' in result and '技术部' in result:
            test_results.append("✅ 企业级过滤器")
            print("✅ 企业级过滤器集成成功")
            print(f"结果预览: {result[:300]}...")
        else:
            test_results.append("❌ 企业级过滤器")
            print("❌ 企业级过滤器集成失败")
        
        # 清理
        if os.path.exists(temp_file):
            os.unlink(temp_file)
            
    except Exception as e:
        test_results.append("❌ 企业级过滤器")
        print(f"❌ 企业级过滤器异常: {e}")
        import traceback
        traceback.print_exc()
    
    # 🔄 测试2：复杂数据结构处理
    print("\n🔄 测试2：复杂数据结构处理")
    print("-" * 60)
    
    try:
        complex_data = {
            "orders": [
                {
                    "id": 1,
                    "customer": "客户A",
                    "items": [
                        {"product": "笔记本", "price": 5000, "quantity": 2, "category": "电子产品"},
                        {"product": "鼠标", "price": 100, "quantity": 1, "category": "配件"}
                    ],
                    "status": "已完成",
                    "date": "2024-01-15"
                },
                {
                    "id": 2,
                    "customer": "客户B",
                    "items": [
                        {"product": "键盘", "price": 300, "quantity": 1, "category": "配件"},
                        {"product": "显示器", "price": 2000, "quantity": 1, "category": "电子产品"}
                    ],
                    "status": "进行中",
                    "date": "2024-01-20"
                }
            ],
            "categories": ["电子产品", "配件", "办公用品"]
        }
        
        template = """
复杂数据结构处理测试
==================

1. 嵌套数据展平:
所有产品: {{ orders | map(attribute='items') | flatten | map(attribute='product') | unique | list | join(', ') }}

2. 深度数据访问:
{{%- for order in orders %}
订单{{ order.id }} ({{ order.customer }}):
  {{%- for item in order.items %}
  - {{ item.product }}: {{ item.price | format_number(0) }}元 × {{ item.quantity }} = {{ (item.price * item.quantity) | format_number(0) }}元
  {{%- endfor %}
  订单总额: {{ order.items | sum_by('price') | format_number(0) }}元
{{%- endfor %}

3. 复杂聚合统计:
{{%- set all_items = orders | map(attribute='items') | flatten | list %}
产品销量统计:
{{%- set products = all_items | map(attribute='product') | unique | list %}
{{%- for product in products %}
{{ product }}: {{ all_items | selectattr('product', 'equalto', product) | sum(attribute='quantity') }}件
{{%- endfor %}

4. 分类统计:
{{%- for category in categories %}
{{ category }}类商品数: {{ all_items | selectattr('category', 'equalto', category) | list | length }}
{{%- endfor %}

5. 状态分析:
已完成订单: {{ orders | selectattr('status', 'equalto', '已完成') | list | length }}个
进行中订单: {{ orders | selectattr('status', 'equalto', '进行中') | list | length }}个
总订单价值: {{ orders | map('sum_by', 'items', 'price') | sum | format_number(0) }}元
        """.strip()
        
        result = engine.render_template_sync(template, complex_data, 'complex_test')
        
        if '笔记本' in result and '客户A' in result and '产品销量统计' in result:
            test_results.append("✅ 复杂数据处理")
            print("✅ 复杂数据处理成功")
        else:
            test_results.append("❌ 复杂数据处理")
            print("❌ 复杂数据处理失败")
            
    except Exception as e:
        test_results.append("❌ 复杂数据处理")
        print(f"❌ 复杂数据处理异常: {e}")
        import traceback
        traceback.print_exc()
    
    # 🧵 测试3：多线程 + 复杂功能
    print("\n🧵 测试3：多线程 + 复杂功能")
    print("-" * 60)
    
    try:
        def worker(worker_id, results):
            try:
                # 每个线程处理不同的数据
                worker_data = {
                    "worker_id": worker_id,
                    "tasks": [
                        {"name": f"任务{i}", "priority": i % 3, "completed": i % 2 == 0}
                        for i in range(1, 6)
                    ]
                }
                
                template = """
线程{{ worker_id }}处理结果
===================
工作线程: {{ worker_id }}
任务总数: {{ tasks | length }}
已完成: {{ tasks | selectattr('completed') | list | length }}
未完成: {{ tasks | rejectattr('completed') | list | length }}
高优先级任务: {{ tasks | selectattr('priority', '>', 1) | map(attribute='name') | list | join(', ') }}
完成率: {{ (tasks | selectattr('completed') | list | length / tasks | length * 100) | round(1) }}%
                """.strip()
                
                result = engine.render_template_sync(template, worker_data, f'thread_{worker_id}')
                
                if f'线程{worker_id}' in result and '完成率' in result:
                    results.append(f"✅ 线程{worker_id}")
                else:
                    results.append(f"❌ 线程{worker_id}")
                    
            except Exception as e:
                results.append(f"❌ 线程{worker_id}: {e}")
        
        # 启动5个线程
        threads = []
        thread_results = []
        
        for i in range(5):
            thread = threading.Thread(target=worker, args=(i+1, thread_results))
            threads.append(thread)
            thread.start()
        
        # 等待完成
        for thread in threads:
            thread.join()
        
        successful_threads = len([r for r in thread_results if r.startswith('✅')])
        
        if successful_threads == 5:
            test_results.append("✅ 多线程复杂功能")
            print("✅ 多线程复杂功能正常")
        else:
            test_results.append("❌ 多线程复杂功能")
            print(f"❌ 多线程复杂功能失败: {successful_threads}/5")
            
    except Exception as e:
        test_results.append("❌ 多线程复杂功能")
        print(f"❌ 多线程复杂功能异常: {e}")
    
    # 📊 生成最终报告
    print("\n📊 集成旧模板引擎功能测试报告")
    print("=" * 80)
    
    successful_tests = len([r for r in test_results if r.startswith('✅')])
    total_tests = len(test_results)
    success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print("测试结果:")
    for result in test_results:
        print(f"  {result}")
    
    print(f"\n总体结果:")
    print(f"  成功测试: {successful_tests}/{total_tests}")
    print(f"  成功率: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("\n🎉 完美！旧模板引擎功能完全集成成功")
        print("\n✅ 成功集成的功能:")
        print("  📁 企业级过滤器 - 完全集成")
        print("  🔄 复杂数据处理 - 完全集成")
        print("  🧵 多线程复杂功能 - 完全集成")
        print("  🔗 链式调用 - 完全集成")
        
        print("\n🚀 新架构成功继承了旧模板引擎的所有强大功能！")
        
    elif success_rate >= 66:
        print("\n✅ 良好！大部分旧功能成功集成")
        print("  核心功能已迁移，可以投入使用")
        
    else:
        print("\n⚠️ 需要进一步优化功能集成")
    
    # 关闭引擎
    print("\n🔧 正在关闭模板引擎...")
    engine.shutdown()
    print("✅ 模板引擎已安全关闭")
    
    return success_rate


if __name__ == "__main__":
    success_rate = integrated_legacy_features_test()
    
    if success_rate == 100:
        print(f"\n🏆 集成测试: {success_rate:.1f}% - 完美成功！")
        print("🎯 新架构完全继承了旧模板引擎的强大功能！")
    elif success_rate >= 66:
        print(f"\n📊 集成测试: {success_rate:.1f}% - 良好")
        print("🎯 核心功能成功迁移，基本满足需求")
    else:
        print(f"\n🔧 集成测试: {success_rate:.1f}% - 需要优化")
