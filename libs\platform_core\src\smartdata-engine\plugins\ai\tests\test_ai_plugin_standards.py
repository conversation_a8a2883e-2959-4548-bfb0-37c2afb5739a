#!/usr/bin/env python3
"""
AI插件标准符合性测试

验证AI插件是否符合插件标准规范
"""

import pytest
import asyncio
from typing import Dict, Any

# 导入测试目标
from plugins.ai import get_plugin_definitions
from plugins.ai.ai_processor import AiDataProcessor
from plugins.ai.smart_ai_loader import SmartAILoader, global_ai_loader
from plugins.ai.ai_factory import AIServiceFactory, create_ai_provider


class TestAIPluginStandards:
    """AI插件标准符合性测试"""
    
    def test_plugin_definitions(self):
        """测试插件定义符合性"""
        definitions = get_plugin_definitions()
        
        # 验证插件定义结构
        assert isinstance(definitions, dict), "插件定义应该是字典"
        assert 'ai' in definitions, "应该包含ai插件定义"
        
        ai_def = definitions['ai']
        
        # 验证必需字段
        required_fields = ['name', 'version', 'description', 'processor_class', 'smart_loader_class']
        for field in required_fields:
            assert field in ai_def, f"插件定义缺少必需字段: {field}"
        
        # 验证字段类型
        assert isinstance(ai_def['name'], str), "name应该是字符串"
        assert isinstance(ai_def['version'], str), "version应该是字符串"
        assert isinstance(ai_def['description'], str), "description应该是字符串"
        
        print("✅ 插件定义符合性测试通过")
    
    def test_main_processor_compliance(self):
        """测试主处理器符合性"""
        processor = AiDataProcessor()
        
        # 验证标准接口方法
        assert hasattr(processor, 'can_process'), "处理器应该有can_process方法"
        assert hasattr(processor, 'process'), "处理器应该有process方法"
        assert hasattr(processor, 'get_processor_info'), "处理器应该有get_processor_info方法"
        
        # 测试can_process方法
        assert callable(processor.can_process), "can_process应该是可调用的"
        
        # 测试不同类型的数据
        assert processor.can_process({'service_type': 'text_generation'}), "应该能处理AI配置字典"
        assert processor.can_process('这是一个文本提示'), "应该能处理文本字符串"
        assert processor.can_process([{'role': 'user', 'content': 'hello'}]), "应该能处理对话消息"
        assert not processor.can_process(123), "不应该处理数字"
        
        # 测试get_processor_info方法
        info = processor.get_processor_info()
        assert isinstance(info, dict), "处理器信息应该是字典"
        assert 'name' in info, "处理器信息应该包含name"
        assert 'version' in info, "处理器信息应该包含version"
        assert 'capabilities' in info, "处理器信息应该包含capabilities"
        
        print("✅ 主处理器符合性测试通过")
    
    def test_smart_loader_compliance(self):
        """测试智能加载器符合性"""
        loader = SmartAILoader()
        
        # 验证标准接口方法
        assert hasattr(loader, 'load'), "智能加载器应该有load方法"
        assert hasattr(loader, 'load_async'), "智能加载器应该有load_async方法"
        
        # 测试load方法
        assert callable(loader.load), "load应该是可调用的"
        assert callable(loader.load_async), "load_async应该是可调用的"
        
        print("✅ 智能加载器符合性测试通过")
    
    def test_ai_factory_functionality(self):
        """测试AI工厂功能"""
        # 测试支持的提供者
        providers = AIServiceFactory.get_supported_providers()
        assert isinstance(providers, list), "支持的提供者应该是列表"
        assert 'openai' in providers, "应该支持OpenAI"
        assert 'claude' in providers, "应该支持Claude"
        assert 'ollama' in providers, "应该支持Ollama"
        
        # 测试提供者检测
        assert AIServiceFactory.detect_provider('text_generation', 'gpt-4') == 'openai'
        assert AIServiceFactory.detect_provider('conversation', 'claude-3') == 'claude'
        
        print("✅ AI工厂功能测试通过")
    
    def test_global_loader_instance(self):
        """测试全局加载器实例"""
        # 验证全局实例存在
        assert global_ai_loader is not None, "全局AI加载器应该存在"
        assert isinstance(global_ai_loader, SmartAILoader), "全局实例应该是SmartAILoader类型"
        
        # 验证全局实例方法
        assert hasattr(global_ai_loader, 'load'), "全局实例应该有load方法"
        assert hasattr(global_ai_loader, 'process_ai_request'), "全局实例应该有process_ai_request方法"
        
        print("✅ 全局加载器实例测试通过")
    
    def test_ai_config_parsing(self):
        """测试AI配置解析"""
        loader = SmartAILoader()
        
        # 测试字典配置解析
        dict_config = {
            'service_type': 'text_generation',
            'prompt': '测试提示',
            'model': 'gpt-3.5-turbo'
        }
        parsed = loader._parse_ai_request(dict_config)
        assert parsed['service_type'] == 'text_generation'
        assert parsed['prompt'] == '测试提示'
        
        # 测试字符串配置解析
        str_config = '这是一个文本提示'
        parsed = loader._parse_ai_request(str_config)
        assert parsed['service_type'] == 'text_generation'
        assert parsed['prompt'] == str_config
        
        # 测试列表配置解析
        list_config = [{'role': 'user', 'content': 'hello'}]
        parsed = loader._parse_ai_request(list_config)
        assert parsed['service_type'] == 'conversation'
        assert parsed['messages'] == list_config
        
        print("✅ AI配置解析测试通过")
    
    def test_error_handling(self):
        """测试错误处理"""
        loader = SmartAILoader()
        
        # 测试无效提供者
        result = loader.process_ai_request('text_generation', '测试', {'provider': 'invalid_provider'})
        assert hasattr(result, 'error'), "应该有错误信息"
        
        print("✅ 错误处理测试通过")
    
    def test_caching_mechanism(self):
        """测试缓存机制"""
        loader = SmartAILoader()
        
        # 验证缓存相关方法
        assert hasattr(loader, '_result_cache'), "应该有结果缓存"
        assert hasattr(loader, '_get_cache_key'), "应该有缓存键生成方法"
        
        # 测试缓存键生成
        cache_key1 = loader._get_cache_key('text_generation', '测试1', {})
        cache_key2 = loader._get_cache_key('text_generation', '测试2', {})
        cache_key3 = loader._get_cache_key('text_generation', '测试1', {})
        
        assert cache_key1 != cache_key2, "不同数据应该有不同的缓存键"
        assert cache_key1 == cache_key3, "相同数据应该有相同的缓存键"
        
        print("✅ 缓存机制测试通过")
    
    def test_provider_configuration(self):
        """测试提供者配置"""
        loader = SmartAILoader()
        
        # 测试配置方法
        assert hasattr(loader, 'configure_provider'), "应该有配置提供者方法"
        assert hasattr(loader, 'get_provider_info'), "应该有获取提供者信息方法"
        
        # 测试配置更新
        config = {'api_key': 'test-key'}
        loader.configure_provider('openai', config)
        
        print("✅ 提供者配置测试通过")
    
    @pytest.mark.asyncio
    async def test_async_functionality(self):
        """测试异步功能"""
        loader = SmartAILoader()
        
        # 测试异步加载
        result = await loader.load_async('测试异步处理')
        assert hasattr(result, 'success') or hasattr(result, 'error'), "异步结果应该有success或error属性"
        
        print("✅ 异步功能测试通过")
    
    def test_service_types_support(self):
        """测试服务类型支持"""
        processor = AiDataProcessor()
        
        # 获取支持的服务类型
        services = processor.get_supported_services()
        assert isinstance(services, list), "支持的服务应该是列表"
        
        # 验证关键服务类型
        expected_services = ['text_generation', 'conversation', 'text_analysis', 'summarization']
        for service in expected_services:
            assert service in services, f"应该支持{service}服务"
        
        print("✅ 服务类型支持测试通过")
    
    def test_provider_support(self):
        """测试提供者支持"""
        processor = AiDataProcessor()
        
        # 获取支持的提供者
        providers = processor.get_supported_providers()
        assert isinstance(providers, list), "支持的提供者应该是列表"
        
        # 验证关键提供者
        expected_providers = ['openai', 'claude', 'ollama']
        for provider in expected_providers:
            assert provider in providers, f"应该支持{provider}提供者"
        
        print("✅ 提供者支持测试通过")


if __name__ == '__main__':
    # 运行所有测试
    test_instance = TestAIPluginStandards()
    
    print("=== AI插件标准符合性测试 ===")
    
    test_instance.test_plugin_definitions()
    test_instance.test_main_processor_compliance()
    test_instance.test_smart_loader_compliance()
    test_instance.test_ai_factory_functionality()
    test_instance.test_global_loader_instance()
    test_instance.test_ai_config_parsing()
    test_instance.test_error_handling()
    test_instance.test_caching_mechanism()
    test_instance.test_provider_configuration()
    test_instance.test_service_types_support()
    test_instance.test_provider_support()
    
    # 异步测试
    asyncio.run(test_instance.test_async_functionality())
    
    print("\n🎉 所有AI插件标准符合性测试通过！")
