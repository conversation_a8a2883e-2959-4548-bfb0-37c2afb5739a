"""
AI Agent系统

提供专业化的AI Agent，包括对话管理、数据分析、内容生成、多模态处理等
"""

import logging
import asyncio
from typing import Any, Dict, List, Optional, Union
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

from core.smart_data_object import SmartDataObject
from .ai_factory import AIServiceFactory, AIServiceType


class AgentType(Enum):
    """Agent类型"""
    CONVERSATION_MANAGER = "conversation_manager"
    DATA_ANALYST = "data_analyst"
    CONTENT_GENERATOR = "content_generator"
    MULTIMODAL_PROCESSOR = "multimodal_processor"
    WORKFLOW_ORCHESTRATOR = "workflow_orchestrator"


@dataclass
class AgentConfig:
    """Agent配置"""
    agent_type: AgentType
    name: str
    description: str
    provider: str = "openai"
    model: str = "gpt-3.5-turbo"
    temperature: float = 0.7
    max_tokens: int = 1000
    system_prompt: str = ""
    capabilities: List[str] = None
    memory_enabled: bool = True
    context_window: int = 10


class BaseAgent(ABC):
    """AI Agent基类"""
    
    def __init__(self, config: AgentConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{config.name}")
        self.memory = [] if config.memory_enabled else None
        self.ai_factory = AIServiceFactory()
        
    @abstractmethod
    async def process(self, input_data: Any, context: Dict = None) -> SmartDataObject:
        """处理输入数据"""
        pass
    
    def add_to_memory(self, interaction: Dict):
        """添加到记忆中"""
        if self.memory is not None:
            self.memory.append(interaction)
            # 保持记忆窗口大小
            if len(self.memory) > self.config.context_window:
                self.memory = self.memory[-self.config.context_window:]
    
    def get_memory_context(self) -> List[Dict]:
        """获取记忆上下文"""
        return self.memory or []


class ConversationManagerAgent(BaseAgent):
    """对话管理Agent"""
    
    def __init__(self, config: AgentConfig = None):
        if config is None:
            config = AgentConfig(
                agent_type=AgentType.CONVERSATION_MANAGER,
                name="ConversationManager",
                description="专业的对话管理Agent，支持多轮对话、上下文理解、情感分析",
                system_prompt="""你是一个专业的对话管理助手。你的职责是：
1. 维护对话的连贯性和上下文
2. 理解用户意图和情感
3. 提供有帮助、准确、友好的回复
4. 记住对话历史并在需要时引用
5. 识别对话中的关键信息和实体""",
                capabilities=["conversation", "context_management", "intent_recognition", "sentiment_analysis"]
            )
        super().__init__(config)
    
    async def process(self, input_data: Any, context: Dict = None) -> SmartDataObject:
        """处理对话请求"""
        try:
            # 构建对话消息
            messages = []
            
            # 添加系统提示
            if self.config.system_prompt:
                messages.append({
                    "role": "system",
                    "content": self.config.system_prompt
                })
            
            # 添加记忆上下文
            memory_context = self.get_memory_context()
            for memory in memory_context[-5:]:  # 最近5轮对话
                if "user_input" in memory:
                    messages.append({"role": "user", "content": memory["user_input"]})
                if "assistant_response" in memory:
                    messages.append({"role": "assistant", "content": memory["assistant_response"]})
            
            # 添加当前输入
            if isinstance(input_data, str):
                user_input = input_data
                messages.append({"role": "user", "content": user_input})
            elif isinstance(input_data, dict) and "message" in input_data:
                user_input = input_data["message"]
                messages.append({"role": "user", "content": user_input})
            elif isinstance(input_data, list):
                # 直接使用消息列表
                messages.extend(input_data)
                user_input = input_data[-1].get("content", "") if input_data else ""
            else:
                user_input = str(input_data)
                messages.append({"role": "user", "content": user_input})
            
            # 调用AI服务
            provider = self.ai_factory.create_provider(self.config.provider)
            response = await provider.process(
                AIServiceType.CONVERSATION,
                messages,
                {
                    "model": self.config.model,
                    "temperature": self.config.temperature,
                    "max_tokens": self.config.max_tokens
                }
            )
            
            # 提取回复
            if hasattr(response, 'response') and hasattr(response.response, 'content'):
                assistant_response = response.response.content
            elif isinstance(response, dict) and 'response' in response:
                assistant_response = response['response'].get('content', str(response))
            else:
                assistant_response = str(response)
            
            # 添加到记忆
            self.add_to_memory({
                "user_input": user_input,
                "assistant_response": assistant_response,
                "timestamp": context.get("timestamp") if context else None,
                "intent": self._extract_intent(user_input),
                "sentiment": self._analyze_sentiment(user_input)
            })
            
            return SmartDataObject({
                "success": True,
                "agent_type": "conversation_manager",
                "response": assistant_response,
                "intent": self._extract_intent(user_input),
                "sentiment": self._analyze_sentiment(user_input),
                "conversation_id": context.get("conversation_id") if context else None,
                "memory_size": len(self.memory) if self.memory else 0
            })
            
        except Exception as e:
            self.logger.error(f"对话管理失败: {e}")
            return SmartDataObject({
                "success": False,
                "error": str(e),
                "agent_type": "conversation_manager"
            })
    
    def _extract_intent(self, text: str) -> str:
        """简单的意图识别"""
        text_lower = text.lower()
        if any(word in text_lower for word in ["问", "什么", "如何", "怎么", "为什么"]):
            return "question"
        elif any(word in text_lower for word in ["请", "帮", "能否", "可以"]):
            return "request"
        elif any(word in text_lower for word in ["谢谢", "感谢", "好的", "明白"]):
            return "acknowledgment"
        else:
            return "general"
    
    def _analyze_sentiment(self, text: str) -> str:
        """简单的情感分析"""
        positive_words = ["好", "棒", "喜欢", "满意", "开心", "高兴"]
        negative_words = ["不好", "差", "讨厌", "不满", "生气", "难过"]
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count > negative_count:
            return "positive"
        elif negative_count > positive_count:
            return "negative"
        else:
            return "neutral"


class DataAnalystAgent(BaseAgent):
    """数据分析Agent"""
    
    def __init__(self, config: AgentConfig = None):
        if config is None:
            config = AgentConfig(
                agent_type=AgentType.DATA_ANALYST,
                name="DataAnalyst",
                description="专业的数据分析Agent，支持数据洞察、趋势分析、报告生成",
                system_prompt="""你是一个专业的数据分析师。你的职责是：
1. 分析数据中的模式和趋势
2. 提供数据洞察和建议
3. 生成清晰的分析报告
4. 识别异常和关键指标
5. 提供可行的业务建议""",
                capabilities=["data_analysis", "trend_analysis", "report_generation", "anomaly_detection"]
            )
        super().__init__(config)
    
    async def process(self, input_data: Any, context: Dict = None) -> SmartDataObject:
        """处理数据分析请求"""
        try:
            # 构建分析提示
            analysis_prompt = self._build_analysis_prompt(input_data, context)
            
            # 调用AI服务
            provider = self.ai_factory.create_provider(self.config.provider)
            response = await provider.process(
                AIServiceType.TEXT_ANALYSIS,
                analysis_prompt,
                {
                    "model": self.config.model,
                    "temperature": 0.3,  # 分析任务使用较低温度
                    "max_tokens": self.config.max_tokens
                }
            )
            
            # 提取分析结果
            if hasattr(response, 'result'):
                analysis_result = response.result
            elif isinstance(response, dict) and 'result' in response:
                analysis_result = response['result']
            else:
                analysis_result = str(response)
            
            return SmartDataObject({
                "success": True,
                "agent_type": "data_analyst",
                "analysis": analysis_result,
                "data_summary": self._summarize_data(input_data),
                "insights": self._extract_insights(analysis_result),
                "recommendations": self._generate_recommendations(analysis_result)
            })
            
        except Exception as e:
            self.logger.error(f"数据分析失败: {e}")
            return SmartDataObject({
                "success": False,
                "error": str(e),
                "agent_type": "data_analyst"
            })
    
    def _build_analysis_prompt(self, data: Any, context: Dict = None) -> str:
        """构建分析提示"""
        prompt = f"{self.config.system_prompt}\n\n"
        prompt += "请分析以下数据：\n"
        
        if isinstance(data, dict):
            prompt += f"数据类型: 字典对象\n"
            prompt += f"数据内容: {str(data)[:1000]}...\n"
        elif isinstance(data, list):
            prompt += f"数据类型: 列表，包含{len(data)}个项目\n"
            prompt += f"样本数据: {str(data[:5])}...\n"
        else:
            prompt += f"数据内容: {str(data)[:1000]}...\n"
        
        if context:
            prompt += f"\n上下文信息: {context}\n"
        
        prompt += "\n请提供详细的分析报告，包括：\n"
        prompt += "1. 数据概览\n2. 关键发现\n3. 趋势分析\n4. 异常检测\n5. 业务建议"
        
        return prompt
    
    def _summarize_data(self, data: Any) -> Dict:
        """数据摘要"""
        if isinstance(data, dict):
            return {
                "type": "dictionary",
                "keys_count": len(data.keys()),
                "sample_keys": list(data.keys())[:5]
            }
        elif isinstance(data, list):
            return {
                "type": "list",
                "length": len(data),
                "sample_items": data[:3] if data else []
            }
        else:
            return {
                "type": type(data).__name__,
                "length": len(str(data)),
                "preview": str(data)[:100]
            }
    
    def _extract_insights(self, analysis: str) -> List[str]:
        """提取洞察"""
        # 简单的洞察提取逻辑
        insights = []
        lines = analysis.split('\n')
        for line in lines:
            if any(keyword in line.lower() for keyword in ["发现", "趋势", "模式", "异常", "关键"]):
                insights.append(line.strip())
        return insights[:5]  # 最多5个洞察
    
    def _generate_recommendations(self, analysis: str) -> List[str]:
        """生成建议"""
        # 简单的建议提取逻辑
        recommendations = []
        lines = analysis.split('\n')
        for line in lines:
            if any(keyword in line.lower() for keyword in ["建议", "推荐", "应该", "可以", "需要"]):
                recommendations.append(line.strip())
        return recommendations[:3]  # 最多3个建议


class ContentGeneratorAgent(BaseAgent):
    """内容生成Agent"""
    
    def __init__(self, config: AgentConfig = None):
        if config is None:
            config = AgentConfig(
                agent_type=AgentType.CONTENT_GENERATOR,
                name="ContentGenerator",
                description="专业的内容生成Agent，支持文章、报告、创意内容生成",
                system_prompt="""你是一个专业的内容创作者。你的职责是：
1. 根据要求生成高质量的内容
2. 确保内容结构清晰、逻辑合理
3. 适应不同的写作风格和语调
4. 提供创意和原创性内容
5. 优化内容的可读性和吸引力""",
                capabilities=["content_generation", "creative_writing", "technical_writing", "copywriting"]
            )
        super().__init__(config)
    
    async def process(self, input_data: Any, context: Dict = None) -> SmartDataObject:
        """处理内容生成请求"""
        try:
            # 构建生成提示
            generation_prompt = self._build_generation_prompt(input_data, context)
            
            # 调用AI服务
            provider = self.ai_factory.create_provider(self.config.provider)
            response = await provider.process(
                AIServiceType.TEXT_GENERATION,
                generation_prompt,
                {
                    "model": self.config.model,
                    "temperature": self.config.temperature,
                    "max_tokens": self.config.max_tokens
                }
            )
            
            # 提取生成内容
            if hasattr(response, 'generated_text'):
                generated_content = response.generated_text
            elif isinstance(response, dict) and 'generated_text' in response:
                generated_content = response['generated_text']
            else:
                generated_content = str(response)
            
            return SmartDataObject({
                "success": True,
                "agent_type": "content_generator",
                "content": generated_content,
                "content_type": context.get("content_type", "general") if context else "general",
                "word_count": len(generated_content.split()),
                "style": context.get("style", "default") if context else "default"
            })
            
        except Exception as e:
            self.logger.error(f"内容生成失败: {e}")
            return SmartDataObject({
                "success": False,
                "error": str(e),
                "agent_type": "content_generator"
            })
    
    def _build_generation_prompt(self, data: Any, context: Dict = None) -> str:
        """构建生成提示"""
        prompt = f"{self.config.system_prompt}\n\n"
        
        if isinstance(data, dict):
            if "topic" in data:
                prompt += f"主题: {data['topic']}\n"
            if "requirements" in data:
                prompt += f"要求: {data['requirements']}\n"
            if "style" in data:
                prompt += f"风格: {data['style']}\n"
            if "length" in data:
                prompt += f"长度: {data['length']}\n"
        else:
            prompt += f"生成内容要求: {str(data)}\n"
        
        if context:
            if "content_type" in context:
                prompt += f"内容类型: {context['content_type']}\n"
            if "target_audience" in context:
                prompt += f"目标受众: {context['target_audience']}\n"
            if "tone" in context:
                prompt += f"语调: {context['tone']}\n"
        
        prompt += "\n请生成符合要求的高质量内容："
        
        return prompt


class MultimodalProcessorAgent(BaseAgent):
    """多模态处理Agent"""
    
    def __init__(self, config: AgentConfig = None):
        if config is None:
            config = AgentConfig(
                agent_type=AgentType.MULTIMODAL_PROCESSOR,
                name="MultimodalProcessor",
                description="多模态处理Agent，支持文本、图像、音频的综合处理",
                system_prompt="""你是一个多模态处理专家。你的职责是：
1. 处理和分析多种类型的数据（文本、图像、音频）
2. 提供跨模态的理解和分析
3. 生成多模态内容描述
4. 识别不同模态间的关联
5. 提供综合性的处理结果""",
                capabilities=["multimodal_analysis", "image_analysis", "audio_analysis", "cross_modal_understanding"]
            )
        super().__init__(config)
    
    async def process(self, input_data: Any, context: Dict = None) -> SmartDataObject:
        """处理多模态数据"""
        try:
            # 分析输入数据类型
            data_types = self._analyze_data_types(input_data)
            
            # 根据数据类型选择处理策略
            if "image" in data_types:
                result = await self._process_image_data(input_data, context)
            elif "audio" in data_types:
                result = await self._process_audio_data(input_data, context)
            elif "text" in data_types:
                result = await self._process_text_data(input_data, context)
            else:
                result = await self._process_mixed_data(input_data, context)
            
            return SmartDataObject({
                "success": True,
                "agent_type": "multimodal_processor",
                "data_types": data_types,
                "processing_result": result,
                "modalities_detected": len(data_types)
            })
            
        except Exception as e:
            self.logger.error(f"多模态处理失败: {e}")
            return SmartDataObject({
                "success": False,
                "error": str(e),
                "agent_type": "multimodal_processor"
            })
    
    def _analyze_data_types(self, data: Any) -> List[str]:
        """分析数据类型"""
        types = []
        
        if isinstance(data, dict):
            if "image" in data or "image_url" in data or "image_path" in data:
                types.append("image")
            if "audio" in data or "audio_url" in data or "audio_path" in data:
                types.append("audio")
            if "text" in data or "content" in data:
                types.append("text")
        elif isinstance(data, str):
            if data.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp')):
                types.append("image")
            elif data.lower().endswith(('.mp3', '.wav', '.flac', '.aac')):
                types.append("audio")
            else:
                types.append("text")
        else:
            types.append("unknown")
        
        return types
    
    async def _process_image_data(self, data: Any, context: Dict = None) -> Dict:
        """处理图像数据"""
        # 这里可以集成图像分析服务
        return {
            "type": "image_analysis",
            "description": "图像分析功能需要集成专门的图像处理服务",
            "placeholder": True
        }
    
    async def _process_audio_data(self, data: Any, context: Dict = None) -> Dict:
        """处理音频数据"""
        # 这里可以集成语音识别服务
        return {
            "type": "audio_analysis",
            "description": "音频分析功能需要集成专门的语音处理服务",
            "placeholder": True
        }
    
    async def _process_text_data(self, data: Any, context: Dict = None) -> Dict:
        """处理文本数据"""
        # 使用现有的文本分析功能
        provider = self.ai_factory.create_provider(self.config.provider)
        response = await provider.process(
            AIServiceType.TEXT_ANALYSIS,
            data,
            {"model": self.config.model}
        )
        
        return {
            "type": "text_analysis",
            "result": response.result if hasattr(response, 'result') else str(response)
        }
    
    async def _process_mixed_data(self, data: Any, context: Dict = None) -> Dict:
        """处理混合数据"""
        return {
            "type": "mixed_analysis",
            "description": "混合数据处理功能",
            "data_summary": str(data)[:200]
        }


class AgentOrchestrator:
    """Agent编排器"""
    
    def __init__(self):
        self.agents = {}
        self.logger = logging.getLogger(f"{__name__}.AgentOrchestrator")
        
        # 注册默认Agent
        self.register_agent("conversation", ConversationManagerAgent())
        self.register_agent("data_analyst", DataAnalystAgent())
        self.register_agent("content_generator", ContentGeneratorAgent())
        self.register_agent("multimodal", MultimodalProcessorAgent())
    
    def register_agent(self, name: str, agent: BaseAgent):
        """注册Agent"""
        self.agents[name] = agent
        self.logger.info(f"注册Agent: {name}")
    
    def get_agent(self, name: str) -> Optional[BaseAgent]:
        """获取Agent"""
        return self.agents.get(name)
    
    def list_agents(self) -> List[str]:
        """列出所有Agent"""
        return list(self.agents.keys())
    
    async def process_with_agent(self, agent_name: str, input_data: Any, context: Dict = None) -> SmartDataObject:
        """使用指定Agent处理数据"""
        agent = self.get_agent(agent_name)
        if not agent:
            return SmartDataObject({
                "success": False,
                "error": f"Agent '{agent_name}' not found",
                "available_agents": self.list_agents()
            })
        
        return await agent.process(input_data, context)


# 全局Agent编排器实例
global_agent_orchestrator = AgentOrchestrator()
