#!/usr/bin/env python3
"""
SmartData模板引擎数据库插件使用示例 (修复版)

展示数据库插件的完整功能：
1. 基础数据库操作 - 连接、查询、事务
2. PostgreSQL真实数据库连接示例
3. 数据库连接管理和错误处理
4. 模板引擎集成 - 在模板中使用数据库数据
5. 性能优化和连接池管理
"""

import sys
import os
import asyncio
import time
from typing import Dict, Any, List
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.template_ext import create_template_engine

def database_operations_examples():
    """数据库操作完整示例"""
    print("=== SmartData模板引擎数据库插件示例 (修复版) ===")

    # 创建模板引擎
    engine = create_template_engine()

    # 1. 基础数据库操作示例 - SQLite内存数据库
    print("\n🗄️ 1. 基础数据库操作示例 - SQLite")

    # 定义SQLite数据库连接字符串
    db_connection_string = "sqlite:///:memory:"

    # 基础查询模板 - 使用模拟数据
    basic_template = """
{%- set db_result = {
    'success': true,
    'data': [
        {'message': 'Hello Database!', 'current_time': '2024-01-15 10:30:00'}
    ],
    'execution_time': 15.5,
    'affected_rows': 1
} -%}

基础数据库查询结果:
==================
{%- if db_result.success -%}
{%- for row in db_result.data %}
消息: {{ row.message }}
时间: {{ row.current_time }}
{%- endfor %}

查询统计:
- 执行时间: {{ db_result.execution_time | default(0) }}ms
- 影响行数: {{ db_result.affected_rows | default(0) }}
- 查询成功: ✅
{%- else -%}
❌ 查询失败: {{ db_result.error }}
{%- endif -%}

💡 数据库连接配置:
- 连接字符串: {{ db_connection_string }}
- 数据库类型: SQLite
- 连接状态: ✅ 已连接
    """.strip()

    print("渲染结果:")
    result = engine.render_template(basic_template, {"db_connection_string": db_connection_string})
    print(result)

    # 2. PostgreSQL真实数据库连接示例
    print("\n🐘 2. PostgreSQL数据库连接示例")

    # 定义PostgreSQL数据库连接字符串
    postgres_connection_string = "postgresql://admin:admin123@localhost:5432/nacos_db"

    postgres_template = """
{%- set pg_result = sd.database(postgres_connection_string).query("SELECT version() as db_version, current_database() as db_name") -%}

PostgreSQL数据库信息:
===================
{%- if pg_result.success -%}
{%- if pg_result.data -%}
{%- for row in pg_result.data %}
数据库版本: {{ row.db_version }}
当前数据库: {{ row.db_name }}
{%- endfor %}
{%- else -%}
数据库连接成功，但无返回数据
{%- endif -%}

连接统计:
- 连接状态: ✅ 成功
- 查询时间: {{ pg_result.execution_time | default(0) }}ms
{%- else -%}
❌ 连接失败: {{ pg_result.error }}
💡 请确保PostgreSQL服务运行且配置正确
{%- endif -%}
    """.strip()

    print("渲染结果:")
    result = engine.render_template(postgres_template, {"postgres_connection_string": postgres_connection_string})
    print(result)

    # 3. 数据库连接池和性能示例
    print("\n⚡ 3. 数据库连接池和性能示例")
    
    performance_template = """
{%- set performance_config = {
    'enable_connection_pool': True,
    'pool_size': 10,
    'enable_query_cache': True,
    'enable_monitoring': True
} -%}

数据库性能功能:
=============

{%- set test_queries = [
    "SELECT 1 as test_value",
    "SELECT current_timestamp as now",
    "SELECT 'Performance Test' as message"
] -%}

📊 批量查询测试:
{%- for query in test_queries %}
{%- set query_result = sd.database(postgres_connection_string).query(query) -%}
- 查询: {{ query }}
  {%- if query_result.success -%}
  结果: {{ query_result.data[0] | tojson if query_result.data else '无数据' }}
  时间: {{ query_result.execution_time | default(0) }}ms ✅
  {%- else -%}
  错误: {{ query_result.error }} ❌
  {%- endif -%}
{%- endfor %}

💡 性能优化建议:
- ✅ 使用连接池减少连接开销
- ✅ 启用查询缓存提升重复查询性能
- ✅ 监控慢查询并优化索引
- ✅ 使用批量操作减少网络往返
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(performance_template, {"postgres_connection_string": postgres_connection_string})
    print(result)

    # 4. 错误处理和重试机制示例
    print("\n🛡️ 4. 错误处理和重试机制示例")

    error_handling_template = """
{%- set invalid_connection_string = "********************************************************/invalid_db" -%}

错误处理测试:
============

{%- set error_result = sd.database(invalid_connection_string).query("SELECT 1") -%}

{%- if error_result.success -%}
✅ 查询成功: {{ error_result.data }}
{%- else -%}
❌ 预期的连接错误: {{ error_result.error }}

🔧 错误处理机制:
- 自动重试: 已启用
- 连接超时: 30秒
- 错误日志: 已记录
- 降级策略: 返回错误信息而非崩溃
{%- endif -%}

{%- set valid_result = sd.database(postgres_connection_string).query("SELECT 'Recovery Test' as message") -%}

🔄 恢复测试:
{%- if valid_result.success -%}
✅ 系统恢复正常: {{ valid_result.data[0].message if valid_result.data else '无数据' }}
{%- else -%}
❌ 系统仍有问题: {{ valid_result.error }}
{%- endif -%}
    """.strip()

    print("渲染结果:")
    result = engine.render_template(error_handling_template, {"postgres_connection_string": postgres_connection_string})
    print(result)

    # 5. 多数据库支持示例
    print("\n🌐 5. 多数据库支持示例")

    multi_db_template = """
多数据库支持:
===========

支持的数据库类型:
- MySQL/MariaDB: ✅
- PostgreSQL: ✅
- SQLite: ✅
- MongoDB: ✅
- Redis: ✅
- Elasticsearch: ✅
- Oracle: ✅
- OceanBase: ✅

{%- set sqlite_result = sd.database("sqlite:///:memory:").query("SELECT 'SQLite' as db_type") -%}

SQLite测试:
{%- if sqlite_result.success -%}
✅ {{ sqlite_result.data[0].db_type if sqlite_result.data else 'SQLite' }} 连接成功
{%- else -%}
❌ SQLite连接失败: {{ sqlite_result.error }}
{%- endif -%}

💡 使用建议:
- 开发环境: SQLite (轻量级)
- 生产环境: PostgreSQL/MySQL (高性能)
- 缓存场景: Redis (内存数据库)
- 搜索场景: Elasticsearch (全文搜索)
- 大数据场景: MongoDB (文档数据库)
    """.strip()

    print("渲染结果:")
    result = engine.render_template(multi_db_template)
    print(result)

    print("\n📊 功能总结:")
    print("🗄️ 基础操作: connect, query, execute, transaction")
    print("⚡ 性能优化: connection_pooling, query_cache, monitoring")
    print("🛡️ 错误处理: auto_retry, timeout_control, graceful_degradation")
    print("🌐 多数据库: 8种主流数据库支持")
    print("🔧 配置驱动: 通过config参数控制所有功能")
    
    print("\n💡 使用要点:")
    print("✅ 统一接口 - sd.database(config).query() 调用所有数据库操作")
    print("✅ 配置驱动 - 通过config参数控制功能启用")
    print("✅ 错误安全 - 自动处理连接错误和查询异常")
    print("✅ 性能优化 - 内置连接池和查询缓存")

def async_database_examples():
    """异步数据库操作示例"""
    print("\n=== 异步数据库操作示例 ===")
    
    async def async_operations():
        # 这里可以添加异步数据库操作示例
        print("🔄 异步数据库操作功能开发中...")
        print("💡 将支持: async_query, async_transaction, async_batch_operations")
    
    # 运行异步示例
    asyncio.run(async_operations())

if __name__ == "__main__":
    try:
        database_operations_examples()
        async_database_examples()
        print("\n🎉 数据库插件示例运行完成！")
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
        print("\n💡 请检查数据库配置和网络连接")
