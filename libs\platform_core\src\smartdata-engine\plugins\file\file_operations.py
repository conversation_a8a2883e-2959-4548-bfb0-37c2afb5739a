"""
文件操作工具

提供文件操作、压缩、转换、监控等功能
"""

import logging
import os
import shutil
import zipfile
import tarfile
from typing import Any, Dict, List, Optional
from pathlib import Path


class FileOperationManager:
    """文件操作管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.FileOperationManager")
    
    def copy_file(self, source: str, destination: str) -> bool:
        """复制文件"""
        try:
            shutil.copy2(source, destination)
            self.logger.info(f"文件复制成功: {source} -> {destination}")
            return True
        except Exception as e:
            self.logger.error(f"文件复制失败: {e}")
            return False
    
    def move_file(self, source: str, destination: str) -> bool:
        """移动文件"""
        try:
            shutil.move(source, destination)
            self.logger.info(f"文件移动成功: {source} -> {destination}")
            return True
        except Exception as e:
            self.logger.error(f"文件移动失败: {e}")
            return False
    
    def delete_file(self, file_path: str) -> bool:
        """删除文件"""
        try:
            os.remove(file_path)
            self.logger.info(f"文件删除成功: {file_path}")
            return True
        except Exception as e:
            self.logger.error(f"文件删除失败: {e}")
            return False
    
    def create_directory(self, dir_path: str) -> bool:
        """创建目录"""
        try:
            os.makedirs(dir_path, exist_ok=True)
            self.logger.info(f"目录创建成功: {dir_path}")
            return True
        except Exception as e:
            self.logger.error(f"目录创建失败: {e}")
            return False


class FileCompressor:
    """文件压缩器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.FileCompressor")
    
    def compress_to_zip(self, source_path: str, output_path: str) -> bool:
        """压缩为ZIP文件"""
        try:
            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                if os.path.isfile(source_path):
                    zipf.write(source_path, os.path.basename(source_path))
                elif os.path.isdir(source_path):
                    for root, dirs, files in os.walk(source_path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, source_path)
                            zipf.write(file_path, arcname)
            
            self.logger.info(f"ZIP压缩成功: {source_path} -> {output_path}")
            return True
        except Exception as e:
            self.logger.error(f"ZIP压缩失败: {e}")
            return False
    
    def extract_zip(self, zip_path: str, extract_to: str) -> bool:
        """解压ZIP文件"""
        try:
            with zipfile.ZipFile(zip_path, 'r') as zipf:
                zipf.extractall(extract_to)
            
            self.logger.info(f"ZIP解压成功: {zip_path} -> {extract_to}")
            return True
        except Exception as e:
            self.logger.error(f"ZIP解压失败: {e}")
            return False
    
    def compress_to_tar(self, source_path: str, output_path: str, compression: str = 'gz') -> bool:
        """压缩为TAR文件"""
        try:
            mode = f'w:{compression}' if compression else 'w'
            with tarfile.open(output_path, mode) as tarf:
                tarf.add(source_path, arcname=os.path.basename(source_path))
            
            self.logger.info(f"TAR压缩成功: {source_path} -> {output_path}")
            return True
        except Exception as e:
            self.logger.error(f"TAR压缩失败: {e}")
            return False


class FileConverter:
    """文件转换器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.FileConverter")
    
    def convert_text_encoding(self, file_path: str, from_encoding: str, to_encoding: str) -> bool:
        """转换文本文件编码"""
        try:
            with open(file_path, 'r', encoding=from_encoding) as f:
                content = f.read()
            
            with open(file_path, 'w', encoding=to_encoding) as f:
                f.write(content)
            
            self.logger.info(f"编码转换成功: {file_path} ({from_encoding} -> {to_encoding})")
            return True
        except Exception as e:
            self.logger.error(f"编码转换失败: {e}")
            return False
    
    def convert_line_endings(self, file_path: str, to_format: str = 'unix') -> bool:
        """转换行结束符"""
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            
            # 统一为LF
            content = content.replace(b'\r\n', b'\n').replace(b'\r', b'\n')
            
            # 根据目标格式转换
            if to_format == 'windows':
                content = content.replace(b'\n', b'\r\n')
            elif to_format == 'mac':
                content = content.replace(b'\n', b'\r')
            # unix格式保持LF不变
            
            with open(file_path, 'wb') as f:
                f.write(content)
            
            self.logger.info(f"行结束符转换成功: {file_path} -> {to_format}")
            return True
        except Exception as e:
            self.logger.error(f"行结束符转换失败: {e}")
            return False


class FileMonitor:
    """文件监控器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.FileMonitor")
        self.watched_paths: Dict[str, Dict[str, Any]] = {}
    
    def add_watch(self, path: str, callback: callable = None) -> bool:
        """添加文件监控"""
        try:
            if not os.path.exists(path):
                self.logger.warning(f"监控路径不存在: {path}")
                return False
            
            self.watched_paths[path] = {
                'callback': callback,
                'last_modified': os.path.getmtime(path),
                'size': os.path.getsize(path) if os.path.isfile(path) else 0
            }
            
            self.logger.info(f"添加文件监控: {path}")
            return True
        except Exception as e:
            self.logger.error(f"添加文件监控失败: {e}")
            return False
    
    def remove_watch(self, path: str) -> bool:
        """移除文件监控"""
        try:
            if path in self.watched_paths:
                del self.watched_paths[path]
                self.logger.info(f"移除文件监控: {path}")
                return True
            else:
                self.logger.warning(f"监控路径不存在: {path}")
                return False
        except Exception as e:
            self.logger.error(f"移除文件监控失败: {e}")
            return False
    
    def check_changes(self) -> List[Dict[str, Any]]:
        """检查文件变化"""
        changes = []
        
        for path, info in self.watched_paths.items():
            try:
                if not os.path.exists(path):
                    changes.append({
                        'path': path,
                        'event': 'deleted',
                        'timestamp': time.time()
                    })
                    continue
                
                current_mtime = os.path.getmtime(path)
                current_size = os.path.getsize(path) if os.path.isfile(path) else 0
                
                if current_mtime != info['last_modified'] or current_size != info['size']:
                    changes.append({
                        'path': path,
                        'event': 'modified',
                        'timestamp': current_mtime,
                        'old_size': info['size'],
                        'new_size': current_size
                    })
                    
                    # 更新记录
                    info['last_modified'] = current_mtime
                    info['size'] = current_size
                    
                    # 调用回调函数
                    if info['callback']:
                        try:
                            info['callback'](path, 'modified')
                        except Exception as e:
                            self.logger.error(f"监控回调函数执行失败: {e}")
                
            except Exception as e:
                self.logger.error(f"检查文件变化失败 {path}: {e}")
        
        return changes
