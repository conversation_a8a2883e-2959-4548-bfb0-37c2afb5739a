#!/usr/bin/env python3
"""
自然语法扩展

提供更贴近Python原生语法的模板功能：
1. 自然函数定义语法
2. 简化的变量赋值
3. Python风格的代码块
"""

import ast
import re
import logging
from typing import Any, Dict, List, Optional, Callable
from jinja2 import nodes
from jinja2.ext import Extension
from jinja2.exceptions import TemplateSyntaxError


class NaturalSyntaxExtension(Extension):
    """
    自然语法扩展
    
    支持更自然的Python风格语法：
    
    {% function calculate_score(user) %}
       base_score = user.commits * 10
       bonus = api("https://api.example.com/bonus/" + user.id).get("bonus", 0)
       return base_score + bonus
    {% endfunction %}
    """
    
    tags = {'function', 'pycode'}
    
    def __init__(self, environment):
        super().__init__(environment)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 注册全局函数
        environment.globals['api'] = self._api_helper
        environment.globals['xpath'] = self._xpath_helper
        environment.globals['jsonpath'] = self._jsonpath_helper
    
    def parse(self, parser):
        """解析标签"""
        lineno = next(parser.stream).lineno
        tag_name = parser.stream.current.value
        
        if tag_name == 'function':
            return self._parse_function(parser, lineno)
        elif tag_name == 'pycode':
            return self._parse_pycode(parser, lineno)
        
        raise TemplateSyntaxError(f"未知标签: {tag_name}", lineno)
    
    def _parse_function(self, parser, lineno):
        """
        解析函数定义
        
        语法: {% function name(params) %} ... {% endfunction %}
        """
        # 解析函数签名
        signature = self._parse_function_signature(parser)
        
        # 解析函数体
        body_nodes = []
        while parser.stream.current.test_any('name:endfunction') is False:
            if parser.stream.current.test('block_end'):
                next(parser.stream)
                continue
            
            # 解析Python风格的语句
            stmt = self._parse_python_statement(parser)
            if stmt:
                body_nodes.append(stmt)
        
        # 消费endfunction标签
        parser.expect('name:endfunction')
        
        # 创建函数定义节点
        return self._create_function_node(signature, body_nodes, lineno)
    
    def _parse_function_signature(self, parser):
        """解析函数签名"""
        # 期望函数名
        func_name = parser.expect('name').value
        
        # 期望左括号
        parser.expect('lparen')
        
        # 解析参数列表
        params = []
        while not parser.stream.current.test('rparen'):
            if params:
                parser.expect('comma')
            
            param_name = parser.expect('name').value
            params.append(param_name)
        
        # 期望右括号
        parser.expect('rparen')
        
        return {
            'name': func_name,
            'params': params
        }
    
    def _parse_python_statement(self, parser):
        """
        解析Python风格的语句
        
        支持：
        - 变量赋值: var = expression
        - 函数调用: func(args)
        - return语句: return expression
        """
        # 跳过空行和注释
        if parser.stream.current.test('block_end'):
            return None
        
        # 检查是否是return语句
        if parser.stream.current.test('name:return'):
            return self._parse_return_statement(parser)
        
        # 检查是否是赋值语句
        if self._is_assignment_statement(parser):
            return self._parse_assignment_statement(parser)
        
        # 其他表达式语句
        return self._parse_expression_statement(parser)
    
    def _is_assignment_statement(self, parser):
        """检查是否是赋值语句"""
        # 简单检查：name = ...
        current = parser.stream.current
        if current.test('name'):
            # 向前看一个token
            next_token = parser.stream.look()
            return next_token.test('assign')
        return False
    
    def _parse_assignment_statement(self, parser):
        """解析赋值语句"""
        # 变量名
        var_name = parser.expect('name').value
        
        # 等号
        parser.expect('assign')
        
        # 表达式
        expr = parser.parse_expression()
        
        # 创建赋值节点
        return nodes.Assign(
            target=nodes.Name(var_name, 'store'),
            node=expr
        )
    
    def _parse_return_statement(self, parser):
        """解析return语句"""
        parser.expect('name:return')
        
        # 解析返回表达式
        expr = parser.parse_expression()
        
        # 创建return节点（使用Output节点模拟）
        return nodes.Output([expr])
    
    def _parse_expression_statement(self, parser):
        """解析表达式语句"""
        expr = parser.parse_expression()
        return nodes.Output([expr])
    
    def _create_function_node(self, signature, body_nodes, lineno):
        """创建函数定义节点"""
        # 创建函数体
        body = body_nodes if body_nodes else [nodes.Const('')]
        
        # 创建宏节点（Jinja2中最接近函数的概念）
        macro = nodes.Macro(
            name=signature['name'],
            args=[nodes.Name(param, 'param') for param in signature['params']],
            defaults=[],
            body=body
        )
        macro.set_lineno(lineno)
        
        return macro
    
    def _api_helper(self, url: str, method: str = 'GET', **kwargs) -> Dict:
        """
        API调用助手
        
        在模板中可以直接使用: api("https://api.example.com/data")
        """
        # 这里可以集成实际的HTTP客户端
        # 目前返回模拟数据
        return {
            'url': url,
            'method': method,
            'status': 'success',
            'data': {'bonus': 100}  # 模拟数据
        }
    
    def _xpath_helper(self, data: Any, path: str) -> Any:
        """
        XPath助手
        
        在模板中可以直接使用: xpath(data, "//user[@id='123']")
        """
        try:
            # 导入统一数据增强器
            from ...core.unified_data_enhancement import enhance_data
            
            enhanced_data = enhance_data(data)
            if hasattr(enhanced_data, 'get'):
                return enhanced_data.get(path)
            
            return None
        except Exception as e:
            self.logger.error(f"XPath查询失败: {path}, 错误: {e}")
            return None
    
    def _jsonpath_helper(self, data: Any, path: str) -> Any:
        """
        JSONPath助手
        
        在模板中可以直接使用: jsonpath(data, "$.user.name")
        """
        try:
            # 导入统一数据增强器
            from ...core.unified_data_enhancement import enhance_data
            
            enhanced_data = enhance_data(data)
            if hasattr(enhanced_data, 'get'):
                return enhanced_data.get(path)
            
            return None
        except Exception as e:
            self.logger.error(f"JSONPath查询失败: {path}, 错误: {e}")
            return None


class PythonStyleTemplateEngine:
    """
    Python风格模板引擎
    
    提供更自然的模板语法支持
    """
    
    def __init__(self, base_engine=None):
        self.base_engine = base_engine
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def preprocess_template(self, template: str) -> str:
        """
        预处理模板，转换自然语法为Jinja2语法
        
        转换规则：
        1. 函数定义中的Python语句转换为Jinja2语法
        2. 简化变量赋值语法
        """
        # 处理函数定义块
        template = self._process_function_blocks(template)
        
        # 处理其他Python风格语法
        template = self._process_python_syntax(template)
        
        return template
    
    def _process_function_blocks(self, template: str) -> str:
        """处理函数定义块"""
        # 使用正则表达式找到函数块
        function_pattern = r'{% function\s+(\w+)\s*\((.*?)\)\s*%}(.*?){% endfunction %}'
        
        def replace_function(match):
            func_name = match.group(1)
            params = match.group(2)
            body = match.group(3)
            
            # 处理函数体中的Python语句
            processed_body = self._process_function_body(body)
            
            return f'{{% macro {func_name}({params}) %}}{processed_body}{{% endmacro %}}'
        
        return re.sub(function_pattern, replace_function, template, flags=re.DOTALL)
    
    def _process_function_body(self, body: str) -> str:
        """处理函数体中的Python语句"""
        lines = body.strip().split('\n')
        processed_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 处理赋值语句
            if '=' in line and not line.startswith('{%') and not line.startswith('{{'):
                # 转换为Jinja2的set语句
                var_name, expression = line.split('=', 1)
                var_name = var_name.strip()
                expression = expression.strip()
                processed_lines.append(f'{{% set {var_name} = {expression} %}}')
            
            # 处理return语句
            elif line.startswith('return '):
                expression = line[7:].strip()  # 去掉'return '
                processed_lines.append(f'{{{{ {expression} }}}}')
            
            # 其他语句保持原样
            else:
                processed_lines.append(line)
        
        return '\n'.join(processed_lines)
    
    def _process_python_syntax(self, template: str) -> str:
        """处理其他Python风格语法"""
        # 这里可以添加更多的语法转换规则
        return template
    
    def render(self, template: str, context: Dict[str, Any]) -> str:
        """渲染模板"""
        # 预处理模板
        processed_template = self.preprocess_template(template)
        
        # 使用基础引擎渲染
        if self.base_engine:
            return self.base_engine.render(processed_template, context)
        
        # 如果没有基础引擎，返回处理后的模板（用于调试）
        return processed_template


# 便利函数
def create_natural_syntax_engine(base_engine=None):
    """创建自然语法模板引擎"""
    return PythonStyleTemplateEngine(base_engine)
