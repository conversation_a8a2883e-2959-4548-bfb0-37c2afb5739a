"""
HTML文件数据适配器

支持HTML文件的同步和异步读写操作，包括表格数据提取和HTML生成
"""

from typing import Any, Dict, List, Optional, Union, AsyncIterator
import logging
import os
from pathlib import Path

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../'))

from core.unified_adapter import UnifiedDataAdapter

# 尝试导入HTML解析库
try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False
    BeautifulSoup = None

# 尝试导入异步文件操作库
try:
    import aiofiles
    AIOFILES_AVAILABLE = True
except ImportError:
    AIOFILES_AVAILABLE = False
    aiofiles = None


class HTMLAdapter(UnifiedDataAdapter):
    """
    HTML文件数据适配器
    
    支持HTML文件特有功能：
    - 同步和异步文件读写
    - HTML表格数据提取
    - CSS选择器查询
    - HTML模板生成
    - 数据到HTML表格转换
    """
    
    def __init__(self):
        super().__init__()
        if not BS4_AVAILABLE:
            self.logger.warning("beautifulsoup4未安装，HTML解析功能受限")
        if not AIOFILES_AVAILABLE:
            self.logger.warning("aiofiles未安装，异步文件操作功能受限")
        
        # 默认配置
        self.default_encoding = 'utf-8'
        self.default_parser = 'html.parser'
    
    def supported_types(self) -> List[str]:
        """返回支持的数据类型列表"""
        return [
            'html',
            'html_file',
            'htm',
            'htm_file',
            'web_page'
        ]
    
    def _is_supported_connection_string(self, connection_string: str) -> bool:
        """检查是否支持的连接字符串"""
        return (connection_string.endswith(('.html', '.htm')) or
                connection_string.startswith('file://'))
    
    def _is_supported_connection_object(self, connection: Any) -> bool:
        """检查是否支持的连接对象"""
        return (isinstance(connection, dict) and 
                ('file_path' in connection or 'path' in connection))
    
    def can_handle(self, data_source: Any) -> bool:
        """检查是否能处理指定的数据源"""
        if isinstance(data_source, str):
            return self._is_supported_connection_string(data_source)
        else:
            return self._is_supported_connection_object(data_source)
    
    def _build_operations(self) -> Dict[str, callable]:
        """构建HTML特有操作列表"""
        operations = {
            'read_html': self._sync_read_html,
            'write_html': self._sync_write_html,
            'extract_tables': self._sync_extract_tables,
            'extract_by_selector': self._sync_extract_by_selector,
        }
        
        # 添加异步操作
        async_ops = self.get_async_operations()
        for name, func in async_ops.items():
            operations[name] = func
        
        return operations
    
    def _parse_file_config(self, connection_source: Any) -> Dict[str, Any]:
        """解析文件配置"""
        if isinstance(connection_source, str):
            # 简单文件路径
            if connection_source.startswith('file://'):
                file_path = connection_source[7:]  # 移除 'file://'
            else:
                file_path = connection_source
            
            return {
                'file_path': file_path,
                'encoding': self.default_encoding,
                'parser': self.default_parser,
                'extract_tables': True,
                'css_selector': None
            }
        elif isinstance(connection_source, dict):
            # 详细配置对象
            config = {
                'file_path': connection_source.get('file_path') or connection_source.get('path'),
                'encoding': connection_source.get('encoding', self.default_encoding),
                'parser': connection_source.get('parser', self.default_parser),
                'extract_tables': connection_source.get('extract_tables', True),
                'css_selector': connection_source.get('css_selector')
            }
            
            return config
        else:
            raise ValueError(f"不支持的文件配置类型: {type(connection_source)}")
    
    # ========================================================================
    # 同步方法实现
    # ========================================================================
    
    def _sync_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """同步查询实现 - 读取HTML文件"""
        # 将SQL查询映射为HTML数据提取
        return self._sync_read_html(connection, params)
    
    def _sync_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """同步执行实现 - 写入HTML文件"""
        if params and 'data' in params:
            return self._sync_write_html(connection, params['data'])
        return 0
    
    def _sync_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """同步事务实现 - 批量文件操作"""
        results = []
        total_affected = 0
        
        for operation in operations:
            op_type = operation.get('type', 'read')
            data = operation.get('data')
            
            if op_type == 'read':
                result = self._sync_read_html(connection)
                results.append(result)
            elif op_type == 'write':
                affected = self._sync_write_html(connection, data)
                results.append(affected)
                total_affected += affected
            elif op_type == 'extract_tables':
                result = self._sync_extract_tables(connection)
                results.append(result)
        
        return {
            'results': results,
            'total_operations': len(operations),
            'total_affected': total_affected,
            'success': True
        }
    
    def _sync_read_html(self, connection: Any, params: Dict = None) -> List[Dict]:
        """同步读取HTML文件"""
        if not BS4_AVAILABLE:
            raise ImportError("beautifulsoup4未安装，无法解析HTML文件")
        
        config = self._parse_file_config(connection)
        file_path = config['file_path']
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"HTML文件不存在: {file_path}")
        
        try:
            with open(file_path, 'r', encoding=config['encoding']) as htmlfile:
                content = htmlfile.read()
                soup = BeautifulSoup(content, config['parser'])
                
                # 根据配置提取数据
                if config['css_selector']:
                    return self._extract_by_css_selector(soup, config['css_selector'])
                elif config['extract_tables']:
                    return self._extract_tables_from_soup(soup)
                else:
                    # 提取所有文本内容
                    text = soup.get_text(strip=True)
                    return [{'content': text, 'title': soup.title.string if soup.title else ''}]
            
        except Exception as e:
            self.logger.error(f"读取HTML文件失败 {file_path}: {e}")
            raise
    
    def _sync_write_html(self, connection: Any, data: List[Dict]) -> int:
        """同步写入HTML文件"""
        config = self._parse_file_config(connection)
        file_path = config['file_path']
        
        if not data:
            return 0
        
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path) or '.', exist_ok=True)
            
            # 生成HTML内容
            html_content = self._generate_html_table(data)
            
            with open(file_path, 'w', encoding=config['encoding']) as htmlfile:
                htmlfile.write(html_content)
            
            self.logger.info(f"成功写入HTML文件: {file_path}, 记录数: {len(data)}")
            return len(data)
            
        except Exception as e:
            self.logger.error(f"写入HTML文件失败 {file_path}: {e}")
            raise
    
    def _sync_extract_tables(self, connection: Any) -> List[Dict]:
        """同步提取HTML表格数据"""
        if not BS4_AVAILABLE:
            raise ImportError("beautifulsoup4未安装，无法解析HTML表格")
        
        config = self._parse_file_config(connection)
        file_path = config['file_path']
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"HTML文件不存在: {file_path}")
        
        try:
            with open(file_path, 'r', encoding=config['encoding']) as htmlfile:
                content = htmlfile.read()
                soup = BeautifulSoup(content, config['parser'])
                return self._extract_tables_from_soup(soup)
            
        except Exception as e:
            self.logger.error(f"提取HTML表格失败 {file_path}: {e}")
            raise
    
    def _sync_extract_by_selector(self, connection: Any, css_selector: str) -> List[Dict]:
        """同步通过CSS选择器提取数据"""
        if not BS4_AVAILABLE:
            raise ImportError("beautifulsoup4未安装，无法使用CSS选择器")
        
        config = self._parse_file_config(connection)
        file_path = config['file_path']
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"HTML文件不存在: {file_path}")
        
        try:
            with open(file_path, 'r', encoding=config['encoding']) as htmlfile:
                content = htmlfile.read()
                soup = BeautifulSoup(content, config['parser'])
                return self._extract_by_css_selector(soup, css_selector)
            
        except Exception as e:
            self.logger.error(f"CSS选择器提取失败 {file_path}: {e}")
            raise
    
    def _extract_tables_from_soup(self, soup: BeautifulSoup) -> List[Dict]:
        """从BeautifulSoup对象中提取表格数据"""
        tables = soup.find_all('table')
        all_rows = []
        
        for table_idx, table in enumerate(tables):
            rows = table.find_all('tr')
            
            # 提取表头
            headers = []
            if rows:
                header_row = rows[0]
                headers = [th.get_text(strip=True) for th in header_row.find_all(['th', 'td'])]
            
            # 提取数据行
            for row_idx, row in enumerate(rows[1:] if headers else rows):
                cells = row.find_all(['td', 'th'])
                row_data = {'table_index': table_idx, 'row_index': row_idx}
                
                for cell_idx, cell in enumerate(cells):
                    header = headers[cell_idx] if cell_idx < len(headers) else f'column_{cell_idx}'
                    row_data[header] = cell.get_text(strip=True)
                
                all_rows.append(row_data)
        
        return all_rows
    
    def _extract_by_css_selector(self, soup: BeautifulSoup, css_selector: str) -> List[Dict]:
        """通过CSS选择器提取数据"""
        elements = soup.select(css_selector)
        results = []
        
        for idx, element in enumerate(elements):
            result = {
                'index': idx,
                'tag': element.name,
                'text': element.get_text(strip=True),
                'attributes': dict(element.attrs) if element.attrs else {}
            }
            
            # 如果是链接，提取href
            if element.name == 'a' and 'href' in element.attrs:
                result['href'] = element.attrs['href']
            
            # 如果是图片，提取src
            if element.name == 'img' and 'src' in element.attrs:
                result['src'] = element.attrs['src']
            
            results.append(result)
        
        return results
    
    def _generate_html_table(self, data: List[Dict]) -> str:
        """生成HTML表格"""
        if not data:
            return "<html><body><p>无数据</p></body></html>"
        
        # 获取所有列名
        columns = set()
        for row in data:
            columns.update(row.keys())
        columns = sorted(list(columns))
        
        # 生成HTML
        html_parts = [
            "<!DOCTYPE html>",
            "<html>",
            "<head>",
            "<meta charset='utf-8'>",
            "<title>数据表格</title>",
            "<style>",
            "table { border-collapse: collapse; width: 100%; }",
            "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }",
            "th { background-color: #f2f2f2; }",
            "</style>",
            "</head>",
            "<body>",
            "<table>",
            "<thead>",
            "<tr>"
        ]
        
        # 添加表头
        for column in columns:
            html_parts.append(f"<th>{column}</th>")
        
        html_parts.extend(["</tr>", "</thead>", "<tbody>"])
        
        # 添加数据行
        for row in data:
            html_parts.append("<tr>")
            for column in columns:
                value = row.get(column, '')
                html_parts.append(f"<td>{value}</td>")
            html_parts.append("</tr>")
        
        html_parts.extend([
            "</tbody>",
            "</table>",
            "</body>",
            "</html>"
        ])
        
        return '\n'.join(html_parts)

    # ========================================================================
    # 异步方法实现
    # ========================================================================

    async def _create_async_connection(self, connection_source: Any) -> Any:
        """创建异步文件连接"""
        config = self._parse_file_config(connection_source)

        # 验证文件路径
        file_path = config['file_path']
        if not os.path.exists(os.path.dirname(file_path) or '.'):
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

        self.logger.info(f"创建异步HTML文件连接: {file_path}")
        return config

    async def _close_async_connection(self, connection: Any) -> None:
        """关闭异步文件连接"""
        self.logger.debug("异步HTML文件连接已关闭")

    async def _create_async_pool(self, connection_source: Any, min_size: int = 5, max_size: int = 20) -> Any:
        """创建异步连接池（文件不需要连接池）"""
        return await self._create_async_connection(connection_source)

    async def _async_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """异步查询实现 - 读取HTML文件"""
        return await self._async_read_html(connection, params)

    async def _async_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """异步执行实现 - 写入HTML文件"""
        if params and 'data' in params:
            return await self._async_write_html(connection, params['data'])
        return 0

    async def _async_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步事务实现 - 批量文件操作"""
        results = []
        total_affected = 0

        for operation in operations:
            op_type = operation.get('type', 'read')
            data = operation.get('data')

            if op_type == 'read':
                result = await self._async_read_html(connection)
                results.append(result)
            elif op_type == 'write':
                affected = await self._async_write_html(connection, data)
                results.append(affected)
                total_affected += affected
            elif op_type == 'extract_tables':
                result = await self._async_extract_tables(connection)
                results.append(result)

        return {
            'results': results,
            'total_operations': len(operations),
            'total_affected': total_affected,
            'success': True
        }

    async def _async_batch(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步批量操作实现"""
        return await self._async_transaction(connection, operations)

    async def _async_stream_query(self, connection: Any, sql: str, params: Dict = None) -> AsyncIterator[Dict]:
        """异步流式查询实现 - 逐个返回HTML元素"""
        if not BS4_AVAILABLE:
            raise ImportError("beautifulsoup4未安装，无法执行异步HTML流式读取")
        if not AIOFILES_AVAILABLE:
            raise ImportError("aiofiles未安装，无法执行异步文件流式读取")

        config = connection
        file_path = config['file_path']

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"HTML文件不存在: {file_path}")

        try:
            async with aiofiles.open(file_path, 'r', encoding=config['encoding']) as htmlfile:
                content = await htmlfile.read()
                soup = BeautifulSoup(content, config['parser'])

                # 根据配置流式返回数据
                if config['css_selector']:
                    elements = soup.select(config['css_selector'])
                    for idx, element in enumerate(elements):
                        yield {
                            'index': idx,
                            'tag': element.name,
                            'text': element.get_text(strip=True),
                            'attributes': dict(element.attrs) if element.attrs else {}
                        }
                elif config['extract_tables']:
                    tables = soup.find_all('table')
                    for table_idx, table in enumerate(tables):
                        rows = table.find_all('tr')
                        headers = []
                        if rows:
                            header_row = rows[0]
                            headers = [th.get_text(strip=True) for th in header_row.find_all(['th', 'td'])]

                        for row_idx, row in enumerate(rows[1:] if headers else rows):
                            cells = row.find_all(['td', 'th'])
                            row_data = {'table_index': table_idx, 'row_index': row_idx}

                            for cell_idx, cell in enumerate(cells):
                                header = headers[cell_idx] if cell_idx < len(headers) else f'column_{cell_idx}'
                                row_data[header] = cell.get_text(strip=True)

                            yield row_data

        except Exception as e:
            self.logger.error(f"异步流式读取HTML文件失败 {file_path}: {e}")
            raise

    async def _async_read_html(self, connection: Any, params: Dict = None) -> List[Dict]:
        """异步读取HTML文件"""
        if not BS4_AVAILABLE:
            raise ImportError("beautifulsoup4未安装，无法解析HTML文件")
        if not AIOFILES_AVAILABLE:
            raise ImportError("aiofiles未安装，无法执行异步文件读取")

        config = connection
        file_path = config['file_path']

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"HTML文件不存在: {file_path}")

        try:
            async with aiofiles.open(file_path, 'r', encoding=config['encoding']) as htmlfile:
                content = await htmlfile.read()
                soup = BeautifulSoup(content, config['parser'])

                # 根据配置提取数据
                if config['css_selector']:
                    return self._extract_by_css_selector(soup, config['css_selector'])
                elif config['extract_tables']:
                    return self._extract_tables_from_soup(soup)
                else:
                    # 提取所有文本内容
                    text = soup.get_text(strip=True)
                    return [{'content': text, 'title': soup.title.string if soup.title else ''}]

        except Exception as e:
            self.logger.error(f"异步读取HTML文件失败 {file_path}: {e}")
            raise

    async def _async_write_html(self, connection: Any, data: List[Dict]) -> int:
        """异步写入HTML文件"""
        if not AIOFILES_AVAILABLE:
            raise ImportError("aiofiles未安装，无法执行异步文件写入")

        config = connection
        file_path = config['file_path']

        if not data:
            return 0

        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path) or '.', exist_ok=True)

            # 生成HTML内容
            html_content = self._generate_html_table(data)

            async with aiofiles.open(file_path, 'w', encoding=config['encoding']) as htmlfile:
                await htmlfile.write(html_content)

            self.logger.info(f"成功异步写入HTML文件: {file_path}, 记录数: {len(data)}")
            return len(data)

        except Exception as e:
            self.logger.error(f"异步写入HTML文件失败 {file_path}: {e}")
            raise

    async def _async_extract_tables(self, connection: Any) -> List[Dict]:
        """异步提取HTML表格数据"""
        if not BS4_AVAILABLE:
            raise ImportError("beautifulsoup4未安装，无法解析HTML表格")
        if not AIOFILES_AVAILABLE:
            raise ImportError("aiofiles未安装，无法执行异步文件读取")

        config = connection
        file_path = config['file_path']

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"HTML文件不存在: {file_path}")

        try:
            async with aiofiles.open(file_path, 'r', encoding=config['encoding']) as htmlfile:
                content = await htmlfile.read()
                soup = BeautifulSoup(content, config['parser'])
                return self._extract_tables_from_soup(soup)

        except Exception as e:
            self.logger.error(f"异步提取HTML表格失败 {file_path}: {e}")
            raise

    # ========================================================================
    # HTML特有功能
    # ========================================================================

    def get_async_operations(self) -> Dict[str, Any]:
        """获取支持的异步操作列表"""
        operations = super().get_async_operations()

        # 添加HTML特有异步操作
        operations.update({
            'async_read_html': self._async_read_html,
            'async_write_html': self._async_write_html,
            'async_extract_tables': self._async_extract_tables,
        })

        return operations
