#!/usr/bin/env python3
"""
调试AsyncTemplateScope问题

精确定位'AsyncTemplateScope' object has no attribute 'register_data_source'错误的来源
"""

import sys
import os
import json
import tempfile
import traceback

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.thread_safe_template_integration import ThreadSafeTemplateIntegration


def debug_async_scope_issue():
    """调试AsyncTemplateScope问题"""
    print("=== 调试AsyncTemplateScope问题 ===")
    print("精确定位错误来源")
    print("=" * 60)
    
    # 创建线程安全的模板引擎
    engine = ThreadSafeTemplateIntegration(
        enable_async=True,
        enable_debug=True,
        isolation_level='thread',
        cleanup_interval=60,
        max_scope_lifetime=300
    )
    
    print("\n🔍 步骤1：检查作用域创建")
    print("-" * 40)
    
    try:
        with engine.create_isolated_scope('debug_scope') as scope:
            print(f"✅ 作用域创建成功")
            print(f"   作用域类型: {type(scope)}")
            print(f"   基础作用域类型: {type(scope.base_scope)}")
            print(f"   基础作用域有register_data_source: {hasattr(scope.base_scope, 'register_data_source')}")
            print(f"   基础作用域有register_async_data_source: {hasattr(scope.base_scope, 'register_async_data_source')}")
            
            # 检查基础作用域的所有方法
            print(f"   基础作用域方法: {[m for m in dir(scope.base_scope) if not m.startswith('_')]}")
            
    except Exception as e:
        print(f"❌ 作用域创建失败: {e}")
        traceback.print_exc()
    
    print("\n🔍 步骤2：测试数据源注册")
    print("-" * 40)
    
    try:
        with engine.create_isolated_scope('debug_scope_2') as scope:
            print("尝试注册简单数据源...")
            
            # 测试简单数据
            simple_data = {"test": "value"}
            try:
                proxy = scope.register_data_source("simple", simple_data)
                print(f"✅ 简单数据注册成功: {type(proxy)}")
            except Exception as e:
                print(f"❌ 简单数据注册失败: {e}")
                traceback.print_exc()
            
            # 测试文件数据源
            print("尝试注册文件数据源...")
            test_file_data = {"users": [{"name": "Alice"}]}
            temp_file = tempfile.mktemp(suffix='.json')
            
            try:
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(test_file_data, f)
                
                file_proxy = scope.register_data_source("file", temp_file)
                print(f"✅ 文件数据注册成功: {type(file_proxy)}")
                
            except Exception as e:
                print(f"❌ 文件数据注册失败: {e}")
                traceback.print_exc()
            finally:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
            
    except Exception as e:
        print(f"❌ 数据源注册测试失败: {e}")
        traceback.print_exc()
    
    print("\n🔍 步骤3：测试模板渲染")
    print("-" * 40)
    
    try:
        # 创建测试文件
        test_file_data = {"users": [{"name": "Alice", "age": 25}]}
        temp_file = tempfile.mktemp(suffix='.json')
        
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(test_file_data, f, ensure_ascii=False, indent=2)
        
        file_path = temp_file.replace('\\', '/')
        
        template = f"""
文件处理调试
===========
{{%- set file_data = sd.file('{file_path}').parse() -%}}
用户数: {{{{ file_data.users | length }}}}
        """.strip()
        
        print("开始模板渲染...")
        result = engine.render_template_sync(template, {}, 'debug_render')
        
        print(f"✅ 模板渲染成功")
        print(f"结果: {result}")
        
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)
            
    except Exception as e:
        print(f"❌ 模板渲染失败: {e}")
        traceback.print_exc()
    
    print("\n🔍 步骤4：检查智能数据加载器")
    print("-" * 40)
    
    try:
        with engine.create_isolated_scope('debug_scope_3') as scope:
            smart_loader = engine._create_smart_loader(scope)
            print(f"✅ 智能数据加载器创建成功: {type(smart_loader)}")
            print(f"   加载器类型: {smart_loader.__class__.__name__}")
            print(f"   模板作用域类型: {type(smart_loader.template_scope)}")
            
            # 检查智能加载器的方法
            print(f"   智能加载器方法: {[m for m in dir(smart_loader) if not m.startswith('_')]}")
            
    except Exception as e:
        print(f"❌ 智能数据加载器检查失败: {e}")
        traceback.print_exc()
    
    print("\n📊 调试总结")
    print("=" * 60)
    print("如果上述步骤都成功，但仍有错误信息，")
    print("说明错误来源于其他地方，可能是：")
    print("1. 智能数据加载器内部的调用")
    print("2. 模板渲染过程中的间接调用")
    print("3. 异步上下文的处理问题")
    
    # 关闭引擎
    engine.shutdown()
    print("\n✅ 调试完成")


if __name__ == "__main__":
    debug_async_scope_issue()
