#!/usr/bin/env python3
"""
复杂数据类型处理测试

验证线程安全版本对复杂数据类型的处理能力：
1. 文件处理修复验证
2. 列表推导、字典推导、集合推导
3. 混合嵌套复杂推导
4. 函数定义、类定义、嵌套函数
5. 链式调用等高级功能
"""

import sys
import os
import json
import tempfile
from datetime import datetime, timedelta

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.thread_safe_template_integration import ThreadSafeTemplateIntegration


def complex_data_processing_test():
    """复杂数据类型处理测试"""
    print("=== 复杂数据类型处理测试 ===")
    print("验证线程安全版本的高级数据处理能力")
    print("=" * 80)
    
    # 创建线程安全的模板引擎
    engine = ThreadSafeTemplateIntegration(
        enable_async=True,
        enable_debug=False,
        isolation_level='thread',
        cleanup_interval=60,
        max_scope_lifetime=300
    )
    
    test_results = []
    
    # 📁 测试1：修复后的文件处理
    print("\n📁 测试1：修复后的文件处理")
    print("-" * 60)
    
    try:
        # 创建复杂的测试数据
        complex_file_data = {
            "company": {
                "name": "智慧科技有限公司",
                "founded": 2020,
                "departments": [
                    {
                        "name": "技术部",
                        "employees": [
                            {
                                "id": 1,
                                "name": "张三",
                                "position": "高级工程师",
                                "skills": ["Python", "AI", "数据分析"],
                                "projects": [
                                    {"name": "项目A", "status": "进行中", "progress": 75},
                                    {"name": "项目B", "status": "已完成", "progress": 100}
                                ],
                                "performance": {
                                    "2023": {"rating": "优秀", "bonus": 15000},
                                    "2024": {"rating": "良好", "bonus": 12000}
                                }
                            },
                            {
                                "id": 2,
                                "name": "李四",
                                "position": "架构师",
                                "skills": ["Java", "微服务", "系统设计"],
                                "projects": [
                                    {"name": "项目C", "status": "计划中", "progress": 0}
                                ],
                                "performance": {
                                    "2023": {"rating": "优秀", "bonus": 18000},
                                    "2024": {"rating": "优秀", "bonus": 20000}
                                }
                            }
                        ]
                    },
                    {
                        "name": "销售部",
                        "employees": [
                            {
                                "id": 3,
                                "name": "王五",
                                "position": "销售经理",
                                "skills": ["销售", "客户管理", "市场分析"],
                                "projects": [
                                    {"name": "客户拓展", "status": "进行中", "progress": 60}
                                ],
                                "performance": {
                                    "2023": {"rating": "良好", "bonus": 10000},
                                    "2024": {"rating": "优秀", "bonus": 15000}
                                }
                            }
                        ]
                    }
                ]
            }
        }
        
        # 创建临时文件
        temp_file = tempfile.mktemp(suffix='.json')
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(complex_file_data, f, ensure_ascii=False, indent=2)
        
        file_path = temp_file.replace('\\', '/')
        
        template = f"""
文件处理修复验证
===============
{{%- set file_data = sd.file('{file_path}').parse() -%}}

1. 基础信息:
公司名称: {{{{ file_data.company.name }}}}
成立年份: {{{{ file_data.company.founded }}}}
部门数量: {{{{ file_data.company.departments | length }}}}

2. 复杂数据访问:
技术部员工数: {{{{ file_data.company.departments[0].employees | length }}}}
第一个员工: {{{{ file_data.company.departments[0].employees[0].name }}}}
第一个员工技能: {{{{ file_data.company.departments[0].employees[0].skills | join(', ') }}}}

3. JSONPath查询:
所有员工姓名: {{{{ sd.jsonpath(file_data, '$.company.departments[*].employees[*].name') }}}}
技术部员工: {{{{ sd.jsonpath(file_data, '$.company.departments[?(@.name=="技术部")].employees[*].name') }}}}
        """.strip()
        
        result = engine.render_template_sync(template, {}, 'file_fix_test')
        
        if '智慧科技有限公司' in result and '张三' in result:
            test_results.append("✅ 文件处理修复")
            print("✅ 文件处理修复成功")
            print(f"结果预览: {result[:200]}...")
        else:
            test_results.append("❌ 文件处理修复")
            print("❌ 文件处理修复失败")
        
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)
            
    except Exception as e:
        test_results.append("❌ 文件处理修复")
        print(f"❌ 文件处理修复异常: {e}")
        import traceback
        traceback.print_exc()
    
    # 🔄 测试2：列表推导、字典推导、集合推导
    print("\n🔄 测试2：推导式处理")
    print("-" * 60)
    
    try:
        complex_data = {
            "numbers": list(range(1, 11)),
            "employees": [
                {"name": "张三", "age": 28, "salary": 8000, "department": "技术部"},
                {"name": "李四", "age": 32, "salary": 12000, "department": "技术部"},
                {"name": "王五", "age": 25, "salary": 6000, "department": "销售部"},
                {"name": "赵六", "age": 30, "salary": 9000, "department": "市场部"},
                {"name": "钱七", "age": 35, "salary": 15000, "department": "技术部"}
            ]
        }
        
        template = """
推导式处理测试
=============

1. 列表推导式模拟:
偶数: {{ numbers | select('even') | list }}
大于5的数: {{ numbers | select('>', 5) | list }}
数字平方: {% for n in numbers %}{{ n * n }}{% if not loop.last %}, {% endif %}{% endfor %}

2. 字典推导式模拟:
员工薪资映射:
{%- for emp in employees %}
{{ emp.name }}: {{ emp.salary }}
{%- endfor %}

3. 集合推导式模拟:
所有部门: {{ employees | map(attribute='department') | unique | list | join(', ') }}
高薪员工(>8000): {{ employees | selectattr('salary', '>', 8000) | map(attribute='name') | list | join(', ') }}

4. 复杂嵌套推导:
技术部高薪员工:
{%- set tech_high_salary = employees | selectattr('department', 'equalto', '技术部') | selectattr('salary', '>', 8000) | list %}
{%- for emp in tech_high_salary %}
- {{ emp.name }}: {{ emp.salary }}元 ({{ emp.age }}岁)
{%- endfor %}

5. 条件筛选和转换:
员工年龄分组:
- 年轻员工(<30): {{ employees | selectattr('age', '<', 30) | map(attribute='name') | list | join(', ') }}
- 中年员工(30-35): {{ employees | selectattr('age', '>=', 30) | selectattr('age', '<=', 35) | map(attribute='name') | list | join(', ') }}

6. 数据统计:
平均薪资: {{ (employees | sum(attribute='salary')) // (employees | length) }}
最高薪资: {{ employees | map(attribute='salary') | max }}
最低薪资: {{ employees | map(attribute='salary') | min }}
        """.strip()
        
        result = engine.render_template_sync(template, complex_data, 'comprehension_test')
        
        if '技术部' in result and '张三' in result and '平均薪资' in result:
            test_results.append("✅ 推导式处理")
            print("✅ 推导式处理成功")
        else:
            test_results.append("❌ 推导式处理")
            print("❌ 推导式处理失败")
            
    except Exception as e:
        test_results.append("❌ 推导式处理")
        print(f"❌ 推导式处理异常: {e}")
    
    # 🔗 测试3：链式调用和复杂数据操作
    print("\n🔗 测试3：链式调用和复杂数据操作")
    print("-" * 60)
    
    try:
        chain_data = {
            "orders": [
                {
                    "id": 1,
                    "customer": "客户A",
                    "items": [
                        {"product": "笔记本", "price": 5000, "quantity": 2},
                        {"product": "鼠标", "price": 100, "quantity": 1}
                    ],
                    "status": "已完成",
                    "date": "2024-01-15"
                },
                {
                    "id": 2,
                    "customer": "客户B",
                    "items": [
                        {"product": "键盘", "price": 300, "quantity": 1},
                        {"product": "显示器", "price": 2000, "quantity": 1}
                    ],
                    "status": "进行中",
                    "date": "2024-01-20"
                },
                {
                    "id": 3,
                    "customer": "客户C",
                    "items": [
                        {"product": "笔记本", "price": 5000, "quantity": 1},
                        {"product": "包", "price": 200, "quantity": 1}
                    ],
                    "status": "已完成",
                    "date": "2024-01-25"
                }
            ]
        }
        
        template = """
链式调用和复杂数据操作测试
========================

1. 多级链式调用:
所有产品: {{ orders | map(attribute='items') | flatten | map(attribute='product') | unique | list | join(', ') }}

2. 复杂计算链:
总订单价值: {{ orders | map('sum', attribute='items', start=0) | sum }}

3. 条件链式过滤:
已完成订单的客户: {{ orders | selectattr('status', 'equalto', '已完成') | map(attribute='customer') | list | join(', ') }}

4. 嵌套数据处理:
{%- for order in orders %}
订单{{ order.id }} ({{ order.customer }}):
  {%- for item in order.items %}
  - {{ item.product }}: {{ item.price }}元 × {{ item.quantity }} = {{ item.price * item.quantity }}元
  {%- endfor %}
  订单总额: {{ order.items | sum(attribute='price') }}元
{%- endfor %}

5. 复杂聚合操作:
产品销量统计:
{%- set all_items = orders | map(attribute='items') | flatten | list %}
{%- set products = all_items | map(attribute='product') | unique | list %}
{%- for product in products %}
{{ product }}: {{ all_items | selectattr('product', 'equalto', product) | sum(attribute='quantity') }}件
{%- endfor %}

6. 日期和状态分析:
2024年1月订单: {{ orders | length }}个
已完成订单: {{ orders | selectattr('status', 'equalto', '已完成') | list | length }}个
进行中订单: {{ orders | selectattr('status', 'equalto', '进行中') | list | length }}个
        """.strip()
        
        result = engine.render_template_sync(template, chain_data, 'chain_test')
        
        if '笔记本' in result and '客户A' in result and '产品销量统计' in result:
            test_results.append("✅ 链式调用")
            print("✅ 链式调用处理成功")
        else:
            test_results.append("❌ 链式调用")
            print("❌ 链式调用处理失败")
            
    except Exception as e:
        test_results.append("❌ 链式调用")
        print(f"❌ 链式调用处理异常: {e}")
    
    # 📊 生成测试报告
    print("\n📊 复杂数据类型处理测试报告")
    print("=" * 80)
    
    successful_tests = len([r for r in test_results if r.startswith('✅')])
    total_tests = len(test_results)
    success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print("测试结果:")
    for result in test_results:
        print(f"  {result}")
    
    print(f"\n总体结果:")
    print(f"  成功测试: {successful_tests}/{total_tests}")
    print(f"  成功率: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("\n🎉 完美！所有复杂数据类型处理都正常")
        print("\n✅ 验证的高级功能:")
        print("  📁 文件处理 - 完美修复")
        print("  🔄 推导式处理 - 完全支持")
        print("  🔗 链式调用 - 完全支持")
        print("  📊 复杂数据操作 - 完全支持")
        
        print("\n🚀 线程安全版本具备企业级复杂数据处理能力！")
        
    elif success_rate >= 66:
        print("\n✅ 良好！大部分复杂功能正常工作")
        print("  核心数据处理能力已具备")
        
    else:
        print("\n⚠️ 需要进一步优化复杂数据处理能力")
    
    # 关闭引擎
    print("\n🔧 正在关闭模板引擎...")
    engine.shutdown()
    print("✅ 模板引擎已安全关闭")
    
    return success_rate


if __name__ == "__main__":
    success_rate = complex_data_processing_test()
    
    if success_rate == 100:
        print(f"\n🏆 复杂数据处理测试: {success_rate:.1f}% - 完美成功！")
        print("🎯 线程安全版本具备完整的企业级数据处理能力！")
    elif success_rate >= 66:
        print(f"\n📊 复杂数据处理测试: {success_rate:.1f}% - 良好")
        print("🎯 核心功能完备，可以处理大部分企业级需求")
    else:
        print(f"\n🔧 复杂数据处理测试: {success_rate:.1f}% - 需要优化")
