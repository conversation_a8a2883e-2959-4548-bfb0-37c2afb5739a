"""
企业级文件处理器

提供完整的文件处理能力
"""

import logging
import asyncio
import time
import os
import shutil
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
import threading
from pathlib import Path

try:
    from ...core.smart_data_object import SmartDataObject
    from ...core.base_processor import BaseProcessor
except ImportError:
    # 绝对导入作为后备
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from core.smart_data_object import SmartDataObject
    from core.base_processor import BaseProcessor


class FileOperation(Enum):
    """文件操作类型"""
    READ = "read"
    WRITE = "write"
    COPY = "copy"
    MOVE = "move"
    DELETE = "delete"
    COMPRESS = "compress"
    DECOMPRESS = "decompress"
    CONVERT = "convert"
    MONITOR = "monitor"
    VALIDATE = "validate"
    BACKUP = "backup"
    SYNC = "sync"
    LIST = "list"
    METADATA = "metadata"


@dataclass
class FileConfig:
    """文件配置"""
    base_path: str = "."
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    allowed_extensions: Optional[List[str]] = None
    blocked_extensions: Optional[List[str]] = None
    enable_backup: bool = True
    backup_path: str = "./backups"
    compression_level: int = 6
    buffer_size: int = 8192
    
    def __post_init__(self):
        if self.allowed_extensions is None:
            self.allowed_extensions = []
        if self.blocked_extensions is None:
            self.blocked_extensions = ['.exe', '.bat', '.cmd', '.scr']


@dataclass
class FileStats:
    """文件统计信息"""
    files_processed: int = 0
    files_read: int = 0
    files_written: int = 0
    files_compressed: int = 0
    files_converted: int = 0
    bytes_processed: int = 0
    errors: int = 0
    last_activity: Optional[float] = None
    
    @property
    def success_rate(self) -> float:
        """处理成功率"""
        total = self.files_processed + self.errors
        return self.files_processed / total if total > 0 else 0.0


class FileProcessor(BaseProcessor):
    """企业级文件处理器"""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(f"{__name__}.FileProcessor")
        
        # 处理器信息
        self.processor_id = "file_processor"
        self.version = "1.0.0"
        self.priority = 65
        
        # 统计信息
        self.stats = FileStats()
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 默认配置
        self.default_config = FileConfig()
        
        # 文件监控器
        self._monitors = {}
    
    def can_process(self, data: Any) -> bool:
        """检查是否可以处理数据"""
        if isinstance(data, dict):
            # 检查是否包含文件相关字段
            file_fields = ['file_path', 'source_path', 'target_path', 'operation', 'files']
            return any(field in data for field in file_fields)
        
        elif isinstance(data, str):
            # 检查是否为文件路径
            return os.path.exists(data) or data.startswith(('./', '/', '\\', 'C:', 'D:'))
        
        return False
    
    async def process(self, data: Any, options: Dict[str, Any] = None) -> SmartDataObject:
        """处理文件操作"""
        try:
            options = options or {}
            
            # 确定操作类型
            operation = self._determine_operation(data, options)
            
            if operation == FileOperation.READ:
                return await self._handle_read_file(data, options)
            elif operation == FileOperation.WRITE:
                return await self._handle_write_file(data, options)
            elif operation == FileOperation.COPY:
                return await self._handle_copy_file(data, options)
            elif operation == FileOperation.MOVE:
                return await self._handle_move_file(data, options)
            elif operation == FileOperation.DELETE:
                return await self._handle_delete_file(data, options)
            elif operation == FileOperation.COMPRESS:
                return await self._handle_compress_file(data, options)
            elif operation == FileOperation.DECOMPRESS:
                return await self._handle_decompress_file(data, options)
            elif operation == FileOperation.CONVERT:
                return await self._handle_convert_file(data, options)
            elif operation == FileOperation.LIST:
                return await self._handle_list_files(data, options)
            elif operation == FileOperation.METADATA:
                return await self._handle_get_metadata(data, options)
            elif operation == FileOperation.VALIDATE:
                return await self._handle_validate_file(data, options)
            elif operation == FileOperation.MONITOR:
                return await self._handle_monitor_files(data, options)
            else:
                return SmartDataObject({
                    'success': False,
                    'error': f'不支持的操作类型: {operation}',
                    'processor': self.processor_id
                })
                
        except Exception as e:
            self.logger.error(f"文件处理失败: {e}")
            with self._lock:
                self.stats.errors += 1
            return SmartDataObject({
                'success': False,
                'error': str(e),
                'processor': self.processor_id
            })
    
    def _determine_operation(self, data: Any, options: Dict[str, Any]) -> FileOperation:
        """确定操作类型"""
        # 从选项中获取操作类型
        if 'operation' in options:
            op_str = options['operation'].lower()
            for op in FileOperation:
                if op.value == op_str:
                    return op
        
        # 从数据中推断操作类型
        if isinstance(data, dict):
            if 'operation' in data:
                op_str = data['operation'].lower()
                for op in FileOperation:
                    if op.value == op_str:
                        return op
            
            # 根据字段推断
            if 'content' in data or 'data' in data:
                return FileOperation.WRITE
            elif 'source_path' in data and 'target_path' in data:
                return FileOperation.COPY
            elif 'files' in data:
                return FileOperation.LIST
        
        elif isinstance(data, str):
            # 默认为读取文件
            return FileOperation.READ
        
        # 默认为读取操作
        return FileOperation.READ
    
    async def _handle_read_file(self, data: Any, options: Dict[str, Any]) -> SmartDataObject:
        """处理读取文件"""
        try:
            # 获取文件路径
            if isinstance(data, dict):
                file_path = data.get('file_path', data.get('path'))
            else:
                file_path = str(data)
            
            if not file_path:
                return SmartDataObject({
                    'success': False,
                    'operation': 'read',
                    'error': '文件路径不能为空',
                    'processor': self.processor_id
                })
            
            # 验证文件路径
            if not os.path.exists(file_path):
                return SmartDataObject({
                    'success': False,
                    'operation': 'read',
                    'error': f'文件不存在: {file_path}',
                    'processor': self.processor_id
                })
            
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size > self.default_config.max_file_size:
                return SmartDataObject({
                    'success': False,
                    'operation': 'read',
                    'error': f'文件过大: {file_size} bytes',
                    'processor': self.processor_id
                })
            
            # 读取文件
            start_time = time.time()
            
            try:
                import aiofiles
                async with aiofiles.open(file_path, 'rb') as f:
                    content = await f.read()
            except ImportError:
                # 如果没有aiofiles，使用同步读取
                with open(file_path, 'rb') as f:
                    content = f.read()
            
            read_time = time.time() - start_time
            
            # 尝试解码为文本
            try:
                text_content = content.decode('utf-8')
                is_text = True
            except UnicodeDecodeError:
                text_content = None
                is_text = False
            
            # 更新统计
            with self._lock:
                self.stats.files_processed += 1
                self.stats.files_read += 1
                self.stats.bytes_processed += file_size
                self.stats.last_activity = time.time()
            
            return SmartDataObject({
                'success': True,
                'operation': 'read',
                'file_path': file_path,
                'file_size': file_size,
                'content': content,
                'text_content': text_content,
                'is_text': is_text,
                'read_time': read_time,
                'processor': self.processor_id
            })
            
        except Exception as e:
            self.logger.error(f"读取文件失败: {e}")
            with self._lock:
                self.stats.errors += 1
            return SmartDataObject({
                'success': False,
                'operation': 'read',
                'error': str(e),
                'processor': self.processor_id
            })
    
    async def _handle_write_file(self, data: Dict[str, Any], options: Dict[str, Any]) -> SmartDataObject:
        """处理写入文件"""
        try:
            file_path = data.get('file_path', data.get('path'))
            content = data.get('content', data.get('data'))
            
            if not file_path:
                return SmartDataObject({
                    'success': False,
                    'operation': 'write',
                    'error': '文件路径不能为空',
                    'processor': self.processor_id
                })
            
            if content is None:
                return SmartDataObject({
                    'success': False,
                    'operation': 'write',
                    'error': '文件内容不能为空',
                    'processor': self.processor_id
                })
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 转换内容为字节
            if isinstance(content, str):
                content_bytes = content.encode('utf-8')
            else:
                content_bytes = content
            
            # 检查文件大小
            if len(content_bytes) > self.default_config.max_file_size:
                return SmartDataObject({
                    'success': False,
                    'operation': 'write',
                    'error': f'内容过大: {len(content_bytes)} bytes',
                    'processor': self.processor_id
                })
            
            # 写入文件
            start_time = time.time()
            
            try:
                import aiofiles
                async with aiofiles.open(file_path, 'wb') as f:
                    await f.write(content_bytes)
            except ImportError:
                # 如果没有aiofiles，使用同步写入
                with open(file_path, 'wb') as f:
                    f.write(content_bytes)
            
            write_time = time.time() - start_time
            
            # 更新统计
            with self._lock:
                self.stats.files_processed += 1
                self.stats.files_written += 1
                self.stats.bytes_processed += len(content_bytes)
                self.stats.last_activity = time.time()
            
            return SmartDataObject({
                'success': True,
                'operation': 'write',
                'file_path': file_path,
                'file_size': len(content_bytes),
                'write_time': write_time,
                'processor': self.processor_id
            })
            
        except Exception as e:
            self.logger.error(f"写入文件失败: {e}")
            with self._lock:
                self.stats.errors += 1
            return SmartDataObject({
                'success': False,
                'operation': 'write',
                'error': str(e),
                'processor': self.processor_id
            })
    
    async def _handle_copy_file(self, data: Dict[str, Any], options: Dict[str, Any]) -> SmartDataObject:
        """处理复制文件"""
        try:
            source_path = data.get('source_path', data.get('source'))
            target_path = data.get('target_path', data.get('target'))
            
            if not source_path or not target_path:
                return SmartDataObject({
                    'success': False,
                    'operation': 'copy',
                    'error': '源路径和目标路径不能为空',
                    'processor': self.processor_id
                })
            
            if not os.path.exists(source_path):
                return SmartDataObject({
                    'success': False,
                    'operation': 'copy',
                    'error': f'源文件不存在: {source_path}',
                    'processor': self.processor_id
                })
            
            # 确保目标目录存在
            os.makedirs(os.path.dirname(target_path), exist_ok=True)
            
            # 复制文件
            start_time = time.time()
            shutil.copy2(source_path, target_path)
            copy_time = time.time() - start_time
            
            file_size = os.path.getsize(target_path)
            
            # 更新统计
            with self._lock:
                self.stats.files_processed += 1
                self.stats.bytes_processed += file_size
                self.stats.last_activity = time.time()
            
            return SmartDataObject({
                'success': True,
                'operation': 'copy',
                'source_path': source_path,
                'target_path': target_path,
                'file_size': file_size,
                'copy_time': copy_time,
                'processor': self.processor_id
            })
            
        except Exception as e:
            self.logger.error(f"复制文件失败: {e}")
            with self._lock:
                self.stats.errors += 1
            return SmartDataObject({
                'success': False,
                'operation': 'copy',
                'error': str(e),
                'processor': self.processor_id
            })
    
    async def _handle_list_files(self, data: Any, options: Dict[str, Any]) -> SmartDataObject:
        """处理列出文件"""
        try:
            # 获取目录路径
            if isinstance(data, dict):
                directory = data.get('directory', data.get('path', '.'))
            else:
                directory = str(data) if os.path.isdir(str(data)) else '.'
            
            if not os.path.exists(directory):
                return SmartDataObject({
                    'success': False,
                    'operation': 'list',
                    'error': f'目录不存在: {directory}',
                    'processor': self.processor_id
                })
            
            # 列出文件
            files = []
            total_size = 0
            
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)
                
                if os.path.isfile(item_path):
                    file_size = os.path.getsize(item_path)
                    file_ext = os.path.splitext(item)[1].lower()
                    file_type = self._get_file_type(file_ext)
                    file_info = {
                        'name': item,
                        'path': item_path,
                        'size': file_size,
                        'type': file_type,
                        'extension': file_ext,
                        'modified': datetime.fromtimestamp(os.path.getmtime(item_path)).strftime('%Y-%m-%d %H:%M:%S'),
                        'is_file': True
                    }
                    files.append(file_info)
                    total_size += file_size
                elif os.path.isdir(item_path):
                    dir_info = {
                        'name': item,
                        'path': item_path,
                        'size': 0,
                        'type': 'directory',
                        'extension': '',
                        'modified': datetime.fromtimestamp(os.path.getmtime(item_path)).strftime('%Y-%m-%d %H:%M:%S'),
                        'is_file': False
                    }
                    files.append(dir_info)
            
            return SmartDataObject({
                'success': True,
                'operation': 'list',
                'directory': directory,
                'files': files,
                'file_count': len([f for f in files if f['is_file']]),
                'directory_count': len([f for f in files if not f['is_file']]),
                'total_size': total_size,
                'processor': self.processor_id
            })
            
        except Exception as e:
            self.logger.error(f"列出文件失败: {e}")
            return SmartDataObject({
                'success': False,
                'operation': 'list',
                'error': str(e),
                'processor': self.processor_id
            })
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            return asdict(self.stats)
    
    def get_processor_info(self) -> Dict[str, Any]:
        """获取处理器信息"""
        return {
            'id': self.processor_id,
            'name': '企业级文件处理器',
            'version': self.version,
            'description': '提供完整的企业级文件处理能力',
            'supported_operations': [op.value for op in FileOperation],
            'capabilities': [
                'file_operations',
                'compression',
                'format_conversion',
                'file_monitoring',
                'batch_processing',
                'security_scanning',
                'metadata_extraction'
            ],
            'priority': self.priority,
            'stats': self.get_stats()
        }

    async def _handle_monitor_files(self, data: Any, options: Dict[str, Any]) -> SmartDataObject:
        """处理文件监控"""
        try:
            # 获取监控目录
            if isinstance(data, dict):
                directory = data.get('directory', '.')
                pattern = data.get('pattern', '*')
            else:
                directory = '.'
                pattern = '*'

            # 扫描目录获取文件信息
            directory_path = Path(directory).resolve()

            if not directory_path.exists():
                return SmartDataObject({
                    'success': False,
                    'error': f'目录不存在: {directory}',
                    'processor': self.processor_id
                })

            # 统计Python文件
            python_files = list(directory_path.glob(pattern))
            total_lines = 0
            total_size = 0
            recent_files = []

            for file_path in python_files:
                if file_path.is_file():
                    try:
                        stat = file_path.stat()
                        file_size = stat.st_size
                        total_size += file_size

                        # 计算代码行数（仅对文本文件）
                        if file_path.suffix in ['.py', '.txt', '.md', '.json', '.yaml', '.yml']:
                            try:
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    lines = len(f.readlines())
                                    total_lines += lines
                            except:
                                lines = 0
                        else:
                            lines = 0

                        recent_files.append({
                            'name': file_path.name,
                            'size': file_size,
                            'lines': lines,
                            'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                        })
                    except Exception as e:
                        self.logger.warning(f"无法读取文件 {file_path}: {e}")

            # 按修改时间排序
            recent_files.sort(key=lambda x: x['modified'], reverse=True)

            # 计算统计信息
            avg_size = total_size / len(python_files) if python_files else 0
            largest_file = max(recent_files, key=lambda x: x['size'])['name'] if recent_files else None
            smallest_file = min(recent_files, key=lambda x: x['size'])['name'] if recent_files else None

            return SmartDataObject({
                'success': True,
                'python_files': len(python_files),
                'total_lines': total_lines,
                'total_size': total_size,
                'avg_size': avg_size,
                'largest_file': largest_file,
                'smallest_file': smallest_file,
                'recent_files': recent_files[:10],  # 只返回最近的10个文件
                'processor': self.processor_id
            })

        except Exception as e:
            self.logger.error(f"文件监控失败: {e}")
            return SmartDataObject({
                'success': False,
                'error': str(e),
                'processor': self.processor_id
            })

    def _get_file_type(self, extension: str) -> str:
        """根据扩展名获取文件类型"""
        type_mapping = {
            '.py': 'python',
            '.js': 'javascript',
            '.html': 'html',
            '.css': 'css',
            '.json': 'json',
            '.xml': 'xml',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.md': 'markdown',
            '.txt': 'text',
            '.log': 'log',
            '.ini': 'config',
            '.cfg': 'config',
            '.conf': 'config',
            '.sql': 'sql',
            '.sh': 'shell',
            '.bat': 'batch',
            '.ps1': 'powershell',
            '.jpg': 'image',
            '.jpeg': 'image',
            '.png': 'image',
            '.gif': 'image',
            '.pdf': 'document',
            '.doc': 'document',
            '.docx': 'document',
            '.xls': 'spreadsheet',
            '.xlsx': 'spreadsheet',
            '.zip': 'archive',
            '.tar': 'archive',
            '.gz': 'archive',
            '.rar': 'archive'
        }
        return type_mapping.get(extension.lower(), 'unknown')

    def get_supported_services(self) -> List[str]:
        """获取支持的服务"""
        return [op.value for op in FileOperation]
