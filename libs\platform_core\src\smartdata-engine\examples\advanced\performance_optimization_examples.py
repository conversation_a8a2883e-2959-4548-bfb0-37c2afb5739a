#!/usr/bin/env python3
"""
SmartData模板引擎性能优化示例

展示模板引擎的高级性能优化功能：
1. 模板预编译 - 提前编译模板以提高渲染性能
2. 智能模板优化 - 自动优化模板结构
3. 性能数据监控 - 实时监控模板性能
4. 缓存策略优化 - 多级缓存提升性能
5. 并发渲染优化 - 支持高并发模板渲染
"""

import sys
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.template_ext import create_template_engine

def performance_optimization_examples():
    """性能优化完整示例"""
    print("=== SmartData模板引擎性能优化示例 ===")
    
    # 创建模板引擎
    engine = create_template_engine()
    
    # 1. 模板预编译示例
    print("\n🚀 1. 模板预编译示例")
    
    # 定义一个复杂的模板
    complex_template = """
{%- set company_data = {
    'name': '科技创新有限公司',
    'departments': [
        {'name': '研发部', 'employees': 50, 'budget': 2000000},
        {'name': '销售部', 'employees': 30, 'budget': 1500000},
        {'name': '市场部', 'employees': 20, 'budget': 1000000},
        {'name': '人事部', 'employees': 10, 'budget': 500000}
    ]
} -%}

企业年度报告
============
公司名称: {{ company_data.name }}
报告日期: {{ now() }}

部门概览:
{%- for dept in company_data.departments %}
{{ loop.index }}. {{ dept.name }}
   - 员工数量: {{ dept.employees }}人
   - 年度预算: ¥{{ format_number(dept.budget) }}
   - 人均预算: ¥{{ format_number(dept.budget / dept.employees) }}
{%- endfor %}

总体统计:
- 总员工数: {{ company_data.departments | sum_by('employees') }}人
- 总预算: ¥{{ format_number(company_data.departments | sum_by('budget')) }}
- 平均部门规模: {{ company_data.departments | avg_by('employees') | round(1) }}人
- 预算最高部门: {{ (company_data.departments | sort(attribute='budget', reverse=true))[0].name }}

{%- set top_dept = (company_data.departments | sort(attribute='budget', reverse=true))[0] -%}
重点关注:
{{ top_dept.name }}作为预算最高的部门，承担着重要的业务职责。
    """.strip()
    
    # 预编译模板
    print("正在预编译模板...")
    start_time = time.time()
    template_id = engine.precompile_template(complex_template, "company_report")
    compile_time = time.time() - start_time
    print(f"✅ 模板预编译完成，耗时: {compile_time:.4f}秒")
    print(f"✅ 模板ID: {template_id}")
    
    # 测试预编译模板的渲染性能
    print("\n📊 预编译模板性能测试:")
    
    # 渲染预编译模板10次
    precompiled_times = []
    for i in range(10):
        start_time = time.time()
        result = engine.render_precompiled_template(template_id)
        render_time = time.time() - start_time
        precompiled_times.append(render_time)
    
    # 渲染普通模板10次作为对比
    normal_times = []
    for i in range(10):
        start_time = time.time()
        result = engine.render_template(complex_template)
        render_time = time.time() - start_time
        normal_times.append(render_time)
    
    avg_precompiled = sum(precompiled_times) / len(precompiled_times)
    avg_normal = sum(normal_times) / len(normal_times)
    performance_improvement = ((avg_normal - avg_precompiled) / avg_normal) * 100
    
    print(f"预编译模板平均渲染时间: {avg_precompiled:.4f}秒")
    print(f"普通模板平均渲染时间: {avg_normal:.4f}秒")
    print(f"性能提升: {performance_improvement:.1f}%")
    
    # 2. 智能模板优化示例
    print("\n🧠 2. 智能模板优化示例")
    
    # 定义一个需要优化的模板
    unoptimized_template = """
    
    {%    set   user_data   =   {
        'name':   'John Doe',
        'age':    30,
        'city':   'New York'
    }    %}
    
    
    {#  这是一个注释，会被优化掉  #}
    
    用户信息:
    姓名:  {{   user_data.name   }}
    年龄:  {{   user_data.age    }}
    城市:  {{   user_data.city   }}
    
    {%   if   user_data.age   >=   18   %}
    状态: 成年人
    {%   else   %}
    状态: 未成年人
    {%   endif   %}
    
    
    """
    
    print("原始模板长度:", len(unoptimized_template))
    print("原始模板:")
    print(repr(unoptimized_template[:100]) + "...")
    
    # 优化模板
    optimized_template = engine.optimize_template(unoptimized_template)
    
    print(f"\n优化后模板长度: {len(optimized_template)}")
    print("优化后模板:")
    print(repr(optimized_template[:100]) + "...")
    
    size_reduction = ((len(unoptimized_template) - len(optimized_template)) / len(unoptimized_template)) * 100
    print(f"模板大小减少: {size_reduction:.1f}%")
    
    # 测试优化效果
    print("\n📊 模板优化性能测试:")
    
    # 测试优化前后的渲染性能
    unoptimized_times = []
    optimized_times = []
    
    for i in range(20):
        # 测试未优化模板
        start_time = time.time()
        engine.render_template(unoptimized_template)
        unoptimized_times.append(time.time() - start_time)
        
        # 测试优化模板
        start_time = time.time()
        engine.render_template(optimized_template)
        optimized_times.append(time.time() - start_time)
    
    avg_unoptimized = sum(unoptimized_times) / len(unoptimized_times)
    avg_optimized = sum(optimized_times) / len(optimized_times)
    optimization_improvement = ((avg_unoptimized - avg_optimized) / avg_unoptimized) * 100
    
    print(f"未优化模板平均渲染时间: {avg_unoptimized:.4f}秒")
    print(f"优化模板平均渲染时间: {avg_optimized:.4f}秒")
    print(f"优化性能提升: {optimization_improvement:.1f}%")
    
    # 3. 并发渲染性能测试
    print("\n⚡ 3. 并发渲染性能测试")
    
    def render_task(template_content, task_id):
        """渲染任务"""
        start_time = time.time()
        result = engine.render_template(template_content)
        end_time = time.time()
        return {
            'task_id': task_id,
            'render_time': end_time - start_time,
            'result_length': len(result)
        }
    
    # 并发渲染测试
    num_threads = 10
    num_tasks_per_thread = 5
    
    print(f"启动 {num_threads} 个线程，每个线程渲染 {num_tasks_per_thread} 次")
    
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = []
        
        for thread_id in range(num_threads):
            for task_id in range(num_tasks_per_thread):
                future = executor.submit(
                    render_task, 
                    complex_template, 
                    f"thread_{thread_id}_task_{task_id}"
                )
                futures.append(future)
        
        # 收集结果
        results = []
        for future in as_completed(futures):
            result = future.result()
            results.append(result)
    
    total_time = time.time() - start_time
    total_tasks = len(results)
    avg_task_time = sum(r['render_time'] for r in results) / total_tasks
    throughput = total_tasks / total_time
    
    print(f"✅ 并发渲染完成:")
    print(f"   总任务数: {total_tasks}")
    print(f"   总耗时: {total_time:.2f}秒")
    print(f"   平均任务时间: {avg_task_time:.4f}秒")
    print(f"   吞吐量: {throughput:.1f} 任务/秒")
    
    # 4. 性能数据分析
    print("\n📈 4. 性能数据分析")
    
    performance_data = engine.get_performance_data()
    
    print("渲染统计:")
    render_stats = performance_data['render_statistics']
    print(f"  总渲染次数: {render_stats['total_renders']}")
    print(f"  总渲染时间: {render_stats['total_render_time']:.4f}秒")
    print(f"  平均渲染时间: {render_stats['average_render_time']:.4f}秒")
    print(f"  渲染速度: {render_stats['renders_per_second']:.1f} 次/秒")
    
    print("\n缓存统计:")
    cache_stats = performance_data['cache_statistics']
    print(f"  缓存命中: {cache_stats['cache_hits']}")
    print(f"  缓存未命中: {cache_stats['cache_misses']}")
    print(f"  缓存命中率: {cache_stats['cache_hit_rate']:.1%}")
    print(f"  缓存模板数: {cache_stats['cached_templates']}")
    
    print("\n编译统计:")
    compile_stats = performance_data['compilation_statistics']
    print(f"  预编译模板数: {compile_stats['precompiled_templates']}")
    
    if compile_stats['compiled_template_details']:
        print("  预编译模板详情:")
        for template_id, details in compile_stats['compiled_template_details'].items():
            print(f"    {template_id}:")
            print(f"      编译时间: {details['compile_time']:.4f}秒")
            print(f"      渲染次数: {details['render_count']}")
            print(f"      平均渲染时间: {details['avg_render_time']:.4f}秒")
    
    print("\n优化统计:")
    opt_stats = performance_data['optimization_statistics']
    for key, value in opt_stats.items():
        print(f"  {key}: {value}")
    
    # 5. 性能报告导出
    print("\n📄 5. 性能报告导出")
    
    # 导出JSON格式报告
    json_report = engine.export_performance_report('json')
    
    # 保存到文件
    report_file = "performance_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(json_report)
    
    print(f"✅ 性能报告已导出到: {report_file}")
    print(f"报告大小: {len(json_report)} 字符")
    
    # 6. 性能优化建议
    print("\n💡 6. 性能优化建议")
    
    suggestions = []
    
    if render_stats['average_render_time'] > 0.01:
        suggestions.append("考虑使用模板预编译来提高渲染性能")
    
    if cache_stats['cache_hit_rate'] < 0.5:
        suggestions.append("考虑增加缓存大小或优化缓存策略")
    
    if compile_stats['precompiled_templates'] == 0:
        suggestions.append("对于频繁使用的模板，建议进行预编译")
    
    if opt_stats.get('optimized', 0) == 0:
        suggestions.append("考虑使用模板优化功能减少模板大小")
    
    if suggestions:
        print("基于当前性能数据的优化建议:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"  {i}. {suggestion}")
    else:
        print("✅ 当前性能表现良好，无需特别优化")
    
    print("\n🎉 性能优化示例完成！")
    
    print("\n📊 性能优化总结:")
    print("✅ 模板预编译 - 提升渲染性能")
    print("✅ 智能优化 - 减少模板大小")
    print("✅ 并发渲染 - 支持高并发场景")
    print("✅ 性能监控 - 实时性能数据")
    print("✅ 报告导出 - 详细性能分析")
    
    print("\n💡 关键优势:")
    print("🚀 预编译技术 - 显著提升重复渲染性能")
    print("🧠 智能优化 - 自动优化模板结构")
    print("⚡ 并发支持 - 高并发场景下的稳定性能")
    print("📈 性能监控 - 全面的性能数据分析")
    print("🔧 优化建议 - 基于数据的性能调优指导")

if __name__ == "__main__":
    performance_optimization_examples()
