"""
CSV文件数据适配器

支持CSV文件的同步和异步读写操作
"""

from typing import Any, Dict, List, Optional, Union, AsyncIterator
import logging
import csv
import os
from pathlib import Path

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../'))

from core.unified_adapter import UnifiedDataAdapter

# 尝试导入异步文件操作库
try:
    import aiofiles
    AIOFILES_AVAILABLE = True
except ImportError:
    AIOFILES_AVAILABLE = False
    aiofiles = None


class CSVAdapter(UnifiedDataAdapter):
    """
    CSV文件数据适配器
    
    支持CSV文件特有功能：
    - 同步和异步文件读写
    - 自定义分隔符和编码
    - 大文件流式处理
    - 数据类型自动推断
    - 列名映射和过滤
    """
    
    def __init__(self):
        super().__init__()
        if not AIOFILES_AVAILABLE:
            self.logger.warning("aiofiles未安装，异步文件操作功能受限")
        
        # 默认配置
        self.default_encoding = 'utf-8'
        self.default_delimiter = ','
        self.default_quotechar = '"'
    
    def supported_types(self) -> List[str]:
        """返回支持的数据类型列表"""
        return [
            'csv',
            'csv_file',
            'tsv',
            'tsv_file',
            'text_file'
        ]
    
    def _is_supported_connection_string(self, connection_string: str) -> bool:
        """检查是否支持的连接字符串"""
        return (connection_string.endswith(('.csv', '.tsv', '.txt')) or
                connection_string.startswith('file://'))
    
    def _is_supported_connection_object(self, connection: Any) -> bool:
        """检查是否支持的连接对象"""
        return (isinstance(connection, dict) and 
                ('file_path' in connection or 'path' in connection))
    
    def can_handle(self, data_source: Any) -> bool:
        """检查是否能处理指定的数据源"""
        if isinstance(data_source, str):
            return self._is_supported_connection_string(data_source)
        else:
            return self._is_supported_connection_object(data_source)
    
    def _build_operations(self) -> Dict[str, callable]:
        """构建CSV特有操作列表"""
        operations = {
            'read_csv': self._sync_read_csv,
            'write_csv': self._sync_write_csv,
            'append_csv': self._sync_append_csv,
        }
        
        # 添加异步操作
        async_ops = self.get_async_operations()
        for name, func in async_ops.items():
            operations[name] = func
        
        return operations
    
    def _parse_file_config(self, connection_source: Any) -> Dict[str, Any]:
        """解析文件配置"""
        if isinstance(connection_source, str):
            # 简单文件路径
            if connection_source.startswith('file://'):
                file_path = connection_source[7:]  # 移除 'file://'
            else:
                file_path = connection_source
            
            return {
                'file_path': file_path,
                'encoding': self.default_encoding,
                'delimiter': self.default_delimiter,
                'quotechar': self.default_quotechar,
                'has_header': True,
                'skip_rows': 0
            }
        elif isinstance(connection_source, dict):
            # 详细配置对象
            config = {
                'file_path': connection_source.get('file_path') or connection_source.get('path'),
                'encoding': connection_source.get('encoding', self.default_encoding),
                'delimiter': connection_source.get('delimiter', self.default_delimiter),
                'quotechar': connection_source.get('quotechar', self.default_quotechar),
                'has_header': connection_source.get('has_header', True),
                'skip_rows': connection_source.get('skip_rows', 0)
            }
            
            # 根据文件扩展名调整分隔符
            if config['file_path'].endswith('.tsv'):
                config['delimiter'] = '\t'
            
            return config
        else:
            raise ValueError(f"不支持的文件配置类型: {type(connection_source)}")
    
    # ========================================================================
    # 同步方法实现
    # ========================================================================
    
    def _sync_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """同步查询实现 - 读取CSV文件"""
        # 将SQL查询映射为文件读取操作
        # 这里简化处理，实际应用中可以支持简单的SQL过滤
        return self._sync_read_csv(connection, params)
    
    def _sync_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """同步执行实现 - 写入CSV文件"""
        if params and 'data' in params:
            return self._sync_write_csv(connection, params['data'])
        return 0
    
    def _sync_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """同步事务实现 - 批量文件操作"""
        results = []
        total_affected = 0
        
        for operation in operations:
            op_type = operation.get('type', 'read')
            data = operation.get('data')
            
            if op_type == 'read':
                result = self._sync_read_csv(connection)
                results.append(result)
            elif op_type == 'write':
                affected = self._sync_write_csv(connection, data)
                results.append(affected)
                total_affected += affected
            elif op_type == 'append':
                affected = self._sync_append_csv(connection, data)
                results.append(affected)
                total_affected += affected
        
        return {
            'results': results,
            'total_operations': len(operations),
            'total_affected': total_affected,
            'success': True
        }
    
    def _sync_read_csv(self, connection: Any, params: Dict = None) -> List[Dict]:
        """同步读取CSV文件"""
        config = self._parse_file_config(connection)
        file_path = config['file_path']
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"CSV文件不存在: {file_path}")
        
        try:
            results = []
            with open(file_path, 'r', encoding=config['encoding'], newline='') as csvfile:
                # 跳过指定行数
                for _ in range(config['skip_rows']):
                    next(csvfile, None)
                
                reader = csv.DictReader(
                    csvfile,
                    delimiter=config['delimiter'],
                    quotechar=config['quotechar']
                )
                
                for row in reader:
                    # 应用参数过滤
                    if params:
                        if 'columns' in params:
                            # 只保留指定列
                            row = {k: v for k, v in row.items() if k in params['columns']}
                        if 'limit' in params and len(results) >= params['limit']:
                            break
                    
                    results.append(row)
            
            self.logger.info(f"成功读取CSV文件: {file_path}, 记录数: {len(results)}")
            return results
            
        except Exception as e:
            self.logger.error(f"读取CSV文件失败 {file_path}: {e}")
            raise
    
    def _sync_write_csv(self, connection: Any, data: List[Dict]) -> int:
        """同步写入CSV文件"""
        config = self._parse_file_config(connection)
        file_path = config['file_path']
        
        if not data:
            return 0
        
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path) or '.', exist_ok=True)
            
            with open(file_path, 'w', encoding=config['encoding'], newline='') as csvfile:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(
                    csvfile,
                    fieldnames=fieldnames,
                    delimiter=config['delimiter'],
                    quotechar=config['quotechar']
                )
                
                if config['has_header']:
                    writer.writeheader()
                
                for row in data:
                    writer.writerow(row)
            
            self.logger.info(f"成功写入CSV文件: {file_path}, 记录数: {len(data)}")
            return len(data)
            
        except Exception as e:
            self.logger.error(f"写入CSV文件失败 {file_path}: {e}")
            raise
    
    def _sync_append_csv(self, connection: Any, data: List[Dict]) -> int:
        """同步追加CSV文件"""
        config = self._parse_file_config(connection)
        file_path = config['file_path']
        
        if not data:
            return 0
        
        try:
            # 检查文件是否存在
            file_exists = os.path.exists(file_path)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path) or '.', exist_ok=True)
            
            with open(file_path, 'a', encoding=config['encoding'], newline='') as csvfile:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(
                    csvfile,
                    fieldnames=fieldnames,
                    delimiter=config['delimiter'],
                    quotechar=config['quotechar']
                )
                
                # 如果文件不存在且需要头部，写入头部
                if not file_exists and config['has_header']:
                    writer.writeheader()
                
                for row in data:
                    writer.writerow(row)
            
            self.logger.info(f"成功追加CSV文件: {file_path}, 记录数: {len(data)}")
            return len(data)
            
        except Exception as e:
            self.logger.error(f"追加CSV文件失败 {file_path}: {e}")
            raise

    # ========================================================================
    # 异步方法实现
    # ========================================================================

    async def _create_async_connection(self, connection_source: Any) -> Any:
        """创建异步文件连接"""
        config = self._parse_file_config(connection_source)

        # 验证文件路径
        file_path = config['file_path']
        if not os.path.exists(os.path.dirname(file_path) or '.'):
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

        self.logger.info(f"创建异步文件连接: {file_path}")
        return config

    async def _close_async_connection(self, connection: Any) -> None:
        """关闭异步文件连接"""
        # 文件连接不需要特殊关闭操作
        self.logger.debug("异步文件连接已关闭")

    async def _create_async_pool(self, connection_source: Any, min_size: int = 5, max_size: int = 20) -> Any:
        """创建异步连接池（文件不需要连接池）"""
        return await self._create_async_connection(connection_source)

    async def _async_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """异步查询实现 - 读取CSV文件"""
        return await self._async_read_csv(connection, params)

    async def _async_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """异步执行实现 - 写入CSV文件"""
        if params and 'data' in params:
            return await self._async_write_csv(connection, params['data'])
        return 0

    async def _async_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步事务实现 - 批量文件操作"""
        results = []
        total_affected = 0

        for operation in operations:
            op_type = operation.get('type', 'read')
            data = operation.get('data')

            if op_type == 'read':
                result = await self._async_read_csv(connection)
                results.append(result)
            elif op_type == 'write':
                affected = await self._async_write_csv(connection, data)
                results.append(affected)
                total_affected += affected
            elif op_type == 'append':
                affected = await self._async_append_csv(connection, data)
                results.append(affected)
                total_affected += affected

        return {
            'results': results,
            'total_operations': len(operations),
            'total_affected': total_affected,
            'success': True
        }

    async def _async_batch(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步批量操作实现"""
        return await self._async_transaction(connection, operations)

    async def _async_stream_query(self, connection: Any, sql: str, params: Dict = None) -> AsyncIterator[Dict]:
        """异步流式查询实现 - 逐行读取CSV"""
        if not AIOFILES_AVAILABLE:
            raise ImportError("aiofiles未安装，无法执行异步文件流式读取")

        config = connection
        file_path = config['file_path']

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"CSV文件不存在: {file_path}")

        try:
            async with aiofiles.open(file_path, 'r', encoding=config['encoding']) as csvfile:
                # 跳过指定行数
                for _ in range(config['skip_rows']):
                    await csvfile.readline()

                # 读取头部
                header_line = await csvfile.readline()
                if not header_line:
                    return

                fieldnames = header_line.strip().split(config['delimiter'])

                # 逐行读取数据
                row_count = 0
                async for line in csvfile:
                    if not line.strip():
                        continue

                    values = line.strip().split(config['delimiter'])

                    # 构建字典
                    row = {}
                    for i, fieldname in enumerate(fieldnames):
                        if i < len(values):
                            row[fieldname.strip(config['quotechar'])] = values[i].strip(config['quotechar'])
                        else:
                            row[fieldname.strip(config['quotechar'])] = ''

                    # 应用参数过滤
                    if params:
                        if 'columns' in params:
                            row = {k: v for k, v in row.items() if k in params['columns']}
                        if 'limit' in params and row_count >= params['limit']:
                            break

                    yield row
                    row_count += 1

        except Exception as e:
            self.logger.error(f"异步流式读取CSV文件失败 {file_path}: {e}")
            raise

    async def _async_read_csv(self, connection: Any, params: Dict = None) -> List[Dict]:
        """异步读取CSV文件"""
        if not AIOFILES_AVAILABLE:
            raise ImportError("aiofiles未安装，无法执行异步文件读取")

        config = connection
        file_path = config['file_path']

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"CSV文件不存在: {file_path}")

        try:
            results = []
            async with aiofiles.open(file_path, 'r', encoding=config['encoding']) as csvfile:
                content = await csvfile.read()

                # 使用同步CSV解析器处理内容
                import io
                csv_reader = csv.DictReader(
                    io.StringIO(content),
                    delimiter=config['delimiter'],
                    quotechar=config['quotechar']
                )

                for row in csv_reader:
                    # 应用参数过滤
                    if params:
                        if 'columns' in params:
                            row = {k: v for k, v in row.items() if k in params['columns']}
                        if 'limit' in params and len(results) >= params['limit']:
                            break

                    results.append(row)

            self.logger.info(f"成功异步读取CSV文件: {file_path}, 记录数: {len(results)}")
            return results

        except Exception as e:
            self.logger.error(f"异步读取CSV文件失败 {file_path}: {e}")
            raise

    async def _async_write_csv(self, connection: Any, data: List[Dict]) -> int:
        """异步写入CSV文件"""
        if not AIOFILES_AVAILABLE:
            raise ImportError("aiofiles未安装，无法执行异步文件写入")

        config = connection
        file_path = config['file_path']

        if not data:
            return 0

        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path) or '.', exist_ok=True)

            # 生成CSV内容
            import io
            output = io.StringIO()
            fieldnames = data[0].keys()
            writer = csv.DictWriter(
                output,
                fieldnames=fieldnames,
                delimiter=config['delimiter'],
                quotechar=config['quotechar']
            )

            if config['has_header']:
                writer.writeheader()

            for row in data:
                writer.writerow(row)

            # 异步写入文件
            async with aiofiles.open(file_path, 'w', encoding=config['encoding']) as csvfile:
                await csvfile.write(output.getvalue())

            self.logger.info(f"成功异步写入CSV文件: {file_path}, 记录数: {len(data)}")
            return len(data)

        except Exception as e:
            self.logger.error(f"异步写入CSV文件失败 {file_path}: {e}")
            raise

    async def _async_append_csv(self, connection: Any, data: List[Dict]) -> int:
        """异步追加CSV文件"""
        if not AIOFILES_AVAILABLE:
            raise ImportError("aiofiles未安装，无法执行异步文件追加")

        config = connection
        file_path = config['file_path']

        if not data:
            return 0

        try:
            # 检查文件是否存在
            file_exists = os.path.exists(file_path)

            # 确保目录存在
            os.makedirs(os.path.dirname(file_path) or '.', exist_ok=True)

            # 生成CSV内容
            import io
            output = io.StringIO()
            fieldnames = data[0].keys()
            writer = csv.DictWriter(
                output,
                fieldnames=fieldnames,
                delimiter=config['delimiter'],
                quotechar=config['quotechar']
            )

            # 如果文件不存在且需要头部，写入头部
            if not file_exists and config['has_header']:
                writer.writeheader()

            for row in data:
                writer.writerow(row)

            # 异步追加文件
            async with aiofiles.open(file_path, 'a', encoding=config['encoding']) as csvfile:
                await csvfile.write(output.getvalue())

            self.logger.info(f"成功异步追加CSV文件: {file_path}, 记录数: {len(data)}")
            return len(data)

        except Exception as e:
            self.logger.error(f"异步追加CSV文件失败 {file_path}: {e}")
            raise

    # ========================================================================
    # CSV特有功能
    # ========================================================================

    def get_async_operations(self) -> Dict[str, Any]:
        """获取支持的异步操作列表"""
        operations = super().get_async_operations()

        # 添加CSV特有异步操作
        operations.update({
            'async_read_csv': self._async_read_csv,
            'async_write_csv': self._async_write_csv,
            'async_append_csv': self._async_append_csv,
        })

        return operations
