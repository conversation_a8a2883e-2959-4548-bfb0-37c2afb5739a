"""
数据库适配器集成测试

测试数据库适配器与核心架构的集成，确保端到端功能正常
"""

import pytest
from unittest.mock import Mock, patch

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from core.enterprise_data_architecture import DataRegistry, TemplateScope, DataProxy
from core.adapters.database.postgresql import PostgreSQLAdapter
from core.adapters.database.mysql import MySQLAdapter
from core.adapters.database.sqlite import SQLiteAdapter


class TestDatabaseIntegration:
    """数据库适配器集成测试套件"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.registry = DataRegistry()
        
        # 注册所有数据库适配器
        self.registry.register_adapter(PostgreSQLAdapter)
        self.registry.register_adapter(MySQLAdapter)
        self.registry.register_adapter(SQLiteAdapter)
        
        # 创建模板作用域
        self.scope = TemplateScope("integration_test", self.registry)
    
    def test_postgresql_adapter_registration(self):
        """测试PostgreSQL适配器注册"""
        supported_types = self.registry.get_supported_types()
        
        # 验证PostgreSQL类型已注册
        postgresql_types = [
            'postgresql', 'postgresql_connection', 'postgresql_connection_string',
            'postgresql_url', 'postgres', 'postgres_connection',
            'postgres_connection_string', 'postgres_url'
        ]
        
        for pg_type in postgresql_types:
            assert pg_type in supported_types
    
    def test_postgresql_connection_string_detection(self):
        """测试PostgreSQL连接字符串自动检测"""
        connection_strings = [
            'postgresql://user:pass@localhost:5432/testdb',
            'postgres://user:pass@localhost:5432/testdb'
        ]
        
        for conn_str in connection_strings:
            # 检测类型
            detected_type = self.registry._detect_type(conn_str)
            assert detected_type in ['postgresql_connection_string', 'postgres_connection_string']
            
            # 获取适配器
            adapter = self.registry.get_adapter(conn_str)
            assert isinstance(adapter, PostgreSQLAdapter)
    
    def test_template_scope_database_registration(self):
        """测试在模板作用域中注册数据库"""
        connection_string = 'postgresql://user:pass@localhost:5432/testdb'
        
        # 在作用域中注册数据库
        with patch.object(PostgreSQLAdapter, '_create_connection') as mock_create:
            mock_connection = Mock()
            mock_create.return_value = mock_connection
            
            # 注册数据源
            proxy = self.scope.register_data_source('db', connection_string)
            
            # 验证代理对象
            assert isinstance(proxy, DataProxy)
            assert isinstance(proxy.adapter, PostgreSQLAdapter)
            assert proxy.original_source == connection_string  # 原始源是连接字符串
            assert proxy.source is mock_connection  # 实际源是连接对象
            
            # 验证作用域状态
            assert len(self.scope.data_proxies) == 1
            assert 'db' in self.scope.data_proxies
    
    def test_database_operations_through_proxy(self):
        """测试通过代理执行数据库操作"""
        connection_string = 'postgresql://user:pass@localhost:5432/testdb'
        
        with patch.object(PostgreSQLAdapter, '_create_connection') as mock_create, \
             patch.object(PostgreSQLAdapter, '_execute_query') as mock_query, \
             patch.object(PostgreSQLAdapter, '_execute_command') as mock_execute:
            
            # Mock连接和操作
            mock_connection = Mock()
            mock_create.return_value = mock_connection
            
            mock_query.return_value = [
                {'id': 1, 'name': 'Alice', 'email': '<EMAIL>'},
                {'id': 2, 'name': 'Bob', 'email': '<EMAIL>'}
            ]
            
            mock_execute.return_value = 1
            
            # 注册数据源
            proxy = self.scope.register_data_source('db', connection_string)
            
            # 执行查询操作
            query_result = proxy.query("SELECT * FROM users")
            
            # 验证查询结果
            assert query_result.success is True
            assert len(query_result.data) == 2
            assert query_result.data[0]['name'] == 'Alice'
            assert query_result.operation == 'query'
            assert query_result.adapter_type == 'PostgreSQLAdapter'
            
            # 执行命令操作
            execute_result = proxy.execute("INSERT INTO users VALUES (3, 'Charlie', '<EMAIL>')")
            
            # 验证执行结果
            assert execute_result.success is True
            assert execute_result.data == 1
            assert execute_result.operation == 'execute'
            
            # 验证Mock调用
            mock_query.assert_called_once()
            mock_execute.assert_called_once()
    
    def test_postgresql_specific_operations(self):
        """测试PostgreSQL特有操作"""
        connection_string = 'postgresql://user:pass@localhost:5432/testdb'
        
        with patch.object(PostgreSQLAdapter, '_create_connection') as mock_create:
            mock_connection = Mock()
            mock_create.return_value = mock_connection
            
            # 注册数据源
            proxy = self.scope.register_data_source('db', connection_string)
            
            # 验证PostgreSQL特有操作存在
            postgresql_operations = [
                'copy_from', 'copy_to', 'listen', 'notify',
                'vacuum', 'reindex', 'analyze_table'
            ]
            
            for operation in postgresql_operations:
                assert hasattr(proxy, operation)
                assert callable(getattr(proxy, operation))
    
    def test_multiple_database_connections(self):
        """测试多个数据库连接"""
        connections = {
            'main_db': 'postgresql://user:pass@localhost:5432/maindb',
            'analytics_db': 'postgresql://user:pass@localhost:5432/analyticsdb',
            'cache_db': 'postgresql://user:pass@localhost:5432/cachedb'
        }
        
        with patch.object(PostgreSQLAdapter, '_create_connection') as mock_create:
            mock_create.return_value = Mock()
            
            # 注册多个数据库连接
            proxies = {}
            for name, conn_str in connections.items():
                proxies[name] = self.scope.register_data_source(name, conn_str)
            
            # 验证所有连接都已注册
            assert len(self.scope.data_proxies) == 3
            
            for name in connections.keys():
                assert name in self.scope.data_proxies
                assert isinstance(self.scope.data_proxies[name], DataProxy)
                assert isinstance(self.scope.data_proxies[name].adapter, PostgreSQLAdapter)
    
    def test_database_error_handling(self):
        """测试数据库错误处理"""
        connection_string = 'postgresql://user:pass@localhost:5432/testdb'
        
        with patch.object(PostgreSQLAdapter, '_create_connection') as mock_create, \
             patch.object(PostgreSQLAdapter, '_execute_query') as mock_query:
            
            mock_connection = Mock()
            mock_create.return_value = mock_connection
            
            # Mock查询异常
            mock_query.side_effect = Exception("Database connection failed")
            
            # 注册数据源
            proxy = self.scope.register_data_source('db', connection_string)
            
            # 执行查询（应该返回错误结果）
            result = proxy.query("SELECT * FROM users")
            
            # 验证错误处理
            assert result.success is False
            assert "Database connection failed" in result.error
            assert result.data is None
            assert result.operation == 'query'
    
    def test_scope_cleanup_with_database(self):
        """测试作用域清理包含数据库连接"""
        connection_string = 'postgresql://user:pass@localhost:5432/testdb'
        
        with patch.object(PostgreSQLAdapter, '_create_connection') as mock_create:
            mock_connection = Mock()
            mock_create.return_value = mock_connection
            
            # 注册数据源
            proxy = self.scope.register_data_source('db', connection_string)
            
            # 验证资源已注册
            assert len(self.scope.data_proxies) == 1
            assert self.scope.lifecycle_manager.get_resource_count() >= 1
            
            # 清理作用域
            self.scope.cleanup()
            
            # 验证清理结果
            assert len(self.scope.data_proxies) == 0
            assert self.scope.lifecycle_manager.get_resource_count() == 0
    
    def test_context_manager_with_database(self):
        """测试上下文管理器与数据库"""
        connection_string = 'postgresql://user:pass@localhost:5432/testdb'
        
        with patch.object(PostgreSQLAdapter, '_create_connection') as mock_create:
            mock_connection = Mock()
            mock_create.return_value = mock_connection
            
            # 使用上下文管理器
            with TemplateScope("context_test", self.registry) as scope:
                # 注册数据源
                proxy = scope.register_data_source('db', connection_string)
                
                # 验证数据源已注册
                assert len(scope.data_proxies) == 1
                assert scope.lifecycle_manager.get_resource_count() >= 1
            
            # 退出上下文后，资源应该被自动清理
            assert len(scope.data_proxies) == 0
            assert scope.lifecycle_manager.get_resource_count() == 0
    
    def test_adapter_metadata_integration(self):
        """测试适配器元数据集成"""
        # 获取适配器信息
        adapter_info = self.registry.get_adapter_info()
        
        # 验证PostgreSQL适配器信息
        postgresql_types = [t for t in adapter_info.keys() if 'postgresql' in t or 'postgres' in t]
        assert len(postgresql_types) > 0
        
        for pg_type in postgresql_types:
            info = adapter_info[pg_type]
            assert info['class_name'] == 'PostgreSQLAdapter'
            assert 'operations' in info
            assert 'metadata' in info
            
            # 验证PostgreSQL特有操作在操作列表中
            operations = info['operations']
            assert 'query' in operations
            assert 'execute' in operations
            assert 'copy_from' in operations
            assert 'vacuum' in operations


class TestMySQLIntegration:
    """MySQL适配器集成测试套件"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.registry = DataRegistry()
        self.registry.register_adapter(MySQLAdapter)
        self.scope = TemplateScope("mysql_integration_test", self.registry)

    def test_mysql_adapter_registration(self):
        """测试MySQL适配器注册"""
        supported_types = self.registry.get_supported_types()

        # 验证MySQL类型已注册
        mysql_types = [
            'mysql', 'mysql_connection', 'mysql_connection_string', 'mysql_url',
            'mariadb', 'mariadb_connection', 'mariadb_connection_string', 'mariadb_url'
        ]

        for mysql_type in mysql_types:
            assert mysql_type in supported_types

    def test_mysql_connection_string_detection(self):
        """测试MySQL连接字符串自动检测"""
        connection_strings = [
            'mysql://user:pass@localhost:3306/testdb',
            'mariadb://user:pass@localhost:3306/testdb'
        ]

        for conn_str in connection_strings:
            # 检测类型
            detected_type = self.registry._detect_type(conn_str)
            assert detected_type == 'mysql_connection_string'

            # 获取适配器
            adapter = self.registry.get_adapter(conn_str)
            assert isinstance(adapter, MySQLAdapter)

    def test_mysql_specific_operations(self):
        """测试MySQL特有操作"""
        connection_string = 'mysql://user:pass@localhost:3306/testdb'

        with patch.object(MySQLAdapter, '_create_connection') as mock_create:
            mock_connection = Mock()
            mock_create.return_value = mock_connection

            # 注册数据源
            proxy = self.scope.register_data_source('db', connection_string)

            # 验证MySQL特有操作存在
            mysql_operations = [
                'show_tables', 'show_databases', 'show_columns', 'show_indexes',
                'show_status', 'show_variables', 'optimize_table', 'repair_table',
                'check_table', 'load_data'
            ]

            for operation in mysql_operations:
                assert hasattr(proxy, operation)
                assert callable(getattr(proxy, operation))


class TestSQLiteIntegration:
    """SQLite适配器集成测试套件"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.registry = DataRegistry()
        self.registry.register_adapter(SQLiteAdapter)
        self.scope = TemplateScope("sqlite_integration_test", self.registry)

    def test_sqlite_adapter_registration(self):
        """测试SQLite适配器注册"""
        supported_types = self.registry.get_supported_types()

        # 验证SQLite类型已注册
        sqlite_types = [
            'sqlite', 'sqlite_connection', 'sqlite_connection_string',
            'sqlite_url', 'sqlite3', 'sqlite3_connection'
        ]

        for sqlite_type in sqlite_types:
            assert sqlite_type in supported_types

    def test_sqlite_connection_string_detection(self):
        """测试SQLite连接字符串自动检测"""
        connection_strings = [
            'sqlite:///path/to/test.db',
            'test.db',
            'test.sqlite',
            'test.sqlite3',
            ':memory:'
        ]

        for conn_str in connection_strings:
            # 检测类型
            detected_type = self.registry._detect_type(conn_str)
            assert detected_type == 'sqlite_connection_string'

            # 获取适配器
            adapter = self.registry.get_adapter(conn_str)
            assert isinstance(adapter, SQLiteAdapter)

    def test_sqlite_memory_database_integration(self):
        """测试SQLite内存数据库集成"""
        # 使用真实的内存数据库进行测试
        proxy = self.scope.register_data_source('db', ':memory:')

        # 验证代理对象
        assert isinstance(proxy, DataProxy)
        assert isinstance(proxy.adapter, SQLiteAdapter)

        # 执行实际的数据库操作
        # 创建表
        create_result = proxy.execute("CREATE TABLE test_users (id INTEGER PRIMARY KEY, name TEXT)")
        assert create_result.success is True

        # 插入数据
        insert_result = proxy.execute("INSERT INTO test_users (name) VALUES ('Alice')")
        assert insert_result.success is True
        assert insert_result.data == 1  # 影响行数

        # 查询数据
        query_result = proxy.query("SELECT * FROM test_users")
        assert query_result.success is True
        assert len(query_result.data) == 1
        assert query_result.data[0]['name'] == 'Alice'

    def test_sqlite_specific_operations(self):
        """测试SQLite特有操作"""
        # 使用真实的内存数据库
        proxy = self.scope.register_data_source('db', ':memory:')

        # 验证SQLite特有操作存在
        sqlite_operations = [
            'pragma', 'vacuum', 'reindex', 'integrity_check', 'quick_check',
            'attach', 'detach', 'backup', 'restore', 'create_function'
        ]

        for operation in sqlite_operations:
            assert hasattr(proxy, operation)
            assert callable(getattr(proxy, operation))

        # 测试实际的PRAGMA操作
        pragma_result = proxy.pragma('table_list')
        assert pragma_result.success is True


class TestMultiDatabaseIntegration:
    """多数据库集成测试套件"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.registry = DataRegistry()

        # 注册所有数据库适配器
        self.registry.register_adapter(PostgreSQLAdapter)
        self.registry.register_adapter(MySQLAdapter)
        self.registry.register_adapter(SQLiteAdapter)

        self.scope = TemplateScope("multi_db_test", self.registry)

    def test_multiple_database_types_registration(self):
        """测试多种数据库类型注册"""
        supported_types = self.registry.get_supported_types()

        # 验证所有数据库类型都已注册
        expected_types = [
            # PostgreSQL
            'postgresql', 'postgresql_connection', 'postgresql_connection_string',
            'postgresql_url', 'postgres', 'postgres_connection',
            'postgres_connection_string', 'postgres_url',
            # MySQL
            'mysql', 'mysql_connection', 'mysql_connection_string', 'mysql_url',
            'mariadb', 'mariadb_connection', 'mariadb_connection_string', 'mariadb_url',
            # SQLite
            'sqlite', 'sqlite_connection', 'sqlite_connection_string',
            'sqlite_url', 'sqlite3', 'sqlite3_connection'
        ]

        for db_type in expected_types:
            assert db_type in supported_types

    def test_mixed_database_connections(self):
        """测试混合数据库连接"""
        connections = {
            'postgres_db': 'postgresql://user:pass@localhost:5432/postgres_db',
            'mysql_db': 'mysql://user:pass@localhost:3306/mysql_db',
            'sqlite_db': ':memory:'
        }

        # 注册不同类型的数据库连接
        proxies = {}

        # PostgreSQL (Mock)
        with patch.object(PostgreSQLAdapter, '_create_connection') as mock_pg_create:
            mock_pg_create.return_value = Mock()
            proxies['postgres_db'] = self.scope.register_data_source('postgres_db', connections['postgres_db'])

        # MySQL (Mock)
        with patch.object(MySQLAdapter, '_create_connection') as mock_mysql_create:
            mock_mysql_create.return_value = Mock()
            proxies['mysql_db'] = self.scope.register_data_source('mysql_db', connections['mysql_db'])

        # SQLite (Real)
        proxies['sqlite_db'] = self.scope.register_data_source('sqlite_db', connections['sqlite_db'])

        # 验证所有连接都已注册
        assert len(self.scope.data_proxies) == 3

        # 验证适配器类型
        assert isinstance(proxies['postgres_db'].adapter, PostgreSQLAdapter)
        assert isinstance(proxies['mysql_db'].adapter, MySQLAdapter)
        assert isinstance(proxies['sqlite_db'].adapter, SQLiteAdapter)

        # 测试SQLite的实际操作
        sqlite_proxy = proxies['sqlite_db']
        create_result = sqlite_proxy.execute("CREATE TABLE test (id INTEGER, name TEXT)")
        assert create_result.success is True


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, '-v'])
