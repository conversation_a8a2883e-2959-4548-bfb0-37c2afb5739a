"""
LifecycleManager生命周期管理器测试

测试LifecycleManager类的所有功能，确保资源生命周期管理的正确性
"""

import pytest
import time
import threading
import weakref
from typing import Any, Callable
from unittest.mock import Mock, MagicMock

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from core.enterprise_data_architecture import LifecycleManager, ResourceManagementError


class MockResource:
    """模拟资源对象"""
    
    def __init__(self, name: str):
        self.name = name
        self.cleaned_up = False
    
    def cleanup(self):
        """清理方法"""
        self.cleaned_up = True
    
    def __del__(self):
        """析构方法"""
        pass


class TestLifecycleManager:
    """LifecycleManager类测试套件"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.manager = LifecycleManager()
    
    def test_manager_initialization(self):
        """测试管理器初始化"""
        assert isinstance(self.manager.resources, dict)
        assert isinstance(self.manager.cleanup_callbacks, dict)
        assert hasattr(self.manager, 'logger')
        assert hasattr(self.manager, '_lock')
        assert self.manager.get_resource_count() == 0
    
    def test_register_single_resource(self):
        """测试注册单个资源"""
        resource = MockResource("test_resource")
        resource_id = "test_id_1"
        
        # 注册资源
        self.manager.register_resource(resource_id, resource)
        
        # 验证资源已注册
        assert self.manager.get_resource_count() == 1
        assert resource_id in self.manager.resources
        assert resource_id in self.manager.cleanup_callbacks
        
        # 验证弱引用正常工作
        weak_ref = self.manager.resources[resource_id]
        assert weak_ref() is resource
    
    def test_register_multiple_resources(self):
        """测试注册多个资源"""
        resources = [
            MockResource("resource_1"),
            MockResource("resource_2"),
            MockResource("resource_3")
        ]
        
        # 注册多个资源
        for i, resource in enumerate(resources):
            self.manager.register_resource(f"id_{i}", resource)
        
        # 验证所有资源已注册
        assert self.manager.get_resource_count() == 3
        
        for i in range(3):
            resource_id = f"id_{i}"
            assert resource_id in self.manager.resources
            assert self.manager.resources[resource_id]() is resources[i]
    
    def test_register_with_cleanup_callback(self):
        """测试注册资源时添加清理回调"""
        resource = MockResource("test_resource")
        resource_id = "test_id"
        callback_called = False
        
        def cleanup_callback():
            nonlocal callback_called
            callback_called = True
            resource.cleanup()
        
        # 注册资源和清理回调
        self.manager.register_resource(resource_id, resource, cleanup_callback)
        
        # 验证回调已注册
        assert len(self.manager.cleanup_callbacks[resource_id]) == 1
        
        # 清理资源
        success = self.manager.cleanup_resource(resource_id)
        
        # 验证清理成功且回调被调用
        assert success is True
        assert callback_called is True
        assert resource.cleaned_up is True
        assert self.manager.get_resource_count() == 0
    
    def test_cleanup_single_resource(self):
        """测试清理单个资源"""
        resource = MockResource("test_resource")
        resource_id = "test_id"
        
        # 注册资源
        self.manager.register_resource(resource_id, resource)
        assert self.manager.get_resource_count() == 1
        
        # 清理资源
        success = self.manager.cleanup_resource(resource_id)
        
        # 验证清理成功
        assert success is True
        assert self.manager.get_resource_count() == 0
        assert resource_id not in self.manager.resources
        assert resource_id not in self.manager.cleanup_callbacks
    
    def test_cleanup_nonexistent_resource(self):
        """测试清理不存在的资源"""
        success = self.manager.cleanup_resource("nonexistent_id")
        assert success is False
    
    def test_cleanup_all_resources(self):
        """测试清理所有资源"""
        resources = [MockResource(f"resource_{i}") for i in range(5)]
        callbacks_called = []
        
        # 注册多个资源，每个都有清理回调
        for i, resource in enumerate(resources):
            def make_callback(idx):
                def callback():
                    callbacks_called.append(idx)
                    resources[idx].cleanup()
                return callback
            
            self.manager.register_resource(f"id_{i}", resource, make_callback(i))
        
        # 验证所有资源已注册
        assert self.manager.get_resource_count() == 5
        
        # 清理所有资源
        count = self.manager.cleanup_all()
        
        # 验证清理结果
        assert count == 5
        assert self.manager.get_resource_count() == 0
        assert len(callbacks_called) == 5
        assert set(callbacks_called) == set(range(5))
        
        # 验证所有资源都被清理
        for resource in resources:
            assert resource.cleaned_up is True
    
    def test_weak_reference_cleanup(self):
        """测试弱引用自动清理"""
        resource_id = "test_id"
        callback_called = False
        
        def cleanup_callback():
            nonlocal callback_called
            callback_called = True
        
        # 在局部作用域中创建资源
        def create_and_register_resource():
            resource = MockResource("temp_resource")
            self.manager.register_resource(resource_id, resource, cleanup_callback)
            return resource
        
        # 创建并注册资源
        resource = create_and_register_resource()
        assert self.manager.get_resource_count() == 1
        
        # 删除资源引用，触发弱引用回调
        del resource
        
        # 强制垃圾回收
        import gc
        gc.collect()
        
        # 等待一小段时间让弱引用回调执行
        time.sleep(0.01)
        
        # 验证资源已自动清理
        assert self.manager.get_resource_count() == 0
        assert callback_called is True
    
    def test_multiple_cleanup_callbacks(self):
        """测试多个清理回调"""
        resource = MockResource("test_resource")
        resource_id = "test_id"
        callbacks_called = []
        
        def make_callback(name):
            def callback():
                callbacks_called.append(name)
            return callback
        
        # 注册资源
        self.manager.register_resource(resource_id, resource)
        
        # 添加多个清理回调
        for i in range(3):
            self.manager.add_cleanup_callback(resource_id, make_callback(f"callback_{i}"))
        
        # 清理资源
        success = self.manager.cleanup_resource(resource_id)
        
        # 验证所有回调都被调用
        assert success is True
        assert len(callbacks_called) == 3
        assert set(callbacks_called) == {"callback_0", "callback_1", "callback_2"}
    
    def test_callback_error_handling(self):
        """测试回调错误处理"""
        resource = MockResource("test_resource")
        resource_id = "test_id"
        good_callback_called = False
        
        def good_callback():
            nonlocal good_callback_called
            good_callback_called = True
        
        def bad_callback():
            raise Exception("Callback error")
        
        # 注册资源和回调
        self.manager.register_resource(resource_id, resource)
        self.manager.add_cleanup_callback(resource_id, bad_callback)
        self.manager.add_cleanup_callback(resource_id, good_callback)
        
        # 清理资源（应该处理回调错误）
        success = self.manager.cleanup_resource(resource_id)
        
        # 验证清理成功，好的回调被调用
        assert success is True
        assert good_callback_called is True
        assert self.manager.get_resource_count() == 0
    
    def test_thread_safety(self):
        """测试线程安全"""
        import threading
        import time
        
        results = []
        errors = []
        
        def register_resources():
            try:
                for i in range(10):
                    resource = MockResource(f"resource_{threading.current_thread().ident}_{i}")
                    self.manager.register_resource(f"thread_{threading.current_thread().ident}_{i}", resource)
                results.append('registered')
            except Exception as e:
                errors.append(str(e))
        
        def cleanup_resources():
            try:
                time.sleep(0.01)  # 稍微延迟
                count = self.manager.cleanup_all()
                results.append(f'cleaned_{count}')
            except Exception as e:
                errors.append(str(e))
        
        # 创建多个线程同时操作
        threads = []
        for _ in range(3):
            threads.append(threading.Thread(target=register_resources))
        threads.append(threading.Thread(target=cleanup_resources))
        
        # 启动所有线程
        for thread in threads:
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证没有错误发生
        assert len(errors) == 0, f"Thread safety test failed with errors: {errors}"
        assert len(results) > 0
    
    def test_resource_count_accuracy(self):
        """测试资源计数准确性"""
        assert self.manager.get_resource_count() == 0
        
        # 注册资源
        resources = []
        for i in range(5):
            resource = MockResource(f"resource_{i}")
            resources.append(resource)
            self.manager.register_resource(f"id_{i}", resource)
            assert self.manager.get_resource_count() == i + 1
        
        # 逐个清理资源
        for i in range(5):
            self.manager.cleanup_resource(f"id_{i}")
            assert self.manager.get_resource_count() == 4 - i
        
        assert self.manager.get_resource_count() == 0


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, '-v'])
