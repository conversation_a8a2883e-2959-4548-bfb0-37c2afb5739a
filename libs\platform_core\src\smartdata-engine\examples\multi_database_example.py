"""
多数据库适配器使用示例

展示如何使用新的企业级模板引擎架构同时操作多种数据库
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../'))

from core.enterprise_data_architecture import DataRegistry, TemplateScope
from core.adapters.database.postgresql import PostgreSQLAdapter
from core.adapters.database.mysql import MySQLAdapter
from core.adapters.database.sqlite import SQLiteAdapter


def main():
    """主函数 - 演示多数据库适配器的使用"""
    
    print("🚀 企业级模板引擎 - 多数据库适配器使用示例")
    print("=" * 70)
    
    # 1. 创建数据注册表并注册所有数据库适配器
    print("\n📋 步骤1: 创建数据注册表并注册适配器")
    registry = DataRegistry()
    
    # 注册所有数据库适配器
    registry.register_adapter(PostgreSQLAdapter)
    registry.register_adapter(MySQLAdapter)
    registry.register_adapter(SQLiteAdapter)
    
    print(f"✅ 已注册适配器，支持的数据类型: {len(registry.get_supported_types())} 种")
    
    # 2. 查看支持的数据库类型
    print("\n📊 步骤2: 查看支持的数据库类型")
    supported_types = registry.get_supported_types()
    
    # 按数据库分组显示
    db_groups = {
        'PostgreSQL': [t for t in supported_types if 'postgresql' in t or 'postgres' in t],
        'MySQL': [t for t in supported_types if 'mysql' in t or 'mariadb' in t],
        'SQLite': [t for t in supported_types if 'sqlite' in t]
    }
    
    for db_name, types in db_groups.items():
        print(f"  🗄️  {db_name}: {len(types)} 种类型")
        for db_type in types[:3]:  # 只显示前3个
            print(f"     • {db_type}")
        if len(types) > 3:
            print(f"     • ... 还有 {len(types) - 3} 种")
    
    # 3. 创建模板作用域并注册多个数据库
    print("\n🎯 步骤3: 创建模板作用域并注册多个数据库")
    
    with TemplateScope("multi_database_example", registry) as scope:
        print(f"✅ 创建作用域: {scope.get_scope_id()}")
        
        # 4. 注册不同类型的数据库连接
        print("\n🔗 步骤4: 注册不同类型的数据库连接")
        
        databases = {
            # PostgreSQL - 主数据库
            'main_db': {
                'connection': 'postgresql://user:password@localhost:5432/main_database',
                'description': 'PostgreSQL主数据库，存储核心业务数据'
            },
            # MySQL - 分析数据库
            'analytics_db': {
                'connection': 'mysql://user:password@localhost:3306/analytics_database',
                'description': 'MySQL分析数据库，存储统计和报表数据'
            },
            # SQLite - 缓存数据库（实际可用）
            'cache_db': {
                'connection': ':memory:',
                'description': 'SQLite内存数据库，用于临时缓存'
            }
        }
        
        proxies = {}
        
        for db_name, db_info in databases.items():
            try:
                print(f"  📊 注册数据库: {db_name}")
                print(f"     描述: {db_info['description']}")
                print(f"     连接: {db_info['connection']}")
                
                # 检测数据库类型
                detected_type = registry._detect_type(db_info['connection'])
                print(f"     检测到类型: {detected_type}")
                
                # 获取适配器
                adapter = registry.get_adapter(db_info['connection'])
                print(f"     使用适配器: {adapter.__class__.__name__}")
                
                # 注册数据源到作用域
                if db_name == 'cache_db':
                    # SQLite可以实际注册
                    proxy = scope.register_data_source(db_name, db_info['connection'])
                    proxies[db_name] = proxy
                    print(f"     ✅ 成功注册（实际连接）")
                else:
                    # PostgreSQL和MySQL只是演示（没有真实数据库）
                    print(f"     ⚠️  演示注册（需要真实数据库）")
                
                print()
                
            except Exception as e:
                print(f"     ❌ 注册失败: {e}")
                print()
        
        # 5. 演示SQLite数据库的实际操作
        if 'cache_db' in proxies:
            print("🎨 步骤5: 演示SQLite数据库的实际操作")
            cache_db = proxies['cache_db']
            
            # 创建表
            print("  📝 创建缓存表...")
            create_result = cache_db.execute("""
                CREATE TABLE cache_entries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE NOT NULL,
                    value TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print(f"     结果: {'成功' if create_result.success else '失败'}")
            
            # 插入数据
            print("  📝 插入缓存数据...")
            insert_queries = [
                "INSERT INTO cache_entries (key, value) VALUES ('user:1001', '{\"name\": \"Alice\", \"role\": \"admin\"}')",
                "INSERT INTO cache_entries (key, value) VALUES ('user:1002', '{\"name\": \"Bob\", \"role\": \"user\"}')",
                "INSERT INTO cache_entries (key, value) VALUES ('config:theme', '{\"primary\": \"blue\", \"secondary\": \"gray\"}')"
            ]
            
            for query in insert_queries:
                result = cache_db.execute(query)
                print(f"     插入结果: {'成功' if result.success else '失败'} (影响行数: {result.data})")
            
            # 查询数据
            print("  📝 查询缓存数据...")
            query_result = cache_db.query("SELECT * FROM cache_entries ORDER BY created_at")
            
            if query_result.success:
                print(f"     查询成功，找到 {len(query_result.data)} 条记录:")
                for entry in query_result.data:
                    print(f"       • {entry['key']}: {entry['value'][:50]}...")
            else:
                print(f"     查询失败: {query_result.error}")
            
            # 演示SQLite特有功能
            print("  🔧 演示SQLite特有功能...")
            
            # PRAGMA操作
            pragma_result = cache_db.pragma('table_info', 'cache_entries')
            if pragma_result.success:
                print(f"     表结构信息: {len(pragma_result.data)} 个字段")
            
            # 完整性检查
            integrity_result = cache_db.integrity_check()
            if integrity_result.success:
                print(f"     完整性检查: {'通过' if integrity_result.data[0].get('integrity_check') == 'ok' else '有问题'}")
        
        # 6. 展示适配器功能对比
        print("\n⚙️  步骤6: 数据库适配器功能对比")
        
        adapters = {
            'PostgreSQL': PostgreSQLAdapter(),
            'MySQL': MySQLAdapter(),
            'SQLite': SQLiteAdapter()
        }
        
        print("  📋 基础操作对比:")
        basic_ops = ['query', 'execute', 'transaction', 'batch']
        for op in basic_ops:
            print(f"     {op:12} ", end="")
            for db_name, adapter in adapters.items():
                operations = adapter.get_operations()
                status = "✅" if op in operations else "❌"
                print(f"{db_name:12} {status}  ", end="")
            print()
        
        print("\n  🔧 特有操作对比:")
        special_ops = {
            'copy_from': 'PostgreSQL',
            'listen': 'PostgreSQL', 
            'vacuum': 'PostgreSQL/SQLite',
            'show_tables': 'MySQL',
            'optimize_table': 'MySQL',
            'pragma': 'SQLite',
            'attach': 'SQLite'
        }
        
        for op, description in special_ops.items():
            print(f"     {op:12} ({description:20}) ", end="")
            for db_name, adapter in adapters.items():
                operations = adapter.get_operations()
                status = "✅" if op in operations else "❌"
                print(f"{db_name:12} {status}  ", end="")
            print()
        
        # 7. 模拟模板渲染场景
        print("\n🎨 步骤7: 模拟模板渲染场景")
        
        template_example = '''
        {# 多数据库模板示例 #}
        
        {# 从主数据库查询用户数据 #}
        {% set users = main_db.query("SELECT * FROM users WHERE active = true LIMIT 10") %}
        
        {# 从分析数据库获取统计数据 #}
        {% set stats = analytics_db.query("SELECT COUNT(*) as total_orders, SUM(amount) as total_revenue FROM orders WHERE date >= '2024-01-01'") %}
        
        {# 从缓存数据库获取配置 #}
        {% set config = cache_db.query("SELECT value FROM cache_entries WHERE key = 'config:theme'") %}
        
        {# 渲染结果 #}
        <h1>业务仪表板</h1>
        
        <div class="stats">
            <h2>统计数据</h2>
            <p>总订单数: {{ stats.data[0].total_orders }}</p>
            <p>总收入: ${{ stats.data[0].total_revenue }}</p>
        </div>
        
        <div class="users">
            <h2>活跃用户 ({{ users.data|length }})</h2>
            <ul>
            {% for user in users.data %}
                <li>{{ user.name }} - {{ user.email }}</li>
            {% endfor %}
            </ul>
        </div>
        
        <div class="theme" style="color: {{ config.data[0].value.primary }}">
            主题配置已应用
        </div>
        '''
        
        print("  📝 模板示例:")
        print(template_example.strip())
        
        # 8. 展示作用域信息
        print("\n📈 步骤8: 作用域信息")
        scope_info = scope.get_scope_info()
        
        print(f"  作用域ID: {scope_info['template_id']}")
        print(f"  数据源数量: {len(scope_info['data_sources'])}")
        print(f"  资源数量: {scope_info['resource_count']}")
        print(f"  注册表支持类型: {len(scope_info['registry_types'])} 种")
        
        if scope_info['data_sources']:
            print("  已注册的数据源:")
            for source_name in scope_info['data_sources']:
                print(f"    • {source_name}")
    
    # 9. 展示自动清理
    print("\n🧹 步骤9: 自动资源清理")
    print("✅ 作用域已自动清理，所有数据库连接已关闭")
    
    # 10. 总结
    print("\n🎉 总结")
    print("=" * 70)
    print("✅ 多数据库支持: 同时支持PostgreSQL、MySQL、SQLite")
    print("✅ 零配置切换: 自动检测数据库类型，无需手动配置")
    print("✅ 统一操作接口: 所有数据库使用相同的方法")
    print("✅ 特有功能支持: 每种数据库的特有功能都可使用")
    print("✅ 模板友好: 在模板中可以直接使用多个数据库")
    print("✅ 自动资源管理: 连接自动创建和清理")
    
    print("\n🚀 企业级特性:")
    print("  • 连接池管理: 自动管理数据库连接")
    print("  • 事务支持: 完整的事务管理功能")
    print("  • 批处理: 高效的批量操作")
    print("  • 错误处理: 统一的错误处理和日志")
    print("  • 性能监控: 内置执行时间统计")
    print("  • 类型安全: 强类型接口，编译时检查")


if __name__ == '__main__':
    main()
