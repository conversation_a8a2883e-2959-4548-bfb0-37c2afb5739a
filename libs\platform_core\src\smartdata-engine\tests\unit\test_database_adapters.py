"""
数据库适配器测试

测试数据库适配器的所有功能，确保数据库操作的正确性
"""

import pytest
from typing import Dict, Any, List
from unittest.mock import Mock, MagicMock, patch

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from core.adapters.database.base import DatabaseAdapterBase, ConnectionInfo
from core.adapters.database.postgresql import PostgreSQLAdapter
from core.adapters.database.mysql import MySQLAdapter
from core.adapters.database.sqlite import SQLiteAdapter


class MockDatabaseAdapter(DatabaseAdapterBase):
    """模拟数据库适配器用于测试基础功能"""
    
    def __init__(self):
        super().__init__()
        self.mock_connection = Mock()
        self.mock_results = []
    
    def supported_types(self) -> List[str]:
        return ['mock_database']
    
    def _is_supported_connection_string(self, connection_string: str) -> bool:
        return connection_string.startswith('mock://')
    
    def _is_supported_connection_object(self, connection: Any) -> bool:
        return hasattr(connection, 'mock_execute')
    
    def _create_connection(self, connection_source):
        return self.mock_connection
    
    def _execute_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        # 模拟查询结果
        if 'users' in sql.lower():
            return [
                {'id': 1, 'name': 'Alice', 'email': '<EMAIL>'},
                {'id': 2, 'name': 'Bob', 'email': '<EMAIL>'}
            ]
        return []
    
    def _execute_command(self, connection: Any, sql: str, params: Dict = None) -> int:
        # 模拟影响行数
        if 'insert' in sql.lower():
            return 1
        elif 'update' in sql.lower():
            return 2
        elif 'delete' in sql.lower():
            return 1
        return 0
    
    def _get_database_info(self, connection: Any) -> Dict[str, Any]:
        return {
            'database_type': 'MockDatabase',
            'version': '1.0.0',
            'database_name': 'test_db'
        }


class TestDatabaseAdapterBase:
    """数据库适配器基础类测试套件"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.adapter = MockDatabaseAdapter()
    
    def test_adapter_initialization(self):
        """测试适配器初始化"""
        assert isinstance(self.adapter.connection_cache, dict)
        assert hasattr(self.adapter, 'logger')
        assert hasattr(self.adapter, '_sql_patterns')
        assert len(self.adapter._sql_patterns) > 0
    
    def test_supported_types(self):
        """测试支持的类型"""
        types = self.adapter.supported_types()
        assert isinstance(types, list)
        assert 'mock_database' in types
    
    def test_can_handle_connection_string(self):
        """测试连接字符串处理"""
        # 支持的连接字符串
        assert self.adapter.can_handle('mock://localhost:5432/testdb') is True
        
        # 不支持的连接字符串
        assert self.adapter.can_handle('postgresql://localhost:5432/testdb') is False
        assert self.adapter.can_handle('not_a_connection_string') is False
    
    def test_can_handle_connection_object(self):
        """测试连接对象处理"""
        # 支持的连接对象
        mock_conn = Mock()
        mock_conn.mock_execute = Mock()
        assert self.adapter.can_handle(mock_conn) is True
        
        # 不支持的连接对象
        regular_object = {'not': 'a_connection'}
        assert self.adapter.can_handle(regular_object) is False
    
    def test_get_operations(self):
        """测试获取操作列表"""
        operations = self.adapter.get_operations()
        
        # 验证基本操作存在
        expected_operations = [
            'query', 'execute', 'transaction', 'batch',
            'procedure', 'function', 'explain', 'analyze',
            'schema', 'tables', 'columns'
        ]
        
        for op in expected_operations:
            assert op in operations
            assert callable(operations[op])
    
    def test_get_metadata(self):
        """测试获取元数据"""
        metadata = self.adapter.get_metadata()
        
        assert isinstance(metadata, dict)
        assert metadata['adapter_name'] == 'MockDatabaseAdapter'
        assert 'adapter_version' in metadata
        assert 'supported_types' in metadata
        assert 'operations' in metadata
        assert 'created_at' in metadata
    
    def test_query_operation(self):
        """测试查询操作"""
        # 测试基本查询
        result = self.adapter.query(self.adapter.mock_connection, "SELECT * FROM users")
        
        assert isinstance(result, list)
        assert len(result) == 2
        assert result[0]['name'] == 'Alice'
        assert result[1]['name'] == 'Bob'
    
    def test_execute_operation(self):
        """测试执行操作"""
        # 测试INSERT
        result = self.adapter.execute(self.adapter.mock_connection, "INSERT INTO users VALUES (3, 'Charlie')")
        assert result == 1
        
        # 测试UPDATE
        result = self.adapter.execute(self.adapter.mock_connection, "UPDATE users SET name = 'Updated'")
        assert result == 2
        
        # 测试DELETE
        result = self.adapter.execute(self.adapter.mock_connection, "DELETE FROM users WHERE id = 1")
        assert result == 1
    
    def test_transaction_operation(self):
        """测试事务操作"""
        operations = [
            {'type': 'execute', 'sql': 'INSERT INTO users VALUES (3, "Charlie")'},
            {'type': 'query', 'sql': 'SELECT * FROM users'},
            {'type': 'execute', 'sql': 'UPDATE users SET name = "Updated"'}
        ]
        
        # Mock事务方法
        self.adapter._begin_transaction = Mock()
        self.adapter._commit_transaction = Mock()
        self.adapter._rollback_transaction = Mock()
        
        result = self.adapter.transaction(self.adapter.mock_connection, operations)
        
        assert isinstance(result, dict)
        assert result['success'] is True
        assert result['total_operations'] == 3
        assert len(result['results']) == 3
        
        # 验证事务方法被调用
        self.adapter._begin_transaction.assert_called_once()
        self.adapter._commit_transaction.assert_called_once()
    
    def test_batch_operation(self):
        """测试批量操作"""
        operations = [
            {'type': 'execute', 'sql': 'INSERT INTO users VALUES (3, "Charlie")'},
            {'type': 'execute', 'sql': 'INSERT INTO users VALUES (4, "David")'}
        ]
        
        result = self.adapter.batch(self.adapter.mock_connection, operations)
        
        assert isinstance(result, dict)
        assert result['success'] is True
        assert result['total_operations'] == 2
        assert len(result['results']) == 2
    
    def test_sql_preprocessing(self):
        """测试SQL预处理"""
        # 测试命名参数替换
        sql = "SELECT * FROM users WHERE name = :name AND age > :age"
        params = {'name': 'Alice', 'age': 25}
        
        processed_sql, processed_params = self.adapter._preprocess_sql(sql, params)
        
        assert ':name' not in processed_sql
        assert ':age' not in processed_sql
        assert processed_params['name'] == 'Alice'
        assert processed_params['age'] == 25
    
    def test_connection_string_parsing(self):
        """测试连接字符串解析"""
        connection_string = "mock://user:pass@localhost:5432/testdb?sslmode=require&timeout=30"
        
        conn_info = self.adapter._parse_connection_string(connection_string)
        
        assert isinstance(conn_info, ConnectionInfo)
        assert conn_info.host == 'localhost'
        assert conn_info.port == 5432
        assert conn_info.database == 'testdb'
        assert conn_info.username == 'user'
        assert conn_info.password == 'pass'
        assert conn_info.options['sslmode'] == 'require'
        assert conn_info.options['timeout'] == '30'
    
    def test_sql_type_detection(self):
        """测试SQL类型检测"""
        test_cases = [
            ('SELECT * FROM users', 'select'),
            ('INSERT INTO users VALUES (1, "test")', 'insert'),
            ('UPDATE users SET name = "test"', 'update'),
            ('DELETE FROM users WHERE id = 1', 'delete'),
            ('CREATE TABLE test (id INT)', 'create'),
            ('DROP TABLE test', 'drop'),
            ('ALTER TABLE users ADD COLUMN email VARCHAR(255)', 'alter'),
            ('UNKNOWN SQL COMMAND', 'unknown')
        ]
        
        for sql, expected_type in test_cases:
            detected_type = self.adapter._get_sql_type(sql)
            assert detected_type == expected_type, f"Failed to detect {expected_type} for: {sql}"


class TestPostgreSQLAdapter:
    """PostgreSQL适配器测试套件"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.adapter = PostgreSQLAdapter()
    
    def test_supported_types(self):
        """测试支持的类型"""
        types = self.adapter.supported_types()
        
        expected_types = [
            'postgresql', 'postgresql_connection', 'postgresql_connection_string',
            'postgresql_url', 'postgres', 'postgres_connection',
            'postgres_connection_string', 'postgres_url'
        ]
        
        for expected_type in expected_types:
            assert expected_type in types
    
    def test_connection_string_detection(self):
        """测试连接字符串检测"""
        # 支持的连接字符串
        supported_strings = [
            'postgresql://user:pass@localhost:5432/db',
            'postgres://user:pass@localhost:5432/db'
        ]
        
        for conn_str in supported_strings:
            assert self.adapter._is_supported_connection_string(conn_str) is True
        
        # 不支持的连接字符串
        unsupported_strings = [
            'mysql://user:pass@localhost:3306/db',
            'sqlite:///path/to/db.sqlite',
            'not_a_connection_string'
        ]
        
        for conn_str in unsupported_strings:
            assert self.adapter._is_supported_connection_string(conn_str) is False
    
    def test_default_port(self):
        """测试默认端口"""
        assert self.adapter._get_default_port() == 5432
    
    def test_postgresql_specific_operations(self):
        """测试PostgreSQL特有操作"""
        operations = self.adapter.get_operations()
        
        # 验证PostgreSQL特有操作存在
        postgresql_operations = [
            'copy_from', 'copy_to', 'listen', 'notify',
            'vacuum', 'reindex', 'analyze_table'
        ]
        
        for op in postgresql_operations:
            assert op in operations
            assert callable(operations[op])
    
    @patch('core.adapters.database.postgresql.PSYCOPG2_AVAILABLE', False)
    def test_psycopg2_not_available(self):
        """测试psycopg2不可用时的处理"""
        adapter = PostgreSQLAdapter()
        
        # 应该记录警告但不抛出异常
        assert adapter is not None
        
        # 尝试创建连接应该抛出ImportError
        with pytest.raises(ImportError):
            adapter._create_connection('postgresql://test')
    
    @patch('core.adapters.database.postgresql.psycopg2')
    def test_create_connection_with_string(self, mock_psycopg2):
        """测试使用连接字符串创建连接"""
        # Mock psycopg2.connect
        mock_connection = Mock()
        mock_psycopg2.connect.return_value = mock_connection
        
        connection_string = 'postgresql://user:pass@localhost:5432/testdb'
        
        # 创建连接
        result = self.adapter._create_connection(connection_string)
        
        # 验证结果
        assert result is mock_connection
        mock_psycopg2.connect.assert_called_once_with(connection_string)
        assert mock_connection.autocommit is False
    
    @patch('core.adapters.database.postgresql.psycopg2')
    def test_create_connection_with_info(self, mock_psycopg2):
        """测试使用ConnectionInfo创建连接"""
        # Mock psycopg2.connect
        mock_connection = Mock()
        mock_psycopg2.connect.return_value = mock_connection
        
        conn_info = ConnectionInfo(
            host='localhost',
            port=5432,
            database='testdb',
            username='user',
            password='pass',
            options={'sslmode': 'require'}
        )
        
        # 创建连接
        result = self.adapter._create_connection(conn_info)
        
        # 验证结果
        assert result is mock_connection
        
        # 验证连接字符串格式
        call_args = mock_psycopg2.connect.call_args[0][0]
        assert 'postgresql://user:pass@localhost:5432/testdb' in call_args
        assert 'sslmode=require' in call_args


class TestMySQLAdapter:
    """MySQL适配器测试套件"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.adapter = MySQLAdapter()

    def test_supported_types(self):
        """测试支持的类型"""
        types = self.adapter.supported_types()

        expected_types = [
            'mysql', 'mysql_connection', 'mysql_connection_string', 'mysql_url',
            'mariadb', 'mariadb_connection', 'mariadb_connection_string', 'mariadb_url'
        ]

        for expected_type in expected_types:
            assert expected_type in types

    def test_connection_string_detection(self):
        """测试连接字符串检测"""
        # 支持的连接字符串
        supported_strings = [
            'mysql://user:pass@localhost:3306/db',
            'mariadb://user:pass@localhost:3306/db'
        ]

        for conn_str in supported_strings:
            assert self.adapter._is_supported_connection_string(conn_str) is True

        # 不支持的连接字符串
        unsupported_strings = [
            'postgresql://user:pass@localhost:5432/db',
            'sqlite:///path/to/db.sqlite',
            'not_a_connection_string'
        ]

        for conn_str in unsupported_strings:
            assert self.adapter._is_supported_connection_string(conn_str) is False

    def test_default_port(self):
        """测试默认端口"""
        assert self.adapter._get_default_port() == 3306

    def test_mysql_specific_operations(self):
        """测试MySQL特有操作"""
        operations = self.adapter.get_operations()

        # 验证MySQL特有操作存在
        mysql_operations = [
            'show_tables', 'show_databases', 'show_columns', 'show_indexes',
            'show_status', 'show_variables', 'optimize_table', 'repair_table',
            'check_table', 'load_data'
        ]

        for op in mysql_operations:
            assert op in operations
            assert callable(operations[op])

    @patch('core.adapters.database.mysql.PYMYSQL_AVAILABLE', False)
    def test_pymysql_not_available(self):
        """测试pymysql不可用时的处理"""
        adapter = MySQLAdapter()

        # 应该记录警告但不抛出异常
        assert adapter is not None

        # 尝试创建连接应该抛出ImportError
        with pytest.raises(ImportError):
            adapter._create_connection('mysql://test')


class TestSQLiteAdapter:
    """SQLite适配器测试套件"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.adapter = SQLiteAdapter()

    def test_supported_types(self):
        """测试支持的类型"""
        types = self.adapter.supported_types()

        expected_types = [
            'sqlite', 'sqlite_connection', 'sqlite_connection_string',
            'sqlite_url', 'sqlite3', 'sqlite3_connection'
        ]

        for expected_type in expected_types:
            assert expected_type in types

    def test_connection_string_detection(self):
        """测试连接字符串检测"""
        # 支持的连接字符串
        supported_strings = [
            'sqlite:///path/to/db.sqlite',
            'test.db',
            'test.sqlite',
            'test.sqlite3',
            ':memory:'
        ]

        for conn_str in supported_strings:
            assert self.adapter._is_supported_connection_string(conn_str) is True

        # 不支持的连接字符串
        unsupported_strings = [
            'postgresql://user:pass@localhost:5432/db',
            'mysql://user:pass@localhost:3306/db',
            'not_a_database_file.txt'
        ]

        for conn_str in unsupported_strings:
            assert self.adapter._is_supported_connection_string(conn_str) is False

    def test_default_port(self):
        """测试默认端口"""
        assert self.adapter._get_default_port() == 0  # SQLite不使用端口

    def test_sqlite_specific_operations(self):
        """测试SQLite特有操作"""
        operations = self.adapter.get_operations()

        # 验证SQLite特有操作存在
        sqlite_operations = [
            'pragma', 'vacuum', 'reindex', 'integrity_check', 'quick_check',
            'attach', 'detach', 'backup', 'restore', 'create_function'
        ]

        for op in sqlite_operations:
            assert op in operations
            assert callable(operations[op])

    def test_memory_database_creation(self):
        """测试内存数据库创建"""
        # 这个测试可以实际运行，因为SQLite是Python内置的
        connection = self.adapter._create_connection(':memory:')

        assert connection is not None
        assert hasattr(connection, 'execute')
        assert hasattr(connection, 'cursor')

        # 测试基本操作
        cursor = connection.cursor()
        cursor.execute("CREATE TABLE test (id INTEGER, name TEXT)")
        cursor.execute("INSERT INTO test VALUES (1, 'test')")
        cursor.execute("SELECT * FROM test")

        result = cursor.fetchall()
        assert len(result) == 1
        assert dict(result[0]) == {'id': 1, 'name': 'test'}

        cursor.close()
        connection.close()

    def test_database_info(self):
        """测试数据库信息获取"""
        connection = self.adapter._create_connection(':memory:')

        info = self.adapter._get_database_info(connection)

        assert info['database_type'] == 'SQLite'
        assert 'sqlite_version' in info
        assert 'python_sqlite_version' in info
        assert 'databases' in info
        assert isinstance(info['databases'], list)

        connection.close()


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, '-v'])
