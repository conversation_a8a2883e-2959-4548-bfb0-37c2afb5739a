"""
异步SQLite数据库适配器

提供高性能的异步SQLite数据库操作，支持并发访问和流式查询
"""

from typing import Any, Dict, List, Optional, Union, AsyncIterator
import logging
import sqlite3
import asyncio
import os

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../'))

from core.unified_adapter import UnifiedDataAdapter
from core.adapters.base import ConnectionInfo

# 尝试导入异步SQLite驱动
try:
    import aiosqlite
    AIOSQLITE_AVAILABLE = True
except ImportError:
    AIOSQLITE_AVAILABLE = False
    aiosqlite = None


class AsyncSQLiteAdapter(UnifiedDataAdapter):
    """
    异步SQLite数据库适配器
    
    支持SQLite特有的异步功能：
    - 异步文件和内存数据库
    - 并发读写访问
    - 流式查询处理大结果集
    - 异步事务管理
    - PRAGMA异步操作
    - 数据库备份和恢复
    """
    
    def __init__(self):
        super().__init__()
        if not AIOSQLITE_AVAILABLE:
            self.logger.warning("aiosqlite未安装，异步SQLite功能受限")
    
    def supported_types(self) -> List[str]:
        """返回支持的数据类型列表"""
        return [
            'sqlite',
            'sqlite_connection',
            'sqlite_connection_string',
            'sqlite_url',
            'sqlite3',
            'sqlite3_connection'
        ]
    
    def _is_supported_connection_string(self, connection_string: str) -> bool:
        """检查是否支持的连接字符串"""
        return (connection_string.startswith('sqlite://') or 
                connection_string.endswith('.db') or
                connection_string.endswith('.sqlite') or
                connection_string.endswith('.sqlite3') or
                connection_string == ':memory:')
    
    def _is_supported_connection_object(self, connection: Any) -> bool:
        """检查是否支持的连接对象"""
        if not AIOSQLITE_AVAILABLE:
            return False
        
        return isinstance(connection, aiosqlite.Connection)
    
    def _get_default_port(self) -> int:
        """获取默认端口"""
        return 0  # SQLite不使用端口

    def can_handle(self, data_source: Any) -> bool:
        """检查是否能处理指定的数据源"""
        if isinstance(data_source, str):
            return self._is_supported_connection_string(data_source)
        else:
            return self._is_supported_connection_object(data_source)

    def _build_operations(self) -> Dict[str, callable]:
        """构建SQLite特有操作列表"""
        operations = {}

        # 添加异步操作
        async_ops = self.get_async_operations()
        for name, func in async_ops.items():
            operations[name] = func

        return operations
    
    # ========================================================================
    # 同步方法实现（兼容性）
    # ========================================================================
    
    def _sync_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """同步查询实现 - 使用sqlite3"""
        # 为了简化，这里抛出异常提示使用异步版本
        raise NotImplementedError("请使用异步版本的SQLite适配器")
    
    def _sync_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """同步执行实现"""
        raise NotImplementedError("请使用异步版本的SQLite适配器")
    
    def _sync_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """同步事务实现"""
        raise NotImplementedError("请使用异步版本的SQLite适配器")
    
    # ========================================================================
    # 异步方法实现
    # ========================================================================
    
    async def _create_async_connection(self, connection_source: Any) -> Any:
        """创建异步SQLite连接"""
        if not AIOSQLITE_AVAILABLE:
            raise ImportError("aiosqlite未安装，无法创建异步SQLite连接")
        
        if isinstance(connection_source, str):
            if connection_source.startswith('sqlite://'):
                # 解析SQLite URL
                database_path = connection_source[9:]  # 移除 'sqlite://'
                if database_path.startswith('///'):
                    database_path = database_path[3:]  # 绝对路径
                elif database_path.startswith('//'):
                    database_path = database_path[2:]  # 相对路径
            else:
                database_path = connection_source
        else:
            # 从ConnectionInfo获取数据库路径
            database_path = connection_source.database
        
        try:
            # 如果是文件数据库，确保目录存在
            if database_path != ':memory:' and not os.path.exists(os.path.dirname(database_path) or '.'):
                os.makedirs(os.path.dirname(database_path), exist_ok=True)
            
            # 创建异步连接
            connection = await aiosqlite.connect(database_path)
            
            # 设置行工厂为字典
            connection.row_factory = aiosqlite.Row
            
            # 启用外键约束
            await connection.execute("PRAGMA foreign_keys = ON")
            
            # 设置WAL模式（如果不是内存数据库）
            if database_path != ':memory:':
                await connection.execute("PRAGMA journal_mode = WAL")
            
            self.logger.info(f"成功创建异步SQLite连接: {database_path}")
            return connection
            
        except Exception as e:
            self.logger.error(f"创建异步SQLite连接失败: {e}")
            raise
    
    async def _close_async_connection(self, connection: Any) -> None:
        """关闭异步连接"""
        try:
            await connection.close()
            self.logger.debug("异步SQLite连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭异步SQLite连接失败: {e}")
    
    async def _create_async_pool(self, connection_source: Any, min_size: int = 5, max_size: int = 20) -> Any:
        """创建异步连接池（SQLite使用简单的连接管理）"""
        # SQLite通常不需要连接池，但我们可以创建一个简单的连接管理器
        return AsyncSQLiteConnectionPool(self, connection_source, min_size, max_size)
    
    async def _async_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """异步查询实现"""
        try:
            if params:
                cursor = await connection.execute(sql, list(params.values()))
            else:
                cursor = await connection.execute(sql)
            
            rows = await cursor.fetchall()
            await cursor.close()
            
            # 转换为字典列表
            return [dict(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"异步SQLite查询失败: {e}")
            raise
    
    async def _async_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """异步执行实现"""
        try:
            if params:
                cursor = await connection.execute(sql, list(params.values()))
            else:
                cursor = await connection.execute(sql)
            
            # 获取影响行数
            affected_rows = cursor.rowcount
            await cursor.close()
            
            # 提交事务
            await connection.commit()
            
            return affected_rows
            
        except Exception as e:
            self.logger.error(f"异步SQLite执行失败: {e}")
            raise
    
    async def _async_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步事务实现"""
        try:
            # SQLite的事务是自动的，我们只需要确保所有操作在同一个连接中执行
            results = []
            total_affected = 0
            
            for operation in operations:
                op_type = operation.get('type', 'execute')
                sql = operation['sql']
                params = operation.get('params')
                
                if op_type == 'query':
                    result = await self._async_query(connection, sql, params)
                    results.append(result)
                else:
                    affected = await self._async_execute(connection, sql, params)
                    results.append(affected)
                    total_affected += affected
            
            return {
                'results': results,
                'total_operations': len(operations),
                'total_affected': total_affected,
                'success': True
            }
            
        except Exception as e:
            self.logger.error(f"异步SQLite事务失败: {e}")
            raise
    
    async def _async_batch(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步批量操作实现"""
        results = []
        total_affected = 0
        
        for operation in operations:
            op_type = operation.get('type', 'execute')
            sql = operation['sql']
            params = operation.get('params')
            
            if op_type == 'query':
                result = await self._async_query(connection, sql, params)
                results.append(result)
            else:
                affected = await self._async_execute(connection, sql, params)
                results.append(affected)
                total_affected += affected
        
        return {
            'results': results,
            'total_operations': len(operations),
            'total_affected': total_affected,
            'success': True
        }
    
    async def _async_stream_query(self, connection: Any, sql: str, params: Dict = None) -> AsyncIterator[Dict]:
        """异步流式查询实现"""
        try:
            if params:
                cursor = await connection.execute(sql, list(params.values()))
            else:
                cursor = await connection.execute(sql)
            
            async for row in cursor:
                yield dict(row)
            
            await cursor.close()
            
        except Exception as e:
            self.logger.error(f"异步SQLite流式查询失败: {e}")
            raise
    
    # ========================================================================
    # SQLite特有异步功能
    # ========================================================================
    
    def get_async_operations(self) -> Dict[str, Any]:
        """获取支持的异步操作列表"""
        operations = super().get_async_operations()
        
        # 添加SQLite特有异步操作
        operations.update({
            'async_pragma': self._async_pragma,
            'async_vacuum': self._async_vacuum,
            'async_backup': self._async_backup,
            'async_restore': self._async_restore,
            'async_attach': self._async_attach,
            'async_detach': self._async_detach,
        })
        
        return operations
    
    async def _async_pragma(self, connection: Any, pragma_name: str, value: Any = None) -> Any:
        """异步PRAGMA操作"""
        try:
            if value is not None:
                await connection.execute(f"PRAGMA {pragma_name} = {value}")
                await connection.commit()
                return {'success': True, 'pragma': pragma_name, 'value': value}
            else:
                cursor = await connection.execute(f"PRAGMA {pragma_name}")
                result = await cursor.fetchall()
                await cursor.close()
                return [dict(row) for row in result] if result else []
                
        except Exception as e:
            self.logger.error(f"异步PRAGMA操作失败: {e}")
            raise
    
    async def _async_vacuum(self, connection: Any, schema: str = None) -> Dict:
        """异步VACUUM操作"""
        try:
            if schema:
                await connection.execute(f"VACUUM {schema}")
            else:
                await connection.execute("VACUUM")
            
            await connection.commit()
            
            return {
                'success': True,
                'operation': 'async_vacuum',
                'schema': schema
            }
            
        except Exception as e:
            self.logger.error(f"异步VACUUM操作失败: {e}")
            raise
    
    async def _async_backup(self, connection: Any, backup_path: str) -> Dict:
        """异步备份数据库"""
        try:
            # 创建备份连接
            backup_conn = await aiosqlite.connect(backup_path)
            
            # 执行备份（这里简化实现）
            await connection.backup(backup_conn)
            await backup_conn.close()
            
            return {
                'success': True,
                'operation': 'async_backup',
                'backup_path': backup_path
            }
            
        except Exception as e:
            self.logger.error(f"异步备份数据库失败: {e}")
            raise
    
    async def _async_restore(self, connection: Any, backup_path: str) -> Dict:
        """异步恢复数据库"""
        try:
            # 创建备份连接
            backup_conn = await aiosqlite.connect(backup_path)
            
            # 执行恢复
            await backup_conn.backup(connection)
            await backup_conn.close()
            
            return {
                'success': True,
                'operation': 'async_restore',
                'backup_path': backup_path
            }
            
        except Exception as e:
            self.logger.error(f"异步恢复数据库失败: {e}")
            raise
    
    async def _async_attach(self, connection: Any, database_path: str, alias: str) -> Dict:
        """异步附加数据库"""
        try:
            await connection.execute(f"ATTACH DATABASE '{database_path}' AS {alias}")
            await connection.commit()
            
            return {
                'success': True,
                'operation': 'async_attach',
                'database_path': database_path,
                'alias': alias
            }
            
        except Exception as e:
            self.logger.error(f"异步附加数据库失败: {e}")
            raise
    
    async def _async_detach(self, connection: Any, alias: str) -> Dict:
        """异步分离数据库"""
        try:
            await connection.execute(f"DETACH DATABASE {alias}")
            await connection.commit()
            
            return {
                'success': True,
                'operation': 'async_detach',
                'alias': alias
            }
            
        except Exception as e:
            self.logger.error(f"异步分离数据库失败: {e}")
            raise


class AsyncSQLiteConnectionPool:
    """异步SQLite连接池（简化实现）"""

    def __init__(self, adapter: AsyncSQLiteAdapter, connection_source: Any, min_size: int, max_size: int):
        self.adapter = adapter
        self.connection_source = connection_source
        self.min_size = min_size
        self.max_size = max_size
        self._connections = asyncio.Queue(maxsize=max_size)
        self._created_count = 0
        self._lock = asyncio.Lock()

    async def __aenter__(self):
        """异步上下文管理器入口"""
        return await self.acquire()

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        # 注意：这里需要连接对象，但上下文管理器无法传递
        # 实际使用时应该用 async with pool.acquire() as conn 的方式
        pass

    def acquire(self):
        """获取连接 - 返回异步上下文管理器"""
        return AsyncConnectionContext(self)

    async def _acquire_connection(self):
        """内部获取连接方法"""
        try:
            # 尝试从池中获取连接
            return self._connections.get_nowait()
        except asyncio.QueueEmpty:
            # 池为空，创建新连接
            async with self._lock:
                if self._created_count < self.max_size:
                    connection = await self.adapter._create_async_connection(self.connection_source)
                    self._created_count += 1
                    return connection
                else:
                    # 等待连接可用
                    return await self._connections.get()

    async def _release_connection(self, connection):
        """内部释放连接方法"""
        try:
            self._connections.put_nowait(connection)
        except asyncio.QueueFull:
            # 池已满，关闭连接
            await self.adapter._close_async_connection(connection)
            async with self._lock:
                self._created_count -= 1

    async def close(self):
        """关闭所有连接"""
        connections = []
        while not self._connections.empty():
            try:
                connection = self._connections.get_nowait()
                connections.append(connection)
            except asyncio.QueueEmpty:
                break

        for connection in connections:
            await self.adapter._close_async_connection(connection)

        self._created_count = 0


class AsyncConnectionContext:
    """异步连接上下文管理器"""

    def __init__(self, pool: AsyncSQLiteConnectionPool):
        self.pool = pool
        self.connection = None

    async def __aenter__(self):
        """获取连接"""
        self.connection = await self.pool._acquire_connection()
        return self.connection

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """释放连接"""
        if self.connection:
            await self.pool._release_connection(self.connection)
