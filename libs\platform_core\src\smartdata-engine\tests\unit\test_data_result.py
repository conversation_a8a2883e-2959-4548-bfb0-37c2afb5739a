"""
DataResult统一数据契约测试

测试DataResult类的所有功能，确保数据契约的正确性和模板友好性
"""

import pytest
import time
from typing import Dict, Any

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from core.enterprise_data_architecture import DataResult, DataArchitectureError


class TestDataResult:
    """DataResult类测试套件"""
    
    def test_success_result_creation(self):
        """测试成功结果创建"""
        data = [{'id': 1, 'name': 'test'}]
        result = DataResult.success_result(
            data=data,
            execution_time=10.5,
            operation='query',
            source_type='database'
        )
        
        assert result.success is True
        assert result.data == data
        assert result.error is None
        assert result.execution_time == 10.5
        assert result.operation == 'query'
        assert result.source_type == 'database'
        assert result.affected_rows == 1  # 自动设置为列表长度
    
    def test_error_result_creation(self):
        """测试错误结果创建"""
        error_msg = "Connection failed"
        result = DataResult.error_result(
            error=error_msg,
            operation='connect',
            source_type='database'
        )
        
        assert result.success is False
        assert result.data is None
        assert result.error == error_msg
        assert result.operation == 'connect'
        assert result.source_type == 'database'
    
    def test_dynamic_attribute_access_from_metadata(self):
        """测试从metadata动态访问属性"""
        result = DataResult.success_result(
            data={'test': 'value'},
            metadata={'custom_field': 'custom_value', 'count': 42}
        )
        
        assert result.custom_field == 'custom_value'
        assert result.count == 42
    
    def test_dynamic_attribute_access_from_data_dict(self):
        """测试从data字典动态访问属性"""
        result = DataResult.success_result(
            data={'user_id': 123, 'username': 'testuser'}
        )
        
        assert result.user_id == 123
        assert result.username == 'testuser'
    
    def test_dynamic_attribute_access_from_single_item_list(self):
        """测试从单元素列表动态访问属性"""
        result = DataResult.success_result(
            data=[{'db_version': 'PostgreSQL 13.0', 'db_name': 'testdb'}]
        )
        
        assert result.db_version == 'PostgreSQL 13.0'
        assert result.db_name == 'testdb'
    
    def test_dynamic_attribute_access_priority(self):
        """测试动态属性访问优先级：metadata > data dict > single item list"""
        result = DataResult.success_result(
            data=[{'name': 'from_list'}],
            metadata={'name': 'from_metadata'}
        )
        
        # metadata优先级最高
        assert result.name == 'from_metadata'
    
    def test_dynamic_attribute_access_not_found(self):
        """测试访问不存在的属性"""
        result = DataResult.success_result(data={'test': 'value'})
        
        with pytest.raises(AttributeError) as exc_info:
            _ = result.nonexistent_field
        
        assert "DataResult对象没有属性 'nonexistent_field'" in str(exc_info.value)
    
    def test_get_method_with_default(self):
        """测试get方法的默认值功能"""
        result = DataResult.success_result(
            data={'existing': 'value'},
            metadata={'meta_field': 'meta_value'}
        )
        
        # 存在的属性
        assert result.get('existing') == 'value'
        assert result.get('meta_field') == 'meta_value'
        
        # 不存在的属性，使用默认值
        assert result.get('nonexistent') is None
        assert result.get('nonexistent', 'default') == 'default'
    
    def test_to_dict_conversion(self):
        """测试转换为字典"""
        result = DataResult.success_result(
            data={'test': 'data'},
            execution_time=15.5,
            affected_rows=2,
            operation='insert',
            source_type='postgresql',
            adapter_type='PostgreSQLAdapter',
            metadata={'custom': 'value'}
        )
        
        result_dict = result.to_dict()
        
        expected_keys = {
            'success', 'data', 'error', 'metadata',
            'execution_time', 'affected_rows', 'operation',
            'source_type', 'adapter_type', 'created_at'
        }
        
        assert set(result_dict.keys()) == expected_keys
        assert result_dict['success'] is True
        assert result_dict['data'] == {'test': 'data'}
        assert result_dict['execution_time'] == 15.5
        assert result_dict['affected_rows'] == 2
    
    def test_from_dict_creation(self):
        """测试从字典创建DataResult"""
        data_dict = {
            'success': True,
            'data': [{'id': 1}],
            'error': None,
            'metadata': {'source': 'test'},
            'execution_time': 20.0,
            'affected_rows': 1,
            'operation': 'select',
            'source_type': 'mysql',
            'adapter_type': 'MySQLAdapter'
        }
        
        result = DataResult.from_dict(data_dict)
        
        assert result.success is True
        assert result.data == [{'id': 1}]
        assert result.metadata == {'source': 'test'}
        assert result.execution_time == 20.0
        assert result.affected_rows == 1
        assert result.operation == 'select'
        assert result.source_type == 'mysql'
        assert result.adapter_type == 'MySQLAdapter'
    
    def test_post_init_processing(self):
        """测试初始化后处理逻辑"""
        # 测试自动设置affected_rows
        result = DataResult(
            success=True,
            data=[{'a': 1}, {'b': 2}, {'c': 3}],
            affected_rows=0  # 应该被自动设置为3
        )
        
        assert result.affected_rows == 3
        
        # 测试负值修正
        result = DataResult(
            success=True,
            execution_time=-5.0,
            affected_rows=-1
        )
        
        assert result.execution_time == 0.0
        assert result.affected_rows == 0
        
        # 测试metadata默认值
        result = DataResult(success=True, metadata=None)
        assert result.metadata == {}
    
    def test_created_at_timestamp(self):
        """测试创建时间戳"""
        before = time.time()
        result = DataResult.success_result(data={'test': 'value'})
        after = time.time()
        
        assert before <= result.created_at <= after
    
    def test_template_friendly_access_patterns(self):
        """测试模板友好的访问模式"""
        # 模拟数据库查询结果
        db_result = DataResult.success_result(
            data=[
                {'user_id': 1, 'username': 'alice', 'email': '<EMAIL>'},
                {'user_id': 2, 'username': 'bob', 'email': '<EMAIL>'}
            ],
            execution_time=25.5,
            affected_rows=2,
            operation='SELECT',
            source_type='postgresql',
            metadata={'query': 'SELECT * FROM users', 'connection': 'postgresql://...'}
        )
        
        # 模板中常见的访问模式
        assert db_result.success is True
        assert len(db_result.data) == 2
        assert db_result.execution_time == 25.5
        assert db_result.affected_rows == 2
        assert db_result.query == 'SELECT * FROM users'  # 从metadata访问
        assert db_result.connection == 'postgresql://...'  # 从metadata访问
        
        # 模拟单行查询结果
        single_result = DataResult.success_result(
            data=[{'version': 'PostgreSQL 13.0', 'database': 'mydb'}]
        )
        
        # 可以直接访问单行数据的字段
        assert single_result.version == 'PostgreSQL 13.0'
        assert single_result.database == 'mydb'
    
    def test_error_handling_in_template_context(self):
        """测试模板上下文中的错误处理"""
        error_result = DataResult.error_result(
            error="Connection timeout",
            operation='connect',
            source_type='postgresql',
            metadata={'host': 'localhost', 'port': 5432}
        )
        
        assert error_result.success is False
        assert error_result.error == "Connection timeout"
        assert error_result.data is None
        assert error_result.host == 'localhost'  # 从metadata访问
        assert error_result.port == 5432  # 从metadata访问
        
        # 在模板中可以安全地检查错误
        # 当data为None时，应该返回默认值[]
        assert error_result.data is None
        assert error_result.get('data', []) == []
        assert error_result.get('nonexistent', 'default') == 'default'


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, '-v'])
