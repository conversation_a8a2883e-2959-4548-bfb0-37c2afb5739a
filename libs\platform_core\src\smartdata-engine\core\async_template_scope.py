"""
异步模板作用域实现

提供模板级别的异步数据上下文管理，支持并行数据获取和处理
"""

import asyncio
import time
import uuid
import logging
from typing import Any, Dict, List, Optional, Callable, Awaitable

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../'))

from core.async_interfaces import IAsyncTemplateScope
from core.async_data_proxy import AsyncDataProxy
from core.async_lifecycle_manager import AsyncLifecycleManager
from core.enterprise_data_architecture import DataRegistry, DataResult


class AsyncTemplateScope(IAsyncTemplateScope):
    """
    异步模板作用域
    
    功能特性：
    - 异步数据源注册和管理
    - 并行数据获取
    - 自动资源清理
    - 性能监控
    - 模板级别的数据隔离
    """
    
    def __init__(self, template_id: str, data_registry: DataRegistry):
        self.template_id = template_id
        self.data_registry = data_registry
        self.scope_id = str(uuid.uuid4())
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 异步数据代理管理
        self.async_data_proxies: Dict[str, AsyncDataProxy] = {}
        
        # 异步生命周期管理器
        self.async_lifecycle_manager = AsyncLifecycleManager()
        
        # 并发控制
        self._lock = asyncio.Lock()
        
        # 统计信息
        self.creation_time = time.time()
        self.operation_count = 0
        self.total_execution_time = 0.0
        
        # 作用域状态
        self._is_closed = False
        
        self.logger.debug(f"创建异步模板作用域: {self.template_id} ({self.scope_id})")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.cleanup_async()
    
    async def register_async_data_source(self, name: str, source: Any) -> AsyncDataProxy:
        """
        异步注册数据源
        
        Args:
            name: 数据源名称
            source: 数据源对象（连接字符串或连接对象）
            
        Returns:
            异步数据代理对象
        """
        if self._is_closed:
            raise RuntimeError("AsyncTemplateScope已关闭")
        
        async with self._lock:
            # 如果数据源已存在，先清理
            if name in self.async_data_proxies:
                await self._cleanup_data_source(name)
            
            # 获取适配器
            adapter = self.data_registry.get_adapter(source)
            
            # 检查适配器是否支持异步
            if not hasattr(adapter, 'async_query'):
                raise ValueError(f"适配器 {type(adapter).__name__} 不支持异步操作")
            
            # 创建异步数据代理
            proxy = AsyncDataProxy(source, adapter, self.async_lifecycle_manager)
            
            # 注册到作用域
            self.async_data_proxies[name] = proxy
            
            self.logger.debug(f"注册异步数据源: {name} -> {type(adapter).__name__}")
            
            return proxy
    
    async def get_async_data_proxy(self, name: str) -> Optional[AsyncDataProxy]:
        """
        获取异步数据代理
        
        Args:
            name: 数据源名称
            
        Returns:
            异步数据代理对象或None
        """
        return self.async_data_proxies.get(name)
    
    async def execute_parallel(self, operations: List[Callable[..., Awaitable]]) -> List[Any]:
        """
        并行执行多个异步操作
        
        Args:
            operations: 异步操作列表
            
        Returns:
            操作结果列表
        """
        if self._is_closed:
            raise RuntimeError("AsyncTemplateScope已关闭")
        
        start_time = time.time()
        
        try:
            self.logger.debug(f"开始并行执行 {len(operations)} 个异步操作")
            
            # 并行执行所有操作
            results = await asyncio.gather(*operations, return_exceptions=True)
            
            execution_time = (time.time() - start_time) * 1000
            self.operation_count += 1
            self.total_execution_time += execution_time
            
            self.logger.debug(f"并行操作完成，耗时: {execution_time:.2f}ms")
            
            return results
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            self.logger.error(f"并行操作失败，耗时: {execution_time:.2f}ms, 错误: {e}")
            raise
    
    async def execute_sequential(self, operations: List[Callable[..., Awaitable]]) -> List[Any]:
        """
        顺序执行多个异步操作
        
        Args:
            operations: 异步操作列表
            
        Returns:
            操作结果列表
        """
        if self._is_closed:
            raise RuntimeError("AsyncTemplateScope已关闭")
        
        start_time = time.time()
        results = []
        
        try:
            self.logger.debug(f"开始顺序执行 {len(operations)} 个异步操作")
            
            for i, operation in enumerate(operations):
                self.logger.debug(f"执行操作 {i+1}/{len(operations)}")
                result = await operation()
                results.append(result)
            
            execution_time = (time.time() - start_time) * 1000
            self.operation_count += 1
            self.total_execution_time += execution_time
            
            self.logger.debug(f"顺序操作完成，耗时: {execution_time:.2f}ms")
            
            return results
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            self.logger.error(f"顺序操作失败，耗时: {execution_time:.2f}ms, 错误: {e}")
            raise
    
    async def parallel_queries(self, queries: Dict[str, Dict]) -> Dict[str, DataResult]:
        """
        并行执行多个查询
        
        Args:
            queries: 查询字典，格式为 {data_source_name: {sql: str, params: dict}}
            
        Returns:
            查询结果字典
        """
        tasks = {}
        
        for source_name, query_config in queries.items():
            proxy = await self.get_async_data_proxy(source_name)
            if proxy is None:
                raise ValueError(f"数据源 {source_name} 未注册")
            
            task = proxy.query(query_config['sql'], query_config.get('params'))
            tasks[source_name] = task
        
        # 并行执行所有查询
        results = await asyncio.gather(*tasks.values(), return_exceptions=True)
        
        # 组装结果
        query_results = {}
        for (source_name, _), result in zip(tasks.items(), results):
            if isinstance(result, Exception):
                query_results[source_name] = DataResult(
                    success=False,
                    error=str(result),
                    operation='parallel_query',
                    source_type=source_name
                )
            else:
                query_results[source_name] = result
        
        return query_results
    
    async def render_template_data(self, template_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        渲染模板数据 - 并行获取所有数据源
        
        Args:
            template_config: 模板配置，包含数据源查询配置
            
        Returns:
            模板数据上下文
        """
        data_tasks = {}
        
        # 为每个数据源创建查询任务
        for source_name, source_config in template_config.items():
            if isinstance(source_config, dict) and 'sql' in source_config:
                proxy = await self.get_async_data_proxy(source_name)
                if proxy:
                    task = proxy.query(source_config['sql'], source_config.get('params'))
                    data_tasks[source_name] = task
        
        if not data_tasks:
            return {}
        
        # 并行执行所有数据获取任务
        self.logger.debug(f"并行获取 {len(data_tasks)} 个数据源的数据")
        
        results = await asyncio.gather(*data_tasks.values(), return_exceptions=True)
        
        # 构建模板上下文
        template_context = {}
        for (source_name, _), result in zip(data_tasks.items(), results):
            if isinstance(result, Exception):
                self.logger.error(f"数据源 {source_name} 查询失败: {result}")
                template_context[source_name] = None
            elif isinstance(result, DataResult) and result.success:
                template_context[source_name] = result.data
            else:
                self.logger.warning(f"数据源 {source_name} 查询未成功")
                template_context[source_name] = None
        
        return template_context
    
    async def _cleanup_data_source(self, name: str) -> None:
        """清理指定数据源"""
        if name in self.async_data_proxies:
            proxy = self.async_data_proxies[name]
            await proxy.close()
            del self.async_data_proxies[name]
            self.logger.debug(f"清理数据源: {name}")
    
    async def cleanup_async(self) -> None:
        """清理异步作用域资源"""
        if self._is_closed:
            return
        
        self._is_closed = True
        
        try:
            self.logger.debug(f"开始清理异步作用域: {self.template_id}")
            
            # 清理所有数据代理
            cleanup_tasks = []
            for name, proxy in self.async_data_proxies.items():
                cleanup_tasks.append(proxy.close())
            
            if cleanup_tasks:
                await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            
            self.async_data_proxies.clear()
            
            # 清理生命周期管理器
            await self.async_lifecycle_manager.cleanup_all_async()
            
            self.logger.debug(f"异步作用域清理完成: {self.template_id}")
            
        except Exception as e:
            self.logger.error(f"清理异步作用域时出错: {e}")
    
    def get_scope_info(self) -> Dict[str, Any]:
        """获取作用域信息"""
        return {
            'template_id': self.template_id,
            'scope_id': self.scope_id,
            'data_sources': list(self.async_data_proxies.keys()),
            'resource_count': self.async_lifecycle_manager.get_async_resource_count(),
            'operation_count': self.operation_count,
            'total_execution_time': self.total_execution_time,
            'avg_execution_time': self.total_execution_time / max(self.operation_count, 1),
            'uptime_seconds': time.time() - self.creation_time,
            'is_closed': self._is_closed,
            'registry_types': len(self.data_registry.get_supported_types())
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        proxy_stats = {}
        for name, proxy in self.async_data_proxies.items():
            proxy_stats[name] = proxy.get_performance_stats()
        
        return {
            'scope_info': self.get_scope_info(),
            'proxy_stats': proxy_stats,
            'lifecycle_manager_stats': self.async_lifecycle_manager.get_resource_info()
        }
    
    def __del__(self):
        """析构函数 - 确保资源清理"""
        if not self._is_closed:
            # 在事件循环中安排清理任务
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.cleanup_async())
            except RuntimeError:
                # 没有运行的事件循环，无法清理
                pass


class AsyncTemplateRenderer:
    """异步模板渲染器 - 高性能模板渲染"""
    
    def __init__(self, data_registry: DataRegistry):
        self.data_registry = data_registry
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def render(self, template_id: str, template_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        异步渲染模板
        
        Args:
            template_id: 模板标识
            template_config: 模板配置
            
        Returns:
            渲染结果
        """
        async with AsyncTemplateScope(template_id, self.data_registry) as scope:
            # 注册所有数据源
            for source_name, source_config in template_config.items():
                if isinstance(source_config, dict) and 'connection' in source_config:
                    await scope.register_async_data_source(source_name, source_config['connection'])
            
            # 并行获取所有数据
            template_data = await scope.render_template_data(template_config)
            
            return {
                'template_id': template_id,
                'data': template_data,
                'performance': scope.get_performance_stats(),
                'render_time': time.time()
            }
