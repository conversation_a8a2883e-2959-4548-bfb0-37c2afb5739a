"""
智能数据对象

基于SmartDataMatcher的统一智能数据处理：
1. sd_data = sd.loader(data_source)
2. 自动使用SmartDataMatcher选择最佳处理器
3. 提供统一的链式方法接口
4. 消除代码重复，使用核心匹配器
"""

import asyncio
import logging
import concurrent.futures
from typing import Any, Dict, List, Optional, Union, Iterator, Callable
from collections.abc import Mapping, Sequence
from functools import wraps
import json

try:
    from .smart_matcher import SmartDataMatcher
    from .interfaces import ProcessingContext
    from ..plugins.plugin_registry import PluginRegistry
except ImportError:
    # 绝对导入作为后备
    import sys
    import os
    current_dir = os.path.dirname(__file__)
    sys.path.insert(0, current_dir)
    sys.path.insert(0, os.path.join(current_dir, '..'))
    from smart_matcher import SmartDataMatcher
    from interfaces import ProcessingContext
    from plugins.plugin_registry import PluginRegistry


def async_safe(func):
    """装饰器：确保异步函数在任何环境下都能正常运行"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            # 检查是否在事件循环中
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 在运行的事件循环中，使用线程池执行
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, func(*args, **kwargs))
                    return future.result(timeout=30)
            else:
                # 没有运行的事件循环，直接运行
                return asyncio.run(func(*args, **kwargs))
        except Exception as e:
            # 如果异步执行失败，尝试同步执行
            logging.getLogger(__name__).warning(f"异步执行失败，尝试同步执行: {e}")
            return None
    return wrapper


class SmartDataObject:
    """
    智能数据对象

    基于SmartDataMatcher的统一数据处理，消除代码重复
    """

    def __init__(self, raw_data: Any, registry: PluginRegistry = None):
        self.raw_data = raw_data
        self.registry = registry
        self.logger = logging.getLogger(self.__class__.__name__)

        # 使用智能匹配器
        self.matcher = SmartDataMatcher()
        if registry:
            # 注册所有处理器到匹配器
            for processor_id, processor in registry.processors.items():
                self.matcher.register_processor(processor)

        # 处理后的数据
        self._processed_data: Optional[Any] = None
        self._processor = None
        self._is_processed = False

        # 缓存
        self._cache: Dict[str, Any] = {}
        
        # 自动处理数据（延迟到第一次访问时）
        # asyncio.create_task(self._auto_process())
    
    async def _auto_process(self):
        """使用SmartDataMatcher自动处理数据"""
        try:
            # 使用智能匹配器找到最佳处理器
            context = ProcessingContext()
            context.add_metadata("source_type", "auto_detect")
            context.add_metadata("target_format", "unified")

            self._processor = await self.matcher.find_best_processor(self.raw_data, context)

            if self._processor:
                # 确保处理器已配置和打开
                try:
                    self._processor.configure({})
                    if hasattr(self._processor, 'open') and callable(self._processor.open):
                        if asyncio.iscoroutinefunction(self._processor.open):
                            await self._processor.open()
                        else:
                            self._processor.open()
                except Exception as e:
                    self.logger.debug(f"处理器配置/打开失败: {e}")

                # 处理数据
                try:
                    result = await self._processor.process(self.raw_data, context=context)
                    self._processed_data = result.get('data', self.raw_data) if isinstance(result, dict) else result
                    self._data_type = self._processor.id
                    self._is_processed = True

                    self.logger.debug(f"SmartDataMatcher处理完成: {self._data_type}")
                finally:
                    # 关闭处理器
                    try:
                        if hasattr(self._processor, 'close') and callable(self._processor.close):
                            if asyncio.iscoroutinefunction(self._processor.close):
                                await self._processor.close()
                            else:
                                self._processor.close()
                    except Exception as e:
                        self.logger.debug(f"处理器关闭失败: {e}")

            # 如果没有处理器或处理失败，使用回退处理
            if not self._is_processed:
                self._processed_data = self._fallback_process(self.raw_data)
                self._data_type = 'fallback'
                self._is_processed = True

        except Exception as e:
            self.logger.error(f"SmartDataMatcher处理失败: {e}")
            self._processed_data = self.raw_data
            self._data_type = 'raw'
            self._is_processed = True
    
    def _fallback_process(self, data: Any) -> Any:
        """回退处理机制"""
        if isinstance(data, str):
            data_stripped = data.strip()

            # 尝试解析JSON
            if data_stripped.startswith(('{', '[')):
                try:
                    parsed = json.loads(data_stripped)
                    self.logger.debug("JSON解析成功")
                    return parsed
                except json.JSONDecodeError:
                    self.logger.debug("JSON解析失败")

            # 尝试解析CSV
            if ',' in data and '\n' in data:
                lines = data.strip().split('\n')
                if len(lines) >= 2:
                    headers = [h.strip() for h in lines[0].split(',')]
                    rows = []
                    for line in lines[1:]:
                        values = [v.strip() for v in line.split(',')]
                        if len(values) == len(headers):
                            rows.append(dict(zip(headers, values)))
                    if rows:
                        self.logger.debug("CSV解析成功")
                        return rows

            # 尝试解析XML
            if data_stripped.startswith('<') and data_stripped.endswith('>'):
                try:
                    import xml.etree.ElementTree as ET
                    root = ET.fromstring(data_stripped)
                    result = self._xml_to_dict(root)
                    self.logger.debug("XML解析成功")
                    return result
                except ET.ParseError:
                    self.logger.debug("XML解析失败")

        return data

    def _xml_to_dict(self, element) -> Dict[str, Any]:
        """将XML元素转换为字典"""
        result = {}

        # 添加属性
        if element.attrib:
            result['@attributes'] = element.attrib

        # 添加文本内容
        if element.text and element.text.strip():
            result['text'] = element.text.strip()

        # 添加子元素
        for child in element:
            child_data = self._xml_to_dict(child)
            if child.tag in result:
                if not isinstance(result[child.tag], list):
                    result[child.tag] = [result[child.tag]]
                result[child.tag].append(child_data)
            else:
                result[child.tag] = child_data

        return result
    
    def _ensure_processed(self):
        """确保数据已处理"""
        if not self._is_processed:
            # 同步处理数据
            try:
                # 尝试运行异步处理
                loop = None
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # 如果事件循环正在运行，使用同步回退
                        self._sync_process()
                    else:
                        loop.run_until_complete(self._auto_process())
                except RuntimeError:
                    # 没有事件循环，使用同步回退
                    self._sync_process()
            except Exception as e:
                self.logger.error(f"数据处理失败: {e}")
                self._sync_process()

    def _sync_process(self):
        """同步处理数据"""
        try:
            self._processed_data = self._fallback_process(self.raw_data)
            self._data_type = 'sync_fallback'
            self._is_processed = True
        except Exception as e:
            self.logger.error(f"同步处理失败: {e}")
            self._processed_data = self.raw_data
            self._data_type = 'raw'
            self._is_processed = True
    
    @property
    def data(self) -> Any:
        """获取处理后的数据"""
        self._ensure_processed()
        return self._processed_data
    
    @property
    def type(self) -> str:
        """获取数据类型"""
        self._ensure_processed()
        return self._data_type or 'unknown'
    
    # ==================== 统一访问接口 ====================
    
    def __getitem__(self, key):
        """支持索引和键访问"""
        data = self.data

        # 处理列表和元组
        if isinstance(data, (list, tuple)):
            if isinstance(key, slice):
                # 支持切片操作，如 data[:5], data[1:3]
                return SmartDataObject(data[key], self.registry)
            else:
                # 支持索引访问
                return SmartDataObject(data[key], self.registry)

        # 处理字典
        elif isinstance(data, dict):
            if key in data:
                return SmartDataObject(data[key], self.registry)
            # 尝试从处理结果中获取
            elif 'data' in data and isinstance(data['data'], dict) and key in data['data']:
                return SmartDataObject(data['data'][key], self.registry)
            elif 'parsed_data' in data and isinstance(data['parsed_data'], dict) and key in data['parsed_data']:
                return SmartDataObject(data['parsed_data'][key], self.registry)
            else:
                raise KeyError(f"键 '{key}' 不存在")

        # 处理处理结果
        elif isinstance(data, dict) and 'data' in data:
            inner_data = data['data']
            if isinstance(inner_data, (list, tuple, dict)):
                return SmartDataObject(inner_data[key], self.registry)

        else:
            raise TypeError(f"数据类型 {type(data)} 不支持索引访问")
    
    def __getattr__(self, name):
        """支持属性访问"""
        if name.startswith('_'):
            # 避免递归
            raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

        data = self.data

        # 处理字典数据
        if isinstance(data, dict):
            if name in data:
                value = data[name]
                # 对于基本类型，直接返回值而不包装
                if isinstance(value, (str, int, float, bool, type(None))):
                    return value
                else:
                    return SmartDataObject(value, self.registry)

        # 处理对象属性
        elif hasattr(data, name):
            attr_value = getattr(data, name)
            if callable(attr_value):
                return attr_value
            else:
                # 对于基本类型，直接返回值
                if isinstance(attr_value, (str, int, float, bool, type(None))):
                    return attr_value
                else:
                    return SmartDataObject(attr_value, self.registry)

        # 处理处理结果中的数据
        elif isinstance(data, dict) and 'data' in data:
            inner_data = data['data']
            if isinstance(inner_data, dict) and name in inner_data:
                value = inner_data[name]
                if isinstance(value, (str, int, float, bool, type(None))):
                    return value
                else:
                    return SmartDataObject(value, self.registry)

        # 尝试解析处理结果
        elif isinstance(data, dict) and 'parsed_data' in data:
            parsed_data = data['parsed_data']
            if isinstance(parsed_data, dict) and name in parsed_data:
                value = parsed_data[name]
                if isinstance(value, (str, int, float, bool, type(None))):
                    return value
                else:
                    return SmartDataObject(value, self.registry)

        raise AttributeError(f"数据对象没有属性 '{name}'")

    def get(self, key: str, default: Any = None) -> Any:
        """获取数据项，类似字典的get方法"""
        data = self.data
        if isinstance(data, dict):
            return data.get(key, default)
        elif hasattr(data, key):
            return getattr(data, key, default)
        else:
            return default

    def __iter__(self):
        """支持迭代"""
        data = self.data
        
        if isinstance(data, (list, tuple)):
            for item in data:
                yield SmartDataObject(item, self.registry)
        elif isinstance(data, dict):
            for key in data:
                yield key
        else:
            raise TypeError(f"数据类型 {type(data)} 不支持迭代")
    
    def __len__(self):
        """支持长度获取"""
        data = self.data
        
        if hasattr(data, '__len__'):
            return len(data)
        else:
            raise TypeError(f"数据类型 {type(data)} 没有长度")
    
    def __str__(self):
        """字符串表示"""
        data = self.data
        if isinstance(data, str):
            return data
        else:
            return str(data)
    
    def __repr__(self):
        """调试表示"""
        return f"SmartDataObject(type={self.type}, data={repr(self.data)})"
    
    def __bool__(self):
        """布尔值判断"""
        data = self.data
        return bool(data)
    
    # ==================== 便捷方法 ====================
    
    def keys(self):
        """获取字典键"""
        data = self.data
        if isinstance(data, dict):
            return data.keys()
        else:
            raise TypeError(f"数据类型 {type(data)} 没有键")
    
    def values(self):
        """获取字典值"""
        data = self.data
        if isinstance(data, dict):
            for value in data.values():
                yield SmartDataObject(value, self.registry)
        else:
            raise TypeError(f"数据类型 {type(data)} 没有值")
    
    def items(self):
        """获取字典项"""
        data = self.data
        if isinstance(data, dict):
            for key, value in data.items():
                yield key, SmartDataObject(value, self.registry)
        else:
            raise TypeError(f"数据类型 {type(data)} 没有项")
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        data = self.data
        if isinstance(data, dict):
            return data
        elif hasattr(data, '__dict__'):
            return data.__dict__
        else:
            raise TypeError(f"数据类型 {type(data)} 无法转换为字典")
    
    def to_list(self) -> List:
        """转换为列表"""
        data = self.data
        if isinstance(data, (list, tuple)):
            return list(data)
        elif isinstance(data, str):
            return list(data)
        else:
            return [data]
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        data = self.data
        try:
            return json.dumps(data, ensure_ascii=False, indent=2)
        except TypeError:
            return json.dumps(str(data), ensure_ascii=False)
    
    def count(self) -> int:
        """计数"""
        data = self.data
        if isinstance(data, (list, tuple, dict, str)):
            return len(data)
        else:
            return 1
    
    def first(self):
        """获取第一个元素"""
        data = self.data
        if isinstance(data, (list, tuple)) and len(data) > 0:
            return SmartDataObject(data[0], self.registry)
        elif isinstance(data, dict) and len(data) > 0:
            first_key = next(iter(data))
            return SmartDataObject(data[first_key], self.registry)
        else:
            return SmartDataObject(data, self.registry)
    
    def last(self):
        """获取最后一个元素"""
        data = self.data
        if isinstance(data, (list, tuple)) and len(data) > 0:
            return SmartDataObject(data[-1], self.registry)
        elif isinstance(data, dict) and len(data) > 0:
            last_key = list(data.keys())[-1]
            return SmartDataObject(data[last_key], self.registry)
        else:
            return SmartDataObject(data, self.registry)
    
    def filter(self, condition):
        """过滤数据"""
        data = self.data
        if isinstance(data, (list, tuple)):
            filtered = [item for item in data if condition(SmartDataObject(item, self.registry))]
            return SmartDataObject(filtered, self.registry)
        else:
            raise TypeError(f"数据类型 {type(data)} 不支持过滤")
    
    def map(self, func):
        """映射数据"""
        data = self.data
        if isinstance(data, (list, tuple)):
            mapped = [func(SmartDataObject(item, self.registry)) for item in data]
            return SmartDataObject(mapped, self.registry)
        else:
            return SmartDataObject(func(self), self.registry)

    def limit(self, count: int):
        """限制数量"""
        data = self.data
        if isinstance(data, (list, tuple)):
            return SmartDataObject(data[:count], self.registry)
        else:
            return self

    def skip(self, count: int):
        """跳过元素"""
        data = self.data
        if isinstance(data, (list, tuple)):
            return SmartDataObject(data[count:], self.registry)
        else:
            return self

    def sort(self, key=None, reverse=False, attribute=None):
        """排序数据，支持attribute参数"""
        data = self.data
        if isinstance(data, (list, tuple)):
            if attribute is not None:
                # 按属性排序，支持 sort(attribute='size', reverse=True)
                def get_attr_value(item):
                    if isinstance(item, dict):
                        return item.get(attribute, 0)
                    elif hasattr(item, attribute):
                        return getattr(item, attribute, 0)
                    else:
                        return 0
                sorted_data = sorted(data, key=get_attr_value, reverse=reverse)
            elif key is None:
                sorted_data = sorted(data, reverse=reverse)
            elif isinstance(key, str):
                # 按字段排序
                sorted_data = sorted(data, key=lambda x: x.get(key) if isinstance(x, dict) else x, reverse=reverse)
            elif callable(key):
                sorted_data = sorted(data, key=lambda x: key(SmartDataObject(x, self.registry)), reverse=reverse)
            else:
                sorted_data = data
            return SmartDataObject(sorted_data, self.registry)
        else:
            return self

    def count(self):
        """计数"""
        data = self.data
        if isinstance(data, (list, tuple, dict)):
            return len(data)
        else:
            return 1

    def sum(self, key=None):
        """求和"""
        data = self.data
        if isinstance(data, (list, tuple)):
            if key is None:
                return sum(x for x in data if isinstance(x, (int, float)))
            else:
                return sum(item.get(key, 0) for item in data if isinstance(item, dict))
        else:
            return 0

    def avg(self, key=None):
        """平均值"""
        data = self.data
        if isinstance(data, (list, tuple)) and len(data) > 0:
            total = self.sum(key)
            return total / len(data)
        else:
            return 0

    def group_by(self, key):
        """分组数据"""
        data = self.data
        if isinstance(data, (list, tuple)):
            groups = {}
            for item in data:
                if isinstance(item, dict) and key in item:
                    group_key = item[key]
                    if group_key not in groups:
                        groups[group_key] = []
                    groups[group_key].append(item)
            return SmartDataObject(groups, self.registry)
        else:
            return self




class SmartDataLoader:
    """
    智能数据加载器

    提供统一的数据访问接口：
    - sd.loader() - 通用数据加载
    - sd.database() - 数据库连接
    - sd.api() - API调用
    - sd.file() - 文件处理
    - sd.stream() - 流数据
    - sd.remote() - 远程主机
    """

    def __init__(self, registry: PluginRegistry = None):
        self.registry = registry
        self.logger = logging.getLogger(self.__class__.__name__)

    def loader(self, data_source: Any) -> SmartDataObject:
        """
        智能加载数据

        用法:
        sd_data = sd.loader(data_source)
        """
        self.logger.debug(f"智能加载数据: {type(data_source)}")
        return SmartDataObject(data_source, self.registry)

    def database(self, connection_string: str):
        """数据库连接器 - 异步优先的智能协调器"""
        try:
            # 导入智能协调器
            from .async_sync_coordinator import SmartDatabaseConnector

            # 尝试创建异步连接器（插件中的企业级实现）
            async_connector = None
            try:
                from plugins.database.connectors import ConnectorFactory
                from urllib.parse import urlparse
                parsed = urlparse(connection_string)
                db_type = parsed.scheme.lower()
                async_connector = ConnectorFactory.create_connector(db_type, enable_debug=False)
                logging.getLogger(__name__).info(f"创建异步连接器成功: {db_type}")
            except Exception as e:
                logging.getLogger(__name__).debug(f"创建异步连接器失败: {e}")

            # 尝试创建同步连接器（原始实现作为回退）
            sync_connector = None
            try:
                sync_connector = DatabaseConnector(connection_string, self.registry)
                logging.getLogger(__name__).info("创建同步连接器成功")
            except Exception as e:
                logging.getLogger(__name__).debug(f"创建同步连接器失败: {e}")

            # 创建智能数据库连接器
            smart_connector = SmartDatabaseConnector(
                connection_string=connection_string,
                async_connector=async_connector,
                sync_connector=sync_connector
            )

            logging.getLogger(__name__).info(f"创建智能数据库连接器成功: {connection_string}")
            return smart_connector

        except Exception as e:
            logging.getLogger(__name__).error(f"智能连接器创建失败: {e}")
            # 最后回退：创建基本的同步连接器
            return DatabaseConnector(connection_string, self.registry)

    def api(self, base_url: str, **kwargs):
        """API连接器 - 使用HTTP插件"""
        try:
            # 使用新的HTTP插件
            from plugins.http_plugin.smart_loader import global_loader

            # 准备API配置
            api_config = {
                'url': base_url,
                'method': kwargs.get('method', 'GET'),
                **kwargs
            }

            # 使用HTTP智能加载器
            result = global_loader.load(api_config)
            logging.getLogger(__name__).info(f"使用HTTP智能加载器: {base_url}")
            return result

        except ImportError:
            # 回退到原始API连接器
            logging.getLogger(__name__).warning("HTTP智能加载器不可用，使用原始API连接器")
            return ApiConnector(base_url, self.registry, **kwargs)
        except Exception as e:
            logging.getLogger(__name__).error(f"HTTP智能加载器失败: {e}")
            # 最后回退
            return ApiConnector(base_url, self.registry, **kwargs)

    def http(self, data: Any, **options):
        """HTTP插件方法 - SmartDataLoader集成标准"""
        try:
            # 尝试使用智能加载器
            from plugins.http_plugin.smart_loader import global_loader

            load_options = options.copy() if options else {}
            result = global_loader.load(data, load_options)
            logging.getLogger(__name__).info(f"使用HTTP智能加载器: {data}")
            return result

        except ImportError:
            # 回退到原始处理器
            logging.getLogger(__name__).warning("HTTP智能加载器不可用，使用原始处理器")
            from plugins.http_plugin.http_processor import HttpProcessor
            processor = HttpProcessor()
            return processor.process(data, options)
        except Exception as e:
            logging.getLogger(__name__).error(f"HTTP智能加载器失败: {e}")
            # 最后回退
            from plugins.http_plugin.http_processor import HttpProcessor
            processor = HttpProcessor()
            return processor.process(data, options)





    def file(self, file_path: str, format_hint: str = None, **options):
        """文件连接器 - 使用智能文件加载器"""
        try:
            # 尝试使用新的智能文件加载器
            from plugins.file_loader.smart_file_loader import global_file_loader

            # 准备选项
            load_options = options.copy() if options else {}
            if format_hint:
                load_options['format_hint'] = format_hint

            # 使用智能文件加载器
            result = global_file_loader.load_file(file_path, load_options)
            logging.getLogger(__name__).info(f"使用智能文件加载器加载: {file_path}")
            return result

        except ImportError:
            # 回退到原始文件连接器
            logging.getLogger(__name__).warning("智能文件加载器不可用，使用原始连接器")
            return FileConnector(file_path, self.registry)
        except Exception as e:
            logging.getLogger(__name__).error(f"智能文件加载器失败: {e}")
            # 最后回退
            return FileConnector(file_path, self.registry)

    def stream(self, data: Any, **options):
        """流数据插件方法 - SmartDataLoader集成标准"""
        try:
            # 尝试使用智能加载器
            from plugins.stream.smart_stream_loader import global_stream_loader

            load_options = options.copy() if options else {}
            result = global_stream_loader.load(data, load_options)
            logging.getLogger(__name__).info(f"使用流数据智能加载器: {data}")
            return result

        except ImportError:
            # 回退到原始处理器
            logging.getLogger(__name__).warning("流数据智能加载器不可用，使用原始处理器")
            from plugins.stream.stream_processor import StreamDataProcessor
            processor = StreamDataProcessor()
            return processor.process(data, options)
        except Exception as e:
            logging.getLogger(__name__).error(f"流数据智能加载器失败: {e}")
            # 最后回退
            from plugins.stream.stream_processor import StreamDataProcessor
            processor = StreamDataProcessor()
            return processor.process(data, options)

    def remote_host(self, data: Any, **options):
        """远程主机插件方法 - SmartDataLoader集成标准"""
        try:
            # 尝试使用智能加载器
            from plugins.remote_host.smart_remote_host_loader import global_remote_host_loader

            load_options = options.copy() if options else {}
            result = global_remote_host_loader.load(data, load_options)
            logging.getLogger(__name__).info(f"使用远程主机智能加载器: {data}")
            return result

        except ImportError:
            # 回退到原始处理器
            logging.getLogger(__name__).warning("远程主机智能加载器不可用，使用原始处理器")
            from plugins.remote_host.remote_host_processor import RemoteHostProcessor
            processor = RemoteHostProcessor()
            return processor.process(data, options)
        except Exception as e:
            logging.getLogger(__name__).error(f"远程主机智能加载器失败: {e}")
            # 最后回退
            from plugins.remote_host.remote_host_processor import RemoteHostProcessor
            processor = RemoteHostProcessor()
            return processor.process(data, options)

    def remote_file(self, data: Any, **options):
        """远程文件插件方法 - SmartDataLoader集成标准"""
        try:
            # 尝试使用智能加载器
            from plugins.remote_file.smart_remote_loader import global_remote_loader

            load_options = options.copy() if options else {}
            result = global_remote_loader.load(data, load_options)
            logging.getLogger(__name__).info(f"使用远程文件智能加载器: {data}")
            return result

        except ImportError:
            # 回退到企业级处理器
            logging.getLogger(__name__).warning("远程文件智能加载器不可用，使用企业级处理器")
            try:
                from plugins.remote_file.enterprise_remote_processor import EnterpriseRemoteFileProcessor
                from core.interfaces import ProcessingContext

                processor = EnterpriseRemoteFileProcessor()

                # 创建ProcessingContext
                context = ProcessingContext(
                    metadata=options
                )

                # 异步处理转同步
                import asyncio
                import concurrent.futures

                async def async_process():
                    return await processor.process(data, context=context)

                try:
                    # 检查是否有运行的事件循环
                    loop = asyncio.get_running_loop()
                    # 如果有运行的循环，在新线程中运行
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, async_process())
                        result = future.result(timeout=30)
                except RuntimeError:
                    # 没有运行的事件循环，直接运行
                    result = asyncio.run(async_process())

                return result
            except ImportError:
                # 最后回退到原始处理器
                from plugins.remote_file.remote_processor import RemoteFileProcessor
                processor = RemoteFileProcessor()
                return processor.process(data, options)
        except Exception as e:
            logging.getLogger(__name__).error(f"远程文件智能加载器失败: {e}")
            # 最后回退到统一处理器
            try:
                from plugins.remote_file.remote_processor import EnterpriseRemoteFileProcessor

                processor = EnterpriseRemoteFileProcessor()

                # 异步处理转同步
                import asyncio
                import concurrent.futures

                async def async_process():
                    return await processor.process(data, context=options)

                try:
                    # 检查是否有运行的事件循环
                    loop = asyncio.get_running_loop()
                    # 如果有运行的循环，在新线程中运行
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, async_process())
                        result = future.result(timeout=30)
                except RuntimeError:
                    # 没有运行的事件循环，直接运行
                    result = asyncio.run(async_process())

                return result
            except Exception:
                return SmartDataObject({
                    'success': False,
                    'error': str(e),
                    'processor': 'remote_file'
                })

    def kafka(self, data: Any, **options):
        """Kafka插件方法 - SmartDataLoader集成标准"""
        try:
            # 尝试使用企业级处理器
            from plugins.kafka.enterprise_kafka_processor import EnterpriseKafkaProcessor

            processor = EnterpriseKafkaProcessor()

            # 异步处理转同步
            import asyncio
            import concurrent.futures

            async def async_process():
                return await processor.process(data, context=options)

            try:
                # 检查是否有运行的事件循环
                loop = asyncio.get_running_loop()
                # 如果有运行的循环，在新线程中运行
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, async_process())
                    result = future.result(timeout=30)
            except RuntimeError:
                # 没有运行的事件循环，直接运行
                try:
                    result = asyncio.run(async_process())
                except RuntimeError as re:
                    # 如果仍然有问题，使用新的事件循环
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        result = loop.run_until_complete(async_process())
                    finally:
                        loop.close()

            logging.getLogger(__name__).info(f"使用企业级Kafka处理器: {data}")
            return result

        except ImportError:
            # 回退到基础处理器
            logging.getLogger(__name__).warning("企业级Kafka处理器不可用，使用基础处理器")
            try:
                from plugins.kafka.kafka_processor import KafkaProcessor
                processor = KafkaProcessor()
                return processor.process(data, options)
            except ImportError:
                logging.getLogger(__name__).error("Kafka处理器不可用")
                return SmartDataObject({
                    'success': False,
                    'error': 'Kafka处理器不可用，请检查插件安装',
                    'processor': 'kafka'
                })
        except Exception as e:
            logging.getLogger(__name__).error(f"Kafka处理器失败: {e}")
            # 最后回退
            try:
                from plugins.kafka.kafka_processor import KafkaProcessor
                processor = KafkaProcessor()
                return processor.process(data, options)
            except:
                return SmartDataObject({
                    'success': False,
                    'error': str(e),
                    'processor': 'kafka'
                })

    def database(self, data: Any, **options):
        """数据库插件方法 - SmartDataLoader集成标准"""
        try:
            # 尝试使用统一处理器
            from plugins.database.database_processor import EnterpriseDatabaseProcessor
            from core.interfaces import ProcessingContext

            processor = EnterpriseDatabaseProcessor()

            # 创建ProcessingContext
            context = ProcessingContext(
                metadata=options
            )

            # 异步处理转同步
            import asyncio
            import concurrent.futures

            async def async_process():
                return await processor.process(data, context=context)

            try:
                # 检查是否有运行的事件循环
                loop = asyncio.get_running_loop()
                # 如果有运行的循环，在新线程中运行
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, async_process())
                    result = future.result(timeout=30)
            except RuntimeError:
                # 没有运行的事件循环，直接运行
                result = asyncio.run(async_process())

            logging.getLogger(__name__).info(f"使用企业级数据库处理器: {data}")
            return result

        except ImportError:
            # 回退到基础处理器
            logging.getLogger(__name__).warning("企业级数据库处理器不可用，使用基础处理器")
            try:
                from plugins.database.database_processor import DatabaseProcessor
                processor = DatabaseProcessor()
                return processor.process(data, options)
            except ImportError:
                logging.getLogger(__name__).error("数据库处理器不可用")
                return SmartDataObject({
                    'success': False,
                    'error': '数据库处理器不可用，请检查插件安装',
                    'processor': 'database'
                })
        except Exception as e:
            logging.getLogger(__name__).error(f"数据库处理器失败: {e}")
            # 最后回退
            try:
                from plugins.database.database_processor import DatabaseProcessor
                processor = DatabaseProcessor()
                return processor.process(data, options)
            except:
                return SmartDataObject({
                    'success': False,
                    'error': str(e),
                    'processor': 'database'
                })

    def email(self, data: Any, **options):
        """邮件插件方法 - SmartDataLoader集成标准"""
        try:
            # 尝试使用邮件处理器
            from plugins.email_plugin.email_processor import EmailProcessor
            from core.interfaces import ProcessingContext

            processor = EmailProcessor()

            # 创建ProcessingContext
            context = ProcessingContext(
                metadata=options
            )

            # 异步处理转同步
            import asyncio
            import concurrent.futures

            async def async_process():
                return await processor.process(data, context=context)

            try:
                # 检查是否有运行的事件循环
                loop = asyncio.get_running_loop()
                # 如果有运行的循环，在新线程中运行
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, async_process())
                    result = future.result(timeout=30)
            except RuntimeError:
                # 没有运行的事件循环，直接运行
                result = asyncio.run(async_process())

            logging.getLogger(__name__).info(f"使用邮件处理器: {data}")
            return result

        except ImportError:
            logging.getLogger(__name__).error("邮件处理器不可用")
            return SmartDataObject({
                'success': False,
                'error': '邮件处理器不可用，请检查插件安装',
                'processor': 'email'
            })
        except Exception as e:
            logging.getLogger(__name__).error(f"邮件处理器失败: {e}")
            return SmartDataObject({
                'success': False,
                'error': str(e),
                'processor': 'email'
            })

    def file(self, data: Any, **options):
        """文件插件方法 - SmartDataLoader集成标准"""
        try:
            # 尝试使用文件处理器
            from plugins.file.file_processor import FileProcessor

            processor = FileProcessor()

            # 异步处理转同步
            import asyncio
            import concurrent.futures

            async def async_process():
                return await processor.process(data, options)

            try:
                # 检查是否有运行的事件循环
                loop = asyncio.get_running_loop()
                # 如果有运行的循环，在新线程中运行
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, async_process())
                    result = future.result(timeout=30)
            except RuntimeError:
                # 没有运行的事件循环，直接运行
                result = asyncio.run(async_process())

            logging.getLogger(__name__).info(f"使用文件处理器: {data}")
            return result

        except ImportError:
            logging.getLogger(__name__).error("文件处理器不可用")
            return SmartDataObject({
                'success': False,
                'error': '文件处理器不可用，请检查插件安装',
                'processor': 'file'
            })
        except Exception as e:
            logging.getLogger(__name__).error(f"文件处理器失败: {e}")
            return SmartDataObject({
                'success': False,
                'error': str(e),
                'processor': 'file'
            })

    def notification(self, data: Any, **options):
        """通知插件方法 - SmartDataLoader集成标准"""
        try:
            # 尝试使用通知处理器
            from plugins.notification.notification_processor import NotificationProcessor

            processor = NotificationProcessor()

            # 异步处理转同步
            import asyncio
            import concurrent.futures

            async def async_process():
                return await processor.process(data, options)

            try:
                # 检查是否有运行的事件循环
                loop = asyncio.get_running_loop()
                # 如果有运行的循环，在新线程中运行
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, async_process())
                    result = future.result(timeout=30)
            except RuntimeError:
                # 没有运行的事件循环，直接运行
                result = asyncio.run(async_process())

            logging.getLogger(__name__).info(f"使用通知处理器: {data}")
            return result

        except ImportError:
            logging.getLogger(__name__).error("通知处理器不可用")
            return SmartDataObject({
                'success': False,
                'error': '通知处理器不可用，请检查插件安装',
                'processor': 'notification'
            })
        except Exception as e:
            logging.getLogger(__name__).error(f"通知处理器失败: {e}")
            return SmartDataObject({
                'success': False,
                'error': str(e),
                'processor': 'notification'
            })

    def ssh(self, host: str, username: str, **options):
        """SSH连接器 - 使用智能Remote Host加载器"""
        try:
            # 构建SSH配置
            ssh_config = {
                'host': host,
                'username': username,
                **options
            }

            # 使用remote_host方法
            return self.remote_host(ssh_config)

        except Exception as e:
            logging.getLogger(__name__).error(f"SSH连接失败: {e}")
            return self.__class__({
                'error': str(e),
                'success': False,
                'connector': 'ssh'
            })

    def websocket(self, url: str, **options):
        """WebSocket连接器 - 使用智能Stream加载器"""
        try:
            # 构建WebSocket配置
            ws_config = {
                'protocol': 'websocket',
                'url': url,
                **options
            }

            # 使用stream方法
            return self.stream(ws_config)

        except Exception as e:
            logging.getLogger(__name__).error(f"WebSocket连接失败: {e}")
            return self.__class__({
                'error': str(e),
                'success': False,
                'connector': 'websocket'
            })

    def ai(self, data: Any, **options):
        """AI插件方法 - SmartDataLoader集成标准"""
        try:
            # 尝试使用智能加载器
            from plugins.ai.smart_ai_loader import global_ai_loader

            load_options = options.copy() if options else {}
            result = global_ai_loader.load(data, load_options)
            logging.getLogger(__name__).info(f"使用AI智能加载器: {data}")
            return result

        except ImportError:
            # 回退到原始处理器
            logging.getLogger(__name__).warning("AI智能加载器不可用，使用原始处理器")
            from plugins.ai.ai_processor import AiDataProcessor
            processor = AiDataProcessor()
            return processor.process(data, options)
        except Exception as e:
            logging.getLogger(__name__).error(f"AI智能加载器失败: {e}")
            # 最后回退
            from plugins.ai.ai_processor import AiDataProcessor
            processor = AiDataProcessor()
            return processor.process(data, options)

    def remote(self, host_config: Dict[str, Any]):
        """远程主机连接器"""
        return RemoteConnector(host_config, self.registry)

    def __call__(self, data_source: Any) -> SmartDataObject:
        """支持 sd(data_source) 调用"""
        return self.loader(data_source)


# 全局智能数据加载器实例
_global_registry: Optional[PluginRegistry] = None

def set_global_registry(registry: PluginRegistry):
    """设置全局插件注册表"""
    global _global_registry
    _global_registry = registry

def get_smart_data_loader() -> SmartDataLoader:
    """获取智能数据加载器"""
    return SmartDataLoader(_global_registry)

class DatabaseConnector:
    """数据库连接器 - 使用SmartDataMatcher"""

    def __init__(self, connection_string: str, registry: PluginRegistry):
        self.connection_string = connection_string
        self.registry = registry
        self.logger = logging.getLogger(self.__class__.__name__)

        # 使用智能匹配器
        self.matcher = SmartDataMatcher()
        if registry:
            for processor_id, processor in registry.processors.items():
                self.matcher.register_processor(processor)

    def query(self, sql: str) -> SmartDataObject:
        """执行SQL查询 - 使用智能匹配器选择最佳数据库处理器"""
        try:
            # 构建数据库查询数据
            query_data = {
                'type': 'database_query',
                'connection_string': self.connection_string,
                'query': sql,
                'config': self._parse_connection_string(self.connection_string)
            }

            # 使用SmartDataObject自动处理，它会使用智能匹配器
            return SmartDataObject(query_data, self.registry)

        except Exception as e:
            self.logger.error(f"数据库查询失败: {e}")
            # 返回模拟数据
            mock_data = [
                {'id': 1, 'name': '张三', 'email': '<EMAIL>', 'age': 25},
                {'id': 2, 'name': '李四', 'email': '<EMAIL>', 'age': 30},
                {'id': 3, 'name': '王五', 'email': '<EMAIL>', 'age': 28}
            ]
            return SmartDataObject(mock_data, self.registry)

    def _parse_connection_string(self, conn_str: str) -> Dict[str, Any]:
        """解析连接字符串"""
        # 简化的解析逻辑
        if conn_str.startswith('mysql://'):
            return {
                'type': 'mysql',
                'host': 'localhost',
                'port': 3306,
                'database': 'test_db',
                'username': 'admin',
                'password': 'admin123'
            }
        return {'type': 'postgresql', 'host': 'localhost', 'port': 5432}




class ApiConnector:
    """API连接器 - 使用SmartDataMatcher"""

    def __init__(self, base_url: str, registry: PluginRegistry, **kwargs):
        self.base_url = base_url
        self.registry = registry
        self.auth = kwargs.get('auth')
        self.headers = kwargs.get('headers', {})
        self.logger = logging.getLogger(self.__class__.__name__)

    def get(self, path: str = '', **params) -> SmartDataObject:
        """GET请求 - 使用智能匹配器"""
        api_data = {
            'type': 'api_request',
            'method': 'GET',
            'url': f"{self.base_url.rstrip('/')}/{path.lstrip('/')}",
            'params': params,
            'headers': self.headers,
            'auth': self.auth
        }
        return SmartDataObject(api_data, self.registry)

    def post(self, path: str = '', data=None, **params) -> SmartDataObject:
        """POST请求 - 使用智能匹配器"""
        api_data = {
            'type': 'api_request',
            'method': 'POST',
            'url': f"{self.base_url.rstrip('/')}/{path.lstrip('/')}",
            'data': data,
            'params': params,
            'headers': self.headers,
            'auth': self.auth
        }
        return SmartDataObject(api_data, self.registry)


class FileConnector:
    """文件连接器 - 使用SmartDataMatcher"""

    def __init__(self, file_path: str, registry: PluginRegistry):
        self.file_path = file_path
        self.registry = registry
        self.logger = logging.getLogger(self.__class__.__name__)

    def parse(self) -> SmartDataObject:
        """解析文件 - 使用智能匹配器"""
        file_data = {
            'type': 'file_path',
            'path': self.file_path,
            'operation': 'parse'
        }
        return SmartDataObject(file_data, self.registry)


class StreamConnector:
    """流数据连接器 - 使用SmartDataMatcher"""

    def __init__(self, config: Dict[str, Any], registry: PluginRegistry):
        self.config = config
        self.registry = registry
        self.logger = logging.getLogger(self.__class__.__name__)

    def listen(self) -> SmartDataObject:
        """监听流数据 - 使用智能匹配器"""
        stream_data = {
            'type': 'stream_data',
            'config': self.config,
            'operation': 'listen'
        }
        return SmartDataObject(stream_data, self.registry)


class RemoteConnector:
    """远程主机连接器 - 使用SmartDataMatcher"""

    def __init__(self, host_config: Dict[str, Any], registry: PluginRegistry):
        self.host_config = host_config
        self.registry = registry
        self.logger = logging.getLogger(self.__class__.__name__)

    def execute(self, command: str) -> SmartDataObject:
        """执行远程命令 - 使用智能匹配器"""
        remote_data = {
            'type': 'remote_command',
            'host_config': self.host_config,
            'command': command,
            'operation': 'execute'
        }
        return SmartDataObject(remote_data, self.registry)


# 创建全局实例
sd = get_smart_data_loader()
