#!/usr/bin/env python3
"""
HTTP插件标准测试 - 遵循插件标准规范

测试HTTP插件的所有功能，确保符合标准要求。
"""

import pytest
import sys
import os

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

# 导入测试组件
try:
    from template.template_ext import create_template_engine
    from plugins.plugin_registry import PluginRegistry
    from plugins.http import HttpProcessor, global_loader
except ImportError as e:
    pytest.skip(f"HTTP插件组件不可用: {e}", allow_module_level=True)


class TestHttpPluginStandard:
    """HTTP插件标准测试"""
    
    @pytest.fixture
    def plugin_registry(self):
        """创建插件注册表"""
        registry = PluginRegistry()
        
        # 注册HTTP插件处理器
        try:
            http_processor = HttpProcessor({'enable_debug': True})
            registry.register_processor(http_processor)
        except Exception as e:
            print(f"注册HTTP处理器失败: {e}")
        
        return registry
    
    @pytest.fixture
    def template_engine(self, plugin_registry):
        """模板引擎测试夹具"""
        engine = create_template_engine(plugin_registry, debug=True)
        
        # 验证sd对象是否正确注册
        assert 'sd' in engine.env.globals, "sd对象未注册到模板环境"
        assert hasattr(engine.env.globals['sd'], 'http'), "sd对象缺少http方法"

        # 添加模板需要的函数
        engine.env.globals['hasattr'] = hasattr

        return engine
    
    def test_plugin_factory(self):
        """测试插件工厂"""
        processor = HttpProcessor()
        
        # 测试类型检测
        assert processor.can_process('https://httpbin.org/get') == True
        assert processor.can_process({'url': 'https://httpbin.org/get'}) == True
        assert processor.can_process('not_a_url') == False
        assert processor.can_process(123) == False
        
        # 测试处理器信息
        info = processor.get_processor_info()
        assert info['name'] == 'HttpProcessor'
        assert info['version'] == '2.0.0'
        assert 'http_requests' in info['capabilities']
    
    def test_smart_loader(self):
        """测试智能加载器"""
        loader = global_loader
        
        # 测试基础加载
        try:
            result = loader.load('https://httpbin.org/get')
            assert hasattr(result, 'success')
            assert hasattr(result, 'data')
            print(f"✅ 基础加载测试: success={result.success}")
        except Exception as e:
            print(f"⚠️ 基础加载测试跳过（网络问题）: {e}")
        
        # 测试缓存功能
        cache_info = loader.get_cache_info()
        assert 'cache_size' in cache_info
        assert 'cached_keys' in cache_info
        
        # 测试加载器信息
        loader_info = loader.get_loader_info()
        assert loader_info['name'] == 'SmartHttpLoader'
        assert loader_info['version'] == '2.0.0'
    
    def test_template_integration(self, template_engine):
        """测试模板引擎集成"""
        # 测试模板中的使用
        template_content = """
{%- set response = sd.http('https://httpbin.org/get') -%}
HTTP测试结果:
响应对象存在: {{ 'YES' if response else 'NO' }}
{%- if response -%}
响应类型: {{ response.__class__.__name__ }}
有success属性: {{ 'YES' if hasattr(response, 'success') else 'NO' }}
有data属性: {{ 'YES' if hasattr(response, 'data') else 'NO' }}
{%- if hasattr(response, 'success') -%}
success值: {{ response.success }}
{%- endif -%}
{%- endif -%}
        """
        
        try:
            result = template_engine.render_template(template_content)
            print(f"模板集成测试结果:\n{result}")
            
            # 验证模板渲染成功
            assert "HTTP测试结果:" in result
            assert "响应对象存在: YES" in result
            
        except Exception as e:
            pytest.fail(f"模板集成测试失败: {e}")
    
    def test_http_methods_comprehensive(self, template_engine):
        """测试HTTP方法全覆盖"""
        template_content = """
{# GET请求测试 #}
{%- set get_response = sd.http('https://httpbin.org/get') -%}
GET: {{ 'SUCCESS' if get_response.success else 'FAILED' }}

{# POST请求测试 #}
{%- set post_response = sd.http({
    'url': 'https://httpbin.org/post',
    'method': 'POST',
    'json': {'test': 'data'}
}) -%}
POST: {{ 'SUCCESS' if post_response.success else 'FAILED' }}

{# PUT请求测试 #}
{%- set put_response = sd.http({
    'url': 'https://httpbin.org/put',
    'method': 'PUT',
    'json': {'update': 'data'}
}) -%}
PUT: {{ 'SUCCESS' if put_response.success else 'FAILED' }}

{# DELETE请求测试 #}
{%- set delete_response = sd.http({
    'url': 'https://httpbin.org/delete',
    'method': 'DELETE'
}) -%}
DELETE: {{ 'SUCCESS' if delete_response.success else 'FAILED' }}
        """
        
        try:
            result = template_engine.render_template(template_content)
            print(f"HTTP方法测试结果:\n{result}")
            
            # 验证至少有一个方法成功
            success_count = result.count('SUCCESS')
            assert success_count > 0, f"所有HTTP方法都失败了: {result}"
            
            print(f"✅ HTTP方法测试通过，{success_count}个方法成功")
            
        except Exception as e:
            pytest.fail(f"HTTP方法测试失败: {e}")
    
    def test_error_handling(self, template_engine):
        """测试错误处理"""
        template_content = """
{# 测试404错误 #}
{%- set error_response = sd.http('https://httpbin.org/status/404') -%}
404错误处理: {{ 'HANDLED' if not error_response.success else 'UNEXPECTED_SUCCESS' }}

{# 测试无效URL #}
{%- set invalid_response = sd.http('https://invalid-domain-12345.com') -%}
无效URL处理: {{ 'HANDLED' if not invalid_response.success else 'UNEXPECTED_SUCCESS' }}
        """
        
        try:
            result = template_engine.render_template(template_content)
            print(f"错误处理测试结果:\n{result}")
            
            # 验证错误被正确处理
            handled_count = result.count('HANDLED')
            assert handled_count >= 1, f"错误处理不完整: {result}"
            
            print("✅ 错误处理测试通过")
            
        except Exception as e:
            pytest.fail(f"错误处理测试失败: {e}")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-s"])
