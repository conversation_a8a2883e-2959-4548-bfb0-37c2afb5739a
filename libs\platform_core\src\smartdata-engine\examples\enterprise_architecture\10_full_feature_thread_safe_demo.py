#!/usr/bin/env python3
"""
完整功能线程安全演示

验证线程安全版本是否支持旧模板引擎的所有强大数据处理功能
"""

import sys
import os
import threading
import time
import json
import tempfile
from typing import Dict, Any

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.thread_safe_template_integration import ThreadSafeTemplateIntegration


def full_feature_demo():
    """完整功能演示"""
    print("=== 完整功能线程安全演示 ===")
    print("验证线程安全版本对旧模板引擎强大功能的支持")
    print("=" * 80)
    
    # 创建线程安全的模板引擎
    engine = ThreadSafeTemplateIntegration(
        enable_async=True,
        enable_debug=True,
        isolation_level='thread',
        cleanup_interval=60,
        max_scope_lifetime=300
    )
    
    # 🧠 示例1：智能数据处理功能
    print("\n🧠 示例1：智能数据处理功能")
    print("-" * 60)
    
    def smart_data_test():
        """智能数据处理测试"""
        try:
            # 复杂的JSON数据
            complex_json = {
                "company": {
                    "name": "智慧科技有限公司",
                    "founded": 2020,
                    "employees": [
                        {
                            "id": 1,
                            "name": "张三",
                            "department": "技术部",
                            "skills": ["Python", "AI", "数据分析"],
                            "projects": [
                                {"name": "项目A", "status": "进行中", "progress": 75},
                                {"name": "项目B", "status": "已完成", "progress": 100}
                            ]
                        },
                        {
                            "id": 2,
                            "name": "李四",
                            "department": "销售部",
                            "skills": ["销售", "客户管理"],
                            "projects": [
                                {"name": "项目C", "status": "计划中", "progress": 0}
                            ]
                        }
                    ]
                }
            }
            
            template = """
智能数据处理演示
===============

1. 基础数据访问:
公司名称: {{ company.name }}
成立年份: {{ company.founded }}
员工总数: {{ company.employees | length }}

2. 智能数据修改器功能:
{%- set smart_company = sd.smart_data(company) -%}
{%- if smart_company -%}
智能修改器类型: {{ smart_company.__class__.__name__ }}
支持操作历史: {{ smart_company.enable_history if smart_company.enable_history is defined else '未知' }}
{%- endif -%}

3. JSONPath查询功能:
第一个员工姓名: {{ sd.jsonpath(company, '$.employees[0].name') }}
技术部员工: {{ sd.jsonpath(company, '$.employees[?(@.department=="技术部")].name') }}

4. 增强数据类型:
{%- set enhanced_company = sd.enhanced_data(company) -%}
增强数据类型: {{ enhanced_company.__class__.__name__ if enhanced_company else '基础类型' }}

5. 复杂数据操作:
{%- for emp in company.employees -%}
员工: {{ emp.name }}
  - 部门: {{ emp.department }}
  - 技能数: {{ emp.skills | length }}
  - 项目数: {{ emp.projects | length }}
  - 进行中项目: {{ emp.projects | selectattr('status', 'equalto', '进行中') | list | length }}
{%- endfor -%}
            """.strip()
            
            context = {
                'company': complex_json['company']
            }
            
            result = engine.render_template_sync(template, context, 'smart_data_test')
            print("✅ 智能数据处理测试成功:")
            print(result)
            return True
            
        except Exception as e:
            print(f"❌ 智能数据处理测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    smart_success = smart_data_test()
    
    # 📊 示例2：多线程智能数据处理
    print("\n📊 示例2：多线程智能数据处理")
    print("-" * 60)
    
    def threaded_smart_data_test():
        """多线程智能数据处理测试"""
        def worker(worker_id: int, results: list):
            try:
                # 每个线程处理不同的数据
                test_data = {
                    "worker_id": worker_id,
                    "data": {
                        "items": [
                            {"id": i, "value": i * worker_id, "active": i % 2 == 0}
                            for i in range(1, 6)
                        ],
                        "metadata": {
                            "created_by": f"worker_{worker_id}",
                            "timestamp": time.time(),
                            "thread_id": threading.current_thread().ident
                        }
                    }
                }
                
                template = """
多线程智能数据处理 - Worker {{ worker_id }}
========================================

1. 基础信息:
工作线程: {{ worker_id }}
数据项数: {{ data.items | length }}
创建者: {{ data.metadata.created_by }}

2. 智能数据处理:
{%- set smart_data = sd.smart_data(data) -%}
{%- if smart_data -%}
智能处理: 成功
数据类型: {{ smart_data.__class__.__name__ }}
{%- else -%}
智能处理: 使用基础类型
{%- endif -%}

3. JSONPath查询:
活跃项目数: {{ sd.jsonpath(data, '$.items[?(@.active==true)]') | length if sd.jsonpath(data, '$.items[?(@.active==true)]') else 0 }}
第一个项目值: {{ sd.jsonpath(data, '$.items[0].value') }}

4. 数据统计:
总值: {{ data.items | sum(attribute='value') }}
平均值: {{ (data.items | sum(attribute='value')) // (data.items | length) }}
活跃率: {{ (data.items | selectattr('active') | list | length) / (data.items | length) * 100 }}%
                """.strip()
                
                result = engine.render_template_sync(
                    template, 
                    test_data, 
                    f'threaded_smart_{worker_id}'
                )
                
                results.append(f"✅ Worker{worker_id}智能处理成功")
                print(f"Worker{worker_id}完成智能数据处理")
                
            except Exception as e:
                results.append(f"❌ Worker{worker_id}失败: {e}")
                print(f"Worker{worker_id}失败: {e}")
        
        # 启动多个工作线程
        threads = []
        results = []
        
        for i in range(3):
            thread = threading.Thread(
                target=worker,
                args=(i + 1, results),
                name=f"SmartWorker-{i + 1}"
            )
            threads.append(thread)
            thread.start()
        
        # 等待完成
        for thread in threads:
            thread.join()
        
        print("\n多线程智能数据处理结果:")
        for result in results:
            print(f"  {result}")
        
        return len([r for r in results if r.startswith('✅')]) == 3
    
    threaded_success = threaded_smart_data_test()
    
    # 🗂️ 示例3：文件数据智能处理
    print("\n🗂️ 示例3：文件数据智能处理")
    print("-" * 60)
    
    def file_smart_data_test():
        """文件数据智能处理测试"""
        try:
            # 创建复杂的JSON测试文件
            complex_data = {
                "database": {
                    "users": [
                        {
                            "id": 1,
                            "profile": {
                                "name": "Alice",
                                "email": "<EMAIL>",
                                "preferences": {
                                    "theme": "dark",
                                    "language": "zh-CN",
                                    "notifications": True
                                }
                            },
                            "activity": {
                                "last_login": "2024-07-29T10:30:00Z",
                                "login_count": 156,
                                "favorite_features": ["dashboard", "reports", "analytics"]
                            }
                        },
                        {
                            "id": 2,
                            "profile": {
                                "name": "Bob",
                                "email": "<EMAIL>",
                                "preferences": {
                                    "theme": "light",
                                    "language": "en-US",
                                    "notifications": False
                                }
                            },
                            "activity": {
                                "last_login": "2024-07-28T15:45:00Z",
                                "login_count": 89,
                                "favorite_features": ["settings", "profile"]
                            }
                        }
                    ]
                }
            }
            
            # 创建临时文件
            temp_file = tempfile.mktemp(suffix='.json')
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(complex_data, f, ensure_ascii=False, indent=2)
            
            template = f"""
文件智能数据处理演示
==================

1. 文件数据加载:
{{%- set file_data = sd.file('{temp_file.replace(chr(92), '/')}').parse() -%}}
用户总数: {{{{ file_data.database.users | length }}}}

2. 智能数据处理:
{{%- set smart_file_data = sd.smart_data(file_data) -%}}
{{%- if smart_file_data -%}}
智能处理: 成功
处理器类型: {{{{ smart_file_data.__class__.__name__ }}}}
{{%- else -%}}
智能处理: 基础类型
{{%- endif -%}}

3. JSONPath复杂查询:
所有用户名: {{{{ sd.jsonpath(file_data, '$.database.users[*].profile.name') }}}}
深色主题用户: {{{{ sd.jsonpath(file_data, '$.database.users[?(@.profile.preferences.theme=="dark")].profile.name') }}}}
高活跃用户: {{{{ sd.jsonpath(file_data, '$.database.users[?(@.activity.login_count>100)].profile.name') }}}}

4. 增强数据类型处理:
{{%- set enhanced_data = sd.enhanced_data(file_data) -%}}
增强处理: {{{{ enhanced_data.__class__.__name__ if enhanced_data else '基础类型' }}}}

5. 复杂数据分析:
{{%- for user in file_data.database.users -%}}
用户: {{{{ user.profile.name }}}}
  - 邮箱: {{{{ user.profile.email }}}}
  - 主题偏好: {{{{ user.profile.preferences.theme }}}}
  - 登录次数: {{{{ user.activity.login_count }}}}
  - 收藏功能: {{{{ user.activity.favorite_features | join(', ') }}}}
  - 通知设置: {{{{ '开启' if user.profile.preferences.notifications else '关闭' }}}}
{{%- endfor -%}}
            """.strip()
            
            result = engine.render_template_sync(template, {}, 'file_smart_test')
            print("✅ 文件智能数据处理测试成功:")
            print(result)
            
            # 清理临时文件
            if os.path.exists(temp_file):
                os.unlink(temp_file)
            
            return True
            
        except Exception as e:
            print(f"❌ 文件智能数据处理测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    file_success = file_smart_data_test()
    
    # 📋 总结报告
    print("\n📋 完整功能测试总结")
    print("=" * 80)
    
    test_results = {
        '智能数据处理': smart_success,
        '多线程智能处理': threaded_success,
        '文件智能处理': file_success
    }
    
    print("功能测试结果:")
    for test_name, success in test_results.items():
        status = "✅ 支持" if success else "❌ 不支持"
        print(f"  {test_name}: {status}")
    
    overall_success = all(test_results.values())
    
    print(f"\n总体结果: {'✅ 完整支持旧模板引擎功能' if overall_success else '❌ 部分功能缺失'}")
    
    if overall_success:
        print("\n🎉 线程安全版本完整支持旧模板引擎的强大功能！")
        print("\n💡 支持的核心功能:")
        print("1. ✅ SmartDataFactory智能数据处理")
        print("2. ✅ JSONPath复杂路径查询")
        print("3. ✅ 智能数据修改器")
        print("4. ✅ 增强数据类型处理")
        print("5. ✅ 多线程安全访问")
        print("6. ✅ 操作历史和事务支持")
        
        print("\n🚀 架构优势:")
        print("- 🔒 线程安全 + 完整功能")
        print("- 🧠 智能数据处理")
        print("- 🔍 强大的路径查询")
        print("- 📊 复杂数据分析")
        print("- 🛡️ 企业级安全保护")
    else:
        print("\n⚠️ 部分旧模板引擎功能需要进一步集成")
    
    # 关闭引擎
    print("\n🔧 正在关闭模板引擎...")
    engine.shutdown()
    print("✅ 模板引擎已安全关闭")
    
    return overall_success


if __name__ == "__main__":
    success = full_feature_demo()
    if success:
        print("\n🎯 线程安全版本完美支持旧模板引擎的所有强大功能！")
    else:
        print("\n🔧 需要进一步完善功能集成")
