"""
SQLite数据库适配器

提供SQLite数据库的完整支持，包括：
- 连接管理
- SQL执行
- 事务处理
- 内存数据库支持
- 文件数据库支持
"""

from typing import Any, Dict, List, Optional, Union
import logging
import sqlite3
import os

from .base import DatabaseAdapterBase, ConnectionInfo


class SQLiteAdapter(DatabaseAdapterBase):
    """
    SQLite数据库适配器

    支持SQLite特有的功能：
    - 内存数据库
    - 文件数据库
    - WAL模式
    - 全文搜索
    - JSON支持（SQLite 3.38+）
    - 自定义函数
    """

    def __init__(self):
        super().__init__()
        self.logger.info(f"SQLite适配器初始化，SQLite版本: {sqlite3.sqlite_version}")

    def supported_types(self) -> List[str]:
        """返回支持的数据类型列表"""
        return [
            'sqlite',
            'sqlite_connection',
            'sqlite_connection_string',
            'sqlite_url',
            'sqlite3',
            'sqlite3_connection'
        ]

    def _is_supported_connection_string(self, connection_string: str) -> bool:
        """检查是否支持的连接字符串"""
        return (connection_string.startswith('sqlite://') or
                connection_string.endswith('.db') or
                connection_string.endswith('.sqlite') or
                connection_string.endswith('.sqlite3') or
                connection_string == ':memory:')

    def _is_supported_connection_object(self, connection: Any) -> bool:
        """检查是否支持的连接对象"""
        return isinstance(connection, sqlite3.Connection)

    def can_handle(self, data_source: Any) -> bool:
        """检查是否能处理指定的数据源"""
        if isinstance(data_source, str):
            return self._is_supported_connection_string(data_source)
        else:
            return self._is_supported_connection_object(data_source)

    def _get_default_port(self) -> int:
        """获取默认端口"""
        return 0  # SQLite不使用端口

    def _create_connection(self, connection_source: Union[str, ConnectionInfo]) -> Any:
        """创建SQLite连接"""
        if isinstance(connection_source, str):
            if connection_source.startswith('sqlite://'):
                # 解析SQLite URL
                database_path = connection_source[9:]  # 移除 'sqlite://'
                if database_path.startswith('///'):
                    database_path = database_path[3:]  # 绝对路径
                elif database_path.startswith('//'):
                    database_path = database_path[2:]  # 相对路径
            else:
                database_path = connection_source
        else:
            # 从ConnectionInfo获取数据库路径
            database_path = connection_source.database

        try:
            # 如果是文件数据库，确保目录存在
            if database_path != ':memory:' and not os.path.exists(os.path.dirname(database_path) or '.'):
                os.makedirs(os.path.dirname(database_path), exist_ok=True)

            # 创建连接
            connection = sqlite3.connect(
                database_path,
                check_same_thread=False,  # 允许多线程访问
                timeout=30.0  # 30秒超时
            )

            # 设置行工厂为字典
            connection.row_factory = sqlite3.Row

            # 启用外键约束
            connection.execute("PRAGMA foreign_keys = ON")

            # 设置WAL模式（如果不是内存数据库）
            if database_path != ':memory:':
                connection.execute("PRAGMA journal_mode = WAL")

            self.logger.info(f"成功创建SQLite连接: {database_path}")
            return connection

        except Exception as e:
            self.logger.error(f"创建SQLite连接失败: {e}")
            raise

    def _execute_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """执行查询 - 返回字典列表"""
        try:
            cursor = connection.cursor()

            # 执行查询
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)

            # 获取结果并转换为字典列表
            results = []
            for row in cursor.fetchall():
                results.append(dict(row))

            cursor.close()
            return results

        except Exception as e:
            self.logger.error(f"SQLite查询执行失败: {e}")
            raise

    def _execute_command(self, connection: Any, sql: str, params: Dict = None) -> int:
        """执行命令 - 返回影响行数"""
        try:
            cursor = connection.cursor()

            # 执行命令
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)

            # 获取影响行数
            affected_rows = cursor.rowcount

            # 提交事务（如果不在事务中）
            if connection.in_transaction:
                connection.commit()

            cursor.close()
            return affected_rows

        except Exception as e:
            self.logger.error(f"SQLite命令执行失败: {e}")
            raise

    def _get_database_info(self, connection: Any) -> Dict[str, Any]:
        """获取SQLite数据库信息"""
        try:
            cursor = connection.cursor()

            # 获取SQLite版本
            cursor.execute("SELECT sqlite_version() as sqlite_version")
            version_info = cursor.fetchone()

            # 获取数据库文件信息
            cursor.execute("PRAGMA database_list")
            database_list = cursor.fetchall()

            # 获取编译选项
            cursor.execute("PRAGMA compile_options")
            compile_options = [row[0] for row in cursor.fetchall()]

            # 获取页面大小和缓存大小
            cursor.execute("PRAGMA page_size")
            page_size = cursor.fetchone()[0]

            cursor.execute("PRAGMA cache_size")
            cache_size = cursor.fetchone()[0]

            cursor.close()

            return {
                'database_type': 'SQLite',
                'sqlite_version': version_info['sqlite_version'] if version_info else 'Unknown',
                'python_sqlite_version': sqlite3.version,
                'databases': [dict(db) for db in database_list],
                'page_size': page_size,
                'cache_size': cache_size,
                'compile_options': compile_options
            }

        except Exception as e:
            self.logger.error(f"获取SQLite数据库信息失败: {e}")
            return {
                'database_type': 'SQLite',
                'error': str(e)
            }

    # ========================================================================
    # SQLite特有功能
    # ========================================================================

    def _build_operations(self) -> Dict[str, callable]:
        """构建SQLite特有操作列表"""
        operations = super()._build_operations()

        # 添加SQLite特有操作
        operations.update({
            'pragma': self._pragma_wrapper,
            'vacuum': self._vacuum_wrapper,
            'reindex': self._reindex_wrapper,
            'integrity_check': self._integrity_check_wrapper,
            'quick_check': self._quick_check_wrapper,
            'attach': self._attach_wrapper,
            'detach': self._detach_wrapper,
            'backup': self._backup_wrapper,
            'restore': self._restore_wrapper,
            'create_function': self._create_function_wrapper,
        })

        return operations

    def _pragma_wrapper(self, connection: Any, pragma_name: str, value: Any = None):
        """PRAGMA操作包装器"""
        return self._pragma(connection, pragma_name, value)

    def _vacuum_wrapper(self, connection: Any, schema: str = None):
        """VACUUM操作包装器"""
        return self._vacuum(connection, schema)

    def _reindex_wrapper(self, connection: Any, index_or_table: str = None):
        """REINDEX操作包装器"""
        return self._reindex(connection, index_or_table)

    def _integrity_check_wrapper(self, connection: Any, table: str = None):
        """完整性检查包装器"""
        return self._integrity_check(connection, table)

    def _quick_check_wrapper(self, connection: Any, table: str = None):
        """快速检查包装器"""
        return self._quick_check(connection, table)

    def _attach_wrapper(self, connection: Any, database_path: str, alias: str):
        """附加数据库包装器"""
        return self._attach(connection, database_path, alias)

    def _detach_wrapper(self, connection: Any, alias: str):
        """分离数据库包装器"""
        return self._detach(connection, alias)

    def _backup_wrapper(self, connection: Any, backup_path: str):
        """备份数据库包装器"""
        return self._backup(connection, backup_path)

    def _restore_wrapper(self, connection: Any, backup_path: str):
        """恢复数据库包装器"""
        return self._restore(connection, backup_path)

    def _create_function_wrapper(self, connection: Any, name: str, func: callable, num_params: int = -1):
        """创建自定义函数包装器"""
        return self._create_function(connection, name, func, num_params)

    def _pragma(self, connection: Any, pragma_name: str, value: Any = None) -> Any:
        """执行PRAGMA命令"""
        try:
            cursor = connection.cursor()

            if value is not None:
                cursor.execute(f"PRAGMA {pragma_name} = {value}")
                connection.commit()
                result = {'success': True, 'pragma': pragma_name, 'value': value}
            else:
                cursor.execute(f"PRAGMA {pragma_name}")
                result = cursor.fetchall()
                result = [dict(row) for row in result] if result else []

            cursor.close()
            return result

        except Exception as e:
            self.logger.error(f"PRAGMA操作失败: {e}")
            raise

    def _vacuum(self, connection: Any, schema: str = None) -> Dict:
        """执行VACUUM操作"""
        try:
            cursor = connection.cursor()

            if schema:
                cursor.execute(f"VACUUM {schema}")
            else:
                cursor.execute("VACUUM")

            cursor.close()

            return {
                'success': True,
                'operation': 'VACUUM',
                'schema': schema
            }

        except Exception as e:
            self.logger.error(f"VACUUM操作失败: {e}")
            raise

    def _reindex(self, connection: Any, index_or_table: str = None) -> Dict:
        """执行REINDEX操作"""
        try:
            cursor = connection.cursor()

            if index_or_table:
                cursor.execute(f"REINDEX {index_or_table}")
            else:
                cursor.execute("REINDEX")

            cursor.close()

            return {
                'success': True,
                'operation': 'REINDEX',
                'target': index_or_table
            }

        except Exception as e:
            self.logger.error(f"REINDEX操作失败: {e}")
            raise

    def _integrity_check(self, connection: Any, table: str = None) -> List[Dict]:
        """执行完整性检查"""
        try:
            cursor = connection.cursor()

            if table:
                cursor.execute(f"PRAGMA integrity_check({table})")
            else:
                cursor.execute("PRAGMA integrity_check")

            results = cursor.fetchall()
            cursor.close()

            return [dict(row) for row in results]

        except Exception as e:
            self.logger.error(f"完整性检查失败: {e}")
            raise

    def _quick_check(self, connection: Any, table: str = None) -> List[Dict]:
        """执行快速检查"""
        try:
            cursor = connection.cursor()

            if table:
                cursor.execute(f"PRAGMA quick_check({table})")
            else:
                cursor.execute("PRAGMA quick_check")

            results = cursor.fetchall()
            cursor.close()

            return [dict(row) for row in results]

        except Exception as e:
            self.logger.error(f"快速检查失败: {e}")
            raise

    def _attach(self, connection: Any, database_path: str, alias: str) -> Dict:
        """附加数据库"""
        try:
            cursor = connection.cursor()
            cursor.execute(f"ATTACH DATABASE '{database_path}' AS {alias}")
            cursor.close()

            return {
                'success': True,
                'operation': 'ATTACH',
                'database_path': database_path,
                'alias': alias
            }

        except Exception as e:
            self.logger.error(f"附加数据库失败: {e}")
            raise

    def _detach(self, connection: Any, alias: str) -> Dict:
        """分离数据库"""
        try:
            cursor = connection.cursor()
            cursor.execute(f"DETACH DATABASE {alias}")
            cursor.close()

            return {
                'success': True,
                'operation': 'DETACH',
                'alias': alias
            }

        except Exception as e:
            self.logger.error(f"分离数据库失败: {e}")
            raise

    def _backup(self, connection: Any, backup_path: str) -> Dict:
        """备份数据库"""
        try:
            # 创建备份连接
            backup_conn = sqlite3.connect(backup_path)

            # 执行备份
            connection.backup(backup_conn)
            backup_conn.close()

            return {
                'success': True,
                'operation': 'BACKUP',
                'backup_path': backup_path
            }

        except Exception as e:
            self.logger.error(f"备份数据库失败: {e}")
            raise

    def _restore(self, connection: Any, backup_path: str) -> Dict:
        """从备份恢复数据库"""
        try:
            # 创建备份连接
            backup_conn = sqlite3.connect(backup_path)

            # 执行恢复
            backup_conn.backup(connection)
            backup_conn.close()

            return {
                'success': True,
                'operation': 'RESTORE',
                'backup_path': backup_path
            }

        except Exception as e:
            self.logger.error(f"恢复数据库失败: {e}")
            raise

    def _create_function(self, connection: Any, name: str, func: callable, num_params: int = -1) -> Dict:
        """创建自定义函数"""
        try:
            connection.create_function(name, num_params, func)

            return {
                'success': True,
                'operation': 'CREATE_FUNCTION',
                'function_name': name,
                'num_params': num_params
            }

        except Exception as e:
            self.logger.error(f"创建自定义函数失败: {e}")
            raise

    # ========================================================================
    # 重写基类方法以适配SQLite特性
    # ========================================================================

    def _get_tables(self, connection: Any, schema: str = None) -> List[str]:
        """获取表列表（SQLite版本）"""
        try:
            cursor = connection.cursor()

            if schema:
                cursor.execute(f"SELECT name FROM {schema}.sqlite_master WHERE type='table'")
            else:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")

            results = cursor.fetchall()
            cursor.close()

            return [row['name'] for row in results]

        except Exception as e:
            self.logger.error(f"获取表列表失败: {e}")
            raise

    def _get_columns(self, connection: Any, table: str, schema: str = None) -> List[Dict]:
        """获取列信息（SQLite版本）"""
        try:
            cursor = connection.cursor()

            if schema:
                cursor.execute(f"PRAGMA {schema}.table_info({table})")
            else:
                cursor.execute(f"PRAGMA table_info({table})")

            results = cursor.fetchall()
            cursor.close()

            return [dict(row) for row in results]

        except Exception as e:
            self.logger.error(f"获取列信息失败: {e}")
            raise
