"""
企业级模板引擎集成

将新的统一适配器系统与现有模板引擎进行深度集成
"""

import logging
import asyncio
import threading
import weakref
import time
from typing import Any, Dict, List, Optional, Union, Callable
from contextlib import asynccontextmanager, contextmanager
from concurrent.futures import ThreadPoolExecutor
import uuid

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../'))

from core.enterprise_data_architecture import DataRegistry, DataProxy, LifecycleManager, TemplateScope, IDataAdapter, ILifecycleManager
from core.data_contract import DataResult, TemplateDataContract, ensure_data_result
from core.interfaces import ITemplateEngine
from core.async_interfaces import IAsyncDataAdapter
from core.async_data_proxy import AsyncDataProxy
from core.async_lifecycle_manager import AsyncLifecycleManager
from core.async_template_scope import AsyncTemplateScope

# 导入现有模板引擎组件
try:
    from template_ext import HybridTemplateEngine, SmartDataTemplateExtension
    from core.smart_data_object import SmartDataLoader
    TEMPLATE_ENGINE_AVAILABLE = True
except ImportError:
    TEMPLATE_ENGINE_AVAILABLE = False
    HybridTemplateEngine = None
    SmartDataTemplateExtension = None
    SmartDataLoader = None


class EnterpriseTemplateIntegration:
    """
    企业级模板引擎集成器
    
    功能特性：
    - 无缝集成新的统一适配器系统
    - 保持向后兼容性
    - 支持同步和异步模板渲染
    - 企业级生命周期管理
    - 统一数据契约
    - 智能适配器选择
    """
    
    def __init__(self, 
                 enable_async: bool = True,
                 enable_legacy_support: bool = True,
                 enable_debug: bool = False):
        self.enable_async = enable_async
        self.enable_legacy_support = enable_legacy_support
        self.enable_debug = enable_debug
        
        self.logger = logging.getLogger(self.__class__.__name__)
        if enable_debug:
            self.logger.setLevel(logging.DEBUG)
        
        # 核心组件
        self.data_registry = DataRegistry()
        self.data_contract = TemplateDataContract()
        
        # 生命周期管理器
        self.sync_lifecycle_manager = LifecycleManager()
        if enable_async:
            self.async_lifecycle_manager = AsyncLifecycleManager()
        
        # 模板作用域缓存
        self.template_scopes: Dict[str, Union[TemplateScope, AsyncTemplateScope]] = {}
        
        # 现有模板引擎实例
        self.legacy_engine: Optional[HybridTemplateEngine] = None
        
        # 注册默认适配器
        self._register_default_adapters()
        
        # 初始化现有模板引擎（如果可用）
        if TEMPLATE_ENGINE_AVAILABLE and enable_legacy_support:
            self._initialize_legacy_engine()
        
        self.logger.info(f"企业级模板引擎集成器初始化完成 - 异步: {enable_async}, 兼容: {enable_legacy_support}")
    
    def _register_default_adapters(self):
        """注册默认适配器"""
        try:
            # 注册数据库适配器
            from core.adapters.database.sqlite import SQLiteAdapter
            from core.adapters.database.postgresql import PostgreSQLAdapter
            from core.adapters.database.mysql import MySQLAdapter
            self.data_registry.register_adapter(SQLiteAdapter)
            self.data_registry.register_adapter(PostgreSQLAdapter)
            self.data_registry.register_adapter(MySQLAdapter)
            
            # 注册异步数据库适配器
            if self.enable_async:
                from core.adapters.database.async_sqlite import AsyncSQLiteAdapter
                from core.adapters.database.async_postgresql import AsyncPostgreSQLAdapter
                from core.adapters.database.async_mysql import AsyncMySQLAdapter
                self.data_registry.register_adapter(AsyncSQLiteAdapter)
                self.data_registry.register_adapter(AsyncPostgreSQLAdapter)
                self.data_registry.register_adapter(AsyncMySQLAdapter)
            
            # 注册API适配器
            from core.adapters.api.rest_api import RestAPIAdapter
            self.data_registry.register_adapter(RestAPIAdapter)
            
            # 注册文件适配器
            from core.adapters.file.csv_adapter import CSVAdapter
            from core.adapters.file.json_adapter import JSONAdapter
            from core.adapters.file.html_adapter import HTMLAdapter
            from core.adapters.file.xml_adapter import XMLAdapter
            self.data_registry.register_adapter(CSVAdapter)
            self.data_registry.register_adapter(JSONAdapter)
            self.data_registry.register_adapter(HTMLAdapter)
            self.data_registry.register_adapter(XMLAdapter)
            
            # 注册内存适配器
            from core.adapters.memory.data_list_adapter import DataListAdapter
            self.data_registry.register_adapter(DataListAdapter)
            
            self.logger.info(f"已注册 {len(self.data_registry.get_supported_types())} 种适配器类型")
            
        except ImportError as e:
            self.logger.warning(f"部分适配器注册失败: {e}")
    
    def _initialize_legacy_engine(self):
        """初始化现有模板引擎"""
        try:
            self.legacy_engine = HybridTemplateEngine(
                enable_debug=self.enable_debug,
                auto_discover_plugins=True
            )
            self.logger.info("现有模板引擎初始化成功")
        except Exception as e:
            self.logger.warning(f"现有模板引擎初始化失败: {e}")
            self.legacy_engine = None
    
    def create_template_scope(self, template_id: Optional[str] = None, is_async: bool = False) -> Union[TemplateScope, AsyncTemplateScope]:
        """创建模板作用域"""
        if template_id is None:
            template_id = str(uuid.uuid4())
        
        if is_async and self.enable_async:
            scope = AsyncTemplateScope(
                template_id=template_id,
                data_registry=self.data_registry
            )
        else:
            scope = TemplateScope(
                template_id=template_id,
                data_registry=self.data_registry
            )
        
        self.template_scopes[template_id] = scope
        self.logger.debug(f"创建模板作用域: {template_id} ({'异步' if is_async else '同步'})")
        return scope
    
    def cleanup_template_scope(self, template_id: str):
        """清理模板作用域"""
        if template_id in self.template_scopes:
            scope = self.template_scopes[template_id]
            if hasattr(scope, 'cleanup'):
                if asyncio.iscoroutinefunction(scope.cleanup):
                    # 异步清理
                    asyncio.create_task(scope.cleanup())
                else:
                    # 同步清理
                    scope.cleanup()
            del self.template_scopes[template_id]
            self.logger.debug(f"清理模板作用域: {template_id}")
    
    def register_data_source(self, 
                           scope: Union[TemplateScope, AsyncTemplateScope],
                           name: str, 
                           source: Any) -> Union[DataProxy, AsyncDataProxy]:
        """在作用域中注册数据源"""
        try:
            # 获取适配器
            adapter = self.data_registry.get_adapter(source)
            
            # 创建数据代理
            if isinstance(scope, AsyncTemplateScope):
                proxy = AsyncDataProxy(
                    original_source=source,
                    adapter=adapter,
                    lifecycle_manager=scope.lifecycle_manager
                )
            else:
                proxy = DataProxy(
                    original_source=source,
                    adapter=adapter,
                    lifecycle_manager=scope.lifecycle_manager
                )
            
            # 注册到作用域
            scope.register_data_source(name, proxy)
            
            self.logger.debug(f"注册数据源: {name} -> {type(adapter).__name__}")
            return proxy
            
        except Exception as e:
            self.logger.error(f"注册数据源失败 {name}: {e}")
            # 返回错误结果
            return DataResult.error_result(error=f"数据源注册失败: {str(e)}")
    
    def create_enhanced_smart_loader(self, scope: Union[TemplateScope, AsyncTemplateScope]) -> 'EnhancedSmartDataLoader':
        """创建增强的智能数据加载器"""
        try:
            from core.enhanced_smart_data_loader import EnhancedSmartDataLoader
            return EnhancedSmartDataLoader(
                data_registry=self.data_registry,
                template_scope=scope
            )
        except ImportError:
            # 回退到简单的智能数据加载器
            self.logger.warning("EnhancedSmartDataLoader不可用，使用简单实现")
            return SimpleSmartDataLoader(self.data_registry, scope)

    def render_template_sync(self,
                           template_string: str,
                           context: Dict[str, Any] = None,
                           template_id: Optional[str] = None) -> str:
        """同步模板渲染"""
        if template_id is None:
            template_id = str(uuid.uuid4())

        # 创建模板作用域
        scope = self.create_template_scope(template_id, is_async=False)

        try:
            # 处理上下文数据
            enhanced_context = self._process_template_context(scope, context or {})

            # 添加增强的智能数据加载器
            enhanced_context['sd'] = self.create_enhanced_smart_loader(scope)

            # 使用现有模板引擎渲染（如果可用）
            if self.legacy_engine:
                return self.legacy_engine.render_template(template_string, enhanced_context)
            else:
                # 简单的Jinja2渲染
                from jinja2 import Environment
                env = Environment()
                template = env.from_string(template_string)
                return template.render(enhanced_context)

        finally:
            # 清理作用域
            self.cleanup_template_scope(template_id)

    async def render_template_async(self,
                                  template_string: str,
                                  context: Dict[str, Any] = None,
                                  template_id: Optional[str] = None) -> str:
        """异步模板渲染"""
        if not self.enable_async:
            # 回退到同步渲染
            return self.render_template_sync(template_string, context, template_id)

        if template_id is None:
            template_id = str(uuid.uuid4())

        # 创建异步模板作用域
        scope = self.create_template_scope(template_id, is_async=True)

        try:
            # 处理上下文数据
            enhanced_context = await self._process_template_context_async(scope, context or {})

            # 添加增强的智能数据加载器
            enhanced_context['sd'] = self.create_enhanced_smart_loader(scope)

            # 使用现有模板引擎异步渲染（如果可用）
            if self.legacy_engine and hasattr(self.legacy_engine, 'render_template_async'):
                return await self.legacy_engine.render_template_async(template_string, enhanced_context)
            else:
                # 简单的异步Jinja2渲染
                from jinja2 import Environment
                env = Environment(enable_async=True)
                template = env.from_string(template_string)
                return await template.render_async(enhanced_context)

        finally:
            # 清理作用域
            self.cleanup_template_scope(template_id)

    def _process_template_context(self, scope, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理模板上下文（同步）"""
        enhanced_context = {}

        for name, value in context.items():
            if self._is_data_source(value):
                # 数据源注册为代理对象
                enhanced_context[name] = self.register_data_source(scope, name, value)
            else:
                # 普通数据保持原样，不进行包装
                # 这样可以保持Jinja2模板的自然语法
                enhanced_context[name] = self._enhance_template_data(value)

        return enhanced_context

    async def _process_template_context_async(self, scope, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理模板上下文（异步）"""
        enhanced_context = {}

        for name, value in context.items():
            if self._is_data_source(value):
                # 数据源注册为代理对象
                enhanced_context[name] = self.register_data_source(scope, name, value)
            else:
                # 普通数据保持原样，不进行包装
                # 这样可以保持Jinja2模板的自然语法
                enhanced_context[name] = self._enhance_template_data(value)

        return enhanced_context


class SimpleSmartDataLoader:
    """简单的智能数据加载器（回退实现）"""

    def __init__(self, data_registry, template_scope):
        self.data_registry = data_registry
        self.template_scope = template_scope
        self.logger = logging.getLogger(self.__class__.__name__)

    def database(self, connection_string: str):
        """数据库连接器"""
        try:
            # 直接返回数据库代理，不通过DataProxy包装
            adapter = self.data_registry.get_adapter(connection_string)
            if adapter:
                connection = adapter.create_connection(connection_string)
                return SimpleDatabaseProxy(connection, adapter)
            else:
                self.logger.warning(f"无法找到数据库适配器: {connection_string}")
                return MockDatabaseProxy(connection_string)
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return MockDatabaseProxy(connection_string)

    def api(self, url_or_config):
        """API连接器"""
        try:
            if isinstance(url_or_config, str):
                config = {'base_url': url_or_config}
            else:
                config = url_or_config

            adapter = self.data_registry.get_adapter(config)
            if adapter:
                connection = adapter.create_connection(config)
                return SimpleAPIProxy(connection, adapter)
            else:
                self.logger.warning(f"无法找到API适配器: {config}")
                return MockAPIProxy(config)
        except Exception as e:
            self.logger.error(f"API连接失败: {e}")
            return MockAPIProxy(url_or_config)

    def file(self, file_path: str):
        """文件连接器"""
        try:
            # 直接返回文件代理，不通过DataProxy包装
            adapter = self.data_registry.get_adapter(file_path)
            if adapter:
                connection = adapter.create_connection(file_path)
                return SimpleFileProxy(connection, adapter, file_path)
            else:
                self.logger.warning(f"无法找到文件适配器: {file_path}")
                return MockFileProxy(file_path)
        except Exception as e:
            self.logger.error(f"文件处理失败: {e}")
            return MockFileProxy(file_path)

    def memory(self, data):
        """内存数据处理器"""
        return SimpleMemoryProxy(data)

    def remote(self, host_config):
        """远程主机连接器"""
        return MockRemoteProxy(host_config)


class SimpleDatabaseProxy:
    """简单数据库代理"""
    def __init__(self, connection, adapter):
        self.connection = connection
        self.adapter = adapter

    def query(self, sql: str, params: tuple = None):
        operations = self.adapter.get_operations()
        if 'query' in operations:
            return operations['query'](self.connection, sql, params or ())
        return []

    def execute(self, sql: str, params: tuple = None):
        operations = self.adapter.get_operations()
        if 'execute' in operations:
            return operations['execute'](self.connection, sql, params or ())
        return None


class SimpleAPIProxy:
    """简单API代理"""
    def __init__(self, connection, adapter):
        self.connection = connection
        self.adapter = adapter

    def get(self, path: str = ''):
        return {'method': 'GET', 'path': path, 'status': 'success'}

    def post(self, path: str = '', data=None):
        return {'method': 'POST', 'path': path, 'data': data, 'status': 'success'}


class SimpleFileProxy:
    """简单文件代理"""
    def __init__(self, connection, adapter, file_path):
        # 如果connection是DataProxy，获取实际路径
        if hasattr(connection, 'get_data'):
            try:
                self.file_path = connection.get_data()
            except:
                self.file_path = file_path
        else:
            self.file_path = connection if isinstance(connection, str) else file_path

        self.adapter = adapter
        self.original_connection = connection

    def read(self):
        try:
            operations = self.adapter.get_operations()
            if 'read' in operations:
                return operations['read'](self.original_connection)
        except:
            pass

        # 直接文件读取
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            return f"文件读取失败: {e}"

    def parse(self):
        try:
            operations = self.adapter.get_operations()
            if 'parse' in operations:
                return operations['parse'](self.original_connection)
        except:
            pass

        # 直接文件解析
        try:
            if self.file_path.endswith('.json'):
                import json
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            elif self.file_path.endswith('.csv'):
                import csv
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    return list(csv.DictReader(f))
            else:
                return self.read()
        except Exception as e:
            return {'error': f'文件解析失败: {e}'}


class SimpleMemoryProxy:
    """简单内存数据代理"""
    def __init__(self, data):
        self.data = data

    def filter(self, criteria: dict):
        if isinstance(self.data, list):
            return [item for item in self.data
                   if all(item.get(k) == v for k, v in criteria.items())]
        return self.data

    def sort(self, key: str, reverse: bool = False):
        if isinstance(self.data, list):
            return sorted(self.data, key=lambda x: x.get(key, ''), reverse=reverse)
        return self.data


# Mock代理类（当适配器不可用时使用）
class MockDatabaseProxy:
    def __init__(self, connection_string):
        self.connection_string = connection_string
    def query(self, sql: str, params: tuple = None):
        return []
    def execute(self, sql: str, params: tuple = None):
        return None

class MockAPIProxy:
    def __init__(self, config):
        self.config = config
    def get(self, path: str = ''):
        return {'method': 'GET', 'path': path, 'status': 'mock'}
    def post(self, path: str = '', data=None):
        return {'method': 'POST', 'path': path, 'status': 'mock'}

class MockFileProxy:
    def __init__(self, file_path):
        self.file_path = file_path
    def read(self):
        return ""
    def parse(self):
        return {}

class MockRemoteProxy:
    def __init__(self, host_config):
        self.host_config = host_config
    def execute(self, command: str):
        return {'command': command, 'status': 'mock'}
    
    async def render_template_async(self, 
                                  template_string: str, 
                                  context: Dict[str, Any] = None,
                                  template_id: Optional[str] = None) -> str:
        """异步模板渲染"""
        if not self.enable_async:
            # 回退到同步渲染
            return self.render_template_sync(template_string, context, template_id)
        
        if template_id is None:
            template_id = str(uuid.uuid4())
        
        # 创建异步模板作用域
        scope = self.create_template_scope(template_id, is_async=True)
        
        try:
            # 处理上下文数据
            enhanced_context = await self._process_template_context_async(scope, context or {})
            
            # 添加增强的智能数据加载器
            enhanced_context['sd'] = self.create_enhanced_smart_loader(scope)
            
            # 使用现有模板引擎异步渲染（如果可用）
            if self.legacy_engine and hasattr(self.legacy_engine, 'render_template_async'):
                return await self.legacy_engine.render_template_async(template_string, enhanced_context)
            else:
                # 简单的异步Jinja2渲染
                from jinja2 import Environment
                env = Environment(enable_async=True)
                template = env.from_string(template_string)
                return await template.render_async(enhanced_context)
                
        finally:
            # 异步清理作用域
            await self._cleanup_template_scope_async(template_id)
    
    def _process_template_context(self, scope: TemplateScope, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理模板上下文（同步）"""
        enhanced_context = {}

        for name, value in context.items():
            if self._is_data_source(value):
                # 数据源注册为代理对象
                enhanced_context[name] = self.register_data_source(scope, name, value)
            else:
                # 普通数据保持原样，不进行包装
                # 这样可以保持Jinja2模板的自然语法
                enhanced_context[name] = self._enhance_template_data(value)

        return enhanced_context
    
    async def _process_template_context_async(self, scope: AsyncTemplateScope, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理模板上下文（异步）"""
        enhanced_context = {}

        for name, value in context.items():
            if self._is_data_source(value):
                # 数据源注册为代理对象
                enhanced_context[name] = self.register_data_source(scope, name, value)
            else:
                # 普通数据保持原样，不进行包装
                # 这样可以保持Jinja2模板的自然语法
                enhanced_context[name] = self._enhance_template_data(value)
        
        return enhanced_context
    
    async def _cleanup_template_scope_async(self, template_id: str):
        """异步清理模板作用域"""
        if template_id in self.template_scopes:
            scope = self.template_scopes[template_id]
            if hasattr(scope, 'cleanup'):
                await scope.cleanup()
            del self.template_scopes[template_id]
            self.logger.debug(f"异步清理模板作用域: {template_id}")
    
    def _is_data_source(self, value: Any) -> bool:
        """
        智能检查是否为数据源

        注意：这里要更保守，避免将普通数据误判为数据源
        """
        # 基本类型通常不是数据源
        if isinstance(value, (str, int, float, bool, type(None))):
            if isinstance(value, str):
                # 只有明确的连接字符串才是数据源
                if any(value.startswith(pattern) for pattern in [
                    'sqlite://', 'postgresql://', 'mysql://', 'mongodb://',
                    'redis://', 'http://', 'https://'
                ]):
                    return True

                # 只有明确的文件路径才是数据源
                if any(value.endswith(ext) for ext in [
                    '.csv', '.json', '.xml', '.html', '.txt', '.xlsx', '.sql'
                ]) and ('/' in value or '\\' in value or value.startswith('.')):
                    return True

            return False

        # 容器类型不是数据源
        if isinstance(value, (list, tuple, set)):
            return False

        # 字典只有包含明确数据源配置才是数据源
        if isinstance(value, dict):
            data_source_keys = {
                'connection_string', 'database_url', 'api_url', 'base_url',
                'file_path', 'host', 'port', 'endpoint'
            }

            # 必须有明确的数据源标识
            if any(key in value for key in data_source_keys):
                return True

            return False

        # 其他类型默认不是数据源
        return False

    def _enhance_template_data(self, value: Any) -> Any:
        """
        增强模板数据

        使用统一数据增强系统，提供XPath/JSONPath支持和可编辑功能
        """
        # 如果是DataProxy，直接返回原始数据以支持模板迭代
        if hasattr(value, '__class__') and 'DataProxy' in str(value.__class__):
            try:
                # 尝试获取DataProxy中的实际数据
                if hasattr(value, 'get_data'):
                    actual_data = value.get_data()
                    return self._enhance_template_data(actual_data)
                elif hasattr(value, 'source'):
                    return self._enhance_template_data(value.source)
            except:
                pass
            # 如果无法获取实际数据，返回DataProxy本身
            return value

        # 导入统一数据增强器
        try:
            from core.unified_data_enhancement import enhance_data, is_enhanced_data

            # 如果已经是增强数据，直接返回
            if is_enhanced_data(value):
                return value

            # 使用统一增强器处理数据
            return enhance_data(value)

        except ImportError:
            # 如果统一增强器不可用，使用原有逻辑
            return self._legacy_enhance_template_data(value)

    def _legacy_enhance_template_data(self, value: Any) -> Any:
        """
        传统数据增强方法（备用）
        """
        # 基本类型保持原样
        if isinstance(value, (str, int, float, bool, type(None))):
            return value

        # 简单容器类型保持原样
        if isinstance(value, (list, tuple, set)):
            return value

        # 简单字典保持原样
        if isinstance(value, dict):
            # 检查是否是复杂的嵌套结构，如果是可以考虑应用智能处理
            if self._is_complex_nested_data(value):
                # 可以选择性地应用现有的智能数据处理
                try:
                    from .components.smart_data_factory import SmartDataFactory
                    factory = SmartDataFactory(enable_debug=False)
                    smart_modifier = factory.create_modifier(value)
                    if smart_modifier:
                        return smart_modifier
                except:
                    pass

            return value

        # 其他类型保持原样
        return value

    def _is_complex_nested_data(self, data: dict) -> bool:
        """检查是否是复杂的嵌套数据结构"""
        if not isinstance(data, dict):
            return False

        # 检查嵌套深度和复杂性
        def check_depth(obj, current_depth=0, max_depth=3):
            if current_depth > max_depth:
                return True

            if isinstance(obj, dict):
                if len(obj) > 10:  # 大字典
                    return True
                for value in obj.values():
                    if check_depth(value, current_depth + 1, max_depth):
                        return True
            elif isinstance(obj, list):
                if len(obj) > 50:  # 大数组
                    return True
                for item in obj[:5]:  # 只检查前5个元素
                    if check_depth(item, current_depth + 1, max_depth):
                        return True

            return False

        return check_depth(data)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = {
            'registered_adapters': len(self.data_registry.get_supported_types()),
            'active_scopes': len(self.template_scopes),
            'async_enabled': self.enable_async,
            'legacy_support': self.enable_legacy_support and self.legacy_engine is not None
        }
        
        # 添加生命周期管理器统计
        if hasattr(self.sync_lifecycle_manager, 'get_stats'):
            stats['sync_lifecycle'] = self.sync_lifecycle_manager.get_stats()
        
        if self.enable_async and hasattr(self.async_lifecycle_manager, 'get_stats'):
            stats['async_lifecycle'] = self.async_lifecycle_manager.get_stats()
        
        return stats


class EnhancedSmartDataLoader:
    """
    增强的智能数据加载器

    集成新的统一适配器系统与现有的SmartDataLoader
    提供向后兼容的API，同时支持新的企业级功能
    """

    def __init__(self,
                 integration: EnterpriseTemplateIntegration,
                 scope: Union[TemplateScope, AsyncTemplateScope],
                 legacy_loader: Optional[SmartDataLoader] = None):
        self.integration = integration
        self.scope = scope
        self.legacy_loader = legacy_loader
        self.logger = logging.getLogger(self.__class__.__name__)

    def database(self, connection_source: Any) -> Union[DataProxy, AsyncDataProxy]:
        """数据库连接 - 使用新的统一适配器"""
        try:
            # 使用新的适配器系统
            adapter = self.integration.data_registry.get_adapter(connection_source)

            if isinstance(self.scope, AsyncTemplateScope):
                proxy = AsyncDataProxy(
                    original_source=connection_source,
                    adapter=adapter,
                    lifecycle_manager=self.scope.lifecycle_manager
                )
            else:
                proxy = DataProxy(
                    original_source=connection_source,
                    adapter=adapter,
                    lifecycle_manager=self.scope.lifecycle_manager
                )

            # 注册到作用域
            db_name = f"db_{id(connection_source)}"
            self.scope.register_data_source(db_name, proxy)

            self.logger.debug(f"数据库连接创建: {type(adapter).__name__}")
            return proxy

        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return DataResult.error_result(error=f"数据库连接失败: {str(e)}")

    def api(self, url_or_config: Any, **options) -> Union[DataProxy, AsyncDataProxy]:
        """API连接 - 使用新的统一适配器"""
        try:
            # 构建API配置
            if isinstance(url_or_config, str):
                api_config = {'base_url': url_or_config, **options}
            else:
                api_config = url_or_config

            # 使用新的适配器系统
            adapter = self.integration.data_registry.get_adapter(api_config)

            if isinstance(self.scope, AsyncTemplateScope):
                proxy = AsyncDataProxy(
                    original_source=api_config,
                    adapter=adapter,
                    lifecycle_manager=self.scope.lifecycle_manager
                )
            else:
                proxy = DataProxy(
                    original_source=api_config,
                    adapter=adapter,
                    lifecycle_manager=self.scope.lifecycle_manager
                )

            # 注册到作用域
            api_name = f"api_{id(api_config)}"
            self.scope.register_data_source(api_name, proxy)

            self.logger.debug(f"API连接创建: {type(adapter).__name__}")
            return proxy

        except Exception as e:
            self.logger.error(f"API连接失败: {e}")
            return DataResult.error_result(error=f"API连接失败: {str(e)}")

    def file(self, file_path_or_config: Any, **options) -> Union[DataProxy, AsyncDataProxy]:
        """文件连接 - 使用新的统一适配器"""
        try:
            # 构建文件配置
            if isinstance(file_path_or_config, str):
                file_config = {'file_path': file_path_or_config, **options}
            else:
                file_config = file_path_or_config

            # 使用新的适配器系统
            adapter = self.integration.data_registry.get_adapter(file_config)

            if isinstance(self.scope, AsyncTemplateScope):
                proxy = AsyncDataProxy(
                    original_source=file_config,
                    adapter=adapter,
                    lifecycle_manager=self.scope.lifecycle_manager
                )
            else:
                proxy = DataProxy(
                    original_source=file_config,
                    adapter=adapter,
                    lifecycle_manager=self.scope.lifecycle_manager
                )

            # 注册到作用域
            file_name = f"file_{id(file_config)}"
            self.scope.register_data_source(file_name, proxy)

            self.logger.debug(f"文件连接创建: {type(adapter).__name__}")
            return proxy

        except Exception as e:
            self.logger.error(f"文件连接失败: {e}")
            return DataResult.error_result(error=f"文件连接失败: {str(e)}")

    def memory(self, data: Any) -> Union[DataProxy, AsyncDataProxy]:
        """内存数据 - 使用新的统一适配器"""
        try:
            # 使用新的适配器系统
            adapter = self.integration.data_registry.get_adapter(data)

            if isinstance(self.scope, AsyncTemplateScope):
                proxy = AsyncDataProxy(
                    original_source=data,
                    adapter=adapter,
                    lifecycle_manager=self.scope.lifecycle_manager
                )
            else:
                proxy = DataProxy(
                    original_source=data,
                    adapter=adapter,
                    lifecycle_manager=self.scope.lifecycle_manager
                )

            # 注册到作用域
            memory_name = f"memory_{id(data)}"
            self.scope.register_data_source(memory_name, proxy)

            self.logger.debug(f"内存数据创建: {type(adapter).__name__}")
            return proxy

        except Exception as e:
            self.logger.error(f"内存数据处理失败: {e}")
            return DataResult.error_result(error=f"内存数据处理失败: {str(e)}")

    def loader(self, data_source: Any) -> Union[DataProxy, AsyncDataProxy]:
        """通用数据加载器 - 智能适配器选择"""
        try:
            # 使用新的适配器系统进行智能选择
            adapter = self.integration.data_registry.get_adapter(data_source)

            if isinstance(self.scope, AsyncTemplateScope):
                proxy = AsyncDataProxy(
                    original_source=data_source,
                    adapter=adapter,
                    lifecycle_manager=self.scope.lifecycle_manager
                )
            else:
                proxy = DataProxy(
                    original_source=data_source,
                    adapter=adapter,
                    lifecycle_manager=self.scope.lifecycle_manager
                )

            # 注册到作用域
            loader_name = f"loader_{id(data_source)}"
            self.scope.register_data_source(loader_name, proxy)

            self.logger.debug(f"通用加载器创建: {type(adapter).__name__}")
            return proxy

        except Exception as e:
            self.logger.error(f"通用加载失败: {e}")
            return DataResult.error_result(error=f"通用加载失败: {str(e)}")

    def __getattr__(self, name: str):
        """向后兼容 - 委托给现有的SmartDataLoader"""
        if self.legacy_loader and hasattr(self.legacy_loader, name):
            legacy_method = getattr(self.legacy_loader, name)

            # 包装返回结果以确保数据契约
            def wrapped_method(*args, **kwargs):
                try:
                    result = legacy_method(*args, **kwargs)
                    return self.integration.data_contract.ensure_data_result(result)
                except Exception as e:
                    self.logger.warning(f"Legacy方法 {name} 调用失败: {e}")
                    return DataResult.error_result(error=f"Legacy方法调用失败: {str(e)}")

            return wrapped_method
        else:
            raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
