# HTTP插件

## 功能描述
提供透明的HTTP/REST API访问能力，用户可以像使用原生httpx一样自然地使用。插件在后台透明处理智能适配、缓存等功能。

## 支持的格式/类型
- **URL字符串**: 直接的HTTP URL
- **HTTP配置字典**: 包含url、method、headers等的配置对象
- **REST配置**: 标准的REST API配置

## 使用示例

### 模板中使用
```jinja2
{# 简单GET请求 #}
{% set response = sd.http('https://api.example.com/data') %}
{{ response.data.json }}

{# 复杂POST请求 #}
{% set response = sd.http({
    'url': 'https://api.example.com/users',
    'method': 'POST',
    'json': {'name': 'John', 'email': '<EMAIL>'},
    'headers': {'Authorization': 'Bearer token123'}
}) %}

{# 错误处理 #}
{% if response.success %}
    成功: {{ response.data.json }}
{% else %}
    错误: {{ response.error }}
{% endif %}
```

### Python中使用
```python
from plugins.http.smart_loader import global_loader

# 简单请求
result = global_loader.load('https://api.example.com/data')

# 复杂请求
result = global_loader.load({
    'url': 'https://api.example.com/users',
    'method': 'POST',
    'json': {'name': 'John'},
    'headers': {'Authorization': 'Bearer token123'}
})

# 异步请求
result = await global_loader.load_async('https://api.example.com/data')

# 批量请求
results = global_loader.load_batch([
    'https://api.example.com/users/1',
    'https://api.example.com/users/2'
])
```

## 配置选项
| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| timeout | float | 30.0 | 请求超时时间（秒） |
| follow_redirects | bool | true | 是否跟随重定向 |
| verify_ssl | bool | true | 是否验证SSL证书 |
| cache | bool | true | 是否启用缓存 |
| enable_debug | bool | false | 是否启用调试模式 |

## HTTP方法支持
- ✅ GET - 获取数据
- ✅ POST - 创建数据
- ✅ PUT - 更新数据
- ✅ DELETE - 删除数据
- ✅ PATCH - 部分更新
- ✅ HEAD - 获取头部信息
- ✅ OPTIONS - 获取选项信息

## 认证支持
```jinja2
{# Basic认证 #}
{% set response = sd.http({
    'url': 'https://api.example.com/protected',
    'auth': {
        'type': 'basic',
        'username': 'user',
        'password': 'pass'
    }
}) %}

{# Bearer Token #}
{% set response = sd.http({
    'url': 'https://api.example.com/protected',
    'headers': {'Authorization': 'Bearer your-token'}
}) %}

{# API Key #}
{% set response = sd.http({
    'url': 'https://api.example.com/protected',
    'headers': {'X-API-Key': 'your-api-key'}
}) %}
```

## 响应对象结构
```python
{
    'success': bool,           # 请求是否成功
    'status_code': int,        # HTTP状态码
    'headers': dict,           # 响应头
    'text': str,              # 响应文本
    'content': bytes,         # 响应内容
    'json': dict,             # JSON数据（如果有）
    'url': str,               # 最终URL
    'request': dict,          # 请求配置
    'error': str              # 错误信息（如果有）
}
```

## 性能特性
- ✅ **异步支持**: 原生异步处理，高并发性能
- ✅ **智能缓存**: 自动缓存成功响应，提升性能
- ✅ **批量处理**: 支持批量HTTP请求
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **透明集成**: 与模板引擎无缝集成

## 错误处理
```jinja2
{% set response = sd.http('https://api.example.com/data') %}
{% if response.success %}
    {# 处理成功响应 #}
    数据: {{ response.data.json }}
{% else %}
    {# 处理错误 #}
    错误代码: {{ response.data.status_code }}
    错误信息: {{ response.error }}
{% endif %}
```

## 缓存管理
```python
from plugins.http.smart_loader import global_loader

# 获取缓存信息
cache_info = global_loader.get_cache_info()
print(f"缓存大小: {cache_info['cache_size']}")

# 清理缓存
global_loader.clear_cache()

# 禁用缓存
result = global_loader.load('https://api.example.com/data', {'cache': False})
```

## 版本历史
- **v2.0.0**: 标准化重构，遵循插件标准规范
  - 简化架构，提升性能
  - 完善错误处理
  - 增强模板集成
  - 添加智能缓存
- **v1.0.0**: 初始版本

## 插件标准符合性
- ✅ **插件定义**: 完整的PLUGIN_DEFINITIONS
- ✅ **主处理器**: 标准的HttpProcessor
- ✅ **智能加载器**: SmartHttpLoader
- ✅ **异步支持**: 完整的异步/同步双模式
- ✅ **模板集成**: 标准的SmartDataLoader集成
- ✅ **测试覆盖**: 完整的测试套件
- ✅ **文档质量**: 详细的使用文档

**符合性等级: A级 (企业级质量)** 🏆
