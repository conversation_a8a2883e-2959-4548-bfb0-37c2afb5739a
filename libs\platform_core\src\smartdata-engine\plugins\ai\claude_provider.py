"""
Claude服务提供者

实现Anthropic Claude API的集成，支持Claude模型的各种AI服务
"""

import asyncio
import logging
from typing import Any, Dict, Optional, List, Union

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False
    anthropic = None

from core.smart_data_object import SmartDataObject
from .ai_factory import IAIServiceProvider, AIServiceType


class ClaudeProvider(IAIServiceProvider):
    """Claude服务提供者"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.api_key = api_key
        
        if not ANTHROPIC_AVAILABLE:
            self.logger.warning("Anthropic库未安装，请运行: pip install anthropic")
            self.client = None
        else:
            try:
                self.client = anthropic.Anthropic(api_key=self.api_key)
            except Exception as e:
                self.logger.error(f"Claude客户端初始化失败: {e}")
                self.client = None
    
    def get_provider_name(self) -> str:
        """获取提供者名称"""
        return "claude"
    
    def can_handle(self, service_type: str, model: str = None) -> bool:
        """检查是否可以处理指定服务"""
        if not ANTHROPIC_AVAILABLE or not self.client:
            return False
        
        # 支持的服务类型
        supported_services = {
            'text_generation', 'conversation', 'text_analysis',
            'summarization', 'translation', 'classification'
        }
        
        if service_type not in supported_services:
            return False
        
        # 检查模型支持
        if model:
            claude_models = {
                'claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku',
                'claude-2.1', 'claude-2.0', 'claude-instant'
            }
            return any(model.startswith(m) for m in claude_models)
        
        return True
    
    async def process(self, service_type: str, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理Claude请求"""
        if not self.client:
            return SmartDataObject({
                'error': 'Claude客户端未初始化',
                'success': False,
                'provider': 'claude'
            })
        
        try:
            options = options or {}
            model = options.get('model', 'claude-3-sonnet-20240229')
            
            if service_type == 'text_generation':
                return await self._handle_text_generation(data, model, options)
            elif service_type == 'conversation':
                return await self._handle_conversation(data, model, options)
            elif service_type in ['text_analysis', 'summarization', 'translation', 'classification']:
                return await self._handle_text_processing(service_type, data, model, options)
            else:
                return SmartDataObject({
                    'error': f'不支持的服务类型: {service_type}',
                    'success': False,
                    'provider': 'claude'
                })
                
        except Exception as e:
            self.logger.error(f"Claude请求处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'success': False,
                'provider': 'claude'
            })
    
    async def _handle_text_generation(self, prompt: str, model: str, options: Dict) -> SmartDataObject:
        """处理文本生成"""
        try:
            response = await asyncio.to_thread(
                self.client.messages.create,
                model=model,
                max_tokens=options.get('max_tokens', 1000),
                temperature=options.get('temperature', 0.7),
                messages=[{"role": "user", "content": str(prompt)}]
            )
            
            generated_text = response.content[0].text
            
            return SmartDataObject({
                'success': True,
                'provider': 'claude',
                'service_type': 'text_generation',
                'model': model,
                'generated_text': generated_text,
                'usage': {
                    'input_tokens': response.usage.input_tokens,
                    'output_tokens': response.usage.output_tokens,
                    'total_tokens': response.usage.input_tokens + response.usage.output_tokens
                }
            })
            
        except Exception as e:
            raise Exception(f"文本生成失败: {e}")
    
    async def _handle_conversation(self, messages: List[Dict], model: str, options: Dict) -> SmartDataObject:
        """处理对话"""
        try:
            # 确保消息格式正确
            if isinstance(messages, str):
                messages = [{"role": "user", "content": messages}]
            elif isinstance(messages, list) and len(messages) > 0:
                # 验证消息格式并转换为Claude格式
                claude_messages = []
                for msg in messages:
                    if not isinstance(msg, dict) or 'role' not in msg or 'content' not in msg:
                        raise ValueError("消息格式不正确，需要包含'role'和'content'字段")
                    
                    # Claude API要求role为user或assistant
                    role = msg['role']
                    if role == 'system':
                        # 将system消息转换为user消息
                        role = 'user'
                    
                    claude_messages.append({
                        "role": role,
                        "content": msg['content']
                    })
            else:
                raise ValueError("消息不能为空")
            
            response = await asyncio.to_thread(
                self.client.messages.create,
                model=model,
                max_tokens=options.get('max_tokens', 1000),
                temperature=options.get('temperature', 0.7),
                messages=claude_messages
            )
            
            assistant_content = response.content[0].text
            
            return SmartDataObject({
                'success': True,
                'provider': 'claude',
                'service_type': 'conversation',
                'model': model,
                'response': {
                    'role': 'assistant',
                    'content': assistant_content
                },
                'conversation_history': messages + [{
                    'role': 'assistant',
                    'content': assistant_content
                }],
                'usage': {
                    'input_tokens': response.usage.input_tokens,
                    'output_tokens': response.usage.output_tokens,
                    'total_tokens': response.usage.input_tokens + response.usage.output_tokens
                }
            })
            
        except Exception as e:
            raise Exception(f"对话处理失败: {e}")
    
    async def _handle_text_processing(self, service_type: str, text: str, model: str, options: Dict) -> SmartDataObject:
        """处理文本分析类任务"""
        try:
            # 根据服务类型构建提示
            prompts = {
                'text_analysis': f"请详细分析以下文本的内容、主题、情感倾向和关键信息：\n\n{text}",
                'summarization': f"请为以下文本生成准确、简洁的摘要：\n\n{text}",
                'translation': f"请将以下文本翻译成{options.get('target_language', '中文')}，保持原意和语调：\n\n{text}",
                'classification': f"请对以下文本进行分类，并详细说明分类依据和置信度：\n\n{text}"
            }
            
            prompt = prompts.get(service_type, f"请处理以下文本：\n\n{text}")
            
            response = await asyncio.to_thread(
                self.client.messages.create,
                model=model,
                max_tokens=options.get('max_tokens', 1000),
                temperature=options.get('temperature', 0.3),
                messages=[{"role": "user", "content": prompt}]
            )
            
            result_text = response.content[0].text
            
            return SmartDataObject({
                'success': True,
                'provider': 'claude',
                'service_type': service_type,
                'model': model,
                'result': result_text,
                'original_text': text,
                'usage': {
                    'input_tokens': response.usage.input_tokens,
                    'output_tokens': response.usage.output_tokens,
                    'total_tokens': response.usage.input_tokens + response.usage.output_tokens
                }
            })
            
        except Exception as e:
            raise Exception(f"{service_type}处理失败: {e}")
    
    def get_supported_models(self) -> List[str]:
        """获取支持的模型列表 - 标准接口"""
        return [
            'claude-3-opus-20240229',
            'claude-3-sonnet-20240229',
            'claude-3-haiku-20240307',
            'claude-2.1',
            'claude-2.0',
            'claude-instant-1.2'
        ]
    
    def get_supported_services(self) -> List[str]:
        """获取支持的服务类型"""
        return [
            'text_generation', 'conversation', 'text_analysis',
            'summarization', 'translation', 'classification'
        ]
