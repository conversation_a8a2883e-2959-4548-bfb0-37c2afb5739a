#!/usr/bin/env python3
"""
SmartData模板引擎流数据处理插件使用示例 (修复版)

展示流数据处理的概念和模拟实现：
1. 模拟实时数据流处理
2. 数据转换和聚合
3. 事件检测和处理
4. 模板引擎集成
"""

import sys
import os
import time
import json
import random
from datetime import datetime, timedelta
from typing import Dict, Any, List, Generator
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.template_ext import create_template_engine

class MockStreamProcessor:
    """模拟流数据处理器"""
    
    def __init__(self):
        self.events = []
        self.metrics = {
            'processed_events': 0,
            'error_count': 0,
            'avg_processing_time': 0
        }
    
    def generate_mock_events(self, count: int = 10) -> List[Dict[str, Any]]:
        """生成模拟事件数据"""
        events = []
        for i in range(count):
            event = {
                'id': f'event_{i}_{int(time.time())}',
                'timestamp': datetime.now().isoformat(),
                'user_id': random.randint(1000, 9999),
                'event_type': random.choice(['login', 'purchase', 'view', 'click']),
                'value': random.uniform(10.0, 1000.0),
                'metadata': {
                    'source': 'web',
                    'session_id': f'session_{random.randint(100, 999)}'
                }
            }
            events.append(event)
        return events
    
    def process_events(self, events: List[Dict[str, Any]]) -> Dict[str, Any]:
        """处理事件数据"""
        start_time = time.time()
        
        try:
            # 统计分析
            total_events = len(events)
            event_types = {}
            total_value = 0
            
            for event in events:
                event_type = event.get('event_type', 'unknown')
                event_types[event_type] = event_types.get(event_type, 0) + 1
                total_value += event.get('value', 0)
            
            # 计算指标
            avg_value = total_value / total_events if total_events > 0 else 0
            processing_time = (time.time() - start_time) * 1000  # ms
            
            # 更新指标
            self.metrics['processed_events'] += total_events
            self.metrics['avg_processing_time'] = processing_time
            
            return {
                'success': True,
                'total_events': total_events,
                'event_types': event_types,
                'total_value': total_value,
                'avg_value': avg_value,
                'processing_time': processing_time,
                'metrics': self.metrics.copy()
            }
            
        except Exception as e:
            self.metrics['error_count'] += 1
            return {
                'success': False,
                'error': str(e),
                'metrics': self.metrics.copy()
            }

def stream_data_processing_examples():
    """流数据处理完整示例"""
    print("=== SmartData模板引擎流数据处理插件示例 (修复版) ===")
    
    # 创建模板引擎和模拟处理器
    engine = create_template_engine()
    stream_processor = MockStreamProcessor()
    
    # 1. 实时数据流处理示例
    print("\n🌊 1. 实时数据流处理示例")
    
    # 生成模拟数据
    mock_events = stream_processor.generate_mock_events(20)
    processing_result = stream_processor.process_events(mock_events)
    
    stream_template = """
实时数据流处理结果:
==================

📊 处理统计:
{%- if result.success -%}
- 总事件数: {{ result.total_events }}
- 处理时间: {{ result.processing_time | round(2) }}ms
- 平均事件价值: {{ result.avg_value | round(2) }}
- 总价值: {{ result.total_value | round(2) }}

📈 事件类型分布:
{%- for event_type, count in result.event_types.items() %}
- {{ event_type }}: {{ count }} 次 ({{ (count / result.total_events * 100) | round(1) }}%)
{%- endfor %}

⚡ 系统指标:
- 累计处理事件: {{ result.metrics.processed_events }}
- 错误计数: {{ result.metrics.error_count }}
- 平均处理时间: {{ result.metrics.avg_processing_time | round(2) }}ms
{%- else -%}
❌ 处理失败: {{ result.error }}
{%- endif -%}
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(stream_template, result=processing_result)
    print(result)
    
    # 2. 窗口分析示例
    print("\n📊 2. 窗口分析示例")
    
    # 模拟时间窗口数据
    window_data = []
    for i in range(5):
        window_events = stream_processor.generate_mock_events(10)
        window_result = stream_processor.process_events(window_events)
        window_data.append({
            'window_id': i + 1,
            'timestamp': datetime.now() - timedelta(minutes=i),
            'result': window_result
        })
    
    window_template = """
时间窗口分析:
============

{%- for window in windows %}
🕐 窗口 {{ window.window_id }} ({{ window.timestamp.strftime('%H:%M:%S') }}):
{%- if window.result.success -%}
- 事件数: {{ window.result.total_events }}
- 总价值: {{ window.result.total_value | round(2) }}
- 处理时间: {{ window.result.processing_time | round(2) }}ms
{%- else -%}
- ❌ 处理失败
{%- endif -%}
{%- endfor %}

📈 趋势分析:
- 总窗口数: {{ windows | length }}
- 平均事件数: {{ (windows | map(attribute='result.total_events') | sum / windows | length) | round(1) }}
- 平均价值: {{ (windows | map(attribute='result.total_value') | sum / windows | length) | round(2) }}

💡 窗口分析优势:
- 实时趋势监控
- 异常检测
- 性能分析
- 容量规划
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(window_template, windows=window_data)
    print(result)
    
    # 3. 事件检测示例
    print("\n🚨 3. 事件检测示例")
    
    # 模拟异常事件检测
    def detect_anomalies(events: List[Dict[str, Any]]) -> Dict[str, Any]:
        anomalies = []
        for event in events:
            # 检测高价值事件
            if event.get('value', 0) > 800:
                anomalies.append({
                    'type': 'high_value',
                    'event_id': event['id'],
                    'value': event['value'],
                    'threshold': 800
                })
            
            # 检测频繁登录
            if event.get('event_type') == 'login':
                anomalies.append({
                    'type': 'frequent_login',
                    'event_id': event['id'],
                    'user_id': event['user_id']
                })
        
        return {
            'total_anomalies': len(anomalies),
            'anomalies': anomalies,
            'detection_time': time.time() * 1000
        }
    
    anomaly_events = stream_processor.generate_mock_events(15)
    anomaly_result = detect_anomalies(anomaly_events)
    
    anomaly_template = """
事件异常检测:
============

🔍 检测结果:
- 检测事件数: {{ events | length }}
- 发现异常数: {{ result.total_anomalies }}
- 检测时间: {{ result.detection_time | round(2) }}ms

🚨 异常详情:
{%- for anomaly in result.anomalies %}
- {{ anomaly.type }}: 事件{{ anomaly.event_id }}
  {%- if anomaly.value -%}
  (价值: {{ anomaly.value | round(2) }}, 阈值: {{ anomaly.threshold }})
  {%- endif -%}
  {%- if anomaly.user_id -%}
  (用户: {{ anomaly.user_id }})
  {%- endif -%}
{%- endfor %}

📊 异常统计:
{%- set anomaly_types = result.anomalies | groupby('type') -%}
{%- for type, group in anomaly_types %}
- {{ type }}: {{ group | list | length }} 次
{%- endfor %}

💡 检测规则:
- 高价值事件: 价值 > 800
- 频繁登录: 登录事件检测
- 可扩展: 支持自定义规则
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(anomaly_template, events=anomaly_events, result=anomaly_result)
    print(result)
    
    # 4. 流式聚合示例
    print("\n📈 4. 流式聚合示例")
    
    # 模拟聚合计算
    def calculate_aggregations(events: List[Dict[str, Any]]) -> Dict[str, Any]:
        # 按用户聚合
        user_stats = {}
        for event in events:
            user_id = event.get('user_id')
            if user_id not in user_stats:
                user_stats[user_id] = {
                    'event_count': 0,
                    'total_value': 0,
                    'event_types': set()
                }
            
            user_stats[user_id]['event_count'] += 1
            user_stats[user_id]['total_value'] += event.get('value', 0)
            user_stats[user_id]['event_types'].add(event.get('event_type'))
        
        # 转换为列表格式
        user_list = []
        for user_id, stats in user_stats.items():
            user_list.append({
                'user_id': user_id,
                'event_count': stats['event_count'],
                'total_value': stats['total_value'],
                'avg_value': stats['total_value'] / stats['event_count'],
                'event_types': list(stats['event_types'])
            })
        
        # 排序
        user_list.sort(key=lambda x: x['total_value'], reverse=True)
        
        return {
            'total_users': len(user_list),
            'top_users': user_list[:5],
            'total_events': len(events),
            'total_value': sum(event.get('value', 0) for event in events)
        }
    
    agg_events = stream_processor.generate_mock_events(25)
    agg_result = calculate_aggregations(agg_events)
    
    aggregation_template = """
流式聚合分析:
============

📊 总体统计:
- 总用户数: {{ result.total_users }}
- 总事件数: {{ result.total_events }}
- 总价值: {{ result.total_value | round(2) }}
- 平均价值: {{ (result.total_value / result.total_events) | round(2) }}

🏆 Top 5 用户:
{%- for user in result.top_users %}
{{ loop.index }}. 用户{{ user.user_id }}:
   - 事件数: {{ user.event_count }}
   - 总价值: {{ user.total_value | round(2) }}
   - 平均价值: {{ user.avg_value | round(2) }}
   - 事件类型: {{ user.event_types | join(', ') }}
{%- endfor %}

💡 聚合分析优势:
- 实时用户画像
- 价值排名
- 行为分析
- 个性化推荐
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(aggregation_template, result=agg_result)
    print(result)
    
    # 5. 流处理架构示例
    print("\n🏗️ 5. 流处理架构示例")
    
    architecture_template = """
流数据处理架构:
==============

🌊 数据流向:
数据源 → 消息队列 → 流处理器 → 存储/输出

📦 核心组件:
1. 数据接入层:
   - Kafka/RabbitMQ: 消息队列
   - HTTP API: 实时数据接入
   - File Watcher: 文件变化监控

2. 流处理层:
   - Event Processing: 事件处理
   - Window Analysis: 窗口分析
   - Pattern Matching: 模式匹配
   - Aggregation: 实时聚合

3. 存储输出层:
   - Database: 结构化存储
   - Cache: 高速缓存
   - File System: 文件输出
   - API: 实时推送

⚡ 性能特性:
- 低延迟: < 100ms
- 高吞吐: > 10K events/sec
- 容错性: 自动重试和恢复
- 可扩展: 水平扩展支持

🔧 配置示例:
{
    "input": {
        "type": "kafka",
        "brokers": ["localhost:9092"],
        "topics": ["events"]
    },
    "processing": {
        "window_size": "5m",
        "batch_size": 1000,
        "parallelism": 4
    },
    "output": {
        "type": "database",
        "connection": "postgresql://..."
    }
}

💡 最佳实践:
- 设计幂等处理逻辑
- 实现背压控制
- 监控处理延迟
- 定期检查点保存
- 异常处理和恢复
    """.strip()
    
    print("渲染结果:")
    result = engine.render_template(architecture_template)
    print(result)
    
    print("\n📊 功能总结:")
    print("🌊 实时处理: 事件接收、转换、输出")
    print("📊 窗口分析: 时间窗口、滑动窗口、会话窗口")
    print("🚨 事件检测: 异常检测、模式匹配、规则引擎")
    print("📈 流式聚合: 实时统计、用户画像、趋势分析")
    print("🏗️ 架构设计: 分层架构、组件化、可扩展")
    
    print("\n💡 使用要点:")
    print("✅ 低延迟 - 毫秒级事件处理")
    print("✅ 高吞吐 - 支持大规模数据流")
    print("✅ 容错性 - 自动重试和恢复机制")
    print("✅ 可扩展 - 水平扩展和负载均衡")

if __name__ == "__main__":
    try:
        stream_data_processing_examples()
        print("\n🎉 流数据处理插件示例运行完成！")
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
        print("\n💡 这是一个概念演示，实际流处理需要相应的基础设施")
