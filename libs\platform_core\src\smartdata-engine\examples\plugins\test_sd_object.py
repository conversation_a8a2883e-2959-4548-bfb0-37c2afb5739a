#!/usr/bin/env python3
"""
测试sd对象是否正常工作
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.template_ext import create_template_engine

def test_sd_object():
    """测试sd对象"""
    print("=== 测试sd对象 ===")
    
    # 创建模板引擎
    engine = create_template_engine()
    
    # 测试1: 检查sd对象是否存在
    print("\n1. 测试sd对象是否存在")
    test_template = """
sd对象类型: {{ sd.__class__.__name__ }}
sd对象方法: {{ sd.__dict__.keys() | list }}
    """.strip()
    
    try:
        result = engine.render_template(test_template)
        print("渲染结果:")
        print(result)
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试2: 测试database方法
    print("\n2. 测试database方法")
    db_test_template = """
{%- set db_connector = sd.database("sqlite:///:memory:") -%}
数据库连接器类型: {{ db_connector.__class__.__name__ }}
数据库连接器方法: {{ db_connector.__dict__.keys() | list }}
    """.strip()
    
    try:
        result = engine.render_template(db_test_template)
        print("渲染结果:")
        print(result)
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试3: 测试query方法
    print("\n3. 测试query方法")
    query_test_template = """
{%- set db_result = sd.database("sqlite:///:memory:").query("SELECT 'Hello' as message") -%}
查询结果类型: {{ db_result.__class__.__name__ }}
查询结果: {{ db_result }}
    """.strip()
    
    try:
        result = engine.render_template(query_test_template)
        print("渲染结果:")
        print(result)
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sd_object()
