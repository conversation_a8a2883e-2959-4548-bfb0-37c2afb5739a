"""
企业级数据架构核心实现
解决模板引擎数据处理的可扩展性和维护性问题

设计原则:
1. 插件化扩展 - 零侵入式添加新数据源
2. 统一数据契约 - 所有数据源返回一致格式
3. 生命周期管理 - 自动资源管理，防止内存泄漏
4. 类型安全 - 强类型接口，编译时错误检查
5. 企业级特性 - 连接池、事务、缓存、监控

版本: 1.0.0
作者: SmartData Team
创建时间: 2025-07-28
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Callable, Optional, Type, Protocol, Union
from dataclasses import dataclass, field
import logging
import threading
import weakref
from contextlib import contextmanager
import uuid
import time
import inspect
from enum import Enum


# ============================================================================
# 枚举定义
# ============================================================================

class DataSourceType(Enum):
    """数据源类型枚举"""
    DATABASE = "database"
    API = "api"
    FILE = "file"
    CACHE = "cache"
    STREAM = "stream"
    UNKNOWN = "unknown"


class OperationStatus(Enum):
    """操作状态枚举"""
    SUCCESS = "success"
    ERROR = "error"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


# ============================================================================
# 核心接口定义
# ============================================================================

class IDataAdapter(Protocol):
    """
    数据适配器统一接口

    所有数据适配器必须实现此接口，确保插件化扩展的一致性
    """

    def supported_types(self) -> List[str]:
        """
        返回支持的数据类型列表

        Returns:
            List[str]: 支持的数据类型列表，如 ['postgresql', 'mysql']
        """
        ...

    def can_handle(self, data_source: Any) -> bool:
        """
        检查是否能处理指定的数据源

        Args:
            data_source: 数据源对象

        Returns:
            bool: 是否能处理
        """
        ...

    def create_proxy(self, data_source: Any, lifecycle_manager: 'ILifecycleManager') -> 'DataProxy':
        """
        创建数据代理对象

        Args:
            data_source: 数据源对象
            lifecycle_manager: 生命周期管理器

        Returns:
            DataProxy: 数据代理对象
        """
        ...

    def get_operations(self) -> Dict[str, Callable]:
        """
        获取支持的操作列表

        Returns:
            Dict[str, Callable]: 操作名称到函数的映射
        """
        ...

    def get_metadata(self) -> Dict[str, Any]:
        """
        获取适配器元数据

        Returns:
            Dict[str, Any]: 元数据信息
        """
        ...


class ILifecycleManager(Protocol):
    """
    生命周期管理器接口

    负责管理数据源的生命周期，防止内存泄漏
    """

    def register_resource(self, resource_id: str, resource: Any, cleanup_callback: Optional[Callable] = None) -> None:
        """
        注册资源

        Args:
            resource_id: 资源唯一标识
            resource: 资源对象
            cleanup_callback: 清理回调函数
        """
        ...

    def cleanup_resource(self, resource_id: str) -> bool:
        """
        清理指定资源

        Args:
            resource_id: 资源唯一标识

        Returns:
            bool: 清理是否成功
        """
        ...

    def cleanup_all(self) -> int:
        """
        清理所有资源

        Returns:
            int: 清理的资源数量
        """
        ...

    def get_resource_count(self) -> int:
        """
        获取当前资源数量

        Returns:
            int: 资源数量
        """
        ...


# ============================================================================
# 数据结果契约
# ============================================================================

@dataclass
class DataResult:
    """
    统一数据结果契约

    所有数据操作必须返回此格式，确保模板中数据访问的一致性
    """
    success: bool
    data: Any = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    # 性能指标
    execution_time: float = 0.0
    affected_rows: int = 0

    # 操作信息
    operation: str = 'unknown'
    source_type: str = 'unknown'
    adapter_type: str = 'unknown'

    # 时间戳
    created_at: float = field(default_factory=time.time)

    def __post_init__(self):
        """初始化后处理"""
        if self.metadata is None:
            self.metadata = {}

        # 确保关键属性有默认值
        if self.execution_time < 0:
            self.execution_time = 0.0

        if self.affected_rows < 0:
            self.affected_rows = 0

        # 如果data是列表且affected_rows为0，自动设置
        if isinstance(self.data, list) and self.affected_rows == 0:
            self.affected_rows = len(self.data)



    def __getattr__(self, name: str) -> Any:
        """
        支持动态属性访问，模板友好

        查找顺序:
        1. metadata中的键
        2. data字典中的键（如果data是字典）
        3. data列表第一个元素的键（如果data是单元素列表且元素是字典）
        """
        # 首先检查metadata
        if name in self.metadata:
            return self.metadata[name]

        # 如果data是字典，尝试从中获取
        if isinstance(self.data, dict) and name in self.data:
            return self.data[name]

        # 如果data是列表且只有一个元素，尝试从元素中获取
        if isinstance(self.data, list) and len(self.data) == 1:
            if isinstance(self.data[0], dict) and name in self.data[0]:
                return self.data[0][name]

        raise AttributeError(f"DataResult对象没有属性 '{name}'")

    def get(self, key: str, default: Any = None) -> Any:
        """
        字典式访问方法

        Args:
            key: 属性键
            default: 默认值

        Returns:
            Any: 属性值或默认值
        """
        try:
            # 首先尝试直接属性访问
            if hasattr(self, key):
                value = getattr(self, key)
                # 如果值为None且提供了非None的默认值，返回默认值
                if value is None and default != None:
                    return default
                return value

            # 然后尝试动态属性访问
            return self.__getattr__(key)
        except AttributeError:
            return default

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式

        Returns:
            Dict[str, Any]: 字典表示
        """
        return {
            'success': self.success,
            'data': self.data,
            'error': self.error,
            'metadata': self.metadata,
            'execution_time': self.execution_time,
            'affected_rows': self.affected_rows,
            'operation': self.operation,
            'source_type': self.source_type,
            'adapter_type': self.adapter_type,
            'created_at': self.created_at
        }

    @classmethod
    def success_result(cls, data: Any, **kwargs) -> 'DataResult':
        """
        创建成功结果

        Args:
            data: 结果数据
            **kwargs: 其他参数

        Returns:
            DataResult: 成功结果对象
        """
        return cls(success=True, data=data, **kwargs)

    @classmethod
    def error_result(cls, error: str, **kwargs) -> 'DataResult':
        """
        创建错误结果

        Args:
            error: 错误信息
            **kwargs: 其他参数

        Returns:
            DataResult: 错误结果对象
        """
        return cls(success=False, error=error, **kwargs)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DataResult':
        """
        从字典创建DataResult

        Args:
            data: 字典数据

        Returns:
            DataResult: 结果对象
        """
        return cls(
            success=data.get('success', False),
            data=data.get('data'),
            error=data.get('error'),
            metadata=data.get('metadata', {}),
            execution_time=data.get('execution_time', 0.0),
            affected_rows=data.get('affected_rows', 0),
            operation=data.get('operation', 'unknown'),
            source_type=data.get('source_type', 'unknown'),
            adapter_type=data.get('adapter_type', 'unknown'),
            created_at=data.get('created_at', time.time())
        )


# ============================================================================
# 数据适配器注册表
# ============================================================================

class DataRegistry:
    """
    数据适配器注册表 - 插件化扩展

    负责管理所有数据适配器的注册、发现和创建
    """

    def __init__(self):
        self.adapters: Dict[str, Type[IDataAdapter]] = {}
        self.type_detectors: List[Callable[[Any], Optional[str]]] = []
        self.logger = logging.getLogger(self.__class__.__name__)
        self._lock = threading.RLock()

        # 注册内置类型检测器
        self._register_builtin_detectors()

    def register_adapter(self, adapter_class: Type[IDataAdapter]) -> None:
        """
        注册数据适配器

        Args:
            adapter_class: 适配器类
        """
        with self._lock:
            try:
                # 创建适配器实例以获取支持的类型
                adapter_instance = adapter_class()
                supported_types = adapter_instance.supported_types()

                for data_type in supported_types:
                    if data_type in self.adapters:
                        self.logger.warning(f"覆盖已存在的适配器: {data_type} ({self.adapters[data_type].__name__} -> {adapter_class.__name__})")

                    self.adapters[data_type] = adapter_class
                    self.logger.info(f"注册适配器: {adapter_class.__name__} -> {data_type}")

            except Exception as e:
                self.logger.error(f"注册适配器失败 {adapter_class.__name__}: {e}")
                raise AdapterCreationError(adapter_class.__name__, e)

    def register_type_detector(self, detector: Callable[[Any], Optional[str]]) -> None:
        """
        注册类型检测器

        Args:
            detector: 类型检测函数，接受数据源，返回类型字符串或None
        """
        with self._lock:
            self.type_detectors.append(detector)
            self.logger.debug(f"注册类型检测器: {detector.__name__}")

    def get_adapter(self, data_source: Any) -> IDataAdapter:
        """
        自动获取最佳适配器

        Args:
            data_source: 数据源对象

        Returns:
            IDataAdapter: 适配器实例

        Raises:
            UnsupportedDataTypeError: 不支持的数据类型
            AdapterCreationError: 适配器创建失败
        """
        data_type = self._detect_type(data_source)

        if not data_type:
            available_types = list(self.adapters.keys())
            raise UnsupportedDataTypeError(
                data_type=f"未知类型 ({type(data_source).__name__})",
                available_types=available_types
            )

        adapter_class = self.adapters.get(data_type)
        if not adapter_class:
            available_types = list(self.adapters.keys())
            raise UnsupportedDataTypeError(data_type, available_types)

        try:
            return adapter_class()
        except Exception as e:
            raise AdapterCreationError(adapter_class.__name__, e)

    def get_supported_types(self) -> List[str]:
        """
        获取所有支持的数据类型

        Returns:
            List[str]: 支持的数据类型列表
        """
        with self._lock:
            return list(self.adapters.keys())

    def get_adapter_info(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有适配器信息

        Returns:
            Dict[str, Dict[str, Any]]: 适配器信息映射
        """
        with self._lock:
            info = {}
            for data_type, adapter_class in self.adapters.items():
                try:
                    adapter_instance = adapter_class()
                    info[data_type] = {
                        'class_name': adapter_class.__name__,
                        'module': adapter_class.__module__,
                        'metadata': adapter_instance.get_metadata() if hasattr(adapter_instance, 'get_metadata') else {},
                        'operations': list(adapter_instance.get_operations().keys()) if hasattr(adapter_instance, 'get_operations') else []
                    }
                except Exception as e:
                    info[data_type] = {
                        'class_name': adapter_class.__name__,
                        'module': adapter_class.__module__,
                        'error': str(e)
                    }
            return info

    def unregister_adapter(self, data_type: str) -> bool:
        """
        注销适配器

        Args:
            data_type: 数据类型

        Returns:
            bool: 是否成功注销
        """
        with self._lock:
            if data_type in self.adapters:
                adapter_class = self.adapters[data_type]
                del self.adapters[data_type]
                self.logger.info(f"注销适配器: {adapter_class.__name__} -> {data_type}")
                return True
            return False

    def clear_adapters(self) -> int:
        """
        清除所有适配器

        Returns:
            int: 清除的适配器数量
        """
        with self._lock:
            count = len(self.adapters)
            self.adapters.clear()
            self.logger.info(f"清除所有适配器，共 {count} 个")
            return count

    def _detect_type(self, data_source: Any) -> Optional[str]:
        """
        检测数据源类型

        Args:
            data_source: 数据源对象

        Returns:
            Optional[str]: 检测到的类型，如果无法检测则返回None
        """
        # 1. 使用注册的类型检测器
        for detector in self.type_detectors:
            try:
                detected_type = detector(data_source)
                if detected_type:
                    self.logger.debug(f"类型检测器 {detector.__name__} 检测到类型: {detected_type}")
                    return detected_type
            except Exception as e:
                self.logger.debug(f"类型检测器 {detector.__name__} 失败: {e}")

        return None

    def _register_builtin_detectors(self) -> None:
        """注册内置类型检测器"""

        def detect_connection_string(data_source: Any) -> Optional[str]:
            """检测连接字符串"""
            if isinstance(data_source, str):
                # 数据库连接字符串
                if data_source.startswith(('postgresql://', 'postgres://')):
                    return 'postgresql_connection_string'
                elif data_source.startswith(('mysql://', 'mariadb://')):
                    return 'mysql_connection_string'
                elif data_source.startswith('sqlite://') or data_source.endswith(('.db', '.sqlite', '.sqlite3')) or data_source == ':memory:':
                    return 'sqlite_connection_string'
                # API URL
                elif data_source.startswith(('http://', 'https://')):
                    return 'http_api'
                # 文件路径检测
                elif data_source.endswith(('.csv', '.tsv')):
                    return 'csv_file'
                elif data_source.endswith(('.json', '.jsonl', '.ndjson')):
                    return 'json_file'
                elif data_source.endswith(('.html', '.htm')):
                    return 'html_file'
                elif data_source.endswith(('.xml', '.xsd')):
                    return 'xml_file'
                elif data_source.startswith(('file://', '/')):
                    return 'file_path'
            return None

        def detect_python_objects(data_source: Any) -> Optional[str]:
            """检测Python内置对象类型"""
            if isinstance(data_source, dict):
                # 检测字典类型的配置对象
                if 'base_url' in data_source or 'url' in data_source:
                    return 'rest_api'
                elif 'file_path' in data_source or 'path' in data_source:
                    file_path = data_source.get('file_path') or data_source.get('path')
                    if isinstance(file_path, str):
                        if file_path.endswith(('.csv', '.tsv')):
                            return 'csv_file'
                        elif file_path.endswith(('.json', '.jsonl', '.ndjson')):
                            return 'json_file'
                        elif file_path.endswith(('.html', '.htm')):
                            return 'html_file'
                        elif file_path.endswith(('.xml', '.xsd')):
                            return 'xml_file'
                        else:
                            return 'file_path'
                elif 'host' in data_source and 'database' in data_source:
                    # 数据库连接配置
                    if 'postgresql' in str(data_source).lower() or data_source.get('port') == 5432:
                        return 'postgresql_connection'
                    elif 'mysql' in str(data_source).lower() or data_source.get('port') == 3306:
                        return 'mysql_connection'
                    else:
                        return 'database_connection'
                else:
                    # 通用配置对象
                    return 'config_object'
            elif isinstance(data_source, list):
                # 检测列表类型
                if all(isinstance(item, dict) for item in data_source):
                    return 'data_list'
                else:
                    return 'list_object'
            elif hasattr(data_source, '__iter__') and not isinstance(data_source, (str, bytes)):
                # 其他可迭代对象
                return 'iterable_object'
            return None

        def detect_database_connection(data_source: Any) -> Optional[str]:
            """检测数据库连接对象"""
            # 只检测非字符串的对象
            if not isinstance(data_source, str) and hasattr(data_source, 'execute') and hasattr(data_source, 'cursor'):
                # 尝试检测具体的数据库类型
                class_name = type(data_source).__name__.lower()
                module_name = type(data_source).__module__.lower()

                if 'postgres' in class_name or 'psycopg' in class_name or 'psycopg' in module_name:
                    return 'postgresql_connection'
                elif 'mysql' in class_name or 'pymysql' in module_name or 'mariadb' in class_name:
                    return 'mysql_connection'
                elif 'sqlite' in class_name or 'sqlite3' in module_name:
                    return 'sqlite_connection'
                else:
                    return 'database_connection'
            return None

        def detect_file_like(data_source: Any) -> Optional[str]:
            """检测文件类对象"""
            if hasattr(data_source, 'read') and hasattr(data_source, 'write'):
                return 'file_like'
            elif hasattr(data_source, 'read'):
                return 'readable_file'
            elif hasattr(data_source, 'write'):
                return 'writable_file'
            return None

        # 注册内置检测器
        self.register_type_detector(detect_python_objects)  # 优先检测Python对象
        self.register_type_detector(detect_connection_string)
        self.register_type_detector(detect_database_connection)
        self.register_type_detector(detect_file_like)


# ============================================================================
# 数据代理层
# ============================================================================

class DataProxy:
    """
    数据代理对象 - 模板友好的统一接口

    为数据源提供统一的操作接口，自动包装结果为DataResult格式
    """

    def __init__(self, source: Any, adapter: IDataAdapter, lifecycle_manager: Optional[ILifecycleManager]):
        self.original_source = source
        self.adapter = adapter
        self.lifecycle_manager = lifecycle_manager
        self.operations = adapter.get_operations()
        self.proxy_id = str(uuid.uuid4())
        self.logger = logging.getLogger(self.__class__.__name__)

        # 对于数据库适配器，如果source是连接字符串，创建实际连接
        if hasattr(adapter, '_create_connection') and isinstance(source, str):
            try:
                self.source = adapter._create_connection(source)
                self.logger.debug(f"为连接字符串创建了实际连接: {source}")
            except Exception as e:
                self.logger.warning(f"无法创建连接，使用原始source: {e}")
                self.source = source
        else:
            self.source = source

        # 注册到生命周期管理器
        if lifecycle_manager:
            lifecycle_manager.register_resource(self.proxy_id, self)

    def __getattr__(self, name: str) -> Any:
        """动态方法调用"""
        # 避免递归：直接访问__dict__中的属性
        if 'operations' in self.__dict__ and name in self.__dict__['operations']:
            operation = self.__dict__['operations'][name]
            return self._create_operation_wrapper(operation, name)

        # 尝试从源对象获取属性
        if 'original_source' in self.__dict__ and hasattr(self.__dict__['original_source'], name):
            attr = getattr(self.__dict__['original_source'], name)
            if callable(attr):
                return self._create_source_method_wrapper(attr, name)
            else:
                return attr

        raise AttributeError(f"操作 '{name}' 不支持")

    def _create_operation_wrapper(self, operation: Callable, operation_name: str) -> Callable:
        """创建操作包装器，确保结果符合模板契约"""
        def wrapper(*args, **kwargs):
            start_time = time.time()

            try:
                self.logger.debug(f"执行操作: {operation_name}, 参数: {args}, {kwargs}")

                # 执行原始操作
                result = operation(self.source, *args, **kwargs)

                execution_time = (time.time() - start_time) * 1000

                # 包装为模板友好格式
                return self._wrap_result(result, operation_name, execution_time)

            except Exception as e:
                execution_time = (time.time() - start_time) * 1000
                self.logger.error(f"操作 {operation_name} 执行失败: {e}")

                return DataResult(
                    success=False,
                    error=str(e),
                    operation=operation_name,
                    source_type=type(self.source).__name__,
                    adapter_type=type(self.adapter).__name__,
                    execution_time=execution_time
                )

        return wrapper

    def _create_source_method_wrapper(self, method: Callable, method_name: str) -> Callable:
        """创建源对象方法包装器"""
        def wrapper(*args, **kwargs):
            start_time = time.time()

            try:
                self.logger.debug(f"执行源方法: {method_name}, 参数: {args}, {kwargs}")

                # 直接调用源对象方法，不传递source参数
                result = method(*args, **kwargs)

                execution_time = (time.time() - start_time) * 1000

                # 包装为模板友好格式
                return self._wrap_result(result, method_name, execution_time)

            except Exception as e:
                execution_time = (time.time() - start_time) * 1000
                self.logger.error(f"源方法 {method_name} 执行失败: {e}")

                return DataResult(
                    success=False,
                    error=str(e),
                    operation=method_name,
                    source_type=type(self.source).__name__,
                    adapter_type=type(self.adapter).__name__,
                    execution_time=execution_time
                )

        return wrapper

    def _wrap_result(self, result: Any, operation_name: str, execution_time: float) -> DataResult:
        """包装结果为DataResult格式"""
        # 如果已经是DataResult，直接返回
        if isinstance(result, DataResult):
            return result

        # 处理datetime对象，避免JSON序列化错误
        processed_result = self._process_datetime_objects(result)

        return DataResult(
            success=True,
            data=processed_result,
            operation=operation_name,
            source_type=type(self.source).__name__,
            adapter_type=type(self.adapter).__name__,
            execution_time=execution_time,
            metadata={
                'proxy_id': self.proxy_id,
                'adapter_type': type(self.adapter).__name__
            }
        )


    def _process_datetime_objects(self, data: Any) -> Any:
        """处理datetime对象，转换为字符串"""
        if hasattr(data, 'isoformat'):  # datetime对象
            return data.isoformat()
        elif isinstance(data, dict):
            return {k: self._process_datetime_objects(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._process_datetime_objects(item) for item in data]
        else:
            return data

    def __del__(self):
        """析构时清理资源"""
        try:
            # 直接访问__dict__避免递归
            if ('lifecycle_manager' in self.__dict__ and
                self.__dict__['lifecycle_manager'] and
                'proxy_id' in self.__dict__):
                self.__dict__['lifecycle_manager'].cleanup_resource(self.__dict__['proxy_id'])
        except:
            pass  # 忽略清理错误
    

    
    def _process_datetime_objects(self, data: Any) -> Any:
        """处理datetime对象，转换为字符串"""
        if hasattr(data, 'isoformat'):  # datetime对象
            return data.isoformat()
        elif isinstance(data, dict):
            return {k: self._process_datetime_objects(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._process_datetime_objects(item) for item in data]
        else:
            return data
    
    def __del__(self):
        """析构时清理资源"""
        if hasattr(self, 'lifecycle_manager') and self.lifecycle_manager:
            try:
                self.lifecycle_manager.cleanup_resource(self.proxy_id)
            except:
                pass  # 忽略清理错误





# ============================================================================
# 生命周期管理器
# ============================================================================

class LifecycleManager:
    """生命周期管理器"""
    
    def __init__(self):
        self.resources: Dict[str, weakref.ref] = {}
        self.cleanup_callbacks: Dict[str, List[Callable]] = {}
        self.logger = logging.getLogger(self.__class__.__name__)
        self._lock = threading.RLock()
    
    def register_resource(self, resource_id: str, resource: Any, cleanup_callback: Optional[Callable] = None) -> None:
        """注册资源"""
        with self._lock:
            # 使用弱引用避免循环引用
            self.resources[resource_id] = weakref.ref(resource, self._resource_cleanup_callback(resource_id))
            self.cleanup_callbacks[resource_id] = []

            # 如果提供了清理回调，添加到列表中
            if cleanup_callback:
                self.cleanup_callbacks[resource_id].append(cleanup_callback)

            self.logger.debug(f"注册资源: {resource_id}")
    
    def add_cleanup_callback(self, resource_id: str, callback: Callable) -> None:
        """添加清理回调"""
        with self._lock:
            if resource_id in self.cleanup_callbacks:
                self.cleanup_callbacks[resource_id].append(callback)
    
    def cleanup_resource(self, resource_id: str) -> bool:
        """清理指定资源"""
        with self._lock:
            if resource_id in self.resources:
                # 执行清理回调
                for callback in self.cleanup_callbacks.get(resource_id, []):
                    try:
                        callback()
                    except Exception as e:
                        self.logger.error(f"清理回调执行失败: {e}")

                # 移除资源引用
                del self.resources[resource_id]
                del self.cleanup_callbacks[resource_id]
                self.logger.debug(f"清理资源: {resource_id}")
                return True
            return False
    
    def cleanup_all(self) -> int:
        """清理所有资源"""
        with self._lock:
            resource_ids = list(self.resources.keys())
            count = 0
            for resource_id in resource_ids:
                if self.cleanup_resource(resource_id):
                    count += 1
            return count

    def get_resource_count(self) -> int:
        """获取当前资源数量"""
        with self._lock:
            return len(self.resources)
    
    def _resource_cleanup_callback(self, resource_id: str):
        """资源清理回调工厂"""
        def callback(ref):
            # ref参数是弱引用对象，这里不需要使用
            self.cleanup_resource(resource_id)
        return callback


# ============================================================================
# 模板作用域管理
# ============================================================================

class TemplateScope:
    """
    模板作用域管理器

    为每个模板提供独立的数据上下文和生命周期管理
    """

    def __init__(self, template_id: str, data_registry: 'DataRegistry' = None):
        self.template_id = template_id
        self.data_registry = data_registry or DataRegistry()
        self.lifecycle_manager = LifecycleManager()
        self.data_proxies: Dict[str, DataProxy] = {}
        self.logger = logging.getLogger(self.__class__.__name__)
        self._lock = threading.RLock()

        self.logger.debug(f"创建模板作用域: {template_id}")

    def get_scope_id(self) -> str:
        """获取作用域ID"""
        return self.template_id

    def register_data_source(self, name: str, source: Any) -> DataProxy:
        """
        注册数据源到作用域

        Args:
            name: 数据源名称
            source: 数据源对象

        Returns:
            DataProxy: 数据代理对象

        Raises:
            UnsupportedDataTypeError: 不支持的数据类型
            AdapterCreationError: 适配器创建失败
        """
        with self._lock:
            try:
                # 获取适配器
                adapter = self.data_registry.get_adapter(source)

                # 创建数据代理
                proxy = DataProxy(source, adapter, self.lifecycle_manager)

                # 注册到作用域
                self.data_proxies[name] = proxy

                self.logger.debug(f"注册数据源到作用域 {self.template_id}: {name}")
                return proxy

            except Exception as e:
                self.logger.error(f"注册数据源失败 {name}: {e}")
                raise

    def get_data_proxy(self, name: str) -> Optional[DataProxy]:
        """
        获取数据代理对象

        Args:
            name: 数据源名称

        Returns:
            Optional[DataProxy]: 数据代理对象，如果不存在则返回None
        """
        with self._lock:
            return self.data_proxies.get(name)

    def list_data_sources(self) -> List[str]:
        """
        列出所有数据源名称

        Returns:
            List[str]: 数据源名称列表
        """
        with self._lock:
            return list(self.data_proxies.keys())

    def remove_data_source(self, name: str) -> bool:
        """
        移除数据源

        Args:
            name: 数据源名称

        Returns:
            bool: 是否成功移除
        """
        with self._lock:
            if name in self.data_proxies:
                proxy = self.data_proxies[name]
                # 清理代理资源
                if hasattr(proxy, 'proxy_id'):
                    self.lifecycle_manager.cleanup_resource(proxy.proxy_id)

                del self.data_proxies[name]
                self.logger.debug(f"移除数据源 {name} 从作用域 {self.template_id}")
                return True
            return False

    def get_context_data(self) -> Dict[str, Any]:
        """
        获取模板上下文数据

        Returns:
            Dict[str, Any]: 包含所有数据代理的上下文字典
        """
        with self._lock:
            context = {}
            for name, proxy in self.data_proxies.items():
                context[name] = proxy
            return context

    def get_scope_info(self) -> Dict[str, Any]:
        """
        获取作用域信息

        Returns:
            Dict[str, Any]: 作用域信息
        """
        with self._lock:
            return {
                'template_id': self.template_id,
                'data_sources': list(self.data_proxies.keys()),
                'resource_count': self.lifecycle_manager.get_resource_count(),
                'registry_types': self.data_registry.get_supported_types()
            }

    def cleanup(self) -> None:
        """清理作用域资源"""
        with self._lock:
            try:
                # 清理所有数据代理
                for name in list(self.data_proxies.keys()):
                    self.remove_data_source(name)

                # 清理生命周期管理器中的所有资源
                cleaned_count = self.lifecycle_manager.cleanup_all()

                self.logger.debug(f"清理作用域 {self.template_id}，清理了 {cleaned_count} 个资源")

            except Exception as e:
                self.logger.error(f"清理作用域失败 {self.template_id}: {e}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()

    def __del__(self):
        """析构时清理资源"""
        try:
            self.cleanup()
        except:
            pass  # 忽略清理错误


# ============================================================================
# 异常定义
# ============================================================================

class DataArchitectureError(Exception):
    """数据架构基础异常"""

    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or 'UNKNOWN_ERROR'
        self.details = details or {}
        self.timestamp = time.time()


class UnsupportedDataTypeError(DataArchitectureError):
    """不支持的数据类型异常"""

    def __init__(self, data_type: str, available_types: List[str] = None):
        message = f"不支持的数据类型: {data_type}"
        if available_types:
            message += f"，可用类型: {', '.join(available_types)}"

        super().__init__(
            message=message,
            error_code='UNSUPPORTED_DATA_TYPE',
            details={
                'data_type': data_type,
                'available_types': available_types or []
            }
        )


class AdapterCreationError(DataArchitectureError):
    """适配器创建异常"""

    def __init__(self, adapter_name: str, cause: Exception = None):
        message = f"创建适配器失败: {adapter_name}"
        if cause:
            message += f"，原因: {str(cause)}"

        super().__init__(
            message=message,
            error_code='ADAPTER_CREATION_ERROR',
            details={
                'adapter_name': adapter_name,
                'cause': str(cause) if cause else None
            }
        )


class TemplateRenderError(DataArchitectureError):
    """模板渲染异常"""

    def __init__(self, template_id: str, cause: Exception = None):
        message = f"模板渲染失败: {template_id}"
        if cause:
            message += f"，原因: {str(cause)}"

        super().__init__(
            message=message,
            error_code='TEMPLATE_RENDER_ERROR',
            details={
                'template_id': template_id,
                'cause': str(cause) if cause else None
            }
        )


class ResourceManagementError(DataArchitectureError):
    """资源管理异常"""

    def __init__(self, resource_id: str, operation: str, cause: Exception = None):
        message = f"资源管理操作失败: {operation} - {resource_id}"
        if cause:
            message += f"，原因: {str(cause)}"

        super().__init__(
            message=message,
            error_code='RESOURCE_MANAGEMENT_ERROR',
            details={
                'resource_id': resource_id,
                'operation': operation,
                'cause': str(cause) if cause else None
            }
        )
