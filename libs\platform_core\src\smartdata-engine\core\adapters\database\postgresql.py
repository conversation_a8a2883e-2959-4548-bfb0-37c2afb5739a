"""
PostgreSQL数据库适配器

提供PostgreSQL数据库的完整支持，包括：
- 连接管理
- SQL执行
- 事务处理
- 存储过程和函数调用
- 批量操作
"""

from typing import Any, Dict, List, Optional, Union
import logging

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../'))

from .base import DatabaseAdapterBase, ConnectionInfo

# 尝试导入PostgreSQL驱动
try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
    from psycopg2 import sql
    PSYCOPG2_AVAILABLE = True
except ImportError:
    PSYCOPG2_AVAILABLE = False
    psycopg2 = None
    RealDictCursor = None
    sql = None


class PostgreSQLAdapter(DatabaseAdapterBase):
    """
    PostgreSQL数据库适配器
    
    支持PostgreSQL特有的功能：
    - JSONB数据类型
    - 数组类型
    - 自定义类型
    - 存储过程和函数
    - 批量插入
    - 流式查询
    """
    
    def __init__(self):
        super().__init__()
        if not PSYCOPG2_AVAILABLE:
            self.logger.warning("psycopg2未安装，PostgreSQL适配器功能受限")
    
    def supported_types(self) -> List[str]:
        """返回支持的数据类型列表"""
        return [
            'postgresql',
            'postgresql_connection',
            'postgresql_connection_string',
            'postgresql_url',
            'postgres',
            'postgres_connection',
            'postgres_connection_string',
            'postgres_url'
        ]
    
    def _is_supported_connection_string(self, connection_string: str) -> bool:
        """检查是否支持的连接字符串"""
        return connection_string.startswith(('postgresql://', 'postgres://'))
    
    def _is_supported_connection_object(self, connection: Any) -> bool:
        """检查是否支持的连接对象"""
        if not PSYCOPG2_AVAILABLE:
            return False

        # 检查是否是psycopg2连接对象
        if (hasattr(connection, 'cursor') and
            hasattr(connection, 'commit') and
            hasattr(connection, 'rollback') and
            'psycopg2' in str(type(connection))):
            return True

        # 检查是否是PostgreSQL配置字典
        if isinstance(connection, dict):
            # 必须包含基本连接信息
            has_host = 'host' in connection
            has_database = 'database' in connection or 'dbname' in connection
            # PostgreSQL默认端口或明确指定为PostgreSQL
            is_postgres = (connection.get('port') == 5432 or
                          'postgresql' in str(connection).lower() or
                          'postgres' in str(connection).lower())
            return has_host and has_database and is_postgres

        return False
    
    def can_handle(self, data_source: Any) -> bool:
        """检查是否能处理指定的数据源"""
        if isinstance(data_source, str):
            return self._is_supported_connection_string(data_source)
        else:
            return self._is_supported_connection_object(data_source)

    def _get_default_port(self) -> int:
        """获取默认端口"""
        return 5432
    
    def _create_connection(self, connection_source: Union[str, ConnectionInfo]) -> Any:
        """创建PostgreSQL连接"""
        if not PSYCOPG2_AVAILABLE:
            raise ImportError("psycopg2未安装，无法创建PostgreSQL连接")
        
        if isinstance(connection_source, str):
            # 直接使用连接字符串
            connection_string = connection_source
        else:
            # 从ConnectionInfo构建连接字符串
            conn_info = connection_source
            connection_string = (
                f"postgresql://{conn_info.username}:{conn_info.password}@"
                f"{conn_info.host}:{conn_info.port}/{conn_info.database}"
            )
            
            # 添加额外选项
            if conn_info.options:
                params = '&'.join([f"{k}={v}" for k, v in conn_info.options.items()])
                connection_string += f"?{params}"
        
        try:
            # 创建连接
            connection = psycopg2.connect(connection_string)
            
            # 设置自动提交模式（可以通过选项控制）
            connection.autocommit = False
            
            self.logger.info(f"成功创建PostgreSQL连接: {connection_string.split('@')[1] if '@' in connection_string else connection_string}")
            return connection
            
        except Exception as e:
            self.logger.error(f"创建PostgreSQL连接失败: {e}")
            raise
    
    def _execute_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """执行查询 - 返回字典列表"""
        try:
            with connection.cursor(cursor_factory=RealDictCursor) as cursor:
                # 执行查询
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)
                
                # 获取结果
                results = cursor.fetchall()
                
                # 转换为字典列表
                return [dict(row) for row in results]
                
        except Exception as e:
            self.logger.error(f"PostgreSQL查询执行失败: {e}")
            raise
    
    def _execute_command(self, connection: Any, sql: str, params: Dict = None) -> int:
        """执行命令 - 返回影响行数"""
        try:
            with connection.cursor() as cursor:
                # 执行命令
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)
                
                # 返回影响行数
                return cursor.rowcount
                
        except Exception as e:
            self.logger.error(f"PostgreSQL命令执行失败: {e}")
            raise
    
    def _get_database_info(self, connection: Any) -> Dict[str, Any]:
        """获取PostgreSQL数据库信息"""
        try:
            with connection.cursor(cursor_factory=RealDictCursor) as cursor:
                # 获取版本信息
                cursor.execute("SELECT version()")
                version_info = cursor.fetchone()
                
                # 获取数据库名称
                cursor.execute("SELECT current_database()")
                database_name = cursor.fetchone()
                
                # 获取用户信息
                cursor.execute("SELECT current_user, session_user")
                user_info = cursor.fetchone()
                
                # 获取连接信息
                cursor.execute("""
                    SELECT inet_server_addr() as server_addr, 
                           inet_server_port() as server_port,
                           current_setting('server_encoding') as encoding
                """)
                connection_info = cursor.fetchone()
                
                return {
                    'database_type': 'PostgreSQL',
                    'version': version_info['version'] if version_info else 'Unknown',
                    'database_name': database_name['current_database'] if database_name else 'Unknown',
                    'current_user': user_info['current_user'] if user_info else 'Unknown',
                    'session_user': user_info['session_user'] if user_info else 'Unknown',
                    'server_addr': connection_info['server_addr'] if connection_info else 'Unknown',
                    'server_port': connection_info['server_port'] if connection_info else 'Unknown',
                    'encoding': connection_info['encoding'] if connection_info else 'Unknown'
                }
                
        except Exception as e:
            self.logger.error(f"获取PostgreSQL数据库信息失败: {e}")
            return {
                'database_type': 'PostgreSQL',
                'error': str(e)
            }
    
    # ========================================================================
    # PostgreSQL特有功能
    # ========================================================================
    
    def _build_operations(self) -> Dict[str, callable]:
        """构建PostgreSQL特有操作列表"""
        operations = super()._build_operations()
        
        # 添加PostgreSQL特有操作
        operations.update({
            'copy_from': self._copy_from_wrapper,
            'copy_to': self._copy_to_wrapper,
            'listen': self._listen_wrapper,
            'notify': self._notify_wrapper,
            'vacuum': self._vacuum_wrapper,
            'reindex': self._reindex_wrapper,
            'analyze_table': self._analyze_table_wrapper,
        })
        
        return operations
    
    def _copy_from_wrapper(self, connection: Any, table: str, file_path: str, 
                          columns: List[str] = None, delimiter: str = '\t'):
        """COPY FROM操作包装器"""
        return self._copy_from(connection, table, file_path, columns, delimiter)
    
    def _copy_to_wrapper(self, connection: Any, table: str, file_path: str,
                        columns: List[str] = None, delimiter: str = '\t'):
        """COPY TO操作包装器"""
        return self._copy_to(connection, table, file_path, columns, delimiter)
    
    def _listen_wrapper(self, connection: Any, channel: str):
        """LISTEN操作包装器"""
        return self._listen(connection, channel)
    
    def _notify_wrapper(self, connection: Any, channel: str, payload: str = None):
        """NOTIFY操作包装器"""
        return self._notify(connection, channel, payload)
    
    def _vacuum_wrapper(self, connection: Any, table: str = None, full: bool = False):
        """VACUUM操作包装器"""
        return self._vacuum(connection, table, full)
    
    def _reindex_wrapper(self, connection: Any, table: str = None, index: str = None):
        """REINDEX操作包装器"""
        return self._reindex(connection, table, index)
    
    def _analyze_table_wrapper(self, connection: Any, table: str = None):
        """ANALYZE操作包装器"""
        return self._analyze_table(connection, table)
    
    def _copy_from(self, connection: Any, table: str, file_path: str,
                  columns: List[str] = None, delimiter: str = '\t') -> Dict:
        """从文件批量导入数据"""
        try:
            with connection.cursor() as cursor:
                with open(file_path, 'r', encoding='utf-8') as f:
                    if columns:
                        cursor.copy_from(f, table, columns=columns, sep=delimiter)
                    else:
                        cursor.copy_from(f, table, sep=delimiter)
                
                return {
                    'success': True,
                    'table': table,
                    'file_path': file_path,
                    'affected_rows': cursor.rowcount
                }
                
        except Exception as e:
            self.logger.error(f"COPY FROM操作失败: {e}")
            raise
    
    def _copy_to(self, connection: Any, table: str, file_path: str,
                columns: List[str] = None, delimiter: str = '\t') -> Dict:
        """导出数据到文件"""
        try:
            with connection.cursor() as cursor:
                with open(file_path, 'w', encoding='utf-8') as f:
                    if columns:
                        cursor.copy_to(f, table, columns=columns, sep=delimiter)
                    else:
                        cursor.copy_to(f, table, sep=delimiter)
                
                return {
                    'success': True,
                    'table': table,
                    'file_path': file_path,
                    'exported_rows': cursor.rowcount
                }
                
        except Exception as e:
            self.logger.error(f"COPY TO操作失败: {e}")
            raise
    
    def _listen(self, connection: Any, channel: str) -> Dict:
        """监听通知"""
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"LISTEN {channel}")
                connection.commit()
                
                return {
                    'success': True,
                    'channel': channel,
                    'status': 'listening'
                }
                
        except Exception as e:
            self.logger.error(f"LISTEN操作失败: {e}")
            raise
    
    def _notify(self, connection: Any, channel: str, payload: str = None) -> Dict:
        """发送通知"""
        try:
            with connection.cursor() as cursor:
                if payload:
                    cursor.execute("SELECT pg_notify(%s, %s)", (channel, payload))
                else:
                    cursor.execute("SELECT pg_notify(%s, '')", (channel,))
                
                connection.commit()
                
                return {
                    'success': True,
                    'channel': channel,
                    'payload': payload,
                    'status': 'sent'
                }
                
        except Exception as e:
            self.logger.error(f"NOTIFY操作失败: {e}")
            raise
    
    def _vacuum(self, connection: Any, table: str = None, full: bool = False) -> Dict:
        """执行VACUUM操作"""
        try:
            # VACUUM不能在事务中执行
            old_autocommit = connection.autocommit
            connection.autocommit = True
            
            try:
                with connection.cursor() as cursor:
                    if table:
                        vacuum_sql = f"VACUUM {'FULL' if full else ''} {table}"
                    else:
                        vacuum_sql = f"VACUUM {'FULL' if full else ''}"
                    
                    cursor.execute(vacuum_sql)
                    
                    return {
                        'success': True,
                        'operation': 'VACUUM',
                        'table': table,
                        'full': full
                    }
            finally:
                connection.autocommit = old_autocommit
                
        except Exception as e:
            self.logger.error(f"VACUUM操作失败: {e}")
            raise
    
    def _reindex(self, connection: Any, table: str = None, index: str = None) -> Dict:
        """执行REINDEX操作"""
        try:
            with connection.cursor() as cursor:
                if index:
                    reindex_sql = f"REINDEX INDEX {index}"
                elif table:
                    reindex_sql = f"REINDEX TABLE {table}"
                else:
                    reindex_sql = "REINDEX DATABASE CURRENT"
                
                cursor.execute(reindex_sql)
                connection.commit()
                
                return {
                    'success': True,
                    'operation': 'REINDEX',
                    'table': table,
                    'index': index
                }
                
        except Exception as e:
            self.logger.error(f"REINDEX操作失败: {e}")
            raise
    
    def _analyze_table(self, connection: Any, table: str = None) -> Dict:
        """执行ANALYZE操作"""
        try:
            with connection.cursor() as cursor:
                if table:
                    analyze_sql = f"ANALYZE {table}"
                else:
                    analyze_sql = "ANALYZE"
                
                cursor.execute(analyze_sql)
                connection.commit()
                
                return {
                    'success': True,
                    'operation': 'ANALYZE',
                    'table': table
                }
                
        except Exception as e:
            self.logger.error(f"ANALYZE操作失败: {e}")
            raise
