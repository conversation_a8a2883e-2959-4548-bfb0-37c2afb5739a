#!/usr/bin/env python3
"""
完整功能测试

验证线程安全版本的所有功能，包括修复后的JSONPath和文件处理
"""

import sys
import os
import threading
import time
import json
import tempfile
import concurrent.futures
from typing import Dict, Any, List

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.thread_safe_template_integration import ThreadSafeTemplateIntegration


def complete_feature_test():
    """完整功能测试"""
    print("=== 完整功能测试 ===")
    print("验证线程安全版本的所有功能（包括修复后的功能）")
    print("=" * 80)
    
    # 创建线程安全的模板引擎
    engine = ThreadSafeTemplateIntegration(
        enable_async=True,
        enable_debug=True,
        isolation_level='thread',
        cleanup_interval=60,
        max_scope_lifetime=300
    )
    
    # 测试结果收集
    test_results = {}
    
    # 🔍 测试1：修复后的JSONPath功能
    print("\n🔍 测试1：修复后的JSONPath功能")
    print("-" * 60)
    test_results['jsonpath'] = test_jsonpath_functionality(engine)
    
    # 📁 测试2：文件处理功能
    print("\n📁 测试2：文件处理功能")
    print("-" * 60)
    test_results['file_processing'] = test_file_processing(engine)
    
    # 🧠 测试3：智能数据处理
    print("\n🧠 测试3：智能数据处理")
    print("-" * 60)
    test_results['smart_data'] = test_smart_data_processing(engine)
    
    # 🧵 测试4：多线程安全性
    print("\n🧵 测试4：多线程安全性")
    print("-" * 60)
    test_results['thread_safety'] = test_thread_safety(engine)
    
    # ⚡ 测试5：异步功能
    print("\n⚡ 测试5：异步功能")
    print("-" * 60)
    test_results['async_functionality'] = test_async_functionality(engine)
    
    # 🔄 测试6：资源清理
    print("\n🔄 测试6：资源清理")
    print("-" * 60)
    test_results['resource_cleanup'] = test_resource_cleanup(engine)
    
    # 📊 生成测试报告
    print("\n📊 测试报告")
    print("=" * 80)
    generate_test_report(test_results)
    
    # 关闭引擎
    engine.shutdown()
    print("\n✅ 测试完成，引擎已关闭")
    
    return test_results


def test_jsonpath_functionality(engine) -> Dict[str, Any]:
    """测试JSONPath功能"""
    results = {'success': False, 'details': [], 'errors': []}
    
    try:
        # 复杂的测试数据
        test_data = {
            "company": {
                "name": "智慧科技",
                "employees": [
                    {"id": 1, "name": "张三", "department": "技术部", "active": True},
                    {"id": 2, "name": "李四", "department": "销售部", "active": False},
                    {"id": 3, "name": "王五", "department": "技术部", "active": True}
                ]
            }
        }
        
        template = """
JSONPath功能测试
===============

1. 基础路径查询:
公司名称: {{ sd.jsonpath(company, '$.name') }}
员工总数: {{ company.employees | length }}

2. 数组元素访问:
第一个员工: {{ sd.jsonpath(company, '$.employees[0].name') }}
第二个员工部门: {{ sd.jsonpath(company, '$.employees[1].department') }}

3. 复杂查询测试:
技术部员工: {{ sd.jsonpath(company, '$.employees[?(@.department=="技术部")]') | length if sd.jsonpath(company, '$.employees[?(@.department=="技术部")]') else 0 }}
活跃员工: {{ sd.jsonpath(company, '$.employees[?(@.active==true)]') | length if sd.jsonpath(company, '$.employees[?(@.active==true)]') else 0 }}

4. 路径存在性测试:
公司名称存在: {{ sd.jsonpath(company, '$.name') is not none }}
不存在路径: {{ sd.jsonpath(company, '$.nonexistent') is none }}
        """.strip()
        
        context = {'company': test_data['company']}
        result = engine.render_template_sync(template, context, 'jsonpath_test')
        
        # 验证结果
        if '智慧科技' in result:
            results['details'].append('✅ 基础路径查询正常')
        else:
            results['errors'].append('❌ 基础路径查询失败')
        
        if '张三' in result:
            results['details'].append('✅ 数组元素访问正常')
        else:
            results['errors'].append('❌ 数组元素访问失败')
        
        print("JSONPath测试结果:")
        print(result)
        
        results['success'] = len(results['errors']) == 0
        
    except Exception as e:
        results['errors'].append(f'JSONPath测试异常: {e}')
        import traceback
        traceback.print_exc()
    
    return results


def test_file_processing(engine) -> Dict[str, Any]:
    """测试文件处理功能"""
    results = {'success': False, 'details': [], 'errors': []}
    
    try:
        # 创建测试JSON文件
        test_data = {
            "products": [
                {"id": 1, "name": "笔记本电脑", "price": 8999, "category": "电子产品"},
                {"id": 2, "name": "无线鼠标", "price": 199, "category": "电子产品"},
                {"id": 3, "name": "机械键盘", "price": 599, "category": "电子产品"}
            ],
            "summary": {
                "total_products": 3,
                "total_value": 9797
            }
        }
        
        # 创建临时文件
        temp_file = tempfile.mktemp(suffix='.json')
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        # 使用正斜杠路径
        file_path = temp_file.replace('\\', '/')
        
        template = f"""
文件处理功能测试
===============

1. 文件数据加载:
{{%- set file_data = sd.file('{file_path}').parse() -%}}
产品总数: {{{{ file_data.summary.total_products }}}}
总价值: ¥{{{{ "{{:,}}".format(file_data.summary.total_value) }}}}

2. 产品列表:
{{%- for product in file_data.products -%}}
- {{{{ product.name }}}}: ¥{{{{ "{{:,}}".format(product.price) }}}}
{{%- endfor -%}}

3. 智能数据处理:
{{%- set smart_file_data = sd.smart_data(file_data) -%}}
智能处理: {{{{ smart_file_data.__class__.__name__ if smart_file_data else '基础类型' }}}}

4. 文件路径验证:
文件路径: {file_path}
处理状态: 成功
        """.strip()
        
        result = engine.render_template_sync(template, {}, 'file_test')
        
        # 验证结果
        if '产品总数: 3' in result:
            results['details'].append('✅ 文件数据加载正常')
        else:
            results['errors'].append('❌ 文件数据加载失败')
        
        if '笔记本电脑' in result:
            results['details'].append('✅ 文件内容解析正常')
        else:
            results['errors'].append('❌ 文件内容解析失败')
        
        print("文件处理测试结果:")
        print(result)
        
        results['success'] = len(results['errors']) == 0
        
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)
            
    except Exception as e:
        results['errors'].append(f'文件处理测试异常: {e}')
        import traceback
        traceback.print_exc()
    
    return results


def test_smart_data_processing(engine) -> Dict[str, Any]:
    """测试智能数据处理"""
    results = {'success': False, 'details': [], 'errors': []}
    
    try:
        # 复杂的嵌套数据
        complex_data = {
            "analytics": {
                "users": [
                    {
                        "id": 1,
                        "profile": {"name": "Alice", "age": 28},
                        "activity": {"logins": 156, "last_active": "2024-07-29"}
                    },
                    {
                        "id": 2,
                        "profile": {"name": "Bob", "age": 32},
                        "activity": {"logins": 89, "last_active": "2024-07-28"}
                    }
                ],
                "metrics": {
                    "total_users": 2,
                    "avg_logins": 122.5,
                    "active_today": 1
                }
            }
        }
        
        template = """
智能数据处理测试
===============

1. 基础数据访问:
用户总数: {{ analytics.metrics.total_users }}
平均登录次数: {{ analytics.metrics.avg_logins }}

2. 智能数据修改器:
{%- set smart_analytics = sd.smart_data(analytics) -%}
智能处理器: {{ smart_analytics.__class__.__name__ if smart_analytics else '基础类型' }}

3. 增强数据类型:
{%- set enhanced_analytics = sd.enhanced_data(analytics) -%}
增强类型: {{ enhanced_analytics.__class__.__name__ if enhanced_analytics else '基础类型' }}

4. 内存数据处理:
{%- set memory_data = sd.memory(analytics.users) -%}
内存处理器: {{ memory_data.__class__.__name__ if memory_data else '基础类型' }}

5. 用户详情:
{%- for user in analytics.users -%}
用户{{ user.id }}: {{ user.profile.name }} ({{ user.profile.age }}岁)
  - 登录次数: {{ user.activity.logins }}
  - 最后活跃: {{ user.activity.last_active }}
{%- endfor -%}
        """.strip()
        
        context = {'analytics': complex_data['analytics']}
        result = engine.render_template_sync(template, context, 'smart_data_test')
        
        # 验证结果
        if '用户总数: 2' in result:
            results['details'].append('✅ 基础数据访问正常')
        else:
            results['errors'].append('❌ 基础数据访问失败')
        
        if 'Alice' in result and 'Bob' in result:
            results['details'].append('✅ 复杂数据遍历正常')
        else:
            results['errors'].append('❌ 复杂数据遍历失败')
        
        print("智能数据处理测试结果:")
        print(result)
        
        results['success'] = len(results['errors']) == 0
        
    except Exception as e:
        results['errors'].append(f'智能数据处理测试异常: {e}')
        import traceback
        traceback.print_exc()
    
    return results


def test_thread_safety(engine) -> Dict[str, Any]:
    """测试多线程安全性"""
    results = {'success': False, 'details': [], 'errors': []}
    
    try:
        def worker_thread(worker_id: int, shared_results: List[str]):
            try:
                template = f"""
线程安全测试 - Worker {worker_id}
============================
工作线程: {worker_id}
线程名称: {{{{ thread_name }}}}
处理数据: {{{{ data.value }}}}
计算结果: {{{{ data.result }}}}
                """.strip()
                
                context = {
                    'thread_name': threading.current_thread().name,
                    'data': {
                        'value': worker_id * 100,
                        'result': worker_id * 100 + 42
                    }
                }
                
                result = engine.render_template_sync(
                    template, 
                    context, 
                    f'thread_safety_test_{worker_id}'
                )
                
                shared_results.append(f'✅ Worker{worker_id}成功')
                
            except Exception as e:
                shared_results.append(f'❌ Worker{worker_id}失败: {e}')
        
        # 启动多个线程
        threads = []
        shared_results = []
        
        for i in range(5):
            thread = threading.Thread(
                target=worker_thread,
                args=(i + 1, shared_results),
                name=f'TestWorker-{i + 1}'
            )
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 分析结果
        successful_threads = len([r for r in shared_results if r.startswith('✅')])
        
        if successful_threads == 5:
            results['details'].append('✅ 所有线程执行成功')
            results['success'] = True
        else:
            results['errors'].append(f'❌ 只有{successful_threads}/5个线程成功')
        
        print(f"线程安全测试结果: {successful_threads}/5 成功")
        for result in shared_results:
            print(f"  {result}")
            
    except Exception as e:
        results['errors'].append(f'线程安全测试异常: {e}')
        import traceback
        traceback.print_exc()
    
    return results


def test_async_functionality(engine) -> Dict[str, Any]:
    """测试异步功能"""
    results = {'success': False, 'details': [], 'errors': []}
    
    try:
        import asyncio
        
        async def async_test():
            template = """
异步功能测试
===========
任务ID: {{ task.id }}
开始时间: {{ task.start_time }}
数据处理: {{ task.data | length }}个项目
状态: {{ task.status }}
            """.strip()
            
            # 创建多个异步任务
            tasks = []
            for i in range(3):
                context = {
                    'task': {
                        'id': f'async_task_{i+1}',
                        'start_time': time.time(),
                        'data': [f'item_{j}' for j in range(i*2 + 1)],
                        'status': 'processing'
                    }
                }
                
                task = engine.render_template_async(
                    template, 
                    context,
                    f'async_test_{i+1}'
                )
                tasks.append(task)
            
            # 等待所有任务完成
            async_results = await asyncio.gather(*tasks)
            
            return len(async_results) == 3
        
        # 运行异步测试
        async_success = asyncio.run(async_test())
        
        if async_success:
            results['details'].append('✅ 异步渲染功能正常')
            results['success'] = True
        else:
            results['errors'].append('❌ 异步渲染功能失败')
        
        print(f"异步功能测试结果: {'成功' if async_success else '失败'}")
        
    except Exception as e:
        results['errors'].append(f'异步功能测试异常: {e}')
        import traceback
        traceback.print_exc()
    
    return results


def test_resource_cleanup(engine) -> Dict[str, Any]:
    """测试资源清理"""
    results = {'success': False, 'details': [], 'errors': []}
    
    try:
        # 创建多个作用域并测试清理
        scope_ids = []
        
        for i in range(10):
            template = f"""
资源清理测试 {i+1}
================
测试数据: {{{{ test_data }}}}
            """.strip()
            
            context = {'test_data': f'cleanup_test_{i+1}'}
            scope_id = f'cleanup_test_{i+1}'
            scope_ids.append(scope_id)
            
            # 使用作用域上下文管理器
            with engine.create_isolated_scope(scope_id) as scope:
                enhanced_context = engine._process_template_context(scope, context)
                
                # 简单渲染测试
                from jinja2 import Environment
                env = Environment()
                template_obj = env.from_string(template)
                result = template_obj.render(enhanced_context)
                
                # 作用域会在with块结束时自动清理
        
        results['details'].append('✅ 作用域自动清理正常')
        results['success'] = True
        
        print("资源清理测试结果: 成功")
        print(f"  创建并清理了{len(scope_ids)}个作用域")
        
    except Exception as e:
        results['errors'].append(f'资源清理测试异常: {e}')
        import traceback
        traceback.print_exc()
    
    return results


def generate_test_report(test_results: Dict[str, Dict[str, Any]]):
    """生成测试报告"""
    print("详细测试报告:")
    print("┌─────────────────────┬────────┬─────────┬─────────┐")
    print("│      测试项目       │  状态  │  成功   │  失败   │")
    print("├─────────────────────┼────────┼─────────┼─────────┤")
    
    total_tests = len(test_results)
    successful_tests = 0
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result['success'] else "❌ 失败"
        success_count = len(result.get('details', []))
        error_count = len(result.get('errors', []))
        
        if result['success']:
            successful_tests += 1
        
        print(f"│ {test_name:<19} │ {status} │   {success_count:2d}    │   {error_count:2d}    │")
    
    print("└─────────────────────┴────────┴─────────┴─────────┘")
    
    success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"\n总体测试结果:")
    print(f"  测试项目: {total_tests}")
    print(f"  成功项目: {successful_tests}")
    print(f"  成功率: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("  评级: 🏆 优秀")
    elif success_rate >= 80:
        print("  评级: ✅ 良好")
    elif success_rate >= 70:
        print("  评级: ⚠️ 一般")
    else:
        print("  评级: ❌ 需要改进")
    
    # 详细错误报告
    print("\n错误详情:")
    for test_name, result in test_results.items():
        if result.get('errors'):
            print(f"  {test_name}:")
            for error in result['errors']:
                print(f"    {error}")


if __name__ == "__main__":
    test_results = complete_feature_test()
    
    # 计算总体成功率
    total_tests = len(test_results)
    successful_tests = sum(1 for result in test_results.values() if result['success'])
    success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"\n🎯 最终结果: {success_rate:.1f}% 功能测试通过")
    
    if success_rate >= 90:
        print("🎉 线程安全版本功能完整，可以投入生产使用！")
    else:
        print("🔧 部分功能需要进一步优化")
