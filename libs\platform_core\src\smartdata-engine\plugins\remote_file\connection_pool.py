"""
统一连接池管理器

提供高效的连接池管理，包括：
- 多协议连接池
- 连接复用和管理
- 健康检查和故障恢复
- 负载均衡
- 连接监控
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum
import threading
from urllib.parse import urlparse

try:
    from ...core.smart_data_object import SmartDataObject
except ImportError:
    # 绝对导入作为后备
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from core.smart_data_object import SmartDataObject


class ConnectionStatus(Enum):
    """连接状态"""
    IDLE = "idle"
    ACTIVE = "active"
    ERROR = "error"
    CLOSED = "closed"


@dataclass
class ConnectionInfo:
    """连接信息"""
    connection_id: str
    protocol: str
    host: str
    port: int
    status: ConnectionStatus
    created_time: float
    last_used_time: float
    use_count: int
    error_count: int
    connection_object: Any = None
    
    @property
    def age(self) -> float:
        """连接年龄（秒）"""
        return time.time() - self.created_time
    
    @property
    def idle_time(self) -> float:
        """空闲时间（秒）"""
        return time.time() - self.last_used_time


@dataclass
class PoolStats:
    """连接池统计"""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    error_connections: int = 0
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return self.successful_requests / self.total_requests if self.total_requests > 0 else 0.0


class RemoteConnectionPool:
    """统一连接池"""

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.RemoteConnectionPool")
        
        # 连接池配置
        self.max_connections = self.config.get('max_connections', 50)
        self.max_connections_per_host = self.config.get('max_connections_per_host', 10)
        self.connection_timeout = self.config.get('connection_timeout', 30.0)
        self.idle_timeout = self.config.get('idle_timeout', 300.0)  # 5分钟
        self.max_retries = self.config.get('max_retries', 3)
        self.health_check_interval = self.config.get('health_check_interval', 60.0)  # 1分钟
        
        # 连接存储
        self.connections: Dict[str, ConnectionInfo] = {}
        self.host_connections: Dict[str, Set[str]] = {}  # host -> connection_ids
        
        # 统计信息
        self.stats = PoolStats()
        
        # 锁
        self._lock = threading.RLock()
        
        # 健康检查任务
        self._health_check_task = None
        # 延迟启动健康检查任务，避免在导入时启动
        # self._start_health_check()
    
    def _start_health_check(self):
        """启动健康检查任务"""
        if self._health_check_task is None:
            try:
                # 检查是否有运行的事件循环
                loop = asyncio.get_running_loop()
                self._health_check_task = loop.create_task(self._periodic_health_check())
            except RuntimeError:
                # 没有运行的事件循环，延迟启动
                self.logger.info("没有运行的事件循环，健康检查任务将在首次使用时启动")
    
    async def _periodic_health_check(self):
        """定期健康检查"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self.cleanup_idle_connections()
                await self.health_check_connections()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"健康检查失败: {e}")
    
    async def get_connection(self, url: str, protocol: str, auth_config: Dict[str, Any] = None) -> Optional[ConnectionInfo]:
        """获取连接"""
        parsed_url = urlparse(url)
        host_key = f"{parsed_url.hostname}:{parsed_url.port or self._get_default_port(protocol)}"
        
        with self._lock:
            # 查找可用的空闲连接
            if host_key in self.host_connections:
                for conn_id in self.host_connections[host_key]:
                    if conn_id in self.connections:
                        conn = self.connections[conn_id]
                        if conn.status == ConnectionStatus.IDLE and conn.error_count < self.max_retries:
                            # 标记为活跃
                            conn.status = ConnectionStatus.ACTIVE
                            conn.last_used_time = time.time()
                            conn.use_count += 1
                            self.stats.total_requests += 1
                            return conn
            
            # 检查是否可以创建新连接
            host_conn_count = len(self.host_connections.get(host_key, set()))
            if (len(self.connections) < self.max_connections and 
                host_conn_count < self.max_connections_per_host):
                
                # 创建新连接
                return await self._create_connection(url, protocol, auth_config)
            
            # 连接池已满，等待或返回None
            self.logger.warning(f"连接池已满，无法为{host_key}创建新连接")
            return None
    
    async def _create_connection(self, url: str, protocol: str, auth_config: Dict[str, Any] = None) -> Optional[ConnectionInfo]:
        """创建新连接"""
        try:
            parsed_url = urlparse(url)
            host_key = f"{parsed_url.hostname}:{parsed_url.port or self._get_default_port(protocol)}"
            
            # 生成连接ID
            connection_id = f"{protocol}_{host_key}_{int(time.time() * 1000)}"
            
            # 创建连接对象
            connection_object = await self._create_protocol_connection(protocol, parsed_url, auth_config)
            
            if connection_object:
                # 创建连接信息
                conn_info = ConnectionInfo(
                    connection_id=connection_id,
                    protocol=protocol,
                    host=parsed_url.hostname,
                    port=parsed_url.port or self._get_default_port(protocol),
                    status=ConnectionStatus.ACTIVE,
                    created_time=time.time(),
                    last_used_time=time.time(),
                    use_count=1,
                    error_count=0,
                    connection_object=connection_object
                )
                
                # 添加到连接池
                self.connections[connection_id] = conn_info
                
                if host_key not in self.host_connections:
                    self.host_connections[host_key] = set()
                self.host_connections[host_key].add(connection_id)
                
                # 更新统计
                self.stats.total_connections += 1
                self.stats.total_requests += 1
                
                self.logger.info(f"创建新连接: {connection_id}")
                return conn_info
            
        except Exception as e:
            self.logger.error(f"创建连接失败 {url}: {e}")
            self.stats.failed_requests += 1
        
        return None
    
    async def _create_protocol_connection(self, protocol: str, parsed_url, auth_config: Dict[str, Any] = None):
        """创建协议特定的连接对象"""
        if protocol.lower() in ['http', 'https']:
            # HTTP连接通常不需要持久连接对象，返回配置信息
            return {
                'type': 'http',
                'url': f"{protocol}://{parsed_url.hostname}:{parsed_url.port or self._get_default_port(protocol)}",
                'auth': auth_config
            }
        
        elif protocol.lower() in ['ftp', 'ftps']:
            try:
                import aioftp
                client = aioftp.Client()
                await client.connect(parsed_url.hostname, parsed_url.port or 21)
                
                if auth_config and auth_config.get('username'):
                    await client.login(auth_config['username'], auth_config.get('password', ''))
                else:
                    await client.login()
                
                return client
            except ImportError:
                self.logger.error("FTP连接需要安装aioftp")
                return None
        
        elif protocol.lower() == 'sftp':
            try:
                import paramiko
                ssh = paramiko.SSHClient()
                ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                
                if auth_config:
                    if auth_config.get('key_file'):
                        key = paramiko.RSAKey.from_private_key_file(auth_config['key_file'])
                        ssh.connect(
                            parsed_url.hostname,
                            port=parsed_url.port or 22,
                            username=auth_config.get('username'),
                            pkey=key,
                            timeout=self.connection_timeout
                        )
                    else:
                        ssh.connect(
                            parsed_url.hostname,
                            port=parsed_url.port or 22,
                            username=auth_config.get('username'),
                            password=auth_config.get('password'),
                            timeout=self.connection_timeout
                        )
                
                return ssh
            except ImportError:
                self.logger.error("SFTP连接需要安装paramiko")
                return None
        
        return None
    
    async def release_connection(self, connection_info: ConnectionInfo, success: bool = True):
        """释放连接"""
        with self._lock:
            if connection_info.connection_id in self.connections:
                conn = self.connections[connection_info.connection_id]
                
                if success:
                    conn.status = ConnectionStatus.IDLE
                    conn.error_count = 0
                    self.stats.successful_requests += 1
                else:
                    conn.error_count += 1
                    if conn.error_count >= self.max_retries:
                        conn.status = ConnectionStatus.ERROR
                    self.stats.failed_requests += 1
                
                conn.last_used_time = time.time()
    
    async def close_connection(self, connection_id: str):
        """关闭连接"""
        with self._lock:
            if connection_id in self.connections:
                conn = self.connections[connection_id]
                
                # 关闭连接对象
                try:
                    if conn.connection_object:
                        if hasattr(conn.connection_object, 'close'):
                            if asyncio.iscoroutinefunction(conn.connection_object.close):
                                await conn.connection_object.close()
                            else:
                                conn.connection_object.close()
                except Exception as e:
                    self.logger.error(f"关闭连接对象失败: {e}")
                
                # 从连接池移除
                del self.connections[connection_id]
                
                # 从主机连接集合移除
                host_key = f"{conn.host}:{conn.port}"
                if host_key in self.host_connections:
                    self.host_connections[host_key].discard(connection_id)
                    if not self.host_connections[host_key]:
                        del self.host_connections[host_key]
                
                # 更新统计
                self.stats.total_connections -= 1
                
                self.logger.info(f"关闭连接: {connection_id}")
    
    async def cleanup_idle_connections(self):
        """清理空闲连接"""
        current_time = time.time()
        idle_connections = []
        
        with self._lock:
            for conn_id, conn in self.connections.items():
                if (conn.status == ConnectionStatus.IDLE and 
                    current_time - conn.last_used_time > self.idle_timeout):
                    idle_connections.append(conn_id)
        
        for conn_id in idle_connections:
            await self.close_connection(conn_id)
        
        if idle_connections:
            self.logger.info(f"清理了{len(idle_connections)}个空闲连接")
    
    async def health_check_connections(self):
        """健康检查连接"""
        error_connections = []
        
        with self._lock:
            for conn_id, conn in self.connections.items():
                if conn.status == ConnectionStatus.ERROR:
                    error_connections.append(conn_id)
        
        for conn_id in error_connections:
            await self.close_connection(conn_id)
        
        if error_connections:
            self.logger.info(f"清理了{len(error_connections)}个错误连接")
    
    def _get_default_port(self, protocol: str) -> int:
        """获取协议默认端口"""
        default_ports = {
            'http': 80,
            'https': 443,
            'ftp': 21,
            'ftps': 990,
            'sftp': 22,
            'ssh': 22
        }
        return default_ports.get(protocol.lower(), 80)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计"""
        with self._lock:
            # 更新实时统计
            active_count = sum(1 for conn in self.connections.values() if conn.status == ConnectionStatus.ACTIVE)
            idle_count = sum(1 for conn in self.connections.values() if conn.status == ConnectionStatus.IDLE)
            error_count = sum(1 for conn in self.connections.values() if conn.status == ConnectionStatus.ERROR)
            
            self.stats.active_connections = active_count
            self.stats.idle_connections = idle_count
            self.stats.error_connections = error_count
            
            return {
                'total_connections': len(self.connections),
                'active_connections': active_count,
                'idle_connections': idle_count,
                'error_connections': error_count,
                'host_distribution': {
                    host: len(conn_ids) 
                    for host, conn_ids in self.host_connections.items()
                },
                'success_rate': self.stats.success_rate,
                'total_requests': self.stats.total_requests,
                'successful_requests': self.stats.successful_requests,
                'failed_requests': self.stats.failed_requests
            }
    
    async def close(self):
        """关闭连接池"""
        # 停止健康检查任务
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # 关闭所有连接
        connection_ids = list(self.connections.keys())
        for conn_id in connection_ids:
            await self.close_connection(conn_id)
        
        self.logger.info("连接池已关闭")


# 全局连接池实例
global_connection_pool = RemoteConnectionPool()

# 企业级兼容接口
EnterpriseConnectionPool = RemoteConnectionPool
