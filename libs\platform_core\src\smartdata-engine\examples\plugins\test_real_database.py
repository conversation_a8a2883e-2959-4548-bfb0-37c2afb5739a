#!/usr/bin/env python3
"""
测试真实数据库连接
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

def test_real_database():
    """测试真实数据库连接"""
    print("=== 测试真实数据库连接 ===")
    
    try:
        from core.unified_connectors import UnifiedDatabaseConnector
        
        # 测试SQLite连接
        print("\n1. 测试SQLite连接")
        sqlite_connector = UnifiedDatabaseConnector("sqlite:///:memory:")
        result = sqlite_connector.query("SELECT 'Hello SQLite' as message")
        print(f"SQLite结果: {result.to_dict()}")
        
        # 测试PostgreSQL连接
        print("\n2. 测试PostgreSQL连接")
        postgres_connector = UnifiedDatabaseConnector("postgresql://admin:admin123@localhost:5432/nacos_db")
        result = postgres_connector.query("SELECT version() as db_version, current_database() as db_name")
        print(f"PostgreSQL结果: {result.to_dict()}")
        
        if not result.success:
            print(f"PostgreSQL连接失败原因: {result.error}")
            
            # 尝试测试连接参数
            print("\n3. 测试PostgreSQL连接参数")
            try:
                import psycopg2
                conn_params = {
                    'host': 'localhost',
                    'port': 5432,
                    'database': 'nacos_db',
                    'user': 'admin',
                    'password': 'admin123'
                }
                
                print(f"尝试连接参数: {conn_params}")
                with psycopg2.connect(**conn_params) as conn:
                    print("✅ PostgreSQL连接成功")
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT version()")
                        version = cursor.fetchone()
                        print(f"数据库版本: {version[0]}")
                        
            except ImportError:
                print("❌ psycopg2未安装")
            except Exception as e:
                print(f"❌ PostgreSQL连接失败: {e}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_real_database()
