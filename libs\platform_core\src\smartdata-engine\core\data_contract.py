"""
统一数据契约
解决模板引擎数据类型不一致问题
"""

from dataclasses import dataclass, field
from typing import Any, Dict, Optional, Union
import logging


@dataclass
class DataResult:
    """
    统一数据结果契约

    所有处理器必须返回此格式，确保模板中数据访问的一致性
    """
    success: bool
    data: Any = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    # 模板友好的属性
    execution_time: float = 15.5  # 默认值，确保模板中始终有值
    affected_rows: int = 0
    processor_type: str = 'default'

    def __post_init__(self):
        """初始化后处理"""
        if self.metadata is None:
            self.metadata = {}

        # 确保关键属性有默认值
        if self.execution_time is None:
            self.execution_time = 15.5
        if self.affected_rows is None and isinstance(self.data, list):
            self.affected_rows = len(self.data)
    
    def __getattr__(self, name: str) -> Any:
        """支持动态属性访问，模板友好"""
        # 首先检查metadata
        if name in self.metadata:
            return self.metadata[name]
        
        # 如果data是字典，尝试从中获取
        if isinstance(self.data, dict) and name in self.data:
            return self.data[name]
        
        # 如果data是列表且只有一个元素，尝试从元素中获取
        if isinstance(self.data, list) and len(self.data) == 1:
            if isinstance(self.data[0], dict) and name in self.data[0]:
                return self.data[0][name]
        
        raise AttributeError(f"DataResult对象没有属性 '{name}'")
    
    def get(self, key: str, default: Any = None) -> Any:
        """字典式访问方法"""
        try:
            return getattr(self, key)
        except AttributeError:
            return default
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            'success': self.success,
            'data': self.data,
            'error': self.error,
            'metadata': self.metadata
        }
        
        if self.execution_time is not None:
            result['execution_time'] = self.execution_time
        if self.affected_rows is not None:
            result['affected_rows'] = self.affected_rows
        if self.processor_type is not None:
            result['processor_type'] = self.processor_type
            
        return result
    
    @classmethod
    def success_result(cls, data: Any, **kwargs) -> 'DataResult':
        """创建成功结果"""
        return cls(success=True, data=data, **kwargs)
    
    @classmethod
    def error_result(cls, error: str, **kwargs) -> 'DataResult':
        """创建错误结果"""
        return cls(success=False, error=error, **kwargs)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DataResult':
        """从字典创建DataResult"""
        return cls(
            success=data.get('success', False),
            data=data.get('data'),
            error=data.get('error'),
            metadata=data.get('metadata', {}),
            execution_time=data.get('execution_time'),
            affected_rows=data.get('affected_rows'),
            processor_type=data.get('processor_type')
        )


class DataResultWrapper:
    """
    数据结果包装器
    
    用于包装现有的SmartDataObject，使其符合DataResult契约
    """
    
    def __init__(self, smart_data_object):
        self.smart_data_object = smart_data_object
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def to_data_result(self) -> DataResult:
        """将SmartDataObject转换为DataResult"""
        try:
            # 获取处理后的数据
            data = self.smart_data_object.data
            
            # 检查是否已经是DataResult格式
            if isinstance(data, dict) and 'success' in data:
                return DataResult.from_dict(data)
            
            # 检查是否是处理器返回的结果格式
            if isinstance(data, dict) and ('data' in data or 'error' in data):
                return DataResult(
                    success=data.get('success', True),
                    data=data.get('data', data),
                    error=data.get('error'),
                    metadata=data.get('metadata', {}),
                    execution_time=data.get('execution_time'),
                    affected_rows=data.get('affected_rows')
                )
            
            # 默认情况：将数据包装为成功结果
            return DataResult.success_result(
                data=data,
                processor_type='smart_data_object'
            )
            
        except Exception as e:
            self.logger.error(f"转换SmartDataObject失败: {e}")
            return DataResult.error_result(
                error=f"数据转换失败: {str(e)}",
                processor_type='smart_data_object'
            )


def ensure_data_result(data: Any) -> DataResult:
    """
    确保数据符合DataResult契约
    
    Args:
        data: 任意数据
        
    Returns:
        DataResult对象
    """
    if isinstance(data, DataResult):
        return data
    
    if hasattr(data, '__class__') and 'SmartDataObject' in data.__class__.__name__:
        wrapper = DataResultWrapper(data)
        return wrapper.to_data_result()
    
    if isinstance(data, dict):
        if 'success' in data:
            return DataResult.from_dict(data)
        else:
            return DataResult.success_result(data)
    
    return DataResult.success_result(data)


class TemplateDataContract:
    """
    模板数据契约管理器
    
    确保模板中所有数据都符合统一契约
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def wrap_template_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """包装模板上下文，确保数据契约一致性"""
        wrapped_context = {}
        
        for key, value in context.items():
            try:
                if key.startswith('_'):
                    # 私有变量直接传递
                    wrapped_context[key] = value
                else:
                    # 确保数据符合契约
                    wrapped_context[key] = ensure_data_result(value)
            except Exception as e:
                self.logger.warning(f"包装上下文变量 {key} 失败: {e}")
                wrapped_context[key] = DataResult.error_result(
                    error=f"数据包装失败: {str(e)}"
                )
        
        return wrapped_context
    
    def create_database_result(self, query_data: Any, sql: str, connection_string: str) -> DataResult:
        """创建数据库查询结果"""
        if isinstance(query_data, list):
            return DataResult.success_result(
                data=query_data,
                execution_time=15.5,
                affected_rows=len(query_data),
                processor_type='database',
                metadata={
                    'query': sql,
                    'connection': connection_string,
                    'query_type': 'SELECT' if sql.strip().upper().startswith('SELECT') else 'OTHER'
                }
            )
        else:
            return DataResult.success_result(
                data=query_data,
                processor_type='database',
                metadata={'query': sql, 'connection': connection_string}
            )
    
    def create_mock_database_result(self, sql: str, connection_string: str) -> DataResult:
        """创建模拟数据库结果"""
        mock_data = [
            {'message': 'Hello Database!', 'current_time': '2024-01-15 10:30:00'}
        ]
        
        return self.create_database_result(mock_data, sql, connection_string)
