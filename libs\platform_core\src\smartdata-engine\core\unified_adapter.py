"""
统一同步/异步数据适配器

提供同时支持同步和异步操作的统一适配器，智能选择最佳执行模式
"""

import asyncio
import time
import logging
from typing import Any, Dict, List, Optional, Callable, Awaitable, Union
from abc import abstractmethod

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../'))

from core.enterprise_data_architecture import IDataAdapter, DataResult
from core.async_interfaces import IAsyncDataAdapter, AsyncContextManager, AsyncPerformanceMonitor
from core.adapters.base import BaseDataAdapter


class UnifiedDataAdapter(BaseDataAdapter, IAsyncDataAdapter):
    """
    统一数据适配器
    
    同时支持同步和异步操作，具有以下特性：
    - 智能模式选择：自动检测执行环境
    - 向后兼容：现有同步代码无需修改
    - 性能优化：异步环境下自动启用并行处理
    - 连接管理：统一的连接和连接池管理
    """
    
    def __init__(self):
        super().__init__()
        self.async_performance_monitor = AsyncPerformanceMonitor()
        self._async_connection_pools = {}
        self._async_connections = {}
        self.logger = logging.getLogger(self.__class__.__name__)
    
    # ========================================================================
    # 智能模式选择
    # ========================================================================
    
    def _detect_async_capability(self, connection: Any) -> bool:
        """检测连接是否支持异步"""
        return (hasattr(connection, '__aenter__') or 
                'async' in str(type(connection)).lower() or
                hasattr(connection, 'execute_async') or
                hasattr(connection, 'fetch_async'))
    
    def _in_async_context(self) -> bool:
        """检测是否在异步上下文中"""
        return AsyncContextManager.is_async_context()
    
    def smart_query(self, connection: Any, sql: str, params: Dict = None):
        """
        智能查询 - 自动选择同步或异步模式
        
        Args:
            connection: 数据库连接
            sql: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果或协程对象
        """
        if self._in_async_context() and self._detect_async_capability(connection):
            return self.async_query(connection, sql, params)
        else:
            return self.query(connection, sql, params)
    
    def smart_execute(self, connection: Any, sql: str, params: Dict = None):
        """
        智能执行 - 自动选择同步或异步模式
        
        Args:
            connection: 数据库连接
            sql: SQL执行语句
            params: 执行参数
            
        Returns:
            执行结果或协程对象
        """
        if self._in_async_context() and self._detect_async_capability(connection):
            return self.async_execute(connection, sql, params)
        else:
            return self.execute(connection, sql, params)
    
    # ========================================================================
    # 同步方法实现（继承自BaseDataAdapter）
    # ========================================================================
    
    def query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """同步查询操作"""
        return self._sync_query(connection, sql, params)
    
    def execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """同步执行操作"""
        return self._sync_execute(connection, sql, params)
    
    def transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """同步事务操作"""
        return self._sync_transaction(connection, operations)
    
    # ========================================================================
    # 异步方法实现
    # ========================================================================
    
    async def async_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """异步查询操作"""
        operation_name = 'async_query'
        
        async def _execute():
            return await self._async_query(connection, sql, params)
        
        return await self.async_performance_monitor.monitor_operation(operation_name, _execute())
    
    async def async_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """异步执行操作"""
        operation_name = 'async_execute'
        
        async def _execute():
            return await self._async_execute(connection, sql, params)
        
        return await self.async_performance_monitor.monitor_operation(operation_name, _execute())
    
    async def async_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步事务操作"""
        operation_name = 'async_transaction'
        
        async def _execute():
            return await self._async_transaction(connection, operations)
        
        return await self.async_performance_monitor.monitor_operation(operation_name, _execute())
    
    async def async_batch(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步批量操作"""
        operation_name = 'async_batch'
        
        async def _execute():
            return await self._async_batch(connection, operations)
        
        return await self.async_performance_monitor.monitor_operation(operation_name, _execute())
    
    async def async_stream_query(self, connection: Any, sql: str, params: Dict = None):
        """异步流式查询"""
        async for row in self._async_stream_query(connection, sql, params):
            yield row
    
    # ========================================================================
    # 连接管理
    # ========================================================================
    
    async def create_async_connection(self, connection_source: Any) -> Any:
        """创建异步连接"""
        return await self._create_async_connection(connection_source)
    
    async def close_async_connection(self, connection: Any) -> None:
        """关闭异步连接"""
        await self._close_async_connection(connection)
    
    async def create_async_pool(self, connection_source: Any, min_size: int = 5, max_size: int = 20) -> Any:
        """创建异步连接池"""
        pool_key = str(connection_source)
        
        if pool_key not in self._async_connection_pools:
            self._async_connection_pools[pool_key] = await self._create_async_pool(
                connection_source, min_size, max_size
            )
        
        return self._async_connection_pools[pool_key]
    
    def get_async_operations(self) -> Dict[str, Callable[..., Awaitable]]:
        """获取支持的异步操作列表"""
        return {
            'async_query': self.async_query,
            'async_execute': self.async_execute,
            'async_transaction': self.async_transaction,
            'async_batch': self.async_batch,
            'async_stream_query': self.async_stream_query,
            'smart_query': self.smart_query,
            'smart_execute': self.smart_execute,
        }
    
    # ========================================================================
    # 并行操作支持
    # ========================================================================
    
    async def parallel_queries(self, connection: Any, queries: List[Dict]) -> List[List[Dict]]:
        """并行执行多个查询"""
        tasks = []
        for query in queries:
            task = self.async_query(connection, query['sql'], query.get('params'))
            tasks.append(task)
        
        return await asyncio.gather(*tasks)
    
    async def parallel_executions(self, connection: Any, commands: List[Dict]) -> List[int]:
        """并行执行多个命令"""
        tasks = []
        for command in commands:
            task = self.async_execute(connection, command['sql'], command.get('params'))
            tasks.append(task)
        
        return await asyncio.gather(*tasks)
    
    # ========================================================================
    # 抽象方法 - 子类必须实现
    # ========================================================================
    
    @abstractmethod
    def _sync_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """同步查询实现"""
        pass
    
    @abstractmethod
    def _sync_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """同步执行实现"""
        pass
    
    @abstractmethod
    def _sync_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """同步事务实现"""
        pass
    
    @abstractmethod
    async def _async_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """异步查询实现"""
        pass
    
    @abstractmethod
    async def _async_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """异步执行实现"""
        pass
    
    @abstractmethod
    async def _async_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步事务实现"""
        pass
    
    @abstractmethod
    async def _async_batch(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步批量操作实现"""
        pass
    
    @abstractmethod
    async def _async_stream_query(self, connection: Any, sql: str, params: Dict = None):
        """异步流式查询实现"""
        pass
    
    @abstractmethod
    async def _create_async_connection(self, connection_source: Any) -> Any:
        """创建异步连接实现"""
        pass
    
    @abstractmethod
    async def _close_async_connection(self, connection: Any) -> None:
        """关闭异步连接实现"""
        pass
    
    @abstractmethod
    async def _create_async_pool(self, connection_source: Any, min_size: int, max_size: int) -> Any:
        """创建异步连接池实现"""
        pass
    
    # ========================================================================
    # 性能监控和统计
    # ========================================================================
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        sync_stats = super().get_metadata()
        async_stats = self.async_performance_monitor.get_performance_stats()
        
        return {
            'adapter_type': 'UnifiedDataAdapter',
            'sync_operations': sync_stats,
            'async_operations': async_stats,
            'connection_pools': len(self._async_connection_pools),
            'async_connections': len(self._async_connections),
            'supports_async': True,
            'supports_sync': True
        }
    
    def reset_performance_stats(self) -> None:
        """重置性能统计"""
        self.async_performance_monitor.reset_stats()
    
    # ========================================================================
    # 资源清理
    # ========================================================================
    
    async def cleanup_async_resources(self) -> None:
        """清理所有异步资源"""
        # 关闭连接池
        for pool in self._async_connection_pools.values():
            if hasattr(pool, 'close'):
                await pool.close()
        
        self._async_connection_pools.clear()
        
        # 关闭连接
        for connection in self._async_connections.values():
            await self._close_async_connection(connection)
        
        self._async_connections.clear()
        
        self.logger.info("异步资源清理完成")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.cleanup_async_resources()


class SmartDataProxy:
    """
    智能数据代理
    
    自动选择同步或异步执行模式，提供统一的操作接口
    """
    
    def __init__(self, source: Any, adapter: UnifiedDataAdapter, lifecycle_manager=None):
        self.source = source
        self.adapter = adapter
        self.lifecycle_manager = lifecycle_manager
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 检测异步能力
        self.is_async_capable = adapter._detect_async_capability(source)
    
    def query(self, sql: str, params: Dict = None):
        """智能查询 - 自动选择最佳模式"""
        if self.is_async_capable and self.adapter._in_async_context():
            return self.async_query(sql, params)
        else:
            result = self.adapter.query(self.source, sql, params)
            return self._wrap_result(result, 'query')
    
    async def async_query(self, sql: str, params: Dict = None) -> DataResult:
        """异步查询"""
        try:
            result = await self.adapter.async_query(self.source, sql, params)
            return self._wrap_result(result, 'async_query')
        except Exception as e:
            return DataResult(
                success=False,
                error=str(e),
                operation='async_query',
                adapter_type=type(self.adapter).__name__
            )
    
    def execute(self, sql: str, params: Dict = None):
        """智能执行 - 自动选择最佳模式"""
        if self.is_async_capable and self.adapter._in_async_context():
            return self.async_execute(sql, params)
        else:
            result = self.adapter.execute(self.source, sql, params)
            return self._wrap_result(result, 'execute', affected_rows=result)
    
    async def async_execute(self, sql: str, params: Dict = None) -> DataResult:
        """异步执行"""
        try:
            result = await self.adapter.async_execute(self.source, sql, params)
            return self._wrap_result(result, 'async_execute', affected_rows=result)
        except Exception as e:
            return DataResult(
                success=False,
                error=str(e),
                operation='async_execute',
                adapter_type=type(self.adapter).__name__
            )
    
    def _wrap_result(self, data: Any, operation: str, **kwargs) -> DataResult:
        """包装结果为DataResult"""
        return DataResult(
            success=True,
            data=data,
            operation=operation,
            adapter_type=type(self.adapter).__name__,
            **kwargs
        )
