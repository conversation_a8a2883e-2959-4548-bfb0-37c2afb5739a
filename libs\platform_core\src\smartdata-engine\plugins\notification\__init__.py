"""
Notification插件

提供企业级通知处理能力，包括：
- 多渠道通知（邮件、短信、推送、Webhook）
- 通知模板和调度
- 批量通知
- 通知队列
- 失败重试
- 统计分析
"""

# 插件定义 - 按照统一标准
PLUGIN_DEFINITIONS = [
    {
        'id': 'notification_processor',
        'name': '通知处理器',
        'description': '企业级多渠道通知处理器，支持邮件、短信、推送等多种通知方式',
        'version': '1.0.0',
        'type': 'processor',
        'category': 'notification',
        'priority': 75,
        'class_name': 'NotificationProcessor',
        'module_file': 'notification_processor',
        'auto_load': True,
        'enabled': True,
        'capabilities': [
            'multi_channel_notification',
            'email_notification',
            'sms_notification',
            'push_notification',
            'webhook_notification',
            'template_rendering',
            'batch_notification',
            'notification_queue',
            'retry_mechanism',
            'delivery_tracking',
            'notification_scheduling',
            'priority_handling',
            'rate_limiting',
            'analytics_reporting'
        ],
        'supported_channels': [
            'email', 'sms', 'push', 'webhook', 'slack', 'teams',
            'telegram', 'wechat', 'dingtalk'
        ],
        'supported_operations': [
            'send', 'send_batch', 'schedule', 'cancel',
            'get_status', 'get_analytics', 'test_channel'
        ],
        'dependencies': [],
        'optional_dependencies': [
            'aiosmtplib', 'twilio', 'firebase-admin', 'requests',
            'celery', 'redis', 'jinja2', 'pydantic'
        ],
        'author': 'SmartData Team',
        'license': 'MIT',
        'tags': ['notification', 'email', 'sms', 'push', 'webhook']
    }
]


def get_plugin_definitions():
    """获取插件定义"""
    return PLUGIN_DEFINITIONS


try:
    # Notification处理器
    from .notification_processor import NotificationProcessor
    
    # 通知渠道
    from .notification_channels import (
        NotificationChannelFactory,
        EmailChannel,
        SMSChannel,
        PushChannel,
        WebhookChannel
    )
    
    # 通知模板
    from .notification_templates import (
        NotificationTemplateEngine,
        NotificationTemplate
    )
    
    # 通知队列
    from .notification_queue import (
        NotificationQueue,
        NotificationScheduler
    )

    __all__ = [
        # 处理器
        'NotificationProcessor',
        
        # 渠道
        'NotificationChannelFactory',
        'EmailChannel',
        'SMSChannel',
        'PushChannel',
        'WebhookChannel',
        
        # 模板
        'NotificationTemplateEngine',
        'NotificationTemplate',
        
        # 队列
        'NotificationQueue',
        'NotificationScheduler',
        
        # 插件定义
        'PLUGIN_DEFINITIONS',
        'get_plugin_definitions'
    ]

except ImportError as e:
    # 优雅处理导入错误
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"Notification插件部分功能导入失败: {e}")
    
    __all__ = ['PLUGIN_DEFINITIONS', 'get_plugin_definitions']
