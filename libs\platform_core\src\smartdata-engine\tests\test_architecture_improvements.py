#!/usr/bin/env python3
"""
架构改进验证测试

验证DataResult包装问题的修复和智能数据源识别
"""

import sys
import os
import unittest
import tempfile
import json

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from template.enterprise_template_integration import EnterpriseTemplateIntegration
from template.enterprise_template_factory import EnterpriseTemplateFactory


class TestArchitectureImprovements(unittest.TestCase):
    """架构改进验证测试"""
    
    def setUp(self):
        """测试前准备"""
        self.integration = EnterpriseTemplateIntegration(
            enable_async=False,
            enable_legacy_support=False,
            enable_debug=False
        )
    
    def tearDown(self):
        """测试后清理"""
        # 清理所有模板作用域
        for template_id in list(self.integration.template_scopes.keys()):
            self.integration.cleanup_template_scope(template_id)
    
    def test_basic_data_types_not_wrapped(self):
        """测试基本数据类型不被包装"""
        template = """
基本数据类型测试:
- 字符串: {{ name }}
- 数字: {{ age }}
- 布尔值: {{ active }}
- 浮点数: {{ score }}
- 空值: {{ empty }}
        """.strip()
        
        context = {
            'name': 'Alice',
            'age': 30,
            'active': True,
            'score': 95.5,
            'empty': None
        }
        
        result = self.integration.render_template_sync(template, context)
        
        # 验证渲染结果不包含DataResult字样
        self.assertNotIn('DataResult', result)
        self.assertIn('Alice', result)
        self.assertIn('30', result)
        self.assertIn('True', result)
        self.assertIn('95.5', result)
    
    def test_math_operations_work(self):
        """测试数学运算正常工作"""
        template = """
数学运算测试:
- 加法: {{ a + b }}
- 减法: {{ a - b }}
- 乘法: {{ a * b }}
- 除法: {{ a / b }}
- 整除: {{ a // b }}
- 取模: {{ a % b }}
        """.strip()
        
        context = {
            'a': 100,
            'b': 3
        }
        
        result = self.integration.render_template_sync(template, context)
        
        # 验证数学运算结果
        self.assertIn('103', result)  # 100 + 3
        self.assertIn('97', result)   # 100 - 3
        self.assertIn('300', result)  # 100 * 3
        self.assertIn('33', result)   # 100 // 3
        self.assertIn('1', result)    # 100 % 3
    
    def test_list_iteration_works(self):
        """测试列表迭代正常工作"""
        template = """
列表迭代测试:
{%- for item in items %}
- {{ item.name }}: {{ item.value }}
{%- endfor %}

总数: {{ items | length }}
        """.strip()
        
        context = {
            'items': [
                {'name': '项目A', 'value': 100},
                {'name': '项目B', 'value': 200},
                {'name': '项目C', 'value': 300}
            ]
        }
        
        result = self.integration.render_template_sync(template, context)
        
        # 验证迭代结果
        self.assertIn('项目A: 100', result)
        self.assertIn('项目B: 200', result)
        self.assertIn('项目C: 300', result)
        self.assertIn('总数: 3', result)
    
    def test_dict_access_works(self):
        """测试字典访问正常工作"""
        template = """
字典访问测试:
- 用户名: {{ user.name }}
- 年龄: {{ user.age }}
- 地址: {{ user.address.city }}
- 技能数量: {{ user.skills | length }}
        """.strip()
        
        context = {
            'user': {
                'name': 'Bob',
                'age': 25,
                'address': {
                    'city': '北京',
                    'district': '朝阳区'
                },
                'skills': ['Python', 'JavaScript', 'SQL']
            }
        }
        
        result = self.integration.render_template_sync(template, context)
        
        # 验证字典访问结果
        self.assertIn('用户名: Bob', result)
        self.assertIn('年龄: 25', result)
        self.assertIn('地址: 北京', result)
        self.assertIn('技能数量: 3', result)
    
    def test_data_source_identification(self):
        """测试数据源识别"""
        # 测试数据库连接字符串识别
        self.assertTrue(self.integration._is_data_source('sqlite:///test.db'))
        self.assertTrue(self.integration._is_data_source('******************************'))
        self.assertTrue(self.integration._is_data_source('mysql://user:pass@host/db'))
        
        # 测试文件路径识别
        self.assertTrue(self.integration._is_data_source('data.csv'))
        self.assertTrue(self.integration._is_data_source('config.json'))
        self.assertTrue(self.integration._is_data_source('report.xml'))
        
        # 测试API配置识别
        api_config = {
            'base_url': 'https://api.example.com',
            'headers': {'Authorization': 'Bearer token'}
        }
        self.assertTrue(self.integration._is_data_source(api_config))
        
        # 测试普通数据不被识别为数据源
        self.assertFalse(self.integration._is_data_source('hello world'))
        self.assertFalse(self.integration._is_data_source(123))
        self.assertFalse(self.integration._is_data_source([1, 2, 3]))
        self.assertFalse(self.integration._is_data_source({'name': 'Alice', 'age': 30}))
    
    def test_data_source_vs_regular_data(self):
        """测试数据源和普通数据的区别处理"""
        template = """
混合数据测试:
- 普通字符串: {{ message }}
- 普通数组长度: {{ items | length }}
- 数据源状态: {{ db_status }}
        """.strip()
        
        # 创建临时CSV文件作为数据源
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write('id,name\n1,test\n')
            csv_file = f.name
        
        try:
            context = {
                'message': '这是普通字符串',  # 普通数据
                'items': [1, 2, 3, 4, 5],    # 普通数据
                'csv_data': csv_file          # 数据源
            }
            
            # 验证数据源识别
            self.assertFalse(self.integration._is_data_source(context['message']))
            self.assertFalse(self.integration._is_data_source(context['items']))
            self.assertTrue(self.integration._is_data_source(context['csv_data']))
            
            # 简化的模板测试
            simple_template = "消息: {{ message }}, 项目数: {{ items | length }}"
            simple_context = {
                'message': context['message'],
                'items': context['items']
            }
            
            result = self.integration.render_template_sync(simple_template, simple_context)
            
            # 验证普通数据正常渲染
            self.assertIn('消息: 这是普通字符串', result)
            self.assertIn('项目数: 5', result)
            self.assertNotIn('DataResult', result)
            
        finally:
            if os.path.exists(csv_file):
                os.unlink(csv_file)
    
    def test_complex_nested_data_handling(self):
        """测试复杂嵌套数据处理"""
        template = """
复杂数据测试:
- 公司: {{ company.name }}
- 员工数: {{ company.employees | length }}
- 第一个员工: {{ company.employees[0].name }}
- 部门数: {{ company.departments | length }}
        """.strip()
        
        context = {
            'company': {
                'name': '科技公司',
                'employees': [
                    {'name': '张三', 'department': '技术部'},
                    {'name': '李四', 'department': '销售部'},
                    {'name': '王五', 'department': '技术部'}
                ],
                'departments': ['技术部', '销售部', '市场部']
            }
        }
        
        result = self.integration.render_template_sync(template, context)
        
        # 验证复杂数据访问
        self.assertIn('公司: 科技公司', result)
        self.assertIn('员工数: 3', result)
        self.assertIn('第一个员工: 张三', result)
        self.assertIn('部门数: 3', result)
        self.assertNotIn('DataResult', result)
    
    def test_enterprise_template_factory_integration(self):
        """测试企业级模板引擎工厂集成"""
        engine = EnterpriseTemplateFactory.create_engine()
        
        template = "计算结果: {{ (a * b) + c }}"
        context = {'a': 10, 'b': 5, 'c': 3}
        
        result = engine.render(template, context)
        
        # 验证计算结果
        self.assertIn('计算结果: 53', result)  # (10 * 5) + 3 = 53
        self.assertNotIn('DataResult', result)
        
        engine.cleanup()


def main():
    """运行架构改进验证测试"""
    print("🧪 运行架构改进验证测试...")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestArchitectureImprovements)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 打印结果
    if result.wasSuccessful():
        print("\n✅ 所有架构改进验证测试通过！")
        print("🎉 DataResult包装问题已修复")
        print("🎯 智能数据源识别正常工作")
        print("🚀 Jinja2模板语法完全兼容")
    else:
        print(f"\n❌ {len(result.failures)} 个测试失败")
        print(f"❌ {len(result.errors)} 个测试错误")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
