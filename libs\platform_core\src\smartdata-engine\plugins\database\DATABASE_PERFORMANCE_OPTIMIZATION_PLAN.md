# 🚀 数据库插件性能优化方案

## 📊 当前性能问题分析

### 🔍 **问题识别**

1. **模板级连接重复创建**
   - 每个模板调用都会创建新的数据库连接
   - 无连接复用，造成资源浪费
   - 连接建立/销毁开销大

2. **缺乏全局连接池管理**
   - 没有跨模板的连接池共享
   - 无法根据数据库实例+环境进行连接管理
   - 连接数无法有效控制

3. **模板作用域内连接管理不当**
   - 同一模板内多次数据库调用重复连接
   - 缺少模板级别的连接单例模式

## 🎯 优化目标

1. **减少连接开销**: 通过连接池和单例模式减少90%的连接创建
2. **提升并发性能**: 支持高并发模板渲染
3. **资源优化**: 合理控制连接数，避免数据库连接耗尽
4. **智能管理**: 基于数据库实例+环境的智能连接管理

## 🏗️ 优化架构设计

### 1. **三层连接管理架构**

```
┌─────────────────────────────────────────────────────────┐
│                模板作用域层                              │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │  模板连接单例    │  │  模板连接单例    │              │
│  │  (Template A)   │  │  (Template B)   │              │
│  └─────────────────┘  └─────────────────┘              │
└─────────────────────────────────────────────────────────┘
                          ↓
┌─────────────────────────────────────────────────────────┐
│                执行主机全局层                            │
│  ┌─────────────────────────────────────────────────────┐│
│  │           全局连接池管理器                           ││
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ ││
│  │  │ 连接池 A     │  │ 连接池 B     │  │ 连接池 C     │ ││
│  │  │prod-mysql   │  │test-postgres │  │dev-redis    │ ││
│  │  └─────────────┘  └─────────────┘  └─────────────┘ ││
│  └─────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────┘
                          ↓
┌─────────────────────────────────────────────────────────┐
│                数据库连接层                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │   MySQL     │  │ PostgreSQL  │  │    Redis    │      │
│  │   Server    │  │   Server    │  │   Server    │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
└─────────────────────────────────────────────────────────┘
```

### 2. **核心组件设计**

#### A. 全局连接池管理器
```python
class GlobalConnectionPoolManager:
    """全局连接池管理器"""
    
    def __init__(self):
        self._pools: Dict[str, ConnectionPool] = {}
        self._lock = asyncio.Lock()
        self._pool_configs: Dict[str, PoolConfig] = {}
    
    def get_pool_key(self, db_instance: str, environment: str, db_type: str) -> str:
        """生成连接池唯一键: {db_instance}_{environment}_{db_type}"""
        return f"{db_instance}_{environment}_{db_type}"
    
    async def get_or_create_pool(self, pool_key: str, config: ConnectionConfig) -> ConnectionPool:
        """获取或创建连接池"""
        if pool_key not in self._pools:
            async with self._lock:
                if pool_key not in self._pools:
                    self._pools[pool_key] = await self._create_pool(config)
        return self._pools[pool_key]
```

#### B. 模板作用域连接单例
```python
class TemplateConnectionSingleton:
    """模板作用域内的连接单例"""
    
    def __init__(self, template_id: str):
        self.template_id = template_id
        self._connections: Dict[str, Any] = {}
        self._lock = asyncio.Lock()
    
    async def get_connection(self, pool_key: str, pool: ConnectionPool):
        """获取模板作用域内的单例连接"""
        if pool_key not in self._connections:
            async with self._lock:
                if pool_key not in self._connections:
                    self._connections[pool_key] = await pool.acquire()
        return self._connections[pool_key]
    
    async def cleanup(self):
        """清理模板作用域连接"""
        for pool_key, connection in self._connections.items():
            await self._release_connection(pool_key, connection)
        self._connections.clear()
```

#### C. 智能连接管理器
```python
class SmartConnectionManager:
    """智能连接管理器 - 整合全局和模板级管理"""
    
    def __init__(self):
        self.global_pool_manager = GlobalConnectionPoolManager()
        self.template_singletons: Dict[str, TemplateConnectionSingleton] = {}
    
    async def get_connection(self, 
                           template_id: str,
                           db_instance: str, 
                           environment: str,
                           config: ConnectionConfig):
        """智能获取连接"""
        # 1. 生成连接池键
        pool_key = self.global_pool_manager.get_pool_key(
            db_instance, environment, config.db_type
        )
        
        # 2. 获取或创建全局连接池
        pool = await self.global_pool_manager.get_or_create_pool(pool_key, config)
        
        # 3. 获取模板作用域单例
        if template_id not in self.template_singletons:
            self.template_singletons[template_id] = TemplateConnectionSingleton(template_id)
        
        singleton = self.template_singletons[template_id]
        
        # 4. 获取模板作用域内的单例连接
        return await singleton.get_connection(pool_key, pool)
```

## 🔧 实施方案

### 阶段1: 全局连接池管理器实现

#### 1.1 创建全局连接池管理器
```python
# 文件: libs/platform_core/src/smartdata-engine/plugins/database/global_pool_manager.py

import asyncio
import logging
from typing import Dict, Optional, Any
from dataclasses import dataclass
from .connectors import ConnectionConfig, ConnectorFactory

@dataclass
class PoolConfig:
    """连接池配置"""
    min_size: int = 5
    max_size: int = 20
    max_idle_time: float = 3600.0  # 1小时
    health_check_interval: float = 300.0  # 5分钟
    
class GlobalConnectionPoolManager:
    """全局连接池管理器"""
    
    _instance: Optional['GlobalConnectionPoolManager'] = None
    _lock = asyncio.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._pools: Dict[str, Any] = {}
            self._pool_configs: Dict[str, PoolConfig] = {}
            self._connectors: Dict[str, Any] = {}
            self.logger = logging.getLogger(f"{__name__}.GlobalConnectionPoolManager")
            self._initialized = True
    
    def get_pool_key(self, db_instance: str, environment: str, db_type: str) -> str:
        """生成连接池唯一键"""
        return f"{db_instance}_{environment}_{db_type}".lower()
    
    async def get_or_create_pool(self, 
                               db_instance: str,
                               environment: str, 
                               config: ConnectionConfig) -> Any:
        """获取或创建连接池"""
        pool_key = self.get_pool_key(db_instance, environment, config.db_type)
        
        if pool_key not in self._pools:
            async with self._lock:
                if pool_key not in self._pools:
                    self.logger.info(f"创建新连接池: {pool_key}")
                    
                    # 创建连接器
                    connector = ConnectorFactory.create_connector(config.db_type)
                    self._connectors[pool_key] = connector
                    
                    # 创建连接池
                    pool = await connector.create_pool(config)
                    self._pools[pool_key] = pool
                    
                    # 保存配置
                    self._pool_configs[pool_key] = PoolConfig()
                    
        return self._pools[pool_key]
    
    async def get_connection(self, pool_key: str):
        """从连接池获取连接"""
        if pool_key in self._pools:
            pool = self._pools[pool_key]
            connector = self._connectors[pool_key]
            return await connector.acquire_connection(pool)
        raise ValueError(f"连接池不存在: {pool_key}")
    
    async def release_connection(self, pool_key: str, connection: Any):
        """释放连接回连接池"""
        if pool_key in self._pools:
            connector = self._connectors[pool_key]
            await connector.release_connection(connection)
    
    async def cleanup_pool(self, pool_key: str):
        """清理指定连接池"""
        if pool_key in self._pools:
            connector = self._connectors[pool_key]
            pool = self._pools[pool_key]
            await connector.close_pool(pool)
            
            del self._pools[pool_key]
            del self._connectors[pool_key]
            del self._pool_configs[pool_key]
            
            self.logger.info(f"连接池已清理: {pool_key}")
    
    async def cleanup_all(self):
        """清理所有连接池"""
        for pool_key in list(self._pools.keys()):
            await self.cleanup_pool(pool_key)
    
    def get_pool_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有连接池统计信息"""
        stats = {}
        for pool_key, pool in self._pools.items():
            connector = self._connectors[pool_key]
            stats[pool_key] = connector.get_pool_stats(pool)
        return stats

# 全局单例实例
global_pool_manager = GlobalConnectionPoolManager()
```

#### 1.2 模板作用域连接管理器
```python
# 文件: libs/platform_core/src/smartdata-engine/plugins/database/template_connection_manager.py

import asyncio
import logging
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager
from .global_pool_manager import global_pool_manager

class TemplateConnectionManager:
    """模板作用域连接管理器"""
    
    def __init__(self, template_id: str):
        self.template_id = template_id
        self._connections: Dict[str, Any] = {}
        self._lock = asyncio.Lock()
        self.logger = logging.getLogger(f"{__name__}.TemplateConnectionManager")
        self._active = True
    
    async def get_connection(self, 
                           db_instance: str,
                           environment: str,
                           config: 'ConnectionConfig') -> Any:
        """获取模板作用域内的单例连接"""
        pool_key = global_pool_manager.get_pool_key(db_instance, environment, config.db_type)
        
        if pool_key not in self._connections and self._active:
            async with self._lock:
                if pool_key not in self._connections and self._active:
                    # 获取全局连接池
                    pool = await global_pool_manager.get_or_create_pool(
                        db_instance, environment, config
                    )
                    
                    # 从连接池获取连接
                    connection = await global_pool_manager.get_connection(pool_key)
                    self._connections[pool_key] = connection
                    
                    self.logger.debug(f"模板 {self.template_id} 获取连接: {pool_key}")
        
        return self._connections.get(pool_key)
    
    async def cleanup(self):
        """清理模板作用域连接"""
        self._active = False
        
        for pool_key, connection in self._connections.items():
            try:
                await global_pool_manager.release_connection(pool_key, connection)
                self.logger.debug(f"模板 {self.template_id} 释放连接: {pool_key}")
            except Exception as e:
                self.logger.error(f"释放连接失败 {pool_key}: {e}")
        
        self._connections.clear()
    
    @asynccontextmanager
    async def connection_scope(self):
        """连接作用域上下文管理器"""
        try:
            yield self
        finally:
            await self.cleanup()

# 模板连接管理器注册表
template_managers: Dict[str, TemplateConnectionManager] = {}
template_managers_lock = asyncio.Lock()

async def get_template_connection_manager(template_id: str) -> TemplateConnectionManager:
    """获取模板连接管理器"""
    if template_id not in template_managers:
        async with template_managers_lock:
            if template_id not in template_managers:
                template_managers[template_id] = TemplateConnectionManager(template_id)
    
    return template_managers[template_id]

async def cleanup_template_manager(template_id: str):
    """清理模板连接管理器"""
    if template_id in template_managers:
        manager = template_managers[template_id]
        await manager.cleanup()
        del template_managers[template_id]
```

### 阶段2: 数据库处理器集成优化

#### 2.1 优化DatabaseProcessor
```python
# 在 database_processor.py 中添加性能优化方法

class DatabaseProcessor(BaseProcessor):
    """数据库处理器 - 性能优化版本"""
    
    def __init__(self):
        super().__init__()
        # ... 现有初始化代码 ...
        
        # 性能优化组件
        self.connection_manager = None  # 延迟初始化
        self._template_context: Optional[str] = None
    
    def set_template_context(self, template_id: str):
        """设置模板上下文"""
        self._template_context = template_id
    
    async def get_optimized_connection(self, config: ConnectionConfig):
        """获取优化的数据库连接"""
        if not self._template_context:
            # 回退到原有连接方式
            return await self._get_legacy_connection(config)
        
        # 使用优化的连接管理
        from .template_connection_manager import get_template_connection_manager
        
        manager = await get_template_connection_manager(self._template_context)
        
        # 解析数据库实例和环境
        db_instance = config.host  # 可以根据需要自定义解析逻辑
        environment = config.get('environment', 'default')
        
        return await manager.get_connection(db_instance, environment, config)
    
    async def execute_optimized_query(self, 
                                    connection_string: str, 
                                    query: str, 
                                    parameters: Dict[str, Any] = None):
        """执行优化的查询"""
        try:
            # 解析连接配置
            config = self._parse_connection_string(connection_string)
            
            # 获取优化连接
            connection = await self.get_optimized_connection(config)
            
            # 执行查询
            result = await self._execute_query_with_connection(connection, query, parameters)
            
            return result
            
        except Exception as e:
            self.logger.error(f"优化查询执行失败: {e}")
            # 回退到原有方式
            return await self._execute_legacy_query(connection_string, query, parameters)
```

### 阶段3: SmartDataObject集成

#### 3.1 模板引擎集成
```python
# 在 smart_data_object.py 中添加模板上下文支持

class SmartDataLoader:
    """智能数据加载器 - 性能优化版本"""
    
    def __init__(self, registry: PluginRegistry = None, template_context: str = None):
        self.registry = registry
        self.template_context = template_context
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def database(self, connection_string: str, template_id: str = None):
        """数据库连接器 - 性能优化版本"""
        try:
            from plugins.database.database_processor import DatabaseProcessor
            
            processor = DatabaseProcessor()
            
            # 设置模板上下文以启用连接优化
            if template_id or self.template_context:
                context_id = template_id or self.template_context
                processor.set_template_context(context_id)
            
            return OptimizedDatabaseConnector(connection_string, processor)
            
        except Exception as e:
            self.logger.error(f"优化数据库连接器创建失败: {e}")
            # 回退到原有实现
            return DatabaseConnector(connection_string, self.registry)

class OptimizedDatabaseConnector:
    """优化的数据库连接器"""
    
    def __init__(self, connection_string: str, processor: DatabaseProcessor):
        self.connection_string = connection_string
        self.processor = processor
        self.logger = logging.getLogger(f"{__name__}.OptimizedDatabaseConnector")
    
    def query(self, sql: str) -> SmartDataObject:
        """执行优化的SQL查询"""
        try:
            # 使用异步安全装饰器执行优化查询
            @async_safe
            async def execute_query():
                return await self.processor.execute_optimized_query(
                    self.connection_string, sql
                )
            
            result = execute_query()
            return SmartDataObject(result.data if result.success else [])
            
        except Exception as e:
            self.logger.error(f"优化查询失败: {e}")
            # 回退到原有方式
            return self._execute_legacy_query(sql)
```

## 📈 性能提升预期

### 1. **连接开销减少**
- **当前**: 每次查询创建新连接 (~100ms)
- **优化后**: 复用连接池连接 (~1ms)
- **提升**: 99%连接时间减少

### 2. **并发性能提升**
- **当前**: 受限于数据库最大连接数
- **优化后**: 通过连接池控制，支持更高并发
- **提升**: 3-5倍并发处理能力

### 3. **资源利用优化**
- **当前**: 连接数 = 并发模板数
- **优化后**: 连接数 = 连接池大小 (可控)
- **提升**: 90%连接资源节省

### 4. **响应时间改善**
- **模板内多次查询**: 从多次连接变为单次连接
- **跨模板查询**: 连接池复用，无需重新建立
- **提升**: 平均响应时间减少70%

## 🚀 实施建议

### 优先级排序
1. **P0 - 全局连接池管理器**: 立即实施，影响最大
2. **P1 - 模板作用域管理**: 第二阶段，提升模板性能
3. **P2 - SmartDataObject集成**: 第三阶段，完善用户体验

### 兼容性保证
- 保持现有API不变
- 新功能通过配置开关控制
- 渐进式迁移，支持回退

### 监控指标
- 连接池使用率
- 平均查询响应时间
- 连接创建/销毁次数
- 模板渲染性能

**🎯 通过此优化方案，数据库插件性能将提升3-5倍，资源利用率提升90%，为高并发模板渲染提供强有力支持！**

## 🔧 快速实施指南

### 立即可实施的优化
1. **启用连接池**: 在现有connectors.py中已有连接池支持
2. **配置优化**: 调整连接池参数以适应高并发场景
3. **监控添加**: 添加连接池使用情况监控

### 渐进式实施路径
1. **第1周**: 实施全局连接池管理器
2. **第2周**: 添加模板作用域连接管理
3. **第3周**: 集成到SmartDataObject
4. **第4周**: 性能测试和调优

### 配置建议
```python
# 高并发场景推荐配置
POOL_CONFIG = {
    'min_size': 10,      # 最小连接数
    'max_size': 50,      # 最大连接数
    'max_idle_time': 1800,  # 30分钟空闲超时
    'health_check_interval': 300  # 5分钟健康检查
}
```
