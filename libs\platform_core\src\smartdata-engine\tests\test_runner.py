"""
企业级模板引擎测试运行器

提供统一的测试执行、覆盖率报告和性能分析
"""

import unittest
import sys
import time
import os
from pathlib import Path
from typing import Dict, List, Optional
import argparse

# 尝试导入覆盖率工具
try:
    import coverage
    COVERAGE_AVAILABLE = True
except ImportError:
    COVERAGE_AVAILABLE = False
    print("警告: coverage 未安装，无法生成覆盖率报告")

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from tests import TEST_CONFIG


class EnterpriseTestRunner:
    """企业级测试运行器"""
    
    def __init__(self):
        self.test_results = {}
        self.coverage_data = None
        self.start_time = None
        self.end_time = None
        
        if COVERAGE_AVAILABLE:
            self.coverage = coverage.Coverage(
                source=['core', 'template'],
                omit=['*/tests/*', '*/examples/*']
            )
        else:
            self.coverage = None
    
    def discover_tests(self, test_type: str = 'all') -> unittest.TestSuite:
        """发现测试用例"""
        loader = unittest.TestLoader()
        suite = unittest.TestSuite()
        
        test_dirs = {
            'unit': 'tests/unit',
            'integration': 'tests/integration', 
            'performance': 'tests/performance'
        }
        
        if test_type == 'all':
            dirs_to_scan = test_dirs.values()
        elif test_type in test_dirs:
            dirs_to_scan = [test_dirs[test_type]]
        else:
            raise ValueError(f"未知的测试类型: {test_type}")
        
        for test_dir in dirs_to_scan:
            if os.path.exists(test_dir):
                discovered = loader.discover(test_dir, pattern='test_*.py')
                suite.addTest(discovered)
        
        return suite
    
    def run_tests(self, 
                  test_type: str = 'all',
                  verbose: bool = True,
                  enable_coverage: bool = True) -> Dict:
        """运行测试"""
        print(f"🚀 开始运行 {test_type} 测试...")
        print("=" * 60)
        
        self.start_time = time.time()
        
        # 启动覆盖率监控
        if enable_coverage and self.coverage:
            self.coverage.start()
        
        # 发现并运行测试
        suite = self.discover_tests(test_type)
        runner = unittest.TextTestRunner(
            verbosity=2 if verbose else 1,
            stream=sys.stdout,
            buffer=True
        )
        
        result = runner.run(suite)
        
        # 停止覆盖率监控
        if enable_coverage and self.coverage:
            self.coverage.stop()
            self.coverage.save()
        
        self.end_time = time.time()
        
        # 收集结果
        self.test_results = {
            'tests_run': result.testsRun,
            'failures': len(result.failures),
            'errors': len(result.errors),
            'skipped': len(result.skipped),
            'success_rate': (result.testsRun - len(result.failures) - len(result.errors)) / max(result.testsRun, 1) * 100,
            'execution_time': self.end_time - self.start_time,
            'failure_details': result.failures,
            'error_details': result.errors
        }
        
        return self.test_results
    
    def generate_coverage_report(self, output_dir: str = 'coverage_report') -> Optional[Dict]:
        """生成覆盖率报告"""
        if not self.coverage:
            print("❌ 覆盖率工具不可用")
            return None
        
        try:
            # 生成覆盖率数据
            coverage_data = {}
            
            # 控制台报告
            print("\n📊 代码覆盖率报告:")
            print("=" * 60)
            self.coverage.report(show_missing=True)
            
            # HTML报告
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            self.coverage.html_report(directory=output_dir)
            print(f"\n📄 详细HTML报告已生成: {output_dir}/index.html")
            
            # 获取覆盖率数据
            total_coverage = self.coverage.report(file=open(os.devnull, 'w'))
            
            coverage_data = {
                'total_coverage': total_coverage,
                'html_report_path': f"{output_dir}/index.html",
                'meets_threshold': total_coverage >= TEST_CONFIG['coverage_threshold']
            }
            
            return coverage_data
            
        except Exception as e:
            print(f"❌ 生成覆盖率报告失败: {e}")
            return None
    
    def print_summary(self, coverage_data: Optional[Dict] = None):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("🎯 测试执行总结")
        print("=" * 60)
        
        # 测试结果
        print(f"📊 测试统计:")
        print(f"  • 总测试数: {self.test_results['tests_run']}")
        print(f"  • 成功: {self.test_results['tests_run'] - self.test_results['failures'] - self.test_results['errors']}")
        print(f"  • 失败: {self.test_results['failures']}")
        print(f"  • 错误: {self.test_results['errors']}")
        print(f"  • 跳过: {self.test_results['skipped']}")
        print(f"  • 成功率: {self.test_results['success_rate']:.1f}%")
        print(f"  • 执行时间: {self.test_results['execution_time']:.2f}秒")
        
        # 覆盖率结果
        if coverage_data:
            print(f"\n📈 代码覆盖率:")
            print(f"  • 总覆盖率: {coverage_data['total_coverage']:.1f}%")
            print(f"  • 阈值要求: {TEST_CONFIG['coverage_threshold']}%")
            status = "✅ 达标" if coverage_data['meets_threshold'] else "❌ 未达标"
            print(f"  • 状态: {status}")
        
        # 整体状态
        overall_success = (
            self.test_results['failures'] == 0 and 
            self.test_results['errors'] == 0 and
            (not coverage_data or coverage_data['meets_threshold'])
        )
        
        print(f"\n🎉 整体状态: {'✅ 通过' if overall_success else '❌ 失败'}")
        
        if not overall_success:
            print("\n⚠️  需要关注的问题:")
            if self.test_results['failures'] > 0:
                print(f"  • {self.test_results['failures']} 个测试失败")
            if self.test_results['errors'] > 0:
                print(f"  • {self.test_results['errors']} 个测试错误")
            if coverage_data and not coverage_data['meets_threshold']:
                print(f"  • 代码覆盖率未达标 ({coverage_data['total_coverage']:.1f}% < {TEST_CONFIG['coverage_threshold']}%)")
    
    def run_full_test_suite(self, enable_coverage: bool = True) -> bool:
        """运行完整测试套件"""
        print("🧪 企业级模板引擎 - 完整测试套件")
        print("=" * 80)
        
        overall_success = True
        
        # 运行单元测试
        print("\n1️⃣ 单元测试")
        unit_results = self.run_tests('unit', enable_coverage=enable_coverage)
        if unit_results['failures'] > 0 or unit_results['errors'] > 0:
            overall_success = False
        
        # 运行集成测试
        if TEST_CONFIG['enable_integration_tests']:
            print("\n2️⃣ 集成测试")
            integration_results = self.run_tests('integration', enable_coverage=False)
            if integration_results['failures'] > 0 or integration_results['errors'] > 0:
                overall_success = False
        
        # 运行性能测试
        if TEST_CONFIG['enable_performance_tests']:
            print("\n3️⃣ 性能测试")
            performance_results = self.run_tests('performance', enable_coverage=False)
            if performance_results['failures'] > 0 or performance_results['errors'] > 0:
                overall_success = False
        
        # 生成覆盖率报告
        coverage_data = None
        if enable_coverage:
            coverage_data = self.generate_coverage_report()
            if coverage_data and not coverage_data['meets_threshold']:
                overall_success = False
        
        # 打印总结
        self.print_summary(coverage_data)
        
        return overall_success


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='企业级模板引擎测试运行器')
    parser.add_argument('--type', choices=['all', 'unit', 'integration', 'performance'], 
                       default='all', help='测试类型')
    parser.add_argument('--no-coverage', action='store_true', help='禁用覆盖率检测')
    parser.add_argument('--quiet', action='store_true', help='静默模式')
    
    args = parser.parse_args()
    
    runner = EnterpriseTestRunner()
    
    if args.type == 'all':
        success = runner.run_full_test_suite(enable_coverage=not args.no_coverage)
    else:
        results = runner.run_tests(
            test_type=args.type,
            verbose=not args.quiet,
            enable_coverage=not args.no_coverage
        )
        
        if not args.no_coverage:
            coverage_data = runner.generate_coverage_report()
        else:
            coverage_data = None
        
        runner.print_summary(coverage_data)
        success = results['failures'] == 0 and results['errors'] == 0
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
