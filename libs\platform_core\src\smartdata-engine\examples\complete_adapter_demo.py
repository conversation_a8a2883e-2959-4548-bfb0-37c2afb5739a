"""
完整适配器演示

展示所有类型适配器的同步和异步使用方法
"""

import asyncio
import os
import json
import tempfile
from pathlib import Path

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../'))

from core.enterprise_data_architecture import DataRegistry
from core.adapters.database.sqlite import SQLiteAdapter
from core.adapters.database.async_sqlite import AsyncSQLiteAdapter
from core.adapters.database.async_mysql import AsyncMySQLAdapter
from core.adapters.api.rest_api import RestAPIAdapter
from core.adapters.file.csv_adapter import CSVAdapter
from core.adapters.file.json_adapter import J<PERSON>NAdapter
from core.adapters.file.html_adapter import HTMLAdapter
from core.adapters.file.xml_adapter import XMLAdapter


async def demo_database_adapters():
    """演示数据库适配器"""
    print("🗄️ 数据库适配器演示")
    print("=" * 60)
    
    # 注册适配器
    registry = DataRegistry()
    registry.register_adapter(SQLiteAdapter)
    registry.register_adapter(AsyncSQLiteAdapter)
    
    # 同步SQLite演示
    print("📊 同步SQLite适配器:")
    sync_adapter = SQLiteAdapter()
    sync_connection = sync_adapter._create_connection(':memory:')

    # 创建测试表
    sync_adapter._execute_command(sync_connection, "CREATE TABLE users (id INTEGER, name TEXT, email TEXT)")
    sync_adapter._execute_command(sync_connection, "INSERT INTO users VALUES (1, 'Alice', '<EMAIL>')")
    sync_adapter._execute_command(sync_connection, "INSERT INTO users VALUES (2, 'Bob', '<EMAIL>')")

    # 查询数据
    users = sync_adapter._execute_query(sync_connection, "SELECT * FROM users")
    print(f"  同步查询结果: {len(users)} 条记录")
    for user in users:
        print(f"    {user}")

    # SQLite连接会自动关闭
    
    # 异步SQLite演示
    print("\n⚡ 异步SQLite适配器:")
    async_adapter = AsyncSQLiteAdapter()
    async_connection = await async_adapter._create_async_connection(':memory:')
    
    # 创建测试表
    await async_adapter._async_execute(async_connection, "CREATE TABLE products (id INTEGER, name TEXT, price REAL)")
    await async_adapter._async_execute(async_connection, "INSERT INTO products VALUES (1, 'Laptop', 999.99)")
    await async_adapter._async_execute(async_connection, "INSERT INTO products VALUES (2, 'Mouse', 29.99)")
    
    # 异步查询数据
    products = await async_adapter._async_query(async_connection, "SELECT * FROM products")
    print(f"  异步查询结果: {len(products)} 条记录")
    for product in products:
        print(f"    {product}")
    
    await async_adapter._close_async_connection(async_connection)
    print()


async def demo_api_adapters():
    """演示API适配器"""
    print("🌐 API适配器演示")
    print("=" * 60)
    
    # 模拟API配置
    api_config = {
        'base_url': 'https://jsonplaceholder.typicode.com',
        'headers': {'Content-Type': 'application/json'},
        'timeout': 10
    }
    
    print("📡 REST API适配器:")
    api_adapter = RestAPIAdapter()
    
    try:
        # 同步API调用
        print("  同步API调用:")
        sync_connection = api_config
        posts = api_adapter._sync_get(sync_connection, '/posts', {'_limit': 3})
        print(f"    获取到 {len(posts)} 篇文章")
        for post in posts[:2]:  # 只显示前2篇
            print(f"    - {post.get('title', 'No title')[:50]}...")
        
        # 异步API调用
        print("\n  异步API调用:")
        async_connection = await api_adapter._create_async_connection(api_config)
        users = await api_adapter._async_get(async_connection, '/users', {'_limit': 3})
        print(f"    获取到 {len(users)} 个用户")
        for user in users[:2]:  # 只显示前2个
            print(f"    - {user.get('name', 'No name')} ({user.get('email', 'No email')})")
        
        await api_adapter._close_async_connection(async_connection)
        
    except Exception as e:
        print(f"    API调用失败 (可能是网络问题): {e}")
    
    print()


async def demo_file_adapters():
    """演示文件适配器"""
    print("📁 文件适配器演示")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    csv_file = os.path.join(temp_dir, 'test_data.csv')
    json_file = os.path.join(temp_dir, 'test_data.json')
    jsonl_file = os.path.join(temp_dir, 'test_data.jsonl')
    
    try:
        # CSV适配器演示
        print("📊 CSV适配器:")
        csv_adapter = CSVAdapter()
        
        # 准备测试数据
        test_data = [
            {'id': 1, 'name': 'Alice', 'age': 30, 'city': 'New York'},
            {'id': 2, 'name': 'Bob', 'age': 25, 'city': 'Los Angeles'},
            {'id': 3, 'name': 'Charlie', 'age': 35, 'city': 'Chicago'}
        ]
        
        # 同步写入CSV
        csv_connection = csv_file
        written = csv_adapter._sync_write_csv(csv_connection, test_data)
        print(f"  同步写入CSV: {written} 条记录")
        
        # 异步读取CSV
        async_csv_connection = await csv_adapter._create_async_connection(csv_file)
        csv_data = await csv_adapter._async_read_csv(async_csv_connection)
        print(f"  异步读取CSV: {len(csv_data)} 条记录")
        for row in csv_data:
            print(f"    {row}")
        
        # JSON适配器演示
        print("\n📄 JSON适配器:")
        json_adapter = JSONAdapter()
        
        # 同步写入JSON
        json_connection = json_file
        json_written = json_adapter._sync_write_json(json_connection, test_data)
        print(f"  同步写入JSON: {json_written} 条记录")
        
        # 异步读取JSON
        async_json_connection = await json_adapter._create_async_connection(json_file)
        json_data = await json_adapter._async_read_json(async_json_connection)
        print(f"  异步读取JSON: {len(json_data)} 条记录")
        
        # JSONLines格式演示
        print("\n📝 JSONLines适配器:")
        jsonl_config = {
            'file_path': jsonl_file,
            'is_jsonlines': True
        }
        
        # 异步写入JSONLines
        async_jsonl_connection = await json_adapter._create_async_connection(jsonl_config)
        jsonl_written = await json_adapter._async_write_json(async_jsonl_connection, test_data)
        print(f"  异步写入JSONLines: {jsonl_written} 条记录")
        
        # 异步流式读取JSONLines
        print("  异步流式读取JSONLines:")
        count = 0
        async for item in json_adapter._async_stream_query(async_jsonl_connection, ''):
            print(f"    流式读取: {item}")
            count += 1
        print(f"  流式读取总计: {count} 条记录")

        # HTML适配器演示
        print("\n🌐 HTML适配器:")
        html_adapter = HTMLAdapter()
        html_file = os.path.join(temp_dir, 'test_data.html')

        # 异步写入HTML
        async_html_connection = await html_adapter._create_async_connection(html_file)
        html_written = await html_adapter._async_write_html(async_html_connection, test_data)
        print(f"  异步写入HTML: {html_written} 条记录")

        # 异步读取HTML
        html_data = await html_adapter._async_read_html(async_html_connection)
        print(f"  异步读取HTML: {len(html_data)} 条记录")

        # XML适配器演示
        print("\n📋 XML适配器:")
        xml_adapter = XMLAdapter()
        xml_file = os.path.join(temp_dir, 'test_data.xml')

        # 异步写入XML
        async_xml_connection = await xml_adapter._create_async_connection(xml_file)
        xml_written = await xml_adapter._async_write_xml(async_xml_connection, test_data)
        print(f"  异步写入XML: {xml_written} 条记录")

        # 异步读取XML
        xml_data = await xml_adapter._async_read_xml(async_xml_connection)
        print(f"  异步读取XML: {len(xml_data)} 条记录")
        
    except Exception as e:
        print(f"  文件操作失败: {e}")
    
    finally:
        # 清理临时文件
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    print()


async def demo_unified_registry():
    """演示统一注册表"""
    print("🔧 统一数据注册表演示")
    print("=" * 60)
    
    # 创建注册表并注册所有适配器
    registry = DataRegistry()
    
    # 注册数据库适配器（同步优先，异步覆盖）
    registry.register_adapter(SQLiteAdapter)
    registry.register_adapter(AsyncSQLiteAdapter)
    
    # 注册API适配器
    registry.register_adapter(RestAPIAdapter)
    
    # 注册文件适配器
    registry.register_adapter(CSVAdapter)
    registry.register_adapter(JSONAdapter)
    registry.register_adapter(HTMLAdapter)
    registry.register_adapter(XMLAdapter)
    
    print("📋 已注册的适配器:")
    supported_types = registry.get_supported_types()
    for adapter_type in supported_types:
        print(f"  - {adapter_type}")
    
    print(f"\n📊 统计信息:")
    print(f"  总适配器数量: {len(supported_types)}")
    print(f"  数据库类型: {len([t for t in supported_types if 'sql' in t.lower() or 'db' in t.lower()])}")
    print(f"  API类型: {len([t for t in supported_types if 'api' in t.lower() or 'http' in t.lower()])}")
    print(f"  文件类型: {len([t for t in supported_types if any(ext in t.lower() for ext in ['csv', 'json', 'file'])])}")
    
    # 演示自动适配器选择
    print(f"\n🎯 自动适配器选择演示:")
    test_sources = [
        ':memory:',
        'test.csv',
        'data.json',
        'page.html',
        'config.xml',
        'https://api.example.com',
        {'base_url': 'https://api.test.com'}
    ]
    
    for source in test_sources:
        try:
            adapter = registry.get_adapter(source)
            print(f"  {source} -> {type(adapter).__name__}")
        except Exception as e:
            print(f"  {source} -> 无匹配适配器 ({str(e)[:50]}...)")
    
    print()


async def demo_performance_comparison():
    """演示性能对比"""
    print("⚡ 性能对比演示")
    print("=" * 60)
    
    # 创建测试数据
    large_data = [
        {'id': i, 'name': f'User_{i}', 'value': i * 1.5}
        for i in range(1000)
    ]
    
    temp_dir = tempfile.mkdtemp()
    test_file = os.path.join(temp_dir, 'performance_test.json')
    
    try:
        json_adapter = JSONAdapter()
        
        # 同步写入测试
        print("📊 同步 vs 异步性能对比:")
        
        import time
        
        # 同步写入
        start_time = time.time()
        sync_written = json_adapter._sync_write_json(test_file, large_data)
        sync_time = (time.time() - start_time) * 1000
        
        # 异步写入
        async_connection = await json_adapter._create_async_connection(test_file + '_async')
        start_time = time.time()
        async_written = await json_adapter._async_write_json(async_connection, large_data)
        async_time = (time.time() - start_time) * 1000
        
        print(f"  同步写入: {sync_written} 条记录，耗时: {sync_time:.2f}ms")
        print(f"  异步写入: {async_written} 条记录，耗时: {async_time:.2f}ms")
        
        if sync_time > 0:
            speedup = sync_time / async_time if async_time > 0 else 1
            print(f"  性能提升: {speedup:.2f}x")
        
    except Exception as e:
        print(f"  性能测试失败: {e}")
    
    finally:
        # 清理临时文件
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    print()


async def main():
    """主演示函数"""
    print("🚀 完整适配器系统演示")
    print("=" * 80)
    print("本演示将展示所有类型适配器的同步和异步使用方法")
    print()
    
    try:
        await demo_database_adapters()
        await demo_api_adapters()
        await demo_file_adapters()
        await demo_unified_registry()
        await demo_performance_comparison()
        
        print("🎉 演示完成！")
        print("=" * 80)
        print("📈 适配器系统特性总结:")
        print("  • 统一接口: 所有适配器遵循统一的接口规范")
        print("  • 同步/异步: 同时支持同步和异步操作模式")
        print("  • 自动选择: 根据数据源自动选择合适的适配器")
        print("  • 高性能: 异步操作提供更好的性能表现")
        print("  • 可扩展: 支持插件化扩展新的适配器类型")
        print("  • 企业级: 完善的错误处理和资源管理")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    # 运行完整演示
    asyncio.run(main())
