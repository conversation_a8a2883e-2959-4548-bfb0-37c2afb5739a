"""
异步数据适配器接口定义

定义所有异步数据操作的统一接口，支持高性能并发处理
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, AsyncIterator, Union, Callable, Awaitable
import asyncio
import time
import logging
from dataclasses import dataclass

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../'))

from core.enterprise_data_architecture import DataResult, ILifecycleManager


class IAsyncDataAdapter(ABC):
    """
    异步数据适配器统一接口
    
    定义所有异步数据操作的标准接口，支持：
    - 异步查询和执行
    - 流式数据处理
    - 并行操作
    - 异步事务管理
    """
    
    @abstractmethod
    async def async_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """
        异步查询操作
        
        Args:
            connection: 异步数据库连接
            sql: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果列表
        """
        pass
    
    @abstractmethod
    async def async_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """
        异步执行操作
        
        Args:
            connection: 异步数据库连接
            sql: SQL执行语句
            params: 执行参数
            
        Returns:
            影响的行数
        """
        pass
    
    @abstractmethod
    async def async_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """
        异步事务操作
        
        Args:
            connection: 异步数据库连接
            operations: 事务操作列表
            
        Returns:
            事务执行结果
        """
        pass
    
    @abstractmethod
    async def async_batch(self, connection: Any, operations: List[Dict]) -> Dict:
        """
        异步批量操作
        
        Args:
            connection: 异步数据库连接
            operations: 批量操作列表
            
        Returns:
            批量操作结果
        """
        pass
    
    @abstractmethod
    async def async_stream_query(self, connection: Any, sql: str, params: Dict = None) -> AsyncIterator[Dict]:
        """
        异步流式查询 - 处理大结果集
        
        Args:
            connection: 异步数据库连接
            sql: SQL查询语句
            params: 查询参数
            
        Yields:
            逐行返回查询结果
        """
        pass
    
    @abstractmethod
    async def create_async_connection(self, connection_source: Any) -> Any:
        """
        创建异步连接
        
        Args:
            connection_source: 连接源（字符串或配置对象）
            
        Returns:
            异步连接对象
        """
        pass
    
    @abstractmethod
    async def close_async_connection(self, connection: Any) -> None:
        """
        关闭异步连接
        
        Args:
            connection: 要关闭的异步连接
        """
        pass
    
    @abstractmethod
    async def create_async_pool(self, connection_source: Any, min_size: int = 5, max_size: int = 20) -> Any:
        """
        创建异步连接池
        
        Args:
            connection_source: 连接源
            min_size: 最小连接数
            max_size: 最大连接数
            
        Returns:
            异步连接池对象
        """
        pass
    
    @abstractmethod
    def get_async_operations(self) -> Dict[str, Callable[..., Awaitable]]:
        """
        获取支持的异步操作列表
        
        Returns:
            异步操作字典
        """
        pass
    
    def supported_types(self) -> List[str]:
        """返回支持的数据类型列表"""
        return []
    
    def can_handle(self, data_source: Any) -> bool:
        """检查是否能处理指定的数据源"""
        return False


class IAsyncLifecycleManager(ABC):
    """
    异步生命周期管理器接口
    
    管理异步资源的生命周期，确保资源正确创建和清理
    """
    
    @abstractmethod
    async def register_async_resource(self, resource_id: str, resource: Any, cleanup_callback: Optional[Callable] = None) -> None:
        """
        注册异步资源
        
        Args:
            resource_id: 资源唯一标识
            resource: 资源对象
            cleanup_callback: 清理回调函数
        """
        pass
    
    @abstractmethod
    async def unregister_async_resource(self, resource_id: str) -> bool:
        """
        注销异步资源
        
        Args:
            resource_id: 资源唯一标识
            
        Returns:
            是否成功注销
        """
        pass
    
    @abstractmethod
    async def cleanup_async_resource(self, resource_id: str) -> bool:
        """
        清理指定异步资源
        
        Args:
            resource_id: 资源唯一标识
            
        Returns:
            是否成功清理
        """
        pass
    
    @abstractmethod
    async def cleanup_all_async(self) -> None:
        """清理所有异步资源"""
        pass
    
    @abstractmethod
    def get_async_resource_count(self) -> int:
        """获取异步资源数量"""
        pass


class IAsyncTemplateScope(ABC):
    """
    异步模板作用域接口
    
    管理模板级别的异步数据上下文和资源
    """
    
    @abstractmethod
    async def register_async_data_source(self, name: str, source: Any) -> 'AsyncDataProxy':
        """
        异步注册数据源
        
        Args:
            name: 数据源名称
            source: 数据源对象
            
        Returns:
            异步数据代理对象
        """
        pass
    
    @abstractmethod
    async def get_async_data_proxy(self, name: str) -> Optional['AsyncDataProxy']:
        """
        获取异步数据代理
        
        Args:
            name: 数据源名称
            
        Returns:
            异步数据代理对象或None
        """
        pass
    
    @abstractmethod
    async def execute_parallel(self, operations: List[Callable[..., Awaitable]]) -> List[Any]:
        """
        并行执行多个异步操作
        
        Args:
            operations: 异步操作列表
            
        Returns:
            操作结果列表
        """
        pass
    
    @abstractmethod
    async def execute_sequential(self, operations: List[Callable[..., Awaitable]]) -> List[Any]:
        """
        顺序执行多个异步操作
        
        Args:
            operations: 异步操作列表
            
        Returns:
            操作结果列表
        """
        pass
    
    @abstractmethod
    async def cleanup_async(self) -> None:
        """清理异步作用域资源"""
        pass


@dataclass
class AsyncOperationResult:
    """异步操作结果"""
    success: bool
    data: Any = None
    error: str = None
    operation: str = 'unknown'
    execution_time: float = 0.0
    connection_info: Dict[str, Any] = None
    
    def to_data_result(self) -> DataResult:
        """转换为标准DataResult"""
        return DataResult(
            success=self.success,
            data=self.data,
            error=self.error,
            operation=self.operation,
            execution_time=self.execution_time,
            adapter_type='AsyncAdapter',
            **self.connection_info or {}
        )


class AsyncContextManager:
    """异步上下文管理器工具类"""
    
    @staticmethod
    def is_async_context() -> bool:
        """检测是否在异步上下文中"""
        try:
            asyncio.current_task()
            return True
        except RuntimeError:
            return False
    
    @staticmethod
    async def safe_gather(*awaitables, return_exceptions: bool = True) -> List[Any]:
        """安全的并行执行，处理异常"""
        return await asyncio.gather(*awaitables, return_exceptions=return_exceptions)
    
    @staticmethod
    async def timeout_operation(operation: Awaitable, timeout: float = 30.0) -> Any:
        """带超时的异步操作"""
        return await asyncio.wait_for(operation, timeout=timeout)


class AsyncPerformanceMonitor:
    """异步性能监控器"""
    
    def __init__(self):
        self.operation_stats = {}
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def monitor_operation(self, operation_name: str, operation: Awaitable) -> Any:
        """监控异步操作性能"""
        start_time = time.time()
        
        try:
            result = await operation
            execution_time = (time.time() - start_time) * 1000
            
            # 记录性能统计
            if operation_name not in self.operation_stats:
                self.operation_stats[operation_name] = {
                    'count': 0,
                    'total_time': 0,
                    'avg_time': 0,
                    'min_time': float('inf'),
                    'max_time': 0
                }
            
            stats = self.operation_stats[operation_name]
            stats['count'] += 1
            stats['total_time'] += execution_time
            stats['avg_time'] = stats['total_time'] / stats['count']
            stats['min_time'] = min(stats['min_time'], execution_time)
            stats['max_time'] = max(stats['max_time'], execution_time)
            
            self.logger.debug(f"异步操作 {operation_name} 完成，耗时: {execution_time:.2f}ms")
            
            return result
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            self.logger.error(f"异步操作 {operation_name} 失败，耗时: {execution_time:.2f}ms, 错误: {e}")
            raise
    
    def get_performance_stats(self) -> Dict[str, Dict]:
        """获取性能统计信息"""
        return self.operation_stats.copy()
    
    def reset_stats(self) -> None:
        """重置性能统计"""
        self.operation_stats.clear()
