#!/usr/bin/env python3
"""
HTTP智能加载器 - 标准化版本

提供智能的HTTP加载能力，支持缓存、批量处理等高级功能。
遵循插件标准规范。
"""

import logging
from typing import Any, Dict, Optional, List

try:
    from ...core.smart_data_object import SmartDataObject
    from ...core.async_sync_coordinator import AsyncSyncCoordinator
except ImportError:
    # 后备实现
    class SmartDataObject:
        def __init__(self, data, success=True, error=None):
            self.data = data
            self.success = success
            self.error = error
            self.raw_data = data
        
        def __getattr__(self, name):
            if hasattr(self.data, name):
                return getattr(self.data, name)
            return None
    
    class AsyncSyncCoordinator:
        def __init__(self, enable_debug=False):
            self.enable_debug = enable_debug
        
        def smart_call(self, async_func, *args, **kwargs):
            import asyncio
            return asyncio.run(async_func(*args, **kwargs))

from .http_processor import HttpProcessor


class SmartHttpLoader:
    """HTTP智能加载器 - 推荐组件"""
    
    def __init__(self, enable_debug: bool = False):
        """初始化智能加载器"""
        self.coordinator = AsyncSyncCoordinator(enable_debug=enable_debug)
        self.processor = HttpProcessor({'enable_debug': enable_debug})
        self._cache: Dict[str, Any] = {}
        self.logger = logging.getLogger(f"{__name__}.SmartHttpLoader")
        
        self.logger.info("HTTP智能加载器初始化完成")
    
    def load(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """智能加载 - 自动选择最佳处理方式"""
        try:
            # 检查缓存
            if self._should_use_cache(options):
                cache_key = self._get_cache_key(data, options)
                if cache_key in self._cache:
                    self.logger.debug(f"缓存命中: {cache_key}")
                    return self._cache[cache_key]
            
            # 使用处理器处理
            result = self.processor.process(data, options)
            
            # 缓存结果
            if self._should_cache_result(result, options):
                cache_key = self._get_cache_key(data, options)
                self._cache[cache_key] = result
                self.logger.debug(f"结果已缓存: {cache_key}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"智能加载失败: {e}")
            return SmartDataObject(
                data={'error': str(e), 'success': False},
                success=False,
                error=str(e)
            )
    
    async def load_async(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """异步加载"""
        try:
            # 检查缓存
            if self._should_use_cache(options):
                cache_key = self._get_cache_key(data, options)
                if cache_key in self._cache:
                    self.logger.debug(f"缓存命中: {cache_key}")
                    return self._cache[cache_key]
            
            # 使用处理器异步处理
            result = await self.processor.process_async(data, options)
            
            # 缓存结果
            if self._should_cache_result(result, options):
                cache_key = self._get_cache_key(data, options)
                self._cache[cache_key] = result
                self.logger.debug(f"结果已缓存: {cache_key}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"异步加载失败: {e}")
            return SmartDataObject(
                data={'error': str(e), 'success': False},
                success=False,
                error=str(e)
            )
    
    def load_batch(self, data_list: List[Any], options: Optional[Dict] = None) -> List[SmartDataObject]:
        """批量加载"""
        results = []
        for data in data_list:
            try:
                result = self.load(data, options)
                results.append(result)
            except Exception as e:
                self.logger.error(f"批量加载项失败: {e}")
                results.append(SmartDataObject(
                    data={'error': str(e), 'success': False},
                    success=False,
                    error=str(e)
                ))
        return results
    
    def _get_cache_key(self, data: Any, options: Optional[Dict] = None) -> str:
        """生成缓存键"""
        import hashlib
        import json
        
        # 创建缓存键
        key_data = {
            'data': str(data),
            'options': options or {}
        }
        
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def _should_use_cache(self, options: Optional[Dict] = None) -> bool:
        """判断是否应该使用缓存"""
        if not options:
            return True
        return options.get('cache', True)
    
    def _should_cache_result(self, result: SmartDataObject, options: Optional[Dict] = None) -> bool:
        """判断是否应该缓存结果"""
        # 只缓存成功的结果
        if not result.success:
            return False
        
        if not options:
            return True
        
        return options.get('cache', True)
    
    def clear_cache(self):
        """清理缓存"""
        self._cache.clear()
        self.logger.info("缓存已清理")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            'cache_size': len(self._cache),
            'cached_keys': list(self._cache.keys())
        }
    
    def get_loader_info(self) -> Dict[str, Any]:
        """获取加载器信息"""
        return {
            'name': 'SmartHttpLoader',
            'version': '2.0.0',
            'description': 'HTTP智能加载器',
            'cache_info': self.get_cache_info(),
            'processor_info': self.processor.get_processor_info()
        }


# 创建全局加载器实例
global_loader = SmartHttpLoader(enable_debug=False)
