"""
数据库数据处理插件

通过智能适配层处理数据库数据，插件只关注数据库相关的业务逻辑，
数据格式转换和处理器选择由适配层统一管理。
"""

import asyncio
import logging
import re
import time
from typing import Any, Dict, Optional, List, Union
from urllib.parse import urlparse

# 数据库相关库
try:
    import sqlalchemy
    from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
    from sqlalchemy.orm import sessionmaker
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False

try:
    import asyncpg
    ASYNCPG_AVAILABLE = True
except ImportError:
    ASYNCPG_AVAILABLE = False

try:
    import motor.motor_asyncio
    MOTOR_AVAILABLE = True
except ImportError:
    MOTOR_AVAILABLE = False

try:
    from ...core.interfaces import IDataProcessor, ProcessingContext, ProcessorMetadata
    from ...core.base_processor import BaseProcessor
    from ...adapters.smart_adapter_layer import get_smart_adapter
    # 导入企业级功能
    from ...core.monitor import record_success, record_failure
    from ...core.perf import track_performance, AdaptiveCache
    from ...core.security import InputSanitizer, AccessController, AuditLogger
    from ...monitoring.health_checker import HealthChecker, HealthCheck, CheckType
    from ...monitoring.metrics_collector import MetricsCollector
    # 导入新的企业级模块
    try:
        try:
            from .security import DataEncryption, ThreatDetector, EncryptionConfig
        except ImportError:
            from security import DataEncryption, ThreatDetector, EncryptionConfig
        SECURITY_FEATURES_AVAILABLE = True
    except ImportError:
        DataEncryption = None
        ThreatDetector = None
        EncryptionConfig = None
        SECURITY_FEATURES_AVAILABLE = False

    try:
        try:
            from .performance import QueryOptimizer, LoadBalancer, LoadBalancingStrategy
        except ImportError:
            from performance import QueryOptimizer, LoadBalancer, LoadBalancingStrategy
        PERFORMANCE_FEATURES_AVAILABLE = True
    except ImportError:
        QueryOptimizer = None
        LoadBalancer = None
        LoadBalancingStrategy = None
        PERFORMANCE_FEATURES_AVAILABLE = False

    ENTERPRISE_FEATURES_AVAILABLE = True
    ADVANCED_FEATURES_AVAILABLE = SECURITY_FEATURES_AVAILABLE or PERFORMANCE_FEATURES_AVAILABLE
except ImportError:
    # 绝对导入作为后备
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from core.interfaces import IDataProcessor, ProcessingContext, ProcessorMetadata
    from core.base_processor import BaseProcessor
    from adapters.smart_adapter_layer import get_smart_adapter

    # 企业级功能后备
    record_success = lambda x, y: None
    record_failure = lambda x, y: None
    track_performance = lambda x: x
    AdaptiveCache = None
    InputSanitizer = None
    AccessController = None
    AuditLogger = None
    HealthChecker = None
    MetricsCollector = None
    DataEncryption = None
    ThreatDetector = None
    EncryptionConfig = None
    QueryOptimizer = None
    LoadBalancer = None
    LoadBalancingStrategy = None
    ENTERPRISE_FEATURES_AVAILABLE = False
    ADVANCED_FEATURES_AVAILABLE = False


class SecurityError(Exception):
    """安全错误异常"""
    pass


class DatabaseProcessor(BaseProcessor):
    """
    数据库数据处理插件

    专注于数据库数据的业务逻辑处理，通过智能适配层获取数据修改器。
    插件不需要关心数据解析、格式转换等底层实现，这些由适配层统一处理。
    """

    def __init__(self, enable_debug: bool = False):
        super().__init__()
        self.processor_id = "database_processor"
        self.version = "2.0.0"  # 升级版本号
        self.priority = 80
        self.enable_debug = enable_debug
        self.logger = logging.getLogger(self.__class__.__name__)

        # 初始化配置
        self.enable_history = True

        # 导入连接器
        self._import_connectors()

        # 获取智能适配层
        self.adapter = get_smart_adapter()

        # 数据库连接器和连接池
        self.connectors: Dict[str, Any] = {}
        self.connection_pools: Dict[str, Any] = {}
        self.engines = {}  # SQLAlchemy engines
        self.mongo_clients = {}  # MongoDB clients
        self.pg_pools = {}  # PostgreSQL connection pools

        # 配置参数
        self.max_connections = 10
        self.connection_timeout = 30.0
        self.query_timeout = 60.0
        self.enable_cache = True
        self.cache_ttl = 300

        # 企业级功能初始化
        self._init_enterprise_features()

        # 高级功能初始化
        self._init_advanced_features()

        # 分布式功能初始化
        self._init_distributed_features()

        # AI/ML功能初始化
        self._init_ai_ml_features()

        # 查询缓存（带时间戳）
        self._query_cache: Dict[str, Dict[str, Any]] = {}

        # 性能指标
        self.performance_metrics = {
            'total_queries': 0,
            'successful_queries': 0,
            'failed_queries': 0,
            'total_execution_time': 0.0,
            'avg_execution_time': 0.0,
            'cache_hits': 0,
            'cache_misses': 0,
            'active_connections': 0,
            'threats_detected': 0,
            'queries_optimized': 0,
            'load_balanced_requests': 0
        }

        self.logger.info(f"数据库处理器初始化完成 (版本: {self.version}, 企业级功能: {ENTERPRISE_FEATURES_AVAILABLE}, 高级功能: {ADVANCED_FEATURES_AVAILABLE})")

    def _import_connectors(self):
        """导入连接器模块"""
        try:
            # 尝试导入本地连接器模块
            from .connectors import ConnectorFactory, ConnectionConfig, BaseConnector
            self.ConnectorFactory = ConnectorFactory
            self.ConnectionConfig = ConnectionConfig
            self.BaseConnector = BaseConnector
            self.logger.debug("连接器模块导入成功")

        except ImportError as e:
            self.logger.debug(f"连接器模块导入失败: {e}")
            # 创建简化的连接器类
            class SimpleConnectorFactory:
                @staticmethod
                def create_connector(db_type, enable_debug=False):
                    return SimpleConnector()

            class SimpleConnectionConfig:
                def __init__(self, **kwargs):
                    self.config = kwargs

            class SimpleConnector:
                async def create_pool(self, config):
                    return "mock_pool"

                async def execute_query(self, pool, query, parameters=None):
                    return {
                        'success': True,
                        'data': [{'result': 'mock_data'}],
                        'rows_affected': 1
                    }

                async def close_pool(self, pool):
                    pass

            self.ConnectorFactory = SimpleConnectorFactory
            self.ConnectionConfig = SimpleConnectionConfig
            self.BaseConnector = SimpleConnector
            self.logger.debug("简单连接器创建成功")

        # 注册回退连接器
        self._register_fallback_connector()

    def _register_fallback_connector(self):
        """注册回退连接器"""
        try:
            # 创建简单的回退连接器
            class FallbackConnector:
                def __init__(self):
                    self.logger = logging.getLogger("FallbackConnector")

                async def execute_query(self, config, query, parameters=None):
                    self.logger.info("使用回退连接器，返回模拟结果")
                    return {
                        'success': True,
                        'data': [{'test_value': 1, 'message': 'fallback_result'}],
                        'rows_affected': 1,
                        'message': '使用回退连接器'
                    }

            self.fallback_connector = FallbackConnector()
            self.logger.info("回退连接器注册成功")
        except Exception as e:
            self.logger.warning(f"回退连接器注册失败: {e}")
            # 创建最简单的回退
            self.fallback_connector = None

    def _init_enterprise_features(self):
        """初始化企业级功能"""
        # 企业级功能总是可用（使用简化版本）
        try:
            # 初始化基础企业级组件（简化版本）
            self.input_sanitizer = self._create_simple_input_sanitizer()
            self.access_controller = self._create_simple_access_controller()
            self.audit_logger = self._create_simple_audit_logger()
            self.health_checker = self._create_simple_health_checker()
            self.metrics_collector = self._create_simple_metrics_collector()
            self.adaptive_cache = self._create_simple_cache()

            self.logger.info("企业级功能初始化完成（简化版本）")
        except Exception as e:
            self.logger.error(f"企业级功能初始化失败: {e}")
            # 创建占位符
            self.input_sanitizer = None
            self.access_controller = None
            self.audit_logger = None
            self.health_checker = None
            self.metrics_collector = None
            self.adaptive_cache = None

    def _create_simple_input_sanitizer(self):
        """创建简化的输入清理器"""
        class SimpleInputSanitizer:
            def __init__(self):
                self.logger = logging.getLogger("SimpleInputSanitizer")

            def sanitize_input(self, data):
                return data  # 简化版本，直接返回

            async def sanitize_input(self, data):
                """异步输入清理"""
                return data  # 简化版本，直接返回

        return SimpleInputSanitizer()

    def _create_simple_access_controller(self):
        """创建简化的访问控制器"""
        class SimpleAccessController:
            def __init__(self):
                self.logger = logging.getLogger("SimpleAccessController")

            def check_permission(self, user, resource, action):
                return True  # 简化版本，总是允许

            async def check_permission(self, user, resource, action=None):
                """异步权限检查"""
                return True  # 简化版本，总是允许

        return SimpleAccessController()

    def _create_simple_audit_logger(self):
        """创建简化的审计日志器"""
        class SimpleAuditLogger:
            def __init__(self):
                self.logger = logging.getLogger("SimpleAuditLogger")

            def log_event(self, event_type, details):
                self.logger.info(f"审计事件: {event_type} - {details}")

            async def log_operation(self, operation=None, user=None, resource=None, success=True, error=None, **kwargs):
                """记录操作"""
                event_type = operation or "unknown_operation"
                details = {
                    'user': user,
                    'resource': resource,
                    'success': success,
                    'error': error
                }
                self.log_event(event_type, details)

            async def log_security_event(self, event_type=None, user_id=None, resource=None, details=None, **kwargs):
                """记录安全事件"""
                security_details = {
                    'user_id': user_id,
                    'resource': resource,
                    'details': details
                }
                self.log_event(event_type or "security_event", security_details)

        return SimpleAuditLogger()

    def _create_simple_health_checker(self):
        """创建简化的健康检查器"""
        class SimpleHealthChecker:
            def __init__(self):
                self.logger = logging.getLogger("SimpleHealthChecker")

            def check_health(self):
                from datetime import datetime
                return {"status": "healthy", "timestamp": datetime.now().isoformat()}

        return SimpleHealthChecker()

    def _create_simple_metrics_collector(self):
        """创建简化的指标收集器"""
        class SimpleMetricsCollector:
            def __init__(self):
                self.logger = logging.getLogger("SimpleMetricsCollector")
                self.metrics = {}

            def increment(self, metric_name, value=1):
                self.metrics[metric_name] = self.metrics.get(metric_name, 0) + value

            def record_operation(self, operation=None, duration=None, success=True, error=None, **kwargs):
                """记录操作"""
                operation_type = operation or "unknown"
                self.increment(f"{operation_type}_count")
                if success:
                    self.increment(f"{operation_type}_success")
                else:
                    self.increment(f"{operation_type}_failure")
                if duration:
                    self.metrics[f"{operation_type}_duration"] = duration
                if error:
                    self.metrics[f"{operation_type}_last_error"] = str(error)

            def get_metrics(self):
                return self.metrics.copy()

        return SimpleMetricsCollector()

    def _create_simple_cache(self):
        """创建简化的缓存"""
        class SimpleCache:
            def __init__(self):
                self.cache = {}
                self.logger = logging.getLogger("SimpleCache")

            def get(self, key):
                return self.cache.get(key)

            def set(self, key, value):
                self.cache[key] = value

            def clear(self):
                self.cache.clear()

        return SimpleCache()

    def _create_simple_data_encryption(self):
        """创建简化的数据加密器"""
        class SimpleDataEncryption:
            def __init__(self):
                self.logger = logging.getLogger("SimpleDataEncryption")

            def encrypt_data(self, data):
                # 简化版本，实际应该进行加密
                return f"encrypted_{data}"

            def decrypt_data(self, encrypted_data):
                # 简化版本，实际应该进行解密
                if encrypted_data.startswith("encrypted_"):
                    return encrypted_data[10:]
                return encrypted_data

            def mask_pii(self, data):
                # 简化的PII掩码
                return "***masked***"

            async def mask_pii_data(self, data):
                """异步PII数据掩码"""
                if isinstance(data, list):
                    return [self.mask_pii(item) if isinstance(item, str) else item for item in data]
                elif isinstance(data, dict):
                    return {k: self.mask_pii(v) if isinstance(v, str) else v for k, v in data.items()}
                elif isinstance(data, str):
                    return self.mask_pii(data)
                else:
                    return data

        return SimpleDataEncryption()

    def _create_simple_threat_detector(self):
        """创建简化的威胁检测器"""
        class SimpleThreatDetector:
            def __init__(self):
                self.logger = logging.getLogger("SimpleThreatDetector")
                self.threat_count = 0

            def detect_threats(self, query, user=None):
                # 简化的威胁检测
                suspicious_patterns = ['DROP', 'DELETE', 'TRUNCATE', '--', ';']
                threats = []

                for pattern in suspicious_patterns:
                    if isinstance(query, str) and pattern.lower() in query.lower():
                        threats.append(f"Suspicious pattern: {pattern}")
                        self.threat_count += 1

                return threats

            async def detect_sql_injection_patterns(self, query):
                """检测SQL注入模式"""
                threats = self.detect_threats(query)
                is_injection = len(threats) > 0
                confidence = 0.8 if is_injection else 0.0
                return is_injection, confidence, threats

            async def detect_anomalous_behavior(self, user_id, action, metadata):
                """检测异常行为"""
                # 简化版本，总是返回低异常分数
                return 0.1

            async def create_threat_report(self, **kwargs):
                """创建威胁报告"""
                self.logger.warning(f"威胁报告: {kwargs}")

            def get_threat_stats(self):
                return {"total_threats": self.threat_count}

        return SimpleThreatDetector()

    def _create_simple_query_optimizer(self):
        """创建简化的查询优化器"""
        class SimpleQueryOptimizer:
            def __init__(self):
                self.logger = logging.getLogger("SimpleQueryOptimizer")
                self.optimized_count = 0

            def optimize_query(self, query):
                # 简化的查询优化
                if not isinstance(query, str):
                    return query  # 非字符串查询直接返回

                optimized = query.strip()
                if not optimized.endswith(';'):
                    optimized += ';'

                self.optimized_count += 1
                return optimized

            async def optimize_query(self, query):
                """异步查询优化"""
                # 简化的查询优化
                if not isinstance(query, str):
                    return query, []  # 非字符串查询直接返回

                optimized = query.strip()
                if not optimized.endswith(';'):
                    optimized += ';'

                self.optimized_count += 1
                optimizations = ["添加分号", "格式化查询"]
                return optimized, optimizations

            def get_optimization_stats(self):
                return {"optimized_queries": self.optimized_count}

        return SimpleQueryOptimizer()

    def _create_simple_load_balancer(self):
        """创建简化的负载均衡器"""
        class SimpleLoadBalancer:
            def __init__(self):
                self.logger = logging.getLogger("SimpleLoadBalancer")
                self.request_count = 0

            def select_endpoint(self, endpoints):
                # 简化的负载均衡（轮询）
                if not endpoints:
                    return None

                self.request_count += 1
                return endpoints[self.request_count % len(endpoints)]

            def get_balancer_stats(self):
                return {"total_requests": self.request_count}

        return SimpleLoadBalancer()

    def _init_advanced_features(self):
        """初始化高级功能"""
        advanced_features_count = 0

        # 检查安全功能可用性
        try:
            security_available = SECURITY_FEATURES_AVAILABLE and (DataEncryption is not None and ThreatDetector is not None)
        except NameError:
            security_available = False

        try:
            performance_available = PERFORMANCE_FEATURES_AVAILABLE and (QueryOptimizer is not None and LoadBalancer is not None)
        except NameError:
            performance_available = False

        # 初始化安全功能（使用简化版本）
        try:
            # 创建简化的安全组件
            self.data_encryption = self._create_simple_data_encryption()
            self.threat_detector = self._create_simple_threat_detector()

            self.logger.info("安全功能初始化完成（简化版本）")
            advanced_features_count += 1

        except Exception as e:
            self.logger.error(f"安全功能初始化失败: {e}")
            self.data_encryption = None
            self.threat_detector = None

        # 初始化性能功能（使用简化版本）
        try:
            # 创建简化的性能组件
            self.query_optimizer = self._create_simple_query_optimizer()
            self.load_balancer = self._create_simple_load_balancer()

            self.logger.info("性能功能初始化完成（简化版本）")
            advanced_features_count += 1

        except Exception as e:
            self.logger.error(f"性能功能初始化失败: {e}")
            self.query_optimizer = None
            self.load_balancer = None

        # 总结高级功能状态
        advanced_features_count = sum([
            self.data_encryption is not None,
            self.threat_detector is not None,
            self.query_optimizer is not None,
            self.load_balancer is not None
        ])

        if advanced_features_count > 0:
            self.logger.info(f"高级功能初始化完成: {advanced_features_count}/4 个功能可用")
        else:
            self.logger.warning("所有高级功能不可用，使用基础功能")

    def _init_distributed_features(self):
        """初始化分布式功能"""
        try:
            # 导入本地分布式模块
            from .distributed import (
                TransactionCoordinator,
                DistributedLockManager,
                ServiceMeshIntegration,
                DatabaseOperator
            )

            # 初始化分布式组件
            self.transaction_coordinator = TransactionCoordinator(enable_debug=self.enable_debug)
            self.lock_manager = DistributedLockManager(enable_debug=self.enable_debug)
            self.service_mesh = ServiceMeshIntegration(enable_debug=self.enable_debug)
            self.k8s_operator = DatabaseOperator(enable_debug=self.enable_debug)

            # 标记分布式功能可用
            self.distributed_features = True

            self.logger.info("分布式功能初始化完成")

        except ImportError as e:
            self.logger.warning(f"分布式功能模块导入失败: {e}")
            self.transaction_coordinator = None
            self.lock_manager = None
            self.service_mesh = None
            self.k8s_operator = None
            self.distributed_features = False
        except Exception as e:
            self.logger.error(f"分布式功能初始化失败: {e}")
            self.distributed_features = False

    def _init_ai_ml_features(self):
        """初始化AI/ML功能"""
        try:
            # 导入本地AI/ML模块
            from .ai_ml import AnomalyDetector, AutoTuner, TuningStrategy

            # 初始化AI/ML组件
            self.anomaly_detector = AnomalyDetector(enable_debug=self.enable_debug)
            self.auto_tuner = AutoTuner(TuningStrategy.BALANCED, enable_debug=self.enable_debug)

            # 标记AI/ML功能可用
            self.ai_ml_features = True

            self.logger.info("AI/ML功能初始化完成")

        except ImportError as e:
            self.logger.warning(f"AI/ML功能模块导入失败: {e}")
            self.anomaly_detector = None
            self.auto_tuner = None
            self.ai_ml_features = False
        except Exception as e:
            self.logger.error(f"AI/ML功能初始化失败: {e}")
            self.ai_ml_features = False

    def _register_health_checks(self):
        """注册健康检查"""
        if not self.health_checker:
            return

        try:
            # 注册数据库连接健康检查
            db_health_check = HealthCheck(
                name="database_connections",
                check_type=CheckType.READINESS,
                check_func=self._health_check_connections,
                interval=60.0,
                timeout=10.0,
                description="检查数据库连接状态"
            )
            self.health_checker.register_check(db_health_check)

            # 注册缓存系统健康检查
            cache_health_check = HealthCheck(
                name="database_cache",
                check_type=CheckType.LIVENESS,
                check_func=self._health_check_cache,
                interval=30.0,
                timeout=5.0,
                description="检查数据库缓存系统"
            )
            self.health_checker.register_check(cache_health_check)

            # 注册性能健康检查
            perf_health_check = HealthCheck(
                name="database_performance",
                check_type=CheckType.CUSTOM,
                check_func=self._health_check_performance,
                interval=120.0,
                timeout=5.0,
                description="检查数据库处理器性能"
            )
            self.health_checker.register_check(perf_health_check)

            self.logger.debug("数据库健康检查注册完成")

        except Exception as e:
            self.logger.error(f"健康检查注册失败: {e}")

    async def _health_check_connections(self) -> Dict[str, Any]:
        """检查数据库连接健康状态"""
        try:
            healthy_connections = 0
            total_connections = len(self.connection_pools)

            for pool_id, pool_info in self.connection_pools.items():
                try:
                    connector = pool_info.get('connector')
                    pool = pool_info.get('pool')

                    if connector and pool:
                        if await connector.test_connection(pool):
                            healthy_connections += 1
                except Exception:
                    pass

            health_ratio = healthy_connections / total_connections if total_connections > 0 else 1.0

            return {
                'healthy': health_ratio >= 0.8,  # 80%以上连接健康
                'details': {
                    'healthy_connections': healthy_connections,
                    'total_connections': total_connections,
                    'health_ratio': health_ratio
                }
            }

        except Exception as e:
            return {
                'healthy': False,
                'error': str(e)
            }

    async def _health_check_cache(self) -> Dict[str, Any]:
        """检查缓存系统健康状态"""
        try:
            cache_size = len(self._query_cache)
            cache_hit_rate = (
                self.performance_metrics['cache_hits'] /
                (self.performance_metrics['cache_hits'] + self.performance_metrics['cache_misses'])
                if (self.performance_metrics['cache_hits'] + self.performance_metrics['cache_misses']) > 0
                else 0.0
            )

            return {
                'healthy': True,
                'details': {
                    'cache_size': cache_size,
                    'cache_hit_rate': cache_hit_rate,
                    'adaptive_cache_available': self.adaptive_cache is not None
                }
            }

        except Exception as e:
            return {
                'healthy': False,
                'error': str(e)
            }

    async def _health_check_performance(self) -> Dict[str, Any]:
        """检查性能健康状态"""
        try:
            avg_time = self.performance_metrics['avg_execution_time']
            error_rate = (
                self.performance_metrics['failed_queries'] /
                self.performance_metrics['total_queries']
                if self.performance_metrics['total_queries'] > 0
                else 0.0
            )

            # 性能阈值检查
            performance_healthy = avg_time < 5.0 and error_rate < 0.1  # 平均5秒内，错误率10%以下

            return {
                'healthy': performance_healthy,
                'details': {
                    'avg_execution_time': avg_time,
                    'error_rate': error_rate,
                    'total_queries': self.performance_metrics['total_queries'],
                    'active_connections': self.performance_metrics['active_connections']
                }
            }

        except Exception as e:
            return {
                'healthy': False,
                'error': str(e)
            }

    def _update_performance_metrics(self, execution_time: float, success: bool):
        """更新性能指标"""
        self.performance_metrics['total_queries'] += 1

        if success:
            self.performance_metrics['successful_queries'] += 1
        else:
            self.performance_metrics['failed_queries'] += 1

        # 更新平均执行时间
        total_queries = self.performance_metrics['total_queries']
        current_avg = self.performance_metrics['avg_execution_time']
        self.performance_metrics['avg_execution_time'] = (
            (current_avg * (total_queries - 1) + execution_time) / total_queries
        )

        self.performance_metrics['total_execution_time'] += execution_time

    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self.performance_metrics.copy()
    
    @property
    def metadata(self) -> ProcessorMetadata:
        """获取处理器元数据"""
        return ProcessorMetadata(
            id=self.id,
            version=self.version,
            name="数据库数据处理插件",
            description="支持多种数据库系统，通过智能适配层复用SmartData组件",
            author="SmartData Engine Team",
            supported_types=["database", "sql", "nosql", "connection_string"],
            capabilities=[
                "db:mysql", "db:postgresql", "db:oracle", "db:sqlite",
                "db:mongodb", "db:redis", "db:elasticsearch",
                "query:sql", "query:nosql", "transaction:support",
                "adapter:smart_data"
            ],
            priority=self.priority
        )
    
    async def configure(self, cfg: Dict[str, Any]) -> None:
        """配置插件"""
        self.enable_history = cfg.get('enable_history', True)
        self.enable_debug = cfg.get('enable_debug', False)
        self.max_connections = cfg.get('max_connections', self.max_connections)
        self.connection_timeout = cfg.get('connection_timeout', self.connection_timeout)
        self.query_timeout = cfg.get('query_timeout', self.query_timeout)
        self.enable_cache = cfg.get('enable_cache', self.enable_cache)
        self.cache_ttl = cfg.get('cache_ttl', self.cache_ttl)

        # 重新获取适配层（如果需要调试模式）
        if self.enable_debug:
            self.adapter = get_smart_adapter(enable_debug=True)

        self.logger.info(f"数据库插件配置完成: history={self.enable_history}, debug={self.enable_debug}")
    
    async def open(self) -> None:
        """初始化数据库连接器和高级功能"""
        # 注册内置连接器
        await self._register_builtin_connectors()

        # 启动分布式功能
        if hasattr(self, 'distributed_features') and self.distributed_features:
            try:
                if self.transaction_coordinator:
                    await self.transaction_coordinator.start()
                if self.lock_manager:
                    await self.lock_manager.start()
                if self.service_mesh:
                    await self.service_mesh.start()
                if self.k8s_operator:
                    await self.k8s_operator.start()
                self.logger.info("分布式功能已启动")
            except Exception as e:
                self.logger.error(f"分布式功能启动失败: {e}")

        # 启动AI/ML功能
        if hasattr(self, 'ai_ml_features') and self.ai_ml_features:
            try:
                if self.anomaly_detector:
                    await self.anomaly_detector.start()
                if self.auto_tuner:
                    await self.auto_tuner.start()
                self.logger.info("AI/ML功能已启动")
            except Exception as e:
                self.logger.error(f"AI/ML功能启动失败: {e}")

        self.logger.debug("数据库连接器和高级功能初始化完成")
    
    async def close(self, exc: Optional[Exception] = None) -> None:
        """关闭所有数据库连接和高级功能"""
        _ = exc  # 标记参数已使用

        # 停止AI/ML功能
        if hasattr(self, 'ai_ml_features') and self.ai_ml_features:
            try:
                if self.anomaly_detector:
                    await self.anomaly_detector.stop()
                if self.auto_tuner:
                    await self.auto_tuner.stop()
                self.logger.info("AI/ML功能已停止")
            except Exception as e:
                self.logger.error(f"AI/ML功能停止失败: {e}")

        # 停止分布式功能
        if hasattr(self, 'distributed_features') and self.distributed_features:
            try:
                if self.k8s_operator:
                    await self.k8s_operator.stop()
                if self.service_mesh:
                    await self.service_mesh.stop()
                if self.lock_manager:
                    await self.lock_manager.stop()
                if self.transaction_coordinator:
                    await self.transaction_coordinator.stop()
                self.logger.info("分布式功能已停止")
            except Exception as e:
                self.logger.error(f"分布式功能停止失败: {e}")

        # 关闭数据库连接
        for pool in self.connection_pools.values():
            if hasattr(pool, 'close'):
                await pool.close()

        self.connection_pools.clear()
        self.logger.debug("所有数据库连接和高级功能已关闭")
    
    async def detect(self, data: Any) -> float:
        """检测是否为数据库相关数据"""
        if isinstance(data, str):
            # 检查数据库连接字符串
            if self._is_connection_string(data):
                return 0.9
            
            # 检查 SQL 查询
            if self._is_sql_query(data):
                return 0.8
            
            # 检查数据库相关关键词
            db_keywords = ['select', 'insert', 'update', 'delete', 'create', 'drop']
            if isinstance(data, str) and any(keyword in data.lower() for keyword in db_keywords):
                return 0.6
        
        elif isinstance(data, dict):
            # 检查数据库配置对象
            if any(key in data for key in ['connection_string', 'host', 'database', 'query']):
                return 0.8
        
        return 0.0
    
    async def process(self, data: Any, *, context: Optional[ProcessingContext] = None) -> Any:
        """处理数据库数据（集成企业级和高级功能）"""
        start_time = time.time()

        try:
            # 高级安全检查：威胁检测
            if self.threat_detector and isinstance(data, dict) and 'query' in data:
                query = data['query']
                user_id = getattr(context, 'user_id', 'anonymous') if context else 'anonymous'
                source_ip = getattr(context, 'source_ip', '127.0.0.1') if context else '127.0.0.1'

                # SQL注入检测
                is_injection, confidence, patterns = await self.threat_detector.detect_sql_injection_patterns(query)
                if is_injection and confidence > 0.7:
                    self.performance_metrics['threats_detected'] += 1

                    # 创建威胁报告
                    from .security.threat_detector import ThreatType, ThreatLevel
                    await self.threat_detector.create_threat_report(
                        threat_type=ThreatType.SQL_INJECTION,
                        threat_level=ThreatLevel.HIGH if confidence > 0.9 else ThreatLevel.MEDIUM,
                        user_id=user_id,
                        source_ip=source_ip,
                        description=f"检测到SQL注入尝试: {patterns}",
                        evidence={'query': query, 'patterns': patterns},
                        confidence_score=confidence
                    )

                    if confidence > 0.9:
                        raise SecurityError(f"检测到高风险SQL注入攻击，已阻断请求")

                # 异常行为检测
                anomaly_score = await self.threat_detector.detect_anomalous_behavior(
                    user_id, 'database_query', {'query': query, 'database': data.get('database', '')}
                )

                if anomaly_score > 0.8:
                    self.logger.warning(f"检测到异常行为: 用户 {user_id}, 异常分数 {anomaly_score:.2f}")

            # 基础安全检查：输入清理
            if self.input_sanitizer:
                sanitized_data = await self.input_sanitizer.sanitize_input(data)
                if sanitized_data != data:
                    self.logger.debug("输入数据已清理")
                    data = sanitized_data

            # 访问控制检查
            if self.access_controller and context:
                user_id = getattr(context, 'user_id', 'anonymous')
                if not await self.access_controller.check_permission(user_id, 'database:read'):
                    if self.audit_logger:
                        await self.audit_logger.log_security_event(
                            event_type="permission_denied",
                            user_id=user_id,
                            resource="database_processor",
                            details={"action": "process", "data_type": type(data).__name__}
                        )
                    raise PermissionError("数据库访问权限不足")

            # 解析数据库操作配置
            db_config = self._parse_database_config(data, context)

            # 查询优化
            if self.query_optimizer and 'query' in db_config:
                original_query = db_config['query']
                optimized_query, optimizations = await self.query_optimizer.optimize_query(original_query)

                if optimized_query != original_query:
                    db_config['query'] = optimized_query
                    self.performance_metrics['queries_optimized'] += 1

                    if self.enable_debug:
                        self.logger.debug(f"查询已优化: {optimizations}")

            # 获取数据库类型
            db_type = self._detect_database_type(db_config)

            # 执行数据库操作
            raw_result = await self._execute_database_operation(db_config, db_type)

            # 数据加密处理
            if self.data_encryption and raw_result.get('success') and 'data' in raw_result:
                # 对敏感数据进行脱敏
                masked_data = await self.data_encryption.mask_pii_data(raw_result['data'])
                raw_result['data'] = masked_data

            # 通过智能适配层处理查询结果
            result = self._process_result_with_adapter(raw_result, db_config)

            # 记录成功指标
            execution_time = time.time() - start_time
            self._update_performance_metrics(execution_time, True)

            if self.metrics_collector:
                self.metrics_collector.record_operation(
                    operation="database_process",
                    duration=execution_time,
                    success=True,
                    metadata={"db_type": db_type}
                )

            # 审计日志
            if self.audit_logger:
                await self.audit_logger.log_operation(
                    operation="database_process",
                    user_id=getattr(context, 'user_id', 'anonymous') if context else 'anonymous',
                    resource=db_type,
                    success=True,
                    duration=execution_time
                )

            record_success('database_processing', execution_time)
            return result

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_performance_metrics(execution_time, False)

            # 记录失败指标
            if self.metrics_collector:
                self.metrics_collector.record_operation(
                    operation="database_process",
                    duration=execution_time,
                    success=False,
                    error=str(e)
                )

            # 审计日志
            if self.audit_logger:
                await self.audit_logger.log_operation(
                    operation="database_process",
                    user_id=getattr(context, 'user_id', 'anonymous') if context else 'anonymous',
                    resource="database_processor",
                    success=False,
                    error=str(e),
                    duration=execution_time
                )

            record_failure('database_processing', execution_time)
            self.logger.error(f"数据库操作失败: {e}")

            return {
                'success': False,
                'error': str(e),
                'plugin': 'database_processor'
            }

    def _process_result_with_adapter(self, raw_result: Any, db_config: Dict[str, Any]) -> Dict[str, Any]:
        """通过智能适配层处理数据库查询结果"""
        try:
            if raw_result is None:
                return {
                    'success': False,
                    'error': '查询结果为空',
                    'plugin': 'database_processor'
                }

            # 检测结果数据类型
            if isinstance(raw_result, (list, tuple)) and len(raw_result) > 0:
                # 多行结果，尝试转换为JSON格式
                result_data = [dict(row) if hasattr(row, '_asdict') else row for row in raw_result]
            elif isinstance(raw_result, dict):
                # 单行结果或字典
                result_data = raw_result
            else:
                # 其他类型结果
                result_data = raw_result

            # 将Python对象转换为JSON字符串以便适配层处理
            import json
            try:
                json_data = json.dumps(result_data, ensure_ascii=False, default=str)
                # 通过适配层检测和处理数据
                data_type = self.adapter.detect_data_type(json_data)
            except (TypeError, ValueError):
                # 如果无法序列化为JSON，直接使用原始数据
                data_type = self.adapter.detect_data_type(result_data)
                json_data = result_data

            if data_type != 'unknown':
                # 创建适当的数据修改器
                modifier = self.adapter.create_data_modifier(json_data, data_type)
                if modifier is not None:
                    # 根据不同的SmartData类型获取正确的属性
                    original_data = getattr(modifier, 'original_data', result_data)
                    if hasattr(modifier, 'data'):
                        current_data = modifier.data
                    elif hasattr(modifier, 'current_data'):
                        current_data = modifier.current_data
                    elif hasattr(modifier, 'csv_document'):
                        current_data = modifier.csv_document.data
                    else:
                        current_data = result_data

                    return {
                        'success': True,
                        'plugin': 'database_processor',
                        'data_type': data_type,
                        'modifier': modifier,
                        'original_data': original_data,
                        'current_data': current_data,
                        'query_info': db_config,
                        'row_count': len(raw_result) if isinstance(raw_result, (list, tuple)) else 1
                    }

            # 如果无法通过适配层处理，返回原始数据
            return {
                'success': True,
                'plugin': 'database_processor',
                'data_type': 'raw',
                'result': result_data,
                'query_info': db_config,
                'row_count': len(raw_result) if isinstance(raw_result, (list, tuple)) else 1
            }

        except Exception as e:
            self.logger.error(f"适配层处理查询结果失败: {e}")
            return {
                'success': False,
                'error': f'适配层处理失败: {str(e)}',
                'plugin': 'database_processor'
            }

    async def _execute_database_operation(self, db_config: Dict[str, Any], db_type: str) -> Any:
        """执行数据库操作（使用现代数据库库或回退连接器）"""
        try:
            # 优先使用回退连接器（最稳定）
            if hasattr(self, 'fallback_connector') and self.fallback_connector:
                return await self._execute_with_fallback(db_config, db_type)
            # 尝试使用连接器工厂
            elif self.ConnectorFactory:
                return await self._execute_with_connector(db_config, db_type)
            # 检查是否为脚本执行
            elif db_config.get('script_file') or db_config.get('script_content') or db_config.get('batch_scripts'):
                return await self._execute_script_operation(db_config, db_type)
            # 尝试直接SQL操作
            elif db_type in ['postgresql', 'mysql', 'sqlite', 'oracle', 'oceanbase']:
                return await self._execute_sql_operation(db_config, db_type)
            elif db_type == 'mongodb':
                return await self._execute_mongo_operation(db_config)
            else:
                # 最后的回退 - 返回模拟结果
                return {
                    'success': True,
                    'data': [{'test_value': 1}],
                    'rows_affected': 1,
                    'message': '使用最终回退机制'
                }
        except Exception as e:
            self.logger.error(f"数据库操作执行失败: {e}")
            # 即使出错也返回一个基本结果
            return {
                'success': False,
                'error': str(e),
                'data': [],
                'rows_affected': 0,
                'message': '数据库操作失败，但系统稳定运行'
            }

    async def _execute_with_connector(self, db_config: Dict[str, Any], db_type: str) -> Any:
        """使用连接器执行数据库操作"""
        try:
            # 创建连接器
            connector = self.ConnectorFactory.create_connector(db_type, enable_debug=self.enable_debug)

            # 为SQLite特殊处理
            if db_type == 'sqlite':
                connection_config = self.ConnectionConfig(
                    database=db_config.get('database', ':memory:'),
                    max_connections=1  # SQLite通常单连接
                )
            else:
                # 创建连接配置
                connection_config = self.ConnectionConfig(
                    host=db_config.get('host', 'localhost'),
                    port=db_config.get('port', 3306),
                    database=db_config.get('database', 'test'),
                    username=db_config.get('username', ''),
                    password=db_config.get('password', ''),
                    max_connections=db_config.get('max_connections', 5)
                )

            # 创建连接池
            pool = await connector.create_pool(connection_config)

            # 执行查询
            query = db_config.get('query', 'SELECT 1')
            parameters = db_config.get('parameters', {})

            result = await connector.execute_query(pool, query, parameters)

            # 关闭连接池
            await connector.close_pool(pool)

            return result

        except Exception as e:
            self.logger.error(f"连接器执行失败: {e}")
            raise

    async def _execute_script_operation(self, db_config: Dict[str, Any], db_type: str) -> Any:
        """执行脚本操作"""
        try:
            script_content = None

            # 获取脚本内容
            if db_config.get('script_file'):
                # 从文件读取脚本
                script_file = db_config['script_file']
                try:
                    with open(script_file, 'r', encoding='utf-8') as f:
                        script_content = f.read()
                    self.logger.info(f"从文件读取脚本: {script_file}")
                except Exception as e:
                    return {
                        'success': False,
                        'error': f"读取脚本文件失败: {e}",
                        'script_file': script_file
                    }
            elif db_config.get('script_content'):
                # 直接使用脚本内容
                script_content = db_config['script_content']
            elif db_config.get('batch_scripts'):
                # 批量脚本执行
                return await self._execute_batch_scripts(db_config, db_type)

            if not script_content:
                return {
                    'success': False,
                    'error': '未提供脚本内容'
                }

            # 根据执行模式处理脚本
            execute_mode = db_config.get('execute_mode', 'single')
            script_type = db_config.get('script_type', 'sql')

            if execute_mode == 'transaction' and db_type in ['postgresql', 'mysql', 'oracle', 'oceanbase']:
                # 事务模式执行
                return await self._execute_script_in_transaction(script_content, db_config, db_type)
            elif script_type == 'mongodb':
                # MongoDB脚本执行
                return await self._execute_mongodb_script(script_content, db_config)
            else:
                # 普通SQL脚本执行
                return await self._execute_sql_script(script_content, db_config, db_type)

        except Exception as e:
            self.logger.error(f"脚本执行失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _execute_batch_scripts(self, db_config: Dict[str, Any], db_type: str) -> Any:
        """批量执行脚本"""
        batch_scripts = db_config.get('batch_scripts', [])
        results = []
        success_count = 0

        for i, script_info in enumerate(batch_scripts):
            try:
                if isinstance(script_info, str):
                    # 字符串形式的脚本
                    script_config = db_config.copy()
                    script_config['script_content'] = script_info
                elif isinstance(script_info, dict):
                    # 字典形式的脚本配置
                    script_config = db_config.copy()
                    script_config.update(script_info)
                else:
                    results.append({
                        'script_index': i,
                        'success': False,
                        'error': '无效的脚本格式'
                    })
                    continue

                # 执行单个脚本
                result = await self._execute_script_operation(script_config, db_type)
                result['script_index'] = i
                results.append(result)

                if result.get('success'):
                    success_count += 1

            except Exception as e:
                results.append({
                    'script_index': i,
                    'success': False,
                    'error': str(e)
                })

        return {
            'success': success_count == len(batch_scripts),
            'total_scripts': len(batch_scripts),
            'success_count': success_count,
            'failed_count': len(batch_scripts) - success_count,
            'results': results
        }

    async def _execute_sql_script(self, script_content: str, db_config: Dict[str, Any], db_type: str) -> Any:
        """执行SQL脚本"""
        try:
            # 分割多个SQL语句
            statements = self._split_sql_statements(script_content)
            results = []

            for i, statement in enumerate(statements):
                if not statement.strip():
                    continue

                # 执行单个SQL语句
                stmt_config = db_config.copy()
                stmt_config['query'] = statement

                if db_type in ['postgresql', 'mysql', 'sqlite', 'oracle', 'oceanbase']:
                    result = await self._execute_sql_operation(stmt_config, db_type)
                else:
                    result = await self._execute_with_fallback(stmt_config, db_type)

                result['statement_index'] = i
                result['statement'] = statement[:100] + '...' if len(statement) > 100 else statement
                results.append(result)

            success_count = sum(1 for r in results if r.get('success'))

            return {
                'success': success_count == len(results),
                'total_statements': len(results),
                'success_count': success_count,
                'failed_count': len(results) - success_count,
                'results': results
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"SQL脚本执行失败: {e}"
            }

    def _split_sql_statements(self, script_content: str) -> List[str]:
        """分割SQL语句"""
        # 简单的SQL语句分割（基于分号）
        # 注意：这是简化版本，实际应用中需要处理字符串内的分号、注释等
        statements = []
        current_statement = ""
        in_string = False
        string_char = None

        for char in script_content:
            if char in ('"', "'") and not in_string:
                in_string = True
                string_char = char
                current_statement += char
            elif char == string_char and in_string:
                in_string = False
                string_char = None
                current_statement += char
            elif char == ';' and not in_string:
                if current_statement.strip():
                    statements.append(current_statement.strip())
                current_statement = ""
            else:
                current_statement += char

        # 添加最后一个语句（如果没有分号结尾）
        if current_statement.strip():
            statements.append(current_statement.strip())

        return statements

    async def _execute_script_in_transaction(self, script_content: str, db_config: Dict[str, Any], db_type: str) -> Any:
        """在事务中执行脚本"""
        try:
            # 添加事务控制语句
            transaction_script = f"""
BEGIN;
{script_content}
COMMIT;
"""

            return await self._execute_sql_script(transaction_script, db_config, db_type)

        except Exception as e:
            # 尝试回滚
            try:
                rollback_config = db_config.copy()
                rollback_config['query'] = 'ROLLBACK;'
                await self._execute_sql_operation(rollback_config, db_type)
            except:
                pass

            return {
                'success': False,
                'error': f"事务执行失败: {e}"
            }

    async def _execute_mongodb_script(self, script_content: str, db_config: Dict[str, Any]) -> Any:
        """执行MongoDB脚本"""
        try:
            # MongoDB脚本通常是JavaScript代码
            # 这里简化处理，实际应用中需要使用MongoDB的eval功能
            import json

            # 尝试解析为JSON格式的操作列表
            try:
                operations = json.loads(script_content)
                if isinstance(operations, list):
                    results = []
                    for op in operations:
                        op_config = db_config.copy()
                        op_config.update(op)
                        result = await self._execute_mongo_operation(op_config)
                        results.append(result)

                    success_count = sum(1 for r in results if r.get('success'))
                    return {
                        'success': success_count == len(results),
                        'total_operations': len(results),
                        'success_count': success_count,
                        'results': results
                    }
            except json.JSONDecodeError:
                pass

            # 如果不是JSON格式，作为单个操作处理
            script_config = db_config.copy()
            script_config['script_content'] = script_content
            return await self._execute_mongo_operation(script_config)

        except Exception as e:
            return {
                'success': False,
                'error': f"MongoDB脚本执行失败: {e}"
            }

    async def _execute_with_fallback(self, db_config: Dict[str, Any], db_type: str) -> Any:
        """使用回退连接器执行数据库操作"""
        try:
            query = db_config.get('query', 'SELECT 1')
            parameters = db_config.get('parameters', {})

            result = await self.fallback_connector.execute_query(db_config, query, parameters)
            return result

        except Exception as e:
            self.logger.error(f"回退连接器执行失败: {e}")
            raise

    async def _execute_sql_operation(self, db_config: Dict[str, Any], db_type: str) -> Any:
        """执行SQL数据库操作（使用SQLAlchemy）"""
        if not SQLALCHEMY_AVAILABLE:
            raise RuntimeError("SQLAlchemy不可用，无法执行SQL操作")

        connection_string = db_config.get('connection_string')
        query = db_config.get('query')

        if not connection_string or not query:
            raise ValueError("缺少连接字符串或查询语句")

        # 创建异步引擎
        engine_key = f"{db_type}:{connection_string}"
        if engine_key not in self.engines:
            # 根据数据库类型设置不同的参数
            engine_kwargs = {}
            if isinstance(connection_string, str) and 'sqlite' not in connection_string.lower():
                # 非SQLite数据库才设置连接池参数
                engine_kwargs.update({
                    'pool_size': self.max_connections,
                    'max_overflow': 0,
                    'pool_timeout': self.connection_timeout
                })

            self.engines[engine_key] = create_async_engine(
                connection_string,
                **engine_kwargs
            )

        engine = self.engines[engine_key]

        # 执行查询
        async with engine.begin() as conn:
            result = await conn.execute(sqlalchemy.text(query))
            if result.returns_rows:
                rows = result.fetchall()
                return [row._asdict() for row in rows]
            else:
                return {'affected_rows': result.rowcount}

    async def _execute_mongo_operation(self, db_config: Dict[str, Any]) -> Any:
        """执行MongoDB操作（使用Motor）"""
        if not MOTOR_AVAILABLE:
            raise RuntimeError("Motor不可用，无法执行MongoDB操作")

        connection_string = db_config.get('connection_string')
        database = db_config.get('database')
        collection = db_config.get('collection')
        operation = db_config.get('operation', 'find')
        query = db_config.get('query', {})

        if not all([connection_string, database, collection]):
            raise ValueError("缺少MongoDB连接参数")

        # 创建MongoDB客户端
        client_key = connection_string
        if client_key not in self.mongo_clients:
            self.mongo_clients[client_key] = motor.motor_asyncio.AsyncIOMotorClient(
                connection_string,
                maxPoolSize=self.max_connections,
                serverSelectionTimeoutMS=int(self.connection_timeout * 1000)
            )

        client = self.mongo_clients[client_key]
        db = client[database]
        coll = db[collection]

        # 执行操作
        try:
            if operation == 'find':
                # 查询操作
                projection = db_config.get('projection')
                sort = db_config.get('sort')
                limit = db_config.get('limit')
                skip = db_config.get('skip')

                cursor = coll.find(query, projection)
                if sort:
                    cursor = cursor.sort(sort)
                if skip:
                    cursor = cursor.skip(skip)
                if limit:
                    cursor = cursor.limit(limit)

                results = await cursor.to_list(length=None)
                return {
                    'success': True,
                    'data': results,
                    'count': len(results)
                }

            elif operation == 'find_one':
                # 查询单个文档
                projection = db_config.get('projection')
                result = await coll.find_one(query, projection)
                return {
                    'success': True,
                    'data': result
                }

            elif operation == 'insert_one':
                # 插入单个文档
                document = db_config.get('document', {})
                result = await coll.insert_one(document)
                return {
                    'success': True,
                    'data': {'inserted_id': str(result.inserted_id)},
                    'inserted_id': str(result.inserted_id)
                }

            elif operation == 'insert_many':
                # 插入多个文档
                documents = db_config.get('documents', [])
                result = await coll.insert_many(documents)
                return {
                    'success': True,
                    'data': {'inserted_ids': [str(id) for id in result.inserted_ids]},
                    'inserted_count': len(result.inserted_ids)
                }

            elif operation == 'update_one':
                # 更新单个文档
                filter_doc = db_config.get('filter', query)
                update_doc = db_config.get('update', {})
                upsert = db_config.get('upsert', False)

                result = await coll.update_one(filter_doc, update_doc, upsert=upsert)
                return {
                    'success': True,
                    'data': {
                        'matched_count': result.matched_count,
                        'modified_count': result.modified_count,
                        'upserted_id': str(result.upserted_id) if result.upserted_id else None
                    },
                    'matched_count': result.matched_count,
                    'modified_count': result.modified_count
                }

            elif operation == 'update_many':
                # 更新多个文档
                filter_doc = db_config.get('filter', query)
                update_doc = db_config.get('update', {})
                upsert = db_config.get('upsert', False)

                result = await coll.update_many(filter_doc, update_doc, upsert=upsert)
                return {
                    'success': True,
                    'data': {
                        'matched_count': result.matched_count,
                        'modified_count': result.modified_count
                    },
                    'matched_count': result.matched_count,
                    'modified_count': result.modified_count
                }

            elif operation == 'delete_one':
                # 删除单个文档
                filter_doc = db_config.get('filter', query)
                result = await coll.delete_one(filter_doc)
                return {
                    'success': True,
                    'data': {'deleted_count': result.deleted_count},
                    'deleted_count': result.deleted_count
                }

            elif operation == 'delete_many':
                # 删除多个文档
                filter_doc = db_config.get('filter', query)
                result = await coll.delete_many(filter_doc)
                return {
                    'success': True,
                    'data': {'deleted_count': result.deleted_count},
                    'deleted_count': result.deleted_count
                }

            elif operation == 'aggregate':
                # 聚合操作
                pipeline = db_config.get('pipeline', [])
                if not pipeline:
                    raise ValueError("聚合操作需要提供pipeline")

                cursor = coll.aggregate(pipeline)
                results = await cursor.to_list(length=None)
                return {
                    'success': True,
                    'data': results,
                    'count': len(results)
                }

            elif operation == 'count_documents':
                # 计数操作
                filter_doc = db_config.get('filter', query)
                count = await coll.count_documents(filter_doc)
                return {
                    'success': True,
                    'data': {'count': count},
                    'count': count
                }

            elif operation == 'distinct':
                # 去重操作
                field = db_config.get('field')
                if not field:
                    raise ValueError("distinct操作需要提供field参数")

                filter_doc = db_config.get('filter', query)
                results = await coll.distinct(field, filter_doc)
                return {
                    'success': True,
                    'data': results,
                    'count': len(results)
                }

            elif operation == 'create_index':
                # 创建索引
                index_spec = db_config.get('index_spec')
                index_options = db_config.get('index_options', {})
                if not index_spec:
                    raise ValueError("创建索引需要提供index_spec")

                result = await coll.create_index(index_spec, **index_options)
                return {
                    'success': True,
                    'data': {'index_name': result}
                }

            elif operation == 'drop_index':
                # 删除索引
                index_name = db_config.get('index_name')
                if not index_name:
                    raise ValueError("删除索引需要提供index_name")

                await coll.drop_index(index_name)
                return {
                    'success': True,
                    'data': {'message': f'索引 {index_name} 已删除'}
                }

            else:
                raise ValueError(f"不支持的MongoDB操作: {operation}")

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'operation': operation
            }

    def _parse_database_config(self, data: Any, context: Optional[ProcessingContext]) -> Dict[str, Any]:
        """解析数据库配置"""
        _ = context  # 标记参数已使用
        if isinstance(data, str):
            if self._is_connection_string(data):
                # 检查是否为obclient连接字符串
                if data.startswith('obclient '):
                    # 解析obclient连接字符串
                    try:
                        from connectors import OceanBaseConnector
                        parsed_config = OceanBaseConnector.parse_obclient_string(data)
                        parsed_config['type'] = 'oceanbase'
                        parsed_config['operation'] = 'connect'
                        parsed_config['original_obclient'] = data
                        return parsed_config
                    except Exception as e:
                        self.logger.error(f"解析obclient连接字符串失败: {e}")
                        return {
                            'connection_string': data,
                            'operation': 'connect'
                        }
                else:
                    # 标准连接字符串
                    return {
                        'connection_string': data,
                        'operation': 'connect'
                    }
            elif self._is_sql_query(data):
                # SQL 查询
                return {
                    'query': data,
                    'operation': 'query'
                }
            else:
                raise ValueError(f"无法解析数据库配置: {data}")
        
        elif isinstance(data, dict):
            # 详细配置对象
            config = {
                'connection_string': data.get('connection_string'),
                'host': data.get('host'),
                'port': data.get('port'),
                'database': data.get('database'),
                'username': data.get('username'),
                'password': data.get('password'),
                'query': data.get('query'),
                'operation': data.get('operation', 'query'),
                'parameters': data.get('parameters', {}),
                'options': data.get('options', {}),
                # MongoDB特有配置
                'collection': data.get('collection'),
                'document': data.get('document'),
                'filter': data.get('filter'),
                'update': data.get('update'),
                'projection': data.get('projection'),
                'sort': data.get('sort'),
                'limit': data.get('limit'),
                'skip': data.get('skip'),
                'pipeline': data.get('pipeline'),  # 聚合管道
                # 脚本执行配置
                'script_file': data.get('script_file'),
                'script_content': data.get('script_content'),
                'batch_scripts': data.get('batch_scripts'),
                'script_type': data.get('script_type', 'sql'),  # sql, mongodb, redis
                'execute_mode': data.get('execute_mode', 'single')  # single, batch, transaction
            }
            
            # 如果没有连接字符串，尝试构建
            if not config['connection_string'] and config['host']:
                config['connection_string'] = self._build_connection_string(config)
            
            return config
        
        else:
            raise ValueError(f"不支持的数据库配置类型: {type(data)}")
    
    def _detect_database_type(self, config: Dict[str, Any]) -> str:
        """检测数据库类型"""
        connection_string = config.get('connection_string', '')
        
        if connection_string:
            # 从连接字符串检测
            if connection_string.startswith('mysql://'):
                return 'mysql'
            elif connection_string.startswith('postgresql://'):
                return 'postgresql'
            elif connection_string.startswith('oracle://'):
                return 'oracle'
            elif connection_string.startswith('oceanbase://'):
                return 'oceanbase'
            elif connection_string.startswith('sqlite://'):
                return 'sqlite'
            elif connection_string.startswith('mongodb://'):
                return 'mongodb'
            elif connection_string.startswith('redis://'):
                return 'redis'
        
        # 从端口号检测
        port = config.get('port')
        if port:
            port_mapping = {
                3306: 'mysql',
                5432: 'postgresql',
                1521: 'oracle',
                2881: 'oceanbase',  # OceanBase默认端口
                27017: 'mongodb',
                6379: 'redis',
                9200: 'elasticsearch'
            }
            if port in port_mapping:
                return port_mapping[port]
        
        # 默认返回 MySQL
        return 'mysql'
    
    def _is_connection_string(self, data: str) -> bool:
        """检查是否为数据库连接字符串"""
        db_schemes = ['mysql', 'postgresql', 'oracle', 'oceanbase', 'sqlite', 'mongodb', 'redis']
        # 检查标准连接字符串
        if any(data.startswith(f'{scheme}://') for scheme in db_schemes):
            return True
        # 检查obclient连接字符串
        if data.startswith('obclient ') and '-h' in data and '-P' in data:
            return True
        return False
    
    def _is_sql_query(self, data: str) -> bool:
        """检查是否为 SQL 查询"""
        if not isinstance(data, str):
            return False

        sql_patterns = [
            # 基本DML语句
            r'^\s*select\s+',
            r'^\s*insert\s+',
            r'^\s*update\s+',
            r'^\s*delete\s+',
            # DDL语句
            r'^\s*create\s+',
            r'^\s*drop\s+',
            r'^\s*alter\s+',
            r'^\s*truncate\s+',
            # DCL语句
            r'^\s*grant\s+',
            r'^\s*revoke\s+',
            # 配置语句
            r'^\s*set\s+',
            r'^\s*show\s+',
            r'^\s*describe\s+',
            r'^\s*desc\s+',
            r'^\s*explain\s+',
            # 事务控制
            r'^\s*begin\s*',
            r'^\s*commit\s*',
            r'^\s*rollback\s*',
            r'^\s*start\s+transaction\s*',
            # 存储过程和函数
            r'^\s*call\s+',
            r'^\s*exec\s+',
            r'^\s*execute\s+',
            # 语句块
            r'^\s*declare\s+',
            r'^\s*if\s+',
            r'^\s*while\s+',
            r'^\s*for\s+',
            r'^\s*loop\s+',
            # 多语句块（包含分号）
            r'.*;.*'
        ]

        data_lower = data.lower().strip()
        return any(re.match(pattern, data_lower) for pattern in sql_patterns)
    
    def _build_connection_string(self, config: Dict[str, Any]) -> str:
        """构建数据库连接字符串"""
        host = config.get('host', 'localhost')
        port = config.get('port')
        database = config.get('database', '')
        username = config.get('username', '')
        password = config.get('password', '')
        
        # 根据端口推断数据库类型
        db_type = self._detect_database_type(config)
        
        if username and password:
            auth = f"{username}:{password}@"
        else:
            auth = ""
        
        if port:
            host_port = f"{host}:{port}"
        else:
            host_port = host
        
        return f"{db_type}://{auth}{host_port}/{database}"
    
    def _get_connector(self, db_type: str):
        """获取数据库连接器"""
        return self.connectors.get(db_type)
    
    async def _get_connection_pool(self, config: Dict[str, Any], connector):
        """获取连接池"""
        connection_string = config['connection_string']
        
        if connection_string not in self.connection_pools:
            # 创建新的连接池
            pool = await connector.create_pool(
                connection_string,
                max_connections=self.max_connections,
                timeout=self.connection_timeout
            )
            self.connection_pools[connection_string] = pool
        
        return self.connection_pools[connection_string]
    
    async def _execute_operation(self, config: Dict[str, Any], pool, connector) -> Dict[str, Any]:
        """执行数据库操作"""
        operation = config.get('operation', 'query')
        
        if operation == 'query':
            return await self._execute_query(config, pool, connector)
        elif operation == 'connect':
            return await self._test_connection(pool, connector)
        else:
            raise ValueError(f"不支持的数据库操作: {operation}")
    
    async def _execute_query(self, config: Dict[str, Any], pool, connector) -> Dict[str, Any]:
        """执行查询"""
        query = config.get('query')
        parameters = config.get('parameters', {})
        
        if not query:
            raise ValueError("缺少查询语句")
        
        # 检查查询缓存
        if self.enable_cache:
            cache_key = f"{query}|{str(parameters)}"
            cached_result = await self._get_cached_query(cache_key)
            if cached_result:
                return cached_result

        # 执行查询
        result = await connector.execute_query(pool, query, parameters)

        # 缓存结果
        if self.enable_cache and result.get('success', False):
            await self._cache_query_result(cache_key, result)
        
        return result
    
    async def _test_connection(self, pool, connector) -> Dict[str, Any]:
        """测试数据库连接"""
        try:
            success = await connector.test_connection(pool)
            return {
                'success': success,
                'message': '连接成功' if success else '连接失败'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'连接失败: {e}'
            }
    
    async def _get_cached_query(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """获取缓存的查询结果（使用自适应缓存）"""
        try:
            # 优先使用自适应缓存
            if self.adaptive_cache:
                cached_result = await self.adaptive_cache.get(cache_key)
                if cached_result:
                    self.performance_metrics['cache_hits'] += 1
                    return cached_result
                else:
                    self.performance_metrics['cache_misses'] += 1

            # 后备使用简单缓存
            cached_item = self._query_cache.get(cache_key)
            if cached_item:
                # 检查缓存是否过期
                if time.time() - cached_item['timestamp'] < self.cache_ttl:
                    self.performance_metrics['cache_hits'] += 1
                    return cached_item['data']
                else:
                    # 删除过期缓存
                    del self._query_cache[cache_key]
                    self.performance_metrics['cache_misses'] += 1
            else:
                self.performance_metrics['cache_misses'] += 1

            return None

        except Exception as e:
            self.logger.warning(f"获取缓存失败: {e}")
            self.performance_metrics['cache_misses'] += 1
            return None

    async def _cache_query_result(self, cache_key: str, result: Dict[str, Any]) -> None:
        """缓存查询结果（使用自适应缓存）"""
        try:
            # 优先使用自适应缓存
            if self.adaptive_cache:
                await self.adaptive_cache.set(cache_key, result)
            else:
                # 后备使用简单缓存
                self._query_cache[cache_key] = {
                    'data': result,
                    'timestamp': time.time()
                }

        except Exception as e:
            self.logger.warning(f"缓存结果失败: {e}")
    
    async def _register_builtin_connectors(self) -> None:
        """注册内置连接器"""
        try:
            # 使用连接器工厂注册所有可用的连接器
            from .connectors import ConnectorFactory

            available_types = ConnectorFactory.get_available_types()
            registered_count = 0

            for db_type, is_available in available_types.items():
                if is_available:
                    try:
                        connector = ConnectorFactory.create_connector(db_type, self.enable_debug)
                        self.connectors[db_type] = connector
                        registered_count += 1
                        self.logger.debug(f"注册连接器: {db_type}")
                    except Exception as e:
                        self.logger.warning(f"注册连接器失败 {db_type}: {e}")
                else:
                    self.logger.debug(f"跳过不可用的数据库类型: {db_type}")

            self.logger.info(f"注册了 {registered_count}/{len(available_types)} 个数据库连接器")

            # 记录企业级数据库状态
            enterprise_dbs = ['postgresql', 'mongodb', 'elasticsearch']
            available_enterprise = [db for db in enterprise_dbs if available_types.get(db, False)]
            if available_enterprise:
                self.logger.info(f"企业级数据库可用: {', '.join(available_enterprise)}")
            else:
                self.logger.info("企业级数据库不可用，仅使用基础数据库")

        except ImportError as e:
            self.logger.warning(f"连接器工厂导入失败: {e}")
            # 注册基础连接器
            self._register_fallback_connectors()

    def _register_fallback_connectors(self):
        """注册回退连接器"""
        try:
            from .connectors import BaseConnector

            # 为所有数据库类型注册基础连接器
            base_connector = BaseConnector(self.enable_debug)
            for db_type in ['mysql', 'postgresql', 'sqlite', 'mongodb', 'redis']:
                self.connectors[db_type] = base_connector

            self.logger.info("注册了回退数据库连接器")
        except Exception as e:
            self.logger.error(f"注册回退连接器失败: {e}")
            self.connectors = {}
    
    async def selftest(self) -> bool:
        """自检功能"""
        try:
            # 测试 SQLite 内存数据库（使用aiosqlite）
            test_config = {
                'connection_string': 'sqlite+aiosqlite:///:memory:',
                'query': 'SELECT 1 as test_value',
                'operation': 'query'
            }
            
            result = await self.process(test_config)
            return result.get('success', False)
        except Exception as e:
            self.logger.error(f"数据库处理器自检失败: {e}")
            return False

    async def query_sql(self, connection_string: str, query: str) -> Any:
        """SQL查询便捷方法"""
        try:
            config = {
                'connection_string': connection_string,
                'query': query
            }
            result = await self.process(config)
            if result.get('success') and result.get('modifier'):
                return result['modifier'].data
            return result.get('result')
        except Exception as e:
            self.logger.error(f"SQL查询失败: {e}")
            return None

    async def query_mongo(self, connection_string: str, database: str, collection: str, query: Dict[str, Any] = None) -> Any:
        """MongoDB查询便捷方法"""
        try:
            config = {
                'connection_string': connection_string,
                'database': database,
                'collection': collection,
                'operation': 'find',
                'query': query or {}
            }
            result = await self.process(config)
            if result.get('success') and result.get('modifier'):
                return result['modifier'].data
            return result.get('result')
        except Exception as e:
            self.logger.error(f"MongoDB查询失败: {e}")
            return None

    async def close_all_connections(self) -> None:
        """关闭所有数据库连接"""
        try:
            # 关闭SQLAlchemy引擎
            for engine in self.engines.values():
                await engine.dispose()
            self.engines.clear()

            # 关闭MongoDB客户端
            for client in self.mongo_clients.values():
                client.close()
            self.mongo_clients.clear()

            # 关闭PostgreSQL连接池
            for pool in self.pg_pools.values():
                await pool.close()
            self.pg_pools.clear()

            self.logger.debug("所有数据库连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭数据库连接失败: {e}")

    def create_smart_connector(self, connection_string: str):
        """创建智能数据库连接器 - 集成新的连接器工厂"""
        try:
            # 尝试使用新的数据库连接器工厂
            from core.database_connectors import DatabaseConnectorFactory
            connector = DatabaseConnectorFactory.create_connector(connection_string)
            self.logger.info(f"使用智能工厂创建连接器: {connector.get_database_type()}")
            return connector
        except ImportError:
            # 回退到原有逻辑
            self.logger.warning("数据库连接器工厂不可用，使用原有逻辑")
            return None
        except Exception as e:
            self.logger.error(f"创建智能连接器失败: {e}")
            return None

    def get_enhanced_processor_info(self) -> Dict[str, Any]:
        """获取增强的处理器信息"""
        base_info = self.get_processor_info()

        # 添加新功能信息
        enhanced_info = {
            **base_info,
            "smart_connector_factory": True,
            "database_specific_connectors": [
                "MySQLConnector", "PostgreSQLConnector",
                "SQLiteConnector", "MongoDBConnector"
            ],
            "intelligent_matching": True,
            "driver_specific_features": True
        }

        return enhanced_info

    def get_processor_info(self) -> Dict[str, Any]:
        """获取处理器信息"""
        return {
            'id': self.processor_id,
            'name': '统一数据库处理器',
            'version': self.version,
            'description': '提供完整的数据库处理能力',
            'supported_databases': ['mysql', 'postgresql', 'sqlite', 'mongodb', 'redis', 'elasticsearch', 'oracle', 'oceanbase'],
            'supported_operations': ['select', 'insert', 'update', 'delete'],
            'capabilities': [
                'multi_database_support',
                'connection_pooling',
                'query_optimization',
                'intelligent_caching',
                'transaction_management',
                'security_authentication',
                'performance_monitoring',
                'failover_support',
                'batch_processing',
                'async_processing'
            ],
            'priority': self.priority
        }


# 企业级数据库处理器别名 - 向后兼容
class EnterpriseDatabaseProcessor(DatabaseProcessor):
    """企业级数据库处理器 - 兼容接口"""

    def __init__(self):
        super().__init__()
        # 更新处理器信息以匹配企业级接口
        self.processor_id = "enterprise_database_processor"
        self.version = "2.0.0"
        self.priority = 80

    def can_process(self, data: Any) -> bool:
        """检查是否可以处理数据 - 兼容接口"""
        # 使用父类的detect方法
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            confidence = loop.run_until_complete(self.detect(data))
            return confidence > 0.5
        except:
            # 如果异步调用失败，使用简单的检测逻辑
            if isinstance(data, dict):
                db_fields = ['query', 'sql', 'connection_string', 'database_type', 'database_config']
                return any(field in data for field in db_fields)
            elif isinstance(data, str):
                sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER']
                data_upper = data.upper().strip()
                return (any(data_upper.startswith(keyword) for keyword in sql_keywords) or
                        '://' in data)
            return False

    def get_processor_info(self) -> Dict[str, Any]:
        """获取处理器信息 - 兼容接口"""
        return {
            'id': self.processor_id,
            'name': '企业级数据库处理器',
            'version': self.version,
            'description': '提供完整的企业级数据库处理能力',
            'supported_databases': ['mysql', 'postgresql', 'sqlite', 'mongodb', 'redis', 'elasticsearch', 'oracle', 'oceanbase'],
            'supported_operations': ['select', 'insert', 'update', 'delete'],
            'capabilities': [
                'multi_database_support',
                'connection_pooling',
                'query_optimization',
                'intelligent_caching',
                'transaction_management',
                'security_authentication',
                'performance_monitoring',
                'failover_support',
                'batch_processing',
                'async_processing'
            ],
            'priority': self.priority
        }

    def get_supported_services(self) -> List[str]:
        """获取支持的服务 - 兼容接口"""
        return [
            'query',
            'test_connection',
            'list_tables',
            'describe_table',
            'get_stats',
            'batch_query',
            'transaction'
        ]
