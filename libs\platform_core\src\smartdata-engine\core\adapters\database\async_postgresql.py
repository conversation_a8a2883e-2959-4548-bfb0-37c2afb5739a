"""
异步PostgreSQL数据库适配器

提供高性能的异步PostgreSQL数据库操作，支持连接池和流式查询
"""

from typing import Any, Dict, List, Optional, Union, AsyncIterator
import logging

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../'))

from core.unified_adapter import UnifiedDataAdapter
from core.adapters.base import ConnectionInfo

# 尝试导入异步PostgreSQL驱动
try:
    import asyncpg
    ASYNCPG_AVAILABLE = True
except ImportError:
    ASYNCPG_AVAILABLE = False
    asyncpg = None


class AsyncPostgreSQLAdapter(UnifiedDataAdapter):
    """
    异步PostgreSQL数据库适配器
    
    支持PostgreSQL特有的异步功能：
    - 异步连接和连接池
    - 流式查询处理大结果集
    - 异步事务管理
    - COPY操作
    - LISTEN/NOTIFY异步消息
    """
    
    def __init__(self):
        super().__init__()
        if not ASYNCPG_AVAILABLE:
            self.logger.warning("asyncpg未安装，异步PostgreSQL功能受限")
    
    def supported_types(self) -> List[str]:
        """返回支持的数据类型列表"""
        return [
            'postgresql',
            'postgresql_connection',
            'postgresql_connection_string',
            'postgresql_url',
            'postgres',
            'postgres_connection',
            'postgres_connection_string',
            'postgres_url'
        ]
    
    def _is_supported_connection_string(self, connection_string: str) -> bool:
        """检查是否支持的连接字符串"""
        return connection_string.startswith(('postgresql://', 'postgres://'))
    
    def _is_supported_connection_object(self, connection: Any) -> bool:
        """检查是否支持的连接对象"""
        if not ASYNCPG_AVAILABLE:
            return False
        
        # 检查是否是asyncpg连接对象
        return (hasattr(connection, 'fetch') and 
                hasattr(connection, 'execute') and
                'asyncpg' in str(type(connection)))
    
    def _get_default_port(self) -> int:
        """获取默认端口"""
        return 5432

    def can_handle(self, data_source: Any) -> bool:
        """检查是否能处理指定的数据源"""
        if isinstance(data_source, str):
            return self._is_supported_connection_string(data_source)
        else:
            return self._is_supported_connection_object(data_source)

    def _build_operations(self) -> Dict[str, callable]:
        """构建PostgreSQL特有操作列表"""
        operations = {}

        # 添加异步操作
        async_ops = self.get_async_operations()
        for name, func in async_ops.items():
            operations[name] = func

        return operations
    
    # ========================================================================
    # 同步方法实现（兼容性）
    # ========================================================================
    
    def _sync_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """同步查询实现 - 使用psycopg2"""
        # 这里可以使用psycopg2作为同步实现
        # 为了简化，这里抛出异常提示使用异步版本
        raise NotImplementedError("请使用异步版本的PostgreSQL适配器")
    
    def _sync_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """同步执行实现"""
        raise NotImplementedError("请使用异步版本的PostgreSQL适配器")
    
    def _sync_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """同步事务实现"""
        raise NotImplementedError("请使用异步版本的PostgreSQL适配器")
    
    # ========================================================================
    # 异步方法实现
    # ========================================================================
    
    async def _create_async_connection(self, connection_source: Any) -> Any:
        """创建异步PostgreSQL连接"""
        if not ASYNCPG_AVAILABLE:
            raise ImportError("asyncpg未安装，无法创建异步PostgreSQL连接")
        
        if isinstance(connection_source, str):
            # 直接使用连接字符串
            connection_string = connection_source
        else:
            # 从ConnectionInfo构建连接字符串
            conn_info = connection_source
            connection_string = (
                f"postgresql://{conn_info.username}:{conn_info.password}@"
                f"{conn_info.host}:{conn_info.port}/{conn_info.database}"
            )
            
            # 添加额外选项
            if conn_info.options:
                params = '&'.join([f"{k}={v}" for k, v in conn_info.options.items()])
                connection_string += f"?{params}"
        
        try:
            # 创建异步连接
            connection = await asyncpg.connect(connection_string)
            
            self.logger.info(f"成功创建异步PostgreSQL连接: {connection_string.split('@')[1] if '@' in connection_string else connection_string}")
            return connection
            
        except Exception as e:
            self.logger.error(f"创建异步PostgreSQL连接失败: {e}")
            raise
    
    async def _close_async_connection(self, connection: Any) -> None:
        """关闭异步连接"""
        try:
            await connection.close()
            self.logger.debug("异步PostgreSQL连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭异步PostgreSQL连接失败: {e}")
    
    async def _create_async_pool(self, connection_source: Any, min_size: int = 5, max_size: int = 20) -> Any:
        """创建异步连接池"""
        if not ASYNCPG_AVAILABLE:
            raise ImportError("asyncpg未安装，无法创建异步连接池")
        
        if isinstance(connection_source, str):
            connection_string = connection_source
        else:
            conn_info = connection_source
            connection_string = (
                f"postgresql://{conn_info.username}:{conn_info.password}@"
                f"{conn_info.host}:{conn_info.port}/{conn_info.database}"
            )
        
        try:
            # 创建连接池
            pool = await asyncpg.create_pool(
                connection_string,
                min_size=min_size,
                max_size=max_size,
                command_timeout=60
            )
            
            self.logger.info(f"成功创建异步PostgreSQL连接池: min={min_size}, max={max_size}")
            return pool
            
        except Exception as e:
            self.logger.error(f"创建异步PostgreSQL连接池失败: {e}")
            raise
    
    async def _async_query(self, connection: Any, sql: str, params: Dict = None) -> List[Dict]:
        """异步查询实现"""
        try:
            if params:
                # 使用位置参数
                param_values = list(params.values())
                rows = await connection.fetch(sql, *param_values)
            else:
                rows = await connection.fetch(sql)
            
            # 转换为字典列表
            return [dict(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"异步PostgreSQL查询失败: {e}")
            raise
    
    async def _async_execute(self, connection: Any, sql: str, params: Dict = None) -> int:
        """异步执行实现"""
        try:
            if params:
                param_values = list(params.values())
                result = await connection.execute(sql, *param_values)
            else:
                result = await connection.execute(sql)
            
            # 解析影响行数
            if result.startswith('INSERT'):
                return int(result.split()[-1])
            elif result.startswith('UPDATE'):
                return int(result.split()[-1])
            elif result.startswith('DELETE'):
                return int(result.split()[-1])
            else:
                return 0
                
        except Exception as e:
            self.logger.error(f"异步PostgreSQL执行失败: {e}")
            raise
    
    async def _async_transaction(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步事务实现"""
        try:
            async with connection.transaction():
                results = []
                total_affected = 0
                
                for operation in operations:
                    op_type = operation.get('type', 'execute')
                    sql = operation['sql']
                    params = operation.get('params')
                    
                    if op_type == 'query':
                        result = await self._async_query(connection, sql, params)
                        results.append(result)
                    else:
                        affected = await self._async_execute(connection, sql, params)
                        results.append(affected)
                        total_affected += affected
                
                return {
                    'results': results,
                    'total_operations': len(operations),
                    'total_affected': total_affected,
                    'success': True
                }
                
        except Exception as e:
            self.logger.error(f"异步PostgreSQL事务失败: {e}")
            raise
    
    async def _async_batch(self, connection: Any, operations: List[Dict]) -> Dict:
        """异步批量操作实现"""
        results = []
        total_affected = 0
        
        for operation in operations:
            op_type = operation.get('type', 'execute')
            sql = operation['sql']
            params = operation.get('params')
            
            if op_type == 'query':
                result = await self._async_query(connection, sql, params)
                results.append(result)
            else:
                affected = await self._async_execute(connection, sql, params)
                results.append(affected)
                total_affected += affected
        
        return {
            'results': results,
            'total_operations': len(operations),
            'total_affected': total_affected,
            'success': True
        }
    
    async def _async_stream_query(self, connection: Any, sql: str, params: Dict = None) -> AsyncIterator[Dict]:
        """异步流式查询实现"""
        try:
            async with connection.transaction():
                if params:
                    param_values = list(params.values())
                    async for row in connection.cursor(sql, *param_values):
                        yield dict(row)
                else:
                    async for row in connection.cursor(sql):
                        yield dict(row)
                        
        except Exception as e:
            self.logger.error(f"异步PostgreSQL流式查询失败: {e}")
            raise
    
    # ========================================================================
    # PostgreSQL特有异步功能
    # ========================================================================
    
    def get_async_operations(self) -> Dict[str, Any]:
        """获取支持的异步操作列表"""
        operations = super().get_async_operations()
        
        # 添加PostgreSQL特有异步操作
        operations.update({
            'async_copy_from': self._async_copy_from,
            'async_copy_to': self._async_copy_to,
            'async_listen': self._async_listen,
            'async_notify': self._async_notify,
            'async_vacuum': self._async_vacuum,
        })
        
        return operations
    
    async def _async_copy_from(self, connection: Any, table: str, file_path: str,
                              columns: List[str] = None, delimiter: str = '\t') -> Dict:
        """异步COPY FROM操作"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if columns:
                    await connection.copy_to_table(table, source=f, columns=columns, delimiter=delimiter)
                else:
                    await connection.copy_to_table(table, source=f, delimiter=delimiter)

            return {
                'success': True,
                'table': table,
                'file_path': file_path,
                'operation': 'async_copy_from'
            }

        except Exception as e:
            self.logger.error(f"异步COPY FROM操作失败: {e}")
            raise
    
    async def _async_copy_to(self, connection: Any, table: str, file_path: str,
                            columns: List[str] = None, delimiter: str = '\t') -> Dict:
        """异步COPY TO操作"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                if columns:
                    await connection.copy_from_table(table, output=f, columns=columns, delimiter=delimiter)
                else:
                    await connection.copy_from_table(table, output=f, delimiter=delimiter)
            
            return {
                'success': True,
                'table': table,
                'file_path': file_path,
                'operation': 'async_copy_to'
            }
            
        except Exception as e:
            self.logger.error(f"异步COPY TO操作失败: {e}")
            raise
    
    async def _async_listen(self, connection: Any, channel: str) -> Dict:
        """异步LISTEN操作"""
        try:
            await connection.add_listener(channel, self._notification_handler)
            
            return {
                'success': True,
                'channel': channel,
                'status': 'listening',
                'operation': 'async_listen'
            }
            
        except Exception as e:
            self.logger.error(f"异步LISTEN操作失败: {e}")
            raise
    
    async def _async_notify(self, connection: Any, channel: str, payload: str = None) -> Dict:
        """异步NOTIFY操作"""
        try:
            if payload:
                await connection.execute("SELECT pg_notify($1, $2)", channel, payload)
            else:
                await connection.execute("SELECT pg_notify($1, '')", channel)
            
            return {
                'success': True,
                'channel': channel,
                'payload': payload,
                'status': 'sent',
                'operation': 'async_notify'
            }
            
        except Exception as e:
            self.logger.error(f"异步NOTIFY操作失败: {e}")
            raise
    
    async def _async_vacuum(self, connection: Any, table: str = None, full: bool = False) -> Dict:
        """异步VACUUM操作"""
        try:
            if table:
                vacuum_sql = f"VACUUM {'FULL' if full else ''} {table}"
            else:
                vacuum_sql = f"VACUUM {'FULL' if full else ''}"
            
            await connection.execute(vacuum_sql)
            
            return {
                'success': True,
                'operation': 'async_vacuum',
                'table': table,
                'full': full
            }
            
        except Exception as e:
            self.logger.error(f"异步VACUUM操作失败: {e}")
            raise
    
    def _notification_handler(self, connection, pid, channel, payload):
        """通知处理器"""
        self.logger.info(f"收到通知 - 频道: {channel}, 负载: {payload}, PID: {pid}")
    
    async def get_async_database_info(self, connection: Any) -> Dict[str, Any]:
        """获取异步数据库信息"""
        try:
            # 获取版本信息
            version_result = await connection.fetchrow("SELECT version()")
            
            # 获取数据库名称
            db_result = await connection.fetchrow("SELECT current_database()")
            
            # 获取用户信息
            user_result = await connection.fetchrow("SELECT current_user, session_user")
            
            return {
                'database_type': 'PostgreSQL',
                'version': version_result['version'] if version_result else 'Unknown',
                'database_name': db_result['current_database'] if db_result else 'Unknown',
                'current_user': user_result['current_user'] if user_result else 'Unknown',
                'session_user': user_result['session_user'] if user_result else 'Unknown',
                'async_capable': True
            }
            
        except Exception as e:
            self.logger.error(f"获取异步PostgreSQL数据库信息失败: {e}")
            return {
                'database_type': 'PostgreSQL',
                'error': str(e),
                'async_capable': True
            }
