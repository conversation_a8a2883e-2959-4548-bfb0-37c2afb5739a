"""
数据库连接器

实现各种数据库的真实连接和操作，支持企业级特性
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, Union, Tuple
from urllib.parse import urlparse
import json
from abc import ABC, abstractmethod
from dataclasses import dataclass
from contextlib import asynccontextmanager

# 数据库驱动导入
try:
    import aiomysql
    AIOMYSQL_AVAILABLE = True
except ImportError:
    AIOMYSQL_AVAILABLE = False

try:
    import asyncpg
    ASYNCPG_AVAILABLE = True
except ImportError:
    ASYNCPG_AVAILABLE = False

try:
    import aiosqlite
    AIOSQLITE_AVAILABLE = True
except ImportError:
    AIOSQLITE_AVAILABLE = False

try:
    import motor.motor_asyncio
    MOTOR_AVAILABLE = True
except ImportError:
    MOTOR_AVAILABLE = False

try:
    import aioredis
    AIOREDIS_AVAILABLE = True
    # 检查RedisJSON支持
    try:
        import redis.commands.json
        REDISJSON_AVAILABLE = True
    except ImportError:
        REDISJSON_AVAILABLE = False
except (ImportError, TypeError) as e:
    # TypeError可能由于aioredis版本兼容性问题导致
    AIOREDIS_AVAILABLE = False
    REDISJSON_AVAILABLE = False

try:
    from elasticsearch import AsyncElasticsearch
    ELASTICSEARCH_AVAILABLE = True
except ImportError:
    ELASTICSEARCH_AVAILABLE = False

try:
    import cx_Oracle_async
    ORACLE_AVAILABLE = True
except ImportError:
    try:
        import oracledb
        ORACLE_AVAILABLE = True
    except ImportError:
        ORACLE_AVAILABLE = False

try:
    import aiomysql  # OceanBase兼容MySQL协议
    OCEANBASE_AVAILABLE = True
except ImportError:
    OCEANBASE_AVAILABLE = False


@dataclass
class ConnectionConfig:
    """数据库连接配置"""
    host: str = "localhost"
    port: int = 3306
    database: str = ""
    username: str = ""
    password: str = ""
    charset: str = "utf8mb4"
    ssl: bool = False
    timeout: float = 30.0
    max_connections: int = 10
    min_connections: int = 1
    max_idle_time: float = 3600.0
    retry_attempts: int = 3
    retry_delay: float = 1.0


@dataclass
class QueryResult:
    """查询结果"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    affected_rows: int = 0
    execution_time: float = 0.0
    query: str = ""
    parameters: Dict[str, Any] = None

    @property
    def row_count(self) -> int:
        """兼容企业级接口的行数属性"""
        return self.affected_rows


@dataclass
class ConnectionResult:
    """连接结果 - 兼容企业级连接器"""
    success: bool
    connection: Any = None
    info: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class BaseConnector(ABC):
    """基础数据库连接器抽象类"""

    def __init__(self, enable_debug: bool = False):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.enable_debug = enable_debug
        self.pools: Dict[str, Any] = {}
        self.connection_stats = {
            'total_connections': 0,
            'active_connections': 0,
            'failed_connections': 0,
            'total_queries': 0,
            'failed_queries': 0,
            'avg_query_time': 0.0
        }

    @abstractmethod
    async def create_pool(self, config: ConnectionConfig) -> Any:
        """创建连接池"""
        pass

    @abstractmethod
    async def execute_query(self, pool: Any, query: str, parameters: Dict[str, Any] = None) -> QueryResult:
        """执行查询"""
        pass

    @abstractmethod
    async def close_pool(self, pool: Any) -> None:
        """关闭连接池"""
        pass

    async def test_connection(self, pool: Any) -> bool:
        """测试连接"""
        try:
            result = await self.execute_query(pool, "SELECT 1", {})
            return result.success
        except Exception as e:
            self.logger.error(f"连接测试失败: {e}")
            return False

    def get_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        return self.connection_stats.copy()

    def _update_stats(self, query_time: float = 0.0, success: bool = True):
        """更新统计信息"""
        self.connection_stats['total_queries'] += 1
        if not success:
            self.connection_stats['failed_queries'] += 1

        # 更新平均查询时间
        total_queries = self.connection_stats['total_queries']
        current_avg = self.connection_stats['avg_query_time']
        self.connection_stats['avg_query_time'] = (
            (current_avg * (total_queries - 1) + query_time) / total_queries
        )


class MysqlConnector(BaseConnector):
    """MySQL 连接器 - 使用aiomysql"""

    def __init__(self, enable_debug: bool = False):
        super().__init__(enable_debug)
        if not AIOMYSQL_AVAILABLE:
            self.logger.warning("aiomysql不可用，MySQL连接器将无法正常工作")

    async def create_pool(self, config: ConnectionConfig) -> Any:
        """创建 MySQL 连接池"""
        if not AIOMYSQL_AVAILABLE:
            raise RuntimeError("aiomysql不可用，请安装: pip install aiomysql")

        try:
            pool = await aiomysql.create_pool(
                host=config.host,
                port=config.port,
                user=config.username,
                password=config.password,
                db=config.database,
                charset=config.charset,
                maxsize=config.max_connections,
                minsize=config.min_connections,
                connect_timeout=config.timeout,
                autocommit=True
            )

            self.connection_stats['total_connections'] += 1
            self.logger.info(f"MySQL连接池创建成功: {config.host}:{config.port}/{config.database}")
            return pool

        except Exception as e:
            self.connection_stats['failed_connections'] += 1
            self.logger.error(f"MySQL连接池创建失败: {e}")
            raise

    async def execute_query(self, pool: Any, query: str, parameters: Dict[str, Any] = None) -> QueryResult:
        """执行 MySQL 查询"""
        start_time = time.time()

        try:
            async with pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    if parameters:
                        await cursor.execute(query, parameters)
                    else:
                        await cursor.execute(query)

                    execution_time = time.time() - start_time

                    if query.strip().upper().startswith('SELECT'):
                        # SELECT查询
                        rows = await cursor.fetchall()
                        result = QueryResult(
                            success=True,
                            data=list(rows),
                            affected_rows=len(rows),
                            execution_time=execution_time,
                            query=query,
                            parameters=parameters or {}
                        )
                    else:
                        # INSERT/UPDATE/DELETE查询
                        affected_rows = cursor.rowcount
                        result = QueryResult(
                            success=True,
                            data={'affected_rows': affected_rows},
                            affected_rows=affected_rows,
                            execution_time=execution_time,
                            query=query,
                            parameters=parameters or {}
                        )

                    self._update_stats(execution_time, True)
                    if self.enable_debug:
                        self.logger.debug(f"MySQL查询执行成功: {query[:100]}... 耗时: {execution_time:.3f}s")

                    return result

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(execution_time, False)
            self.logger.error(f"MySQL查询执行失败: {e}")

            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                query=query,
                parameters=parameters or {}
            )

    async def close_pool(self, pool: Any) -> None:
        """关闭 MySQL 连接池"""
        try:
            pool.close()
            await pool.wait_closed()
            self.logger.debug("MySQL连接池已关闭")
        except Exception as e:
            self.logger.error(f"关闭MySQL连接池失败: {e}")


class PostgresConnector(BaseConnector):
    """PostgreSQL 连接器 - 使用asyncpg"""

    def __init__(self, enable_debug: bool = False):
        super().__init__(enable_debug)
        if not ASYNCPG_AVAILABLE:
            self.logger.warning("asyncpg不可用，PostgreSQL连接器将无法正常工作")

    async def create_pool(self, config: ConnectionConfig) -> Any:
        """创建 PostgreSQL 连接池"""
        if not ASYNCPG_AVAILABLE:
            raise RuntimeError("asyncpg不可用，请安装: pip install asyncpg")

        try:
            # 构建连接字符串
            dsn = f"postgresql://{config.username}:{config.password}@{config.host}:{config.port}/{config.database}"

            pool = await asyncpg.create_pool(
                dsn,
                min_size=config.min_connections,
                max_size=config.max_connections,
                command_timeout=config.timeout,
                server_settings={
                    'application_name': 'smartdata_engine',
                    'timezone': 'UTC'
                }
            )

            self.connection_stats['total_connections'] += 1
            self.logger.info(f"PostgreSQL连接池创建成功: {config.host}:{config.port}/{config.database}")
            return pool

        except Exception as e:
            self.connection_stats['failed_connections'] += 1
            self.logger.error(f"PostgreSQL连接池创建失败: {e}")
            raise

    async def execute_query(self, pool: Any, query: str, parameters: Dict[str, Any] = None) -> QueryResult:
        """执行 PostgreSQL 查询"""
        start_time = time.time()

        try:
            async with pool.acquire() as conn:
                # 转换参数格式（asyncpg使用$1, $2格式）
                if parameters:
                    # 简单的参数替换，实际应用中需要更复杂的处理
                    param_values = list(parameters.values())
                    result_rows = await conn.fetch(query, *param_values)
                else:
                    result_rows = await conn.fetch(query)

                execution_time = time.time() - start_time

                if query.strip().upper().startswith('SELECT'):
                    # 转换为字典列表
                    data = [dict(row) for row in result_rows]
                    result = QueryResult(
                        success=True,
                        data=data,
                        affected_rows=len(data),
                        execution_time=execution_time,
                        query=query,
                        parameters=parameters or {}
                    )
                else:
                    # 对于非SELECT查询，使用execute方法
                    if parameters:
                        param_values = list(parameters.values())
                        status = await conn.execute(query, *param_values)
                    else:
                        status = await conn.execute(query)

                    # 解析affected rows from status (e.g., "INSERT 0 1")
                    affected_rows = 0
                    if status:
                        parts = status.split()
                        if len(parts) > 1 and parts[-1].isdigit():
                            affected_rows = int(parts[-1])

                    result = QueryResult(
                        success=True,
                        data={'affected_rows': affected_rows},
                        affected_rows=affected_rows,
                        execution_time=execution_time,
                        query=query,
                        parameters=parameters or {}
                    )

                self._update_stats(execution_time, True)
                if self.enable_debug:
                    self.logger.debug(f"PostgreSQL查询执行成功: {query[:100]}... 耗时: {execution_time:.3f}s")

                return result

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(execution_time, False)
            self.logger.error(f"PostgreSQL查询执行失败: {e}")

            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                query=query,
                parameters=parameters or {}
            )

    async def close_pool(self, pool: Any) -> None:
        """关闭 PostgreSQL 连接池"""
        try:
            await pool.close()
            self.logger.debug("PostgreSQL连接池已关闭")
        except Exception as e:
            self.logger.error(f"关闭PostgreSQL连接池失败: {e}")


class SqliteConnector(BaseConnector):
    """SQLite 连接器 - 使用aiosqlite"""

    def __init__(self, enable_debug: bool = False):
        super().__init__(enable_debug)
        if not AIOSQLITE_AVAILABLE:
            self.logger.warning("aiosqlite不可用，SQLite连接器将无法正常工作")

    async def create_pool(self, config: ConnectionConfig) -> Any:
        """创建 SQLite 连接（SQLite不使用连接池，返回数据库路径）"""
        if not AIOSQLITE_AVAILABLE:
            raise RuntimeError("aiosqlite不可用，请安装: pip install aiosqlite")

        try:
            # SQLite使用文件路径，从config中提取
            db_path = config.database or ":memory:"

            # 测试连接
            async with aiosqlite.connect(db_path) as conn:
                await conn.execute("SELECT 1")

            self.connection_stats['total_connections'] += 1
            self.logger.info(f"SQLite连接创建成功: {db_path}")

            # 返回数据库路径作为"连接池"
            return {'db_path': db_path, 'timeout': config.timeout}

        except Exception as e:
            self.connection_stats['failed_connections'] += 1
            self.logger.error(f"SQLite连接创建失败: {e}")
            raise

    async def execute_query(self, pool: Any, query: str, parameters: Dict[str, Any] = None) -> QueryResult:
        """执行 SQLite 查询"""
        start_time = time.time()
        db_path = pool['db_path']

        try:
            async with aiosqlite.connect(db_path) as conn:
                # 设置row_factory以获取字典格式结果
                conn.row_factory = aiosqlite.Row

                cursor = await conn.cursor()

                if parameters:
                    # 转换参数格式
                    if isinstance(parameters, dict):
                        # 命名参数
                        await cursor.execute(query, parameters)
                    else:
                        # 位置参数
                        await cursor.execute(query, parameters)
                else:
                    await cursor.execute(query)

                execution_time = time.time() - start_time

                if query.strip().upper().startswith('SELECT'):
                    # SELECT查询
                    rows = await cursor.fetchall()
                    data = [dict(row) for row in rows]
                    result = QueryResult(
                        success=True,
                        data=data,
                        affected_rows=len(data),
                        execution_time=execution_time,
                        query=query,
                        parameters=parameters or {}
                    )
                else:
                    # INSERT/UPDATE/DELETE查询
                    await conn.commit()
                    affected_rows = cursor.rowcount
                    result = QueryResult(
                        success=True,
                        data={'affected_rows': affected_rows},
                        affected_rows=affected_rows,
                        execution_time=execution_time,
                        query=query,
                        parameters=parameters or {}
                    )

                await cursor.close()

                self._update_stats(execution_time, True)
                if self.enable_debug:
                    self.logger.debug(f"SQLite查询执行成功: {query[:100]}... 耗时: {execution_time:.3f}s")

                return result

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(execution_time, False)
            self.logger.error(f"SQLite查询执行失败: {e}")

            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                query=query,
                parameters=parameters or {}
            )

    async def close_pool(self, pool: Any) -> None:
        """关闭 SQLite 连接（SQLite无需特殊关闭操作）"""
        _ = pool  # 标记参数已使用
        self.logger.debug("SQLite连接已关闭")


class MongoConnector(BaseConnector):
    """MongoDB 连接器 - 使用motor"""

    def __init__(self, enable_debug: bool = False):
        super().__init__(enable_debug)
        if not MOTOR_AVAILABLE:
            self.logger.warning("motor不可用，MongoDB连接器将无法正常工作")

    async def create_pool(self, config: ConnectionConfig) -> Any:
        """创建 MongoDB 连接"""
        if not MOTOR_AVAILABLE:
            raise RuntimeError("motor不可用，请安装: pip install motor")

        try:
            # 构建MongoDB连接字符串
            if config.username and config.password:
                connection_string = f"mongodb://{config.username}:{config.password}@{config.host}:{config.port}/{config.database}"
            else:
                connection_string = f"mongodb://{config.host}:{config.port}/{config.database}"

            client = motor.motor_asyncio.AsyncIOMotorClient(
                connection_string,
                maxPoolSize=config.max_connections,
                minPoolSize=config.min_connections,
                maxIdleTimeMS=int(config.max_idle_time * 1000),
                serverSelectionTimeoutMS=int(config.timeout * 1000),
                connectTimeoutMS=int(config.timeout * 1000)
            )

            # 测试连接
            await client.admin.command('ping')

            self.connection_stats['total_connections'] += 1
            self.logger.info(f"MongoDB连接创建成功: {config.host}:{config.port}/{config.database}")

            return {
                'client': client,
                'database': config.database
            }

        except Exception as e:
            self.connection_stats['failed_connections'] += 1
            self.logger.error(f"MongoDB连接创建失败: {e}")
            raise

    async def execute_query(self, pool: Any, query: str, parameters: Dict[str, Any] = None) -> QueryResult:
        """执行 MongoDB 查询"""
        start_time = time.time()
        client = pool['client']
        database_name = pool['database']

        try:
            # 解析MongoDB查询（简化实现）
            # 实际应用中需要更复杂的查询解析器
            db = client[database_name]

            # 假设query是JSON格式的MongoDB查询
            try:
                query_obj = json.loads(query) if isinstance(query, str) else query
            except json.JSONDecodeError:
                raise ValueError(f"无效的MongoDB查询格式: {query}")

            collection_name = query_obj.get('collection', 'default')
            operation = query_obj.get('operation', 'find')
            filter_obj = query_obj.get('filter', {})
            options = query_obj.get('options', {})

            collection = db[collection_name]
            execution_time = time.time() - start_time

            if operation == 'find':
                cursor = collection.find(filter_obj, **options)
                documents = await cursor.to_list(length=None)
                result = QueryResult(
                    success=True,
                    data=documents,
                    affected_rows=len(documents),
                    execution_time=execution_time,
                    query=query,
                    parameters=parameters or {}
                )
            elif operation == 'find_one':
                document = await collection.find_one(filter_obj, **options)
                result = QueryResult(
                    success=True,
                    data=document,
                    affected_rows=1 if document else 0,
                    execution_time=execution_time,
                    query=query,
                    parameters=parameters or {}
                )
            elif operation == 'count':
                count = await collection.count_documents(filter_obj)
                result = QueryResult(
                    success=True,
                    data={'count': count},
                    affected_rows=count,
                    execution_time=execution_time,
                    query=query,
                    parameters=parameters or {}
                )
            elif operation == 'insert_one':
                document = query_obj.get('document', {})
                insert_result = await collection.insert_one(document)
                result = QueryResult(
                    success=True,
                    data={'inserted_id': str(insert_result.inserted_id)},
                    affected_rows=1,
                    execution_time=execution_time,
                    query=query,
                    parameters=parameters or {}
                )
            elif operation == 'update_one':
                update_obj = query_obj.get('update', {})
                update_result = await collection.update_one(filter_obj, update_obj)
                result = QueryResult(
                    success=True,
                    data={'modified_count': update_result.modified_count},
                    affected_rows=update_result.modified_count,
                    execution_time=execution_time,
                    query=query,
                    parameters=parameters or {}
                )
            elif operation == 'delete_one':
                delete_result = await collection.delete_one(filter_obj)
                result = QueryResult(
                    success=True,
                    data={'deleted_count': delete_result.deleted_count},
                    affected_rows=delete_result.deleted_count,
                    execution_time=execution_time,
                    query=query,
                    parameters=parameters or {}
                )
            else:
                raise ValueError(f"不支持的MongoDB操作: {operation}")

            self._update_stats(execution_time, True)
            if self.enable_debug:
                self.logger.debug(f"MongoDB查询执行成功: {operation} 耗时: {execution_time:.3f}s")

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(execution_time, False)
            self.logger.error(f"MongoDB查询执行失败: {e}")

            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                query=query,
                parameters=parameters or {}
            )

    async def close_pool(self, pool: Any) -> None:
        """关闭 MongoDB 连接"""
        try:
            client = pool['client']
            client.close()
            self.logger.debug("MongoDB连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭MongoDB连接失败: {e}")


class RedisConnector(BaseConnector):
    """Redis 连接器 - 使用aioredis"""

    def __init__(self, enable_debug: bool = False):
        super().__init__(enable_debug)
        if not AIOREDIS_AVAILABLE:
            self.logger.warning("aioredis不可用，Redis连接器将无法正常工作")

    async def create_pool(self, config: ConnectionConfig) -> Any:
        """创建 Redis 连接池"""
        if not AIOREDIS_AVAILABLE:
            raise RuntimeError("aioredis不可用，请安装: pip install aioredis")

        try:
            # 构建Redis连接URL
            if config.password:
                redis_url = f"redis://:{config.password}@{config.host}:{config.port}/{config.database}"
            else:
                redis_url = f"redis://{config.host}:{config.port}/{config.database}"

            pool = aioredis.ConnectionPool.from_url(
                redis_url,
                max_connections=config.max_connections,
                socket_timeout=config.timeout,
                socket_connect_timeout=config.timeout,
                retry_on_timeout=True
            )

            # 测试连接
            redis_client = aioredis.Redis(connection_pool=pool)
            await redis_client.ping()

            self.connection_stats['total_connections'] += 1
            self.logger.info(f"Redis连接池创建成功: {config.host}:{config.port}/{config.database}")

            return pool

        except Exception as e:
            self.connection_stats['failed_connections'] += 1
            self.logger.error(f"Redis连接池创建失败: {e}")
            raise

    async def execute_query(self, pool: Any, query: str, parameters: Dict[str, Any] = None) -> QueryResult:
        """执行 Redis 命令"""
        start_time = time.time()

        try:
            redis_client = aioredis.Redis(connection_pool=pool)

            # 解析Redis命令
            if isinstance(query, str):
                # 简单的命令解析
                parts = query.strip().split()
                if not parts:
                    raise ValueError("空的Redis命令")

                command = parts[0].upper()
                args = parts[1:] if len(parts) > 1 else []

                # 添加参数
                if parameters:
                    args.extend(parameters.values())

            else:
                # 假设query是命令列表
                command = query[0].upper()
                args = query[1:] if len(query) > 1 else []

            # 执行命令
            if command == 'GET':
                result_data = await redis_client.get(args[0]) if args else None
                if result_data:
                    result_data = result_data.decode('utf-8')
            elif command == 'SET':
                result_data = await redis_client.set(args[0], args[1]) if len(args) >= 2 else False
            elif command == 'DEL':
                result_data = await redis_client.delete(*args) if args else 0
            elif command == 'EXISTS':
                result_data = await redis_client.exists(*args) if args else 0
            elif command == 'KEYS':
                pattern = args[0] if args else '*'
                keys = await redis_client.keys(pattern)
                result_data = [key.decode('utf-8') for key in keys]
            elif command == 'PING':
                result_data = await redis_client.ping()
            # RedisJSON 命令支持
            elif command == 'JSON.SET' and REDISJSON_AVAILABLE:
                if len(args) >= 3:
                    result_data = await redis_client.execute_command('JSON.SET', args[0], args[1], args[2])
                else:
                    raise ValueError("JSON.SET 需要至少3个参数: key, path, json")
            elif command == 'JSON.GET' and REDISJSON_AVAILABLE:
                if len(args) >= 1:
                    result_data = await redis_client.execute_command('JSON.GET', *args)
                    if result_data:
                        import json
                        result_data = json.loads(result_data.decode('utf-8'))
                else:
                    raise ValueError("JSON.GET 需要至少1个参数: key")
            elif command == 'JSON.DEL' and REDISJSON_AVAILABLE:
                if len(args) >= 1:
                    result_data = await redis_client.execute_command('JSON.DEL', *args)
                else:
                    raise ValueError("JSON.DEL 需要至少1个参数: key")
            elif command == 'JSON.TYPE' and REDISJSON_AVAILABLE:
                if len(args) >= 1:
                    result_data = await redis_client.execute_command('JSON.TYPE', *args)
                    if result_data:
                        result_data = result_data.decode('utf-8')
                else:
                    raise ValueError("JSON.TYPE 需要至少1个参数: key")
            else:
                # 通用命令执行
                result_data = await redis_client.execute_command(command, *args)

            execution_time = time.time() - start_time

            result = QueryResult(
                success=True,
                data=result_data,
                affected_rows=1,
                execution_time=execution_time,
                query=query,
                parameters=parameters or {}
            )

            self._update_stats(execution_time, True)
            if self.enable_debug:
                self.logger.debug(f"Redis命令执行成功: {command} 耗时: {execution_time:.3f}s")

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(execution_time, False)
            self.logger.error(f"Redis命令执行失败: {e}")

            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                query=query,
                parameters=parameters or {}
            )

    async def close_pool(self, pool: Any) -> None:
        """关闭 Redis 连接池"""
        try:
            await pool.disconnect()
            self.logger.debug("Redis连接池已关闭")
        except Exception as e:
            self.logger.error(f"关闭Redis连接池失败: {e}")


class ElasticsearchConnector(BaseConnector):
    """Elasticsearch 连接器 - 使用elasticsearch-py"""

    def __init__(self, enable_debug: bool = False):
        super().__init__(enable_debug)
        if not ELASTICSEARCH_AVAILABLE:
            self.logger.warning("elasticsearch不可用，Elasticsearch连接器将无法正常工作")

    async def create_pool(self, config: ConnectionConfig) -> Any:
        """创建 Elasticsearch 连接"""
        if not ELASTICSEARCH_AVAILABLE:
            raise RuntimeError("elasticsearch不可用，请安装: pip install elasticsearch")

        try:
            # 构建Elasticsearch连接配置
            hosts = [f"{config.host}:{config.port}"]

            es_config = {
                'hosts': hosts,
                'timeout': config.timeout,
                'max_retries': config.retry_attempts,
                'retry_on_timeout': True
            }

            # 添加认证信息
            if config.username and config.password:
                es_config['http_auth'] = (config.username, config.password)

            # 添加SSL配置
            if config.ssl:
                es_config['use_ssl'] = True
                es_config['verify_certs'] = True

            client = AsyncElasticsearch(**es_config)

            # 测试连接
            await client.ping()

            self.connection_stats['total_connections'] += 1
            self.logger.info(f"Elasticsearch连接创建成功: {config.host}:{config.port}")

            return client

        except Exception as e:
            self.connection_stats['failed_connections'] += 1
            self.logger.error(f"Elasticsearch连接创建失败: {e}")
            raise

    async def execute_query(self, pool: Any, query: str, parameters: Dict[str, Any] = None) -> QueryResult:
        """执行 Elasticsearch 查询"""
        start_time = time.time()
        client = pool

        try:
            # 解析Elasticsearch查询
            if isinstance(query, str):
                try:
                    query_obj = json.loads(query)
                except json.JSONDecodeError:
                    raise ValueError(f"无效的Elasticsearch查询格式: {query}")
            else:
                query_obj = query

            # 提取查询参数
            index = query_obj.get('index', '_all')
            operation = query_obj.get('operation', 'search')
            body = query_obj.get('body', {})

            # 合并参数
            if parameters:
                body.update(parameters)

            execution_time = time.time() - start_time

            if operation == 'search':
                response = await client.search(index=index, body=body)
                result = QueryResult(
                    success=True,
                    data=response,
                    affected_rows=response['hits']['total']['value'],
                    execution_time=execution_time,
                    query=query,
                    parameters=parameters or {}
                )
            elif operation == 'index':
                doc_id = query_obj.get('id')
                response = await client.index(index=index, id=doc_id, body=body)
                result = QueryResult(
                    success=True,
                    data=response,
                    affected_rows=1,
                    execution_time=execution_time,
                    query=query,
                    parameters=parameters or {}
                )
            elif operation == 'get':
                doc_id = query_obj.get('id')
                response = await client.get(index=index, id=doc_id)
                result = QueryResult(
                    success=True,
                    data=response,
                    affected_rows=1,
                    execution_time=execution_time,
                    query=query,
                    parameters=parameters or {}
                )
            elif operation == 'delete':
                doc_id = query_obj.get('id')
                response = await client.delete(index=index, id=doc_id)
                result = QueryResult(
                    success=True,
                    data=response,
                    affected_rows=1,
                    execution_time=execution_time,
                    query=query,
                    parameters=parameters or {}
                )
            else:
                raise ValueError(f"不支持的Elasticsearch操作: {operation}")

            self._update_stats(execution_time, True)
            if self.enable_debug:
                self.logger.debug(f"Elasticsearch查询执行成功: {operation} 耗时: {execution_time:.3f}s")

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(execution_time, False)
            self.logger.error(f"Elasticsearch查询执行失败: {e}")

            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                query=query,
                parameters=parameters or {}
            )

    async def close_pool(self, pool: Any) -> None:
        """关闭 Elasticsearch 连接"""
        try:
            await pool.close()
            self.logger.debug("Elasticsearch连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭Elasticsearch连接失败: {e}")


class OracleConnector(BaseConnector):
    """Oracle 连接器 - 使用oracledb"""

    def __init__(self, enable_debug: bool = False):
        super().__init__(enable_debug)
        if not ORACLE_AVAILABLE:
            self.logger.warning("Oracle驱动不可用，Oracle连接器将无法正常工作")

    async def create_pool(self, config: ConnectionConfig) -> Any:
        """创建 Oracle 连接池"""
        if not ORACLE_AVAILABLE:
            raise RuntimeError("Oracle驱动不可用，请安装: pip install oracledb")

        try:
            # 导入Oracle驱动
            try:
                import oracledb
                # 使用新版本oracledb
                oracledb.init_oracle_client()  # 初始化Oracle客户端

                # 构建连接字符串
                dsn = f"{config.host}:{config.port}/{config.database}"

                pool = oracledb.create_pool(
                    user=config.username,
                    password=config.password,
                    dsn=dsn,
                    min=config.min_connections,
                    max=config.max_connections,
                    increment=1,
                    threaded=True,
                    getmode=oracledb.POOL_GETMODE_WAIT
                )

            except ImportError:
                # 回退到cx_Oracle_async
                import cx_Oracle_async as cx_Oracle

                dsn = f"{config.host}:{config.port}/{config.database}"
                pool = await cx_Oracle.create_pool(
                    user=config.username,
                    password=config.password,
                    dsn=dsn,
                    min=config.min_connections,
                    max=config.max_connections,
                    increment=1,
                    threaded=True
                )

            self.connection_stats['total_connections'] += 1
            self.logger.info(f"Oracle连接池创建成功: {config.host}:{config.port}/{config.database}")
            return pool

        except Exception as e:
            self.connection_stats['failed_connections'] += 1
            self.logger.error(f"Oracle连接池创建失败: {e}")
            raise

    async def execute_query(self, pool: Any, query: str, parameters: Dict[str, Any] = None) -> QueryResult:
        """执行 Oracle 查询"""
        start_time = time.time()

        try:
            # 获取连接
            if hasattr(pool, 'acquire'):
                # 异步连接池
                async with pool.acquire() as conn:
                    cursor = await conn.cursor()

                    if parameters:
                        await cursor.execute(query, parameters)
                    else:
                        await cursor.execute(query)

                    execution_time = time.time() - start_time

                    if query.strip().upper().startswith('SELECT'):
                        # SELECT查询
                        rows = await cursor.fetchall()
                        # 获取列名
                        columns = [desc[0] for desc in cursor.description]
                        # 转换为字典列表
                        data = [dict(zip(columns, row)) for row in rows]

                        result = QueryResult(
                            success=True,
                            data=data,
                            affected_rows=len(data),
                            execution_time=execution_time,
                            query=query,
                            parameters=parameters or {}
                        )
                    else:
                        # INSERT/UPDATE/DELETE查询
                        affected_rows = cursor.rowcount
                        await conn.commit()

                        result = QueryResult(
                            success=True,
                            data={'affected_rows': affected_rows},
                            affected_rows=affected_rows,
                            execution_time=execution_time,
                            query=query,
                            parameters=parameters or {}
                        )

                    await cursor.close()
            else:
                # 同步连接池（oracledb）
                with pool.acquire() as conn:
                    cursor = conn.cursor()

                    if parameters:
                        cursor.execute(query, parameters)
                    else:
                        cursor.execute(query)

                    execution_time = time.time() - start_time

                    if query.strip().upper().startswith('SELECT'):
                        # SELECT查询
                        rows = cursor.fetchall()
                        # 获取列名
                        columns = [desc[0] for desc in cursor.description]
                        # 转换为字典列表
                        data = [dict(zip(columns, row)) for row in rows]

                        result = QueryResult(
                            success=True,
                            data=data,
                            affected_rows=len(data),
                            execution_time=execution_time,
                            query=query,
                            parameters=parameters or {}
                        )
                    else:
                        # INSERT/UPDATE/DELETE查询
                        affected_rows = cursor.rowcount
                        conn.commit()

                        result = QueryResult(
                            success=True,
                            data={'affected_rows': affected_rows},
                            affected_rows=affected_rows,
                            execution_time=execution_time,
                            query=query,
                            parameters=parameters or {}
                        )

                    cursor.close()

            self._update_stats(execution_time, True)
            if self.enable_debug:
                self.logger.debug(f"Oracle查询执行成功: {query[:100]}... 耗时: {execution_time:.3f}s")

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(execution_time, False)
            self.logger.error(f"Oracle查询执行失败: {e}")

            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                query=query,
                parameters=parameters or {}
            )

    async def close_pool(self, pool: Any) -> None:
        """关闭 Oracle 连接池"""
        try:
            if hasattr(pool, 'close'):
                if asyncio.iscoroutinefunction(pool.close):
                    await pool.close()
                else:
                    pool.close()
            self.logger.debug("Oracle连接池已关闭")
        except Exception as e:
            self.logger.error(f"关闭Oracle连接池失败: {e}")


class OceanBaseConnector(BaseConnector):
    """OceanBase 连接器 - 兼容MySQL协议"""

    def __init__(self, enable_debug: bool = False):
        super().__init__(enable_debug)
        if not OCEANBASE_AVAILABLE:
            self.logger.warning("OceanBase驱动不可用，OceanBase连接器将无法正常工作")

    @staticmethod
    def parse_obclient_string(obclient_cmd: str) -> dict:
        """
        解析obclient连接字符串
        格式: obclient -h host -P port -u user@tenant#cluster:db_id -D database -p password
        示例: obclient -h *********** -P9160 -uBSSP@arcdbcs#bs_test02:100001 -DBSSP -pchNY3863##
        """
        import re

        config = {}

        # 解析主机
        host_match = re.search(r'-h\s+([^\s]+)', obclient_cmd)
        if host_match:
            config['host'] = host_match.group(1)

        # 解析端口
        port_match = re.search(r'-P\s*(\d+)', obclient_cmd)
        if port_match:
            config['port'] = int(port_match.group(1))

        # 解析用户名（包含租户和集群信息）
        user_match = re.search(r'-u\s*([^@]+)@([^#]+)#([^:]+):(\d+)', obclient_cmd)
        if user_match:
            config['username'] = user_match.group(1)  # 用户名
            config['tenant'] = user_match.group(2)    # 租户
            config['cluster'] = user_match.group(3)   # 集群
            config['db_id'] = user_match.group(4)     # 数据库ID
        else:
            # 简单用户名格式
            simple_user_match = re.search(r'-u\s*([^\s]+)', obclient_cmd)
            if simple_user_match:
                config['username'] = simple_user_match.group(1)

        # 解析数据库名
        db_match = re.search(r'-D\s*([^\s]+)', obclient_cmd)
        if db_match:
            config['database'] = db_match.group(1)

        # 解析密码
        pwd_match = re.search(r'-p\s*([^\s]+)', obclient_cmd)
        if pwd_match:
            config['password'] = pwd_match.group(1)

        return config

    async def create_pool(self, config: ConnectionConfig) -> Any:
        """创建 OceanBase 连接池"""
        if not OCEANBASE_AVAILABLE:
            raise RuntimeError("OceanBase驱动不可用，请安装: pip install aiomysql")

        try:
            # OceanBase兼容MySQL协议，使用aiomysql
            pool = await aiomysql.create_pool(
                host=config.host,
                port=config.port,
                user=config.username,
                password=config.password,
                db=config.database,
                charset=config.charset,
                maxsize=config.max_connections,
                minsize=config.min_connections,
                connect_timeout=config.timeout,
                autocommit=True,
                # OceanBase特定配置
                sql_mode='TRADITIONAL',
                init_command="SET SESSION sql_mode='TRADITIONAL'"
            )

            self.connection_stats['total_connections'] += 1
            self.logger.info(f"OceanBase连接池创建成功: {config.host}:{config.port}/{config.database}")
            return pool

        except Exception as e:
            self.connection_stats['failed_connections'] += 1
            self.logger.error(f"OceanBase连接池创建失败: {e}")
            raise

    async def execute_query(self, pool: Any, query: str, parameters: Dict[str, Any] = None) -> QueryResult:
        """执行 OceanBase 查询"""
        start_time = time.time()

        try:
            async with pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    if parameters:
                        await cursor.execute(query, parameters)
                    else:
                        await cursor.execute(query)

                    execution_time = time.time() - start_time

                    if query.strip().upper().startswith('SELECT'):
                        # SELECT查询
                        rows = await cursor.fetchall()
                        result = QueryResult(
                            success=True,
                            data=list(rows),
                            affected_rows=len(rows),
                            execution_time=execution_time,
                            query=query,
                            parameters=parameters or {}
                        )
                    else:
                        # INSERT/UPDATE/DELETE查询
                        affected_rows = cursor.rowcount
                        result = QueryResult(
                            success=True,
                            data={'affected_rows': affected_rows},
                            affected_rows=affected_rows,
                            execution_time=execution_time,
                            query=query,
                            parameters=parameters or {}
                        )

                    self._update_stats(execution_time, True)
                    if self.enable_debug:
                        self.logger.debug(f"OceanBase查询执行成功: {query[:100]}... 耗时: {execution_time:.3f}s")

                    return result

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(execution_time, False)
            self.logger.error(f"OceanBase查询执行失败: {e}")

            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                query=query,
                parameters=parameters or {}
            )

    async def close_pool(self, pool: Any) -> None:
        """关闭 OceanBase 连接池"""
        try:
            pool.close()
            await pool.wait_closed()
            self.logger.debug("OceanBase连接池已关闭")
        except Exception as e:
            self.logger.error(f"关闭OceanBase连接池失败: {e}")


# ===================== 连接器工厂类 =====================

class ConnectorFactory:
    """数据库连接器工厂"""

    _connectors = {
        'mysql': MysqlConnector,
        'postgresql': PostgresConnector,
        'postgres': PostgresConnector,  # 别名
        'sqlite': SqliteConnector,
        'mongodb': MongoConnector,
        'mongo': MongoConnector,  # 别名
        'redis': RedisConnector,
        'elasticsearch': ElasticsearchConnector,
        'es': ElasticsearchConnector,  # 别名
        'oracle': OracleConnector,
        'oceanbase': OceanBaseConnector,
        'ob': OceanBaseConnector  # OceanBase别名
    }

    @classmethod
    def create_connector(cls, db_type: str, enable_debug: bool = False) -> BaseConnector:
        """创建数据库连接器"""
        db_type = db_type.lower()

        if db_type not in cls._connectors:
            raise ValueError(f"不支持的数据库类型: {db_type}")

        connector_class = cls._connectors[db_type]
        return connector_class(enable_debug=enable_debug)

    @classmethod
    def get_supported_types(cls) -> List[str]:
        """获取支持的数据库类型"""
        return list(cls._connectors.keys())

    @classmethod
    def get_available_types(cls) -> Dict[str, bool]:
        """获取可用的数据库类型（检查依赖）"""
        availability = {
            'mysql': AIOMYSQL_AVAILABLE,
            'postgresql': ASYNCPG_AVAILABLE,
            'postgres': ASYNCPG_AVAILABLE,
            'sqlite': AIOSQLITE_AVAILABLE,
            'mongodb': MOTOR_AVAILABLE,
            'mongo': MOTOR_AVAILABLE,
            'redis': AIOREDIS_AVAILABLE,
            'elasticsearch': ELASTICSEARCH_AVAILABLE,
            'es': ELASTICSEARCH_AVAILABLE,
            'oracle': ORACLE_AVAILABLE,
            'oceanbase': OCEANBASE_AVAILABLE,
            'ob': OCEANBASE_AVAILABLE
        }
        return availability


class EnterpriseConnectorFactory:
    """企业级连接器工厂 - 兼容接口"""

    @classmethod
    def create_connector(cls, database_type: str, config: Dict[str, Any]):
        """创建企业级兼容连接器"""
        # 将企业级配置转换为标准连接器配置
        connector = ConnectorFactory.create_connector(database_type, enable_debug=False)

        # 创建企业级兼容包装器
        return EnterpriseConnectorWrapper(connector, config)

    @classmethod
    def get_supported_databases(cls) -> List[str]:
        """获取支持的数据库类型"""
        return ConnectorFactory.get_supported_types()


class EnterpriseConnectorWrapper:
    """企业级连接器包装器 - 提供兼容接口"""

    def __init__(self, connector: BaseConnector, config: Dict[str, Any]):
        self.connector = connector
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.connection = None
        self.pool = None

    async def connect(self) -> ConnectionResult:
        """建立连接"""
        try:
            # 转换配置格式
            connection_config = ConnectionConfig(
                host=self.config.get('host', 'localhost'),
                port=self.config.get('port', 3306),
                username=self.config.get('username', ''),
                password=self.config.get('password', ''),
                database=self.config.get('database', ''),
                charset=self.config.get('charset', 'utf8mb4'),
                timeout=self.config.get('timeout', 30.0)
            )

            # 创建连接池
            self.pool = await self.connector.create_pool(connection_config)

            return ConnectionResult(
                success=True,
                connection=self.pool,
                info={
                    'host': connection_config.host,
                    'database': connection_config.database,
                    'type': self.config.get('database_type', 'unknown')
                }
            )

        except Exception as e:
            error_msg = f"连接失败: {e}"
            self.logger.error(error_msg)
            return ConnectionResult(success=False, error=error_msg)

    async def disconnect(self):
        """断开连接"""
        if self.pool:
            try:
                await self.connector.close_pool(self.pool)
            except Exception as e:
                self.logger.error(f"断开连接失败: {e}")
            finally:
                self.pool = None

    async def execute_query(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> QueryResult:
        """执行查询"""
        if not self.pool:
            await self.connect()

        try:
            result = await self.connector.execute_query(self.pool, query, parameters or {})

            # 转换为企业级格式
            if result.success:
                # 确保data是列表格式
                data = result.data
                if not isinstance(data, list):
                    data = [data] if data is not None else []

                return QueryResult(
                    success=True,
                    data=data,
                    affected_rows=result.affected_rows,
                    execution_time=result.execution_time,
                    error=None
                )
            else:
                return QueryResult(
                    success=False,
                    data=[],
                    affected_rows=0,
                    execution_time=result.execution_time,
                    error=result.error
                )

        except Exception as e:
            error_msg = f"查询执行失败: {e}"
            self.logger.error(error_msg)
            return QueryResult(
                success=False,
                data=[],
                affected_rows=0,
                execution_time=0.0,
                error=error_msg
            )

    async def test_connection(self) -> ConnectionResult:
        """测试连接"""
        try:
            result = await self.connect()
            if result.success:
                await self.disconnect()
            return result
        except Exception as e:
            error_msg = f"连接测试失败: {e}"
            self.logger.error(error_msg)
            return ConnectionResult(success=False, error=error_msg)
