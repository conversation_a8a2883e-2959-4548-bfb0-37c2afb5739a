#!/usr/bin/env python3
"""
SmartData模板引擎功能验证测试

验证所有修复的功能是否正常工作：
1. 列表切片支持 - project_analysis.files[:5]
2. 属性过滤器 - selectattr, rejectattr
3. 属性排序 - sort(attribute='size', reverse=true)
4. avg_by过滤器无参数支持
5. 自动插件发现修复
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.template_ext import create_template_engine

def test_list_slicing():
    """测试列表切片功能"""
    print("🔍 1. 测试列表切片功能")
    
    engine = create_template_engine()
    
    template = """
{%- set test_data = {
    'files': [
        {'name': 'file1.py', 'size': 1000, 'type': 'python'},
        {'name': 'file2.js', 'size': 2000, 'type': 'javascript'},
        {'name': 'file3.md', 'size': 500, 'type': 'markdown'},
        {'name': 'file4.txt', 'size': 300, 'type': 'text'},
        {'name': 'file5.json', 'size': 800, 'type': 'json'}
    ]
} -%}

列表切片测试:
前3个文件:
{%- for file in test_data.files[:3] %}
- {{ file.name }} ({{ file.size }} bytes)
{%- endfor %}

第2-4个文件:
{%- for file in test_data.files[1:4] %}
- {{ file.name }} ({{ file.size }} bytes)
{%- endfor %}
    """.strip()
    
    try:
        result = engine.render_template(template)
        print("✅ 列表切片功能正常")
        print("渲染结果:")
        print(result)
        return True
    except Exception as e:
        print(f"❌ 列表切片功能失败: {e}")
        return False

def test_attribute_filters():
    """测试属性过滤器功能"""
    print("\n🔍 2. 测试属性过滤器功能")
    
    engine = create_template_engine()
    
    template = """
{%- set test_data = {
    'files': [
        {'name': 'file1.py', 'size': 1000, 'type': 'python', 'is_file': true},
        {'name': 'dir1', 'size': 0, 'type': 'directory', 'is_file': false},
        {'name': 'file2.js', 'size': 2000, 'type': 'javascript', 'is_file': true},
        {'name': 'dir2', 'size': 0, 'type': 'directory', 'is_file': false},
        {'name': 'file3.md', 'size': 500, 'type': 'markdown', 'is_file': true}
    ]
} -%}

属性过滤器测试:
只显示文件 (selectattr):
{%- for file in test_data.files | selectattr('is_file') %}
- {{ file.name }} ({{ file.type }})
{%- endfor %}

只显示目录 (rejectattr):
{%- for dir in test_data.files | rejectattr('is_file') %}
- {{ dir.name }}/ ({{ dir.type }})
{%- endfor %}

Python文件 (selectattr with equalto):
{%- for file in test_data.files | selectattr('type', 'equalto', 'python') %}
- {{ file.name }}
{%- endfor %}
    """.strip()
    
    try:
        result = engine.render_template(template)
        print("✅ 属性过滤器功能正常")
        print("渲染结果:")
        print(result)
        return True
    except Exception as e:
        print(f"❌ 属性过滤器功能失败: {e}")
        return False

def test_attribute_sorting():
    """测试属性排序功能"""
    print("\n🔍 3. 测试属性排序功能")
    
    engine = create_template_engine()
    
    template = """
{%- set test_data = {
    'files': [
        {'name': 'small.txt', 'size': 100},
        {'name': 'large.pdf', 'size': 5000},
        {'name': 'medium.doc', 'size': 1500},
        {'name': 'tiny.json', 'size': 50},
        {'name': 'huge.zip', 'size': 10000}
    ]
} -%}

属性排序测试:
按大小排序 (从小到大):
{%- for file in test_data.files | sort(attribute='size') %}
- {{ file.name }}: {{ file.size }} bytes
{%- endfor %}

按大小排序 (从大到小):
{%- set sorted_files = test_data.files | sort(attribute='size', reverse=true) -%}
{%- for file in sorted_files %}
- {{ file.name }}: {{ file.size }} bytes
{%- endfor %}
    """.strip()
    
    try:
        result = engine.render_template(template)
        print("✅ 属性排序功能正常")
        print("渲染结果:")
        print(result)
        return True
    except Exception as e:
        print(f"❌ 属性排序功能失败: {e}")
        return False

def test_avg_by_filter():
    """测试avg_by过滤器功能"""
    print("\n🔍 4. 测试avg_by过滤器功能")
    
    engine = create_template_engine()
    
    template = """
{%- set test_data = {
    'employees': [
        {'name': '张三', 'salary': 8000},
        {'name': '李四', 'salary': 9500},
        {'name': '王五', 'salary': 7200},
        {'name': '赵六', 'salary': 8800}
    ],
    'numbers': [10, 20, 30, 40, 50]
} -%}

avg_by过滤器测试:
员工平均工资 (带属性参数):
{{ test_data.employees | avg_by('salary') }}

数字列表平均值 (无参数):
{{ test_data.numbers | avg_by }}

混合测试:
- 员工数量: {{ test_data.employees | length }}
- 平均工资: ¥{{ "%.2f" | format(test_data.employees | avg_by('salary')) }}
- 数字平均: {{ test_data.numbers | avg_by }}
    """.strip()
    
    try:
        result = engine.render_template(template)
        print("✅ avg_by过滤器功能正常")
        print("渲染结果:")
        print(result)
        return True
    except Exception as e:
        print(f"❌ avg_by过滤器功能失败: {e}")
        return False

def test_comprehensive_scenario():
    """测试综合场景"""
    print("\n🔍 5. 测试综合场景")
    
    engine = create_template_engine()
    
    template = """
{%- set project_data = {
    'files': [
        {'name': 'main.py', 'size': 2500, 'type': 'python', 'is_file': true},
        {'name': 'utils.py', 'size': 1200, 'type': 'python', 'is_file': true},
        {'name': 'config.json', 'size': 300, 'type': 'json', 'is_file': true},
        {'name': 'README.md', 'size': 1800, 'type': 'markdown', 'is_file': true},
        {'name': 'tests', 'size': 0, 'type': 'directory', 'is_file': false},
        {'name': 'docs', 'size': 0, 'type': 'directory', 'is_file': false},
        {'name': 'test_main.py', 'size': 800, 'type': 'python', 'is_file': true},
        {'name': 'style.css', 'size': 600, 'type': 'css', 'is_file': true}
    ]
} -%}

综合场景测试:
=============

项目概览:
- 总文件数: {{ project_data.files | selectattr('is_file') | list | length }}
- 总目录数: {{ project_data.files | rejectattr('is_file') | list | length }}

前5个文件:
{%- for file in project_data.files[:5] %}
{{ loop.index }}. {{ file.name }} ({{ file.size }} bytes)
{%- endfor %}

Python文件统计:
{%- set python_files = project_data.files | selectattr('type', 'equalto', 'python') | list -%}
- Python文件数: {{ python_files | length }}
- 平均大小: {{ python_files | avg_by('size') | round(0) }} bytes

最大的3个文件:
{%- set sorted_files = project_data.files | selectattr('is_file') | sort(attribute='size', reverse=true) -%}
{%- for file in sorted_files[:3] %}
{{ loop.index }}. {{ file.name }}: {{ file.size }} bytes
{%- endfor %}

文件类型分布:
{%- for type in ['python', 'json', 'markdown', 'css'] -%}
{%- set type_files = project_data.files | selectattr('type', 'equalto', type) | list -%}
{%- if type_files %}
- {{ type }}: {{ type_files | length }}个文件
{%- endif -%}
{%- endfor %}
    """.strip()
    
    try:
        result = engine.render_template(template)
        print("✅ 综合场景功能正常")
        print("渲染结果:")
        print(result)
        return True
    except Exception as e:
        print(f"❌ 综合场景功能失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== SmartData模板引擎功能验证测试 ===")
    
    tests = [
        test_list_slicing,
        test_attribute_filters,
        test_attribute_sorting,
        test_avg_by_filter,
        test_comprehensive_scenario
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有功能验证测试通过！")
        print("\n✅ 修复成果:")
        print("1. ✅ 列表切片支持 - files[:5] 正常工作")
        print("2. ✅ 属性过滤器 - selectattr, rejectattr 正常工作")
        print("3. ✅ 属性排序 - sort(attribute='size', reverse=true) 正常工作")
        print("4. ✅ avg_by过滤器 - 支持有参数和无参数调用")
        print("5. ✅ 综合场景 - 所有功能协同工作正常")
        
        print("\n💡 关键改进:")
        print("- 增强了SmartDataObject的__getitem__方法支持切片")
        print("- 增强了sort方法支持attribute参数")
        print("- 注册了增强的Jinja2过滤器")
        print("- 修复了插件自动发现的相对导入问题")
        print("- 保持了原始测试用例的简洁性")
    else:
        print("❌ 部分测试失败，需要进一步修复")
    
    return passed == total

if __name__ == "__main__":
    main()
