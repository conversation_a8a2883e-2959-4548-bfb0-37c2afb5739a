"""
异步生命周期管理器实现

管理异步资源的生命周期，确保资源正确创建和清理，防止内存泄漏
"""

import asyncio
import weakref
import logging
from typing import Any, Dict, Optional, Callable, Set
import time

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../'))

from core.async_interfaces import IAsyncLifecycleManager


class AsyncLifecycleManager(IAsyncLifecycleManager):
    """
    异步生命周期管理器
    
    功能特性：
    - 异步资源注册和管理
    - 弱引用避免循环引用
    - 自动清理机制
    - 并发安全
    - 性能监控
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 使用弱引用存储资源，避免循环引用
        self._async_resources: Dict[str, weakref.ReferenceType] = {}
        self._cleanup_callbacks: Dict[str, Callable] = {}
        self._resource_metadata: Dict[str, Dict] = {}
        
        # 并发控制
        self._lock = asyncio.Lock()
        
        # 统计信息
        self._total_registered = 0
        self._total_cleaned = 0
        self._creation_time = time.time()
        
        # 自动清理任务
        self._cleanup_task = None
        self._cleanup_interval = 60  # 60秒清理一次
        self._start_auto_cleanup()
    
    def _start_auto_cleanup(self):
        """启动自动清理任务"""
        try:
            # 检查是否有运行的事件循环
            loop = asyncio.get_running_loop()
            if self._cleanup_task is None or self._cleanup_task.done():
                self._cleanup_task = asyncio.create_task(self._auto_cleanup_loop())
        except RuntimeError:
            # 没有运行的事件循环，延迟启动清理任务
            self.logger.debug("没有运行的事件循环，延迟启动自动清理任务")
            self._cleanup_task = None
    
    async def _auto_cleanup_loop(self):
        """自动清理循环"""
        while True:
            try:
                await asyncio.sleep(self._cleanup_interval)
                await self._cleanup_dead_references()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"自动清理过程中出错: {e}")
    
    async def _cleanup_dead_references(self):
        """清理已失效的弱引用"""
        async with self._lock:
            dead_keys = []
            
            for resource_id, weak_ref in self._async_resources.items():
                if weak_ref() is None:
                    dead_keys.append(resource_id)
            
            for key in dead_keys:
                self.logger.debug(f"清理失效引用: {key}")
                del self._async_resources[key]
                self._cleanup_callbacks.pop(key, None)
                self._resource_metadata.pop(key, None)
                self._total_cleaned += 1
    
    async def register_async_resource(self, resource_id: str, resource: Any, cleanup_callback: Optional[Callable] = None) -> None:
        """
        注册异步资源
        
        Args:
            resource_id: 资源唯一标识
            resource: 资源对象
            cleanup_callback: 清理回调函数
        """
        async with self._lock:
            # 如果资源已存在，先清理旧资源
            if resource_id in self._async_resources:
                await self._cleanup_resource_internal(resource_id)
            
            # 创建弱引用
            def cleanup_on_delete(weak_ref):
                """资源被删除时的回调"""
                try:
                    asyncio.create_task(self._handle_resource_deletion(resource_id))
                except RuntimeError:
                    # 没有运行的事件循环，忽略清理
                    self.logger.debug(f"无法创建清理任务，资源 {resource_id} 将在下次清理时处理")
            
            weak_ref = weakref.ref(resource, cleanup_on_delete)
            
            # 注册资源
            self._async_resources[resource_id] = weak_ref
            if cleanup_callback:
                self._cleanup_callbacks[resource_id] = cleanup_callback
            
            # 记录元数据
            self._resource_metadata[resource_id] = {
                'type': type(resource).__name__,
                'registered_at': time.time(),
                'has_cleanup_callback': cleanup_callback is not None
            }
            
            self._total_registered += 1
            
            self.logger.debug(f"注册异步资源: {resource_id} ({type(resource).__name__})")
    
    async def _handle_resource_deletion(self, resource_id: str):
        """处理资源删除事件"""
        async with self._lock:
            if resource_id in self._async_resources:
                self.logger.debug(f"资源已被垃圾回收: {resource_id}")
                del self._async_resources[resource_id]
                self._cleanup_callbacks.pop(resource_id, None)
                self._resource_metadata.pop(resource_id, None)
                self._total_cleaned += 1
    
    async def unregister_async_resource(self, resource_id: str) -> bool:
        """
        注销异步资源
        
        Args:
            resource_id: 资源唯一标识
            
        Returns:
            是否成功注销
        """
        async with self._lock:
            if resource_id not in self._async_resources:
                return False
            
            await self._cleanup_resource_internal(resource_id)
            return True
    
    async def cleanup_async_resource(self, resource_id: str) -> bool:
        """
        清理指定异步资源
        
        Args:
            resource_id: 资源唯一标识
            
        Returns:
            是否成功清理
        """
        async with self._lock:
            return await self._cleanup_resource_internal(resource_id)
    
    async def _cleanup_resource_internal(self, resource_id: str) -> bool:
        """内部资源清理方法"""
        if resource_id not in self._async_resources:
            return False
        
        try:
            # 获取资源对象
            weak_ref = self._async_resources[resource_id]
            resource = weak_ref()
            
            if resource is not None:
                # 执行清理回调
                cleanup_callback = self._cleanup_callbacks.get(resource_id)
                if cleanup_callback:
                    if asyncio.iscoroutinefunction(cleanup_callback):
                        await cleanup_callback(resource)
                    else:
                        cleanup_callback(resource)
                
                # 如果资源有close方法，调用它
                if hasattr(resource, 'close'):
                    if asyncio.iscoroutinefunction(resource.close):
                        await resource.close()
                    else:
                        resource.close()
            
            # 移除引用
            del self._async_resources[resource_id]
            self._cleanup_callbacks.pop(resource_id, None)
            self._resource_metadata.pop(resource_id, None)
            self._total_cleaned += 1
            
            self.logger.debug(f"清理异步资源成功: {resource_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"清理异步资源失败 {resource_id}: {e}")
            return False
    
    async def cleanup_all_async(self) -> None:
        """清理所有异步资源"""
        async with self._lock:
            resource_ids = list(self._async_resources.keys())
            
            self.logger.info(f"开始清理所有异步资源，共 {len(resource_ids)} 个")
            
            # 并行清理所有资源
            cleanup_tasks = []
            for resource_id in resource_ids:
                task = self._cleanup_resource_internal(resource_id)
                cleanup_tasks.append(task)
            
            if cleanup_tasks:
                results = await asyncio.gather(*cleanup_tasks, return_exceptions=True)
                
                success_count = sum(1 for result in results if result is True)
                self.logger.info(f"异步资源清理完成: {success_count}/{len(resource_ids)} 成功")
    
    def get_async_resource_count(self) -> int:
        """获取当前异步资源数量"""
        return len(self._async_resources)
    
    def get_resource_info(self) -> Dict[str, Any]:
        """获取资源信息"""
        return {
            'current_resources': len(self._async_resources),
            'total_registered': self._total_registered,
            'total_cleaned': self._total_cleaned,
            'uptime_seconds': time.time() - self._creation_time,
            'auto_cleanup_interval': self._cleanup_interval,
            'resources': {
                resource_id: {
                    'alive': self._async_resources[resource_id]() is not None,
                    **metadata
                }
                for resource_id, metadata in self._resource_metadata.items()
            }
        }
    
    async def force_cleanup_dead_references(self) -> int:
        """强制清理所有失效引用"""
        await self._cleanup_dead_references()
        return self._total_cleaned
    
    def set_auto_cleanup_interval(self, interval: int) -> None:
        """设置自动清理间隔"""
        self._cleanup_interval = max(10, interval)  # 最小10秒
        self.logger.info(f"自动清理间隔设置为: {self._cleanup_interval}秒")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.cleanup_all_async()
        
        # 取消自动清理任务
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
    
    def __del__(self):
        """析构函数"""
        # 取消自动清理任务
        if hasattr(self, '_cleanup_task') and self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()


class AsyncResourcePool:
    """异步资源池 - 管理可复用的异步资源"""
    
    def __init__(self, resource_factory: Callable, max_size: int = 10):
        self.resource_factory = resource_factory
        self.max_size = max_size
        self._pool: asyncio.Queue = asyncio.Queue(maxsize=max_size)
        self._created_count = 0
        self._lock = asyncio.Lock()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def acquire(self) -> Any:
        """获取资源"""
        try:
            # 尝试从池中获取资源
            resource = self._pool.get_nowait()
            self.logger.debug("从池中获取资源")
            return resource
        except asyncio.QueueEmpty:
            # 池为空，创建新资源
            async with self._lock:
                if self._created_count < self.max_size:
                    resource = await self.resource_factory()
                    self._created_count += 1
                    self.logger.debug(f"创建新资源，当前总数: {self._created_count}")
                    return resource
                else:
                    # 等待资源可用
                    self.logger.debug("等待资源可用")
                    return await self._pool.get()
    
    async def release(self, resource: Any) -> None:
        """释放资源回池"""
        try:
            self._pool.put_nowait(resource)
            self.logger.debug("资源已归还到池")
        except asyncio.QueueFull:
            # 池已满，关闭资源
            if hasattr(resource, 'close'):
                if asyncio.iscoroutinefunction(resource.close):
                    await resource.close()
                else:
                    resource.close()
            
            async with self._lock:
                self._created_count -= 1
            
            self.logger.debug("池已满，关闭多余资源")
    
    async def close_all(self) -> None:
        """关闭所有资源"""
        resources = []
        
        # 获取所有资源
        while not self._pool.empty():
            try:
                resource = self._pool.get_nowait()
                resources.append(resource)
            except asyncio.QueueEmpty:
                break
        
        # 关闭所有资源
        for resource in resources:
            if hasattr(resource, 'close'):
                if asyncio.iscoroutinefunction(resource.close):
                    await resource.close()
                else:
                    resource.close()
        
        self._created_count = 0
        self.logger.info(f"已关闭 {len(resources)} 个池化资源")
