#!/usr/bin/env python3
"""
增强的智能数据加载器

结合新架构的适配器系统和旧模板引擎的自然语言接口，
提供在模板中直接使用API、数据库、文件、远程主机等功能的能力。
"""

import logging
from typing import Any, Dict, List, Optional, Union, Callable
from urllib.parse import urlparse

# 导入新架构的适配器系统
from .enterprise_data_architecture import DataRegistry
from .data_contract import DataResult

# 导入旧模板引擎的组件（如果可用）
try:
    from .smart_data_object import SmartDataLoader as LegacySmartDataLoader
    LEGACY_LOADER_AVAILABLE = True
except ImportError:
    LEGACY_LOADER_AVAILABLE = False


class EnhancedSmartDataLoader:
    """
    增强的智能数据加载器
    
    结合新旧架构的优势：
    1. 使用新架构的适配器系统（59种适配器）
    2. 提供旧架构的自然语言接口
    3. 在模板中直接使用 sd.database()、sd.api()、sd.file() 等
    """
    
    def __init__(self, data_registry: DataRegistry, template_scope=None):
        self.data_registry = data_registry
        self.template_scope = template_scope
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 如果可用，创建旧版加载器作为回退
        self.legacy_loader = None
        if LEGACY_LOADER_AVAILABLE:
            try:
                self.legacy_loader = LegacySmartDataLoader()
                self.logger.debug("旧版SmartDataLoader可用，将作为回退选项")
            except Exception as e:
                self.logger.debug(f"旧版SmartDataLoader初始化失败: {e}")
    
    def database(self, connection_string: str):
        """
        数据库连接器
        
        在模板中使用：
        {% set users = sd.database('sqlite:///app.db').query('SELECT * FROM users') %}
        {% set result = sd.database(':memory:').execute('CREATE TABLE test(id INT)') %}
        """
        try:
            # 使用新架构的适配器系统
            adapter = self.data_registry.get_adapter(connection_string)
            if adapter:
                # 如果有模板作用域，注册数据源
                if self.template_scope:
                    proxy = self.template_scope.register_data_source(
                        f'db_{hash(connection_string)}', 
                        connection_string
                    )
                    return DatabaseProxy(proxy, adapter)
                else:
                    # 直接创建连接
                    connection = adapter.create_connection(connection_string)
                    return DatabaseProxy(connection, adapter)
            
            # 回退到旧版实现
            if self.legacy_loader:
                self.logger.debug("使用旧版数据库连接器")
                return self.legacy_loader.database(connection_string)
            
            raise ValueError(f"无法处理数据库连接: {connection_string}")
            
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    def api(self, url_or_config: Union[str, Dict], **kwargs):
        """
        API连接器
        
        在模板中使用：
        {% set data = sd.api('https://api.example.com').get('/users') %}
        {% set result = sd.api({'base_url': 'https://api.com', 'auth': 'token'}).post('/data', data) %}
        """
        try:
            # 准备API配置
            if isinstance(url_or_config, str):
                api_config = {'base_url': url_or_config, **kwargs}
            else:
                api_config = {**url_or_config, **kwargs}
            
            # 使用新架构的适配器系统
            adapter = self.data_registry.get_adapter(api_config)
            if adapter:
                # 如果有模板作用域，注册数据源
                if self.template_scope:
                    proxy = self.template_scope.register_data_source(
                        f'api_{hash(str(api_config))}', 
                        api_config
                    )
                    return APIProxy(proxy, adapter)
                else:
                    # 直接创建连接
                    connection = adapter.create_connection(api_config)
                    return APIProxy(connection, adapter)
            
            # 回退到旧版实现
            if self.legacy_loader:
                self.logger.debug("使用旧版API连接器")
                if isinstance(url_or_config, str):
                    return self.legacy_loader.api(url_or_config, **kwargs)
                else:
                    return self.legacy_loader.api(url_or_config.get('base_url', ''), **url_or_config)
            
            raise ValueError(f"无法处理API连接: {api_config}")
            
        except Exception as e:
            self.logger.error(f"API连接失败: {e}")
            raise
    
    def file(self, file_path: str, **options):
        """
        文件连接器

        在模板中使用：
        {% set data = sd.file('data.csv').read() %}
        {% set content = sd.file('config.json').parse() %}
        {% set result = sd.file('output.txt').write('Hello World') %}
        """
        try:
            # 使用新架构的适配器系统
            adapter = self.data_registry.get_adapter(file_path)
            if adapter:
                # 如果有模板作用域，注册数据源
                if self.template_scope:
                    proxy = self.template_scope.register_data_source(
                        f'file_{hash(file_path)}',
                        file_path
                    )
                    return FileProxy(proxy, adapter, options, original_file_path=file_path)
                else:
                    # 直接创建连接
                    connection = adapter.create_connection(file_path)
                    return FileProxy(connection, adapter, options, original_file_path=file_path)

            # 回退到旧版实现
            if self.legacy_loader:
                self.logger.debug("使用旧版文件连接器")
                return self.legacy_loader.file(file_path, **options)

            raise ValueError(f"无法处理文件: {file_path}")

        except Exception as e:
            self.logger.error(f"文件处理失败: {e}")
            raise
    
    def remote(self, host_config: Dict[str, Any]):
        """
        远程主机连接器
        
        在模板中使用：
        {% set result = sd.remote({'host': '*************', 'user': 'admin'}).execute('ls -la') %}
        {% set files = sd.remote(ssh_config).list_files('/home/<USER>') %}
        """
        try:
            # 使用新架构的适配器系统（如果有远程主机适配器）
            adapter = self.data_registry.get_adapter(host_config)
            if adapter:
                if self.template_scope:
                    proxy = self.template_scope.register_data_source(
                        f'remote_{hash(str(host_config))}', 
                        host_config
                    )
                    return RemoteProxy(proxy, adapter)
                else:
                    connection = adapter.create_connection(host_config)
                    return RemoteProxy(connection, adapter)
            
            # 回退到旧版实现
            if self.legacy_loader:
                self.logger.debug("使用旧版远程主机连接器")
                return self.legacy_loader.remote(host_config)
            
            raise ValueError(f"无法处理远程主机连接: {host_config}")
            
        except Exception as e:
            self.logger.error(f"远程主机连接失败: {e}")
            raise
    
    def memory(self, data: Any):
        """
        内存数据处理器
        
        在模板中使用：
        {% set processed = sd.memory(raw_data).filter({'status': 'active'}) %}
        {% set sorted_data = sd.memory(data_list).sort('created_at') %}
        """
        try:
            # 使用新架构的内存适配器
            adapter = self.data_registry.get_adapter(data)
            if adapter:
                if self.template_scope:
                    proxy = self.template_scope.register_data_source(
                        f'memory_{hash(str(data))}', 
                        data
                    )
                    return MemoryProxy(proxy, adapter)
                else:
                    connection = adapter.create_connection(data)
                    return MemoryProxy(connection, adapter)
            
            # 回退到旧版实现
            if self.legacy_loader:
                self.logger.debug("使用旧版内存数据处理器")
                return self.legacy_loader.loader(data)
            
            # 最后回退：直接返回数据
            return data
            
        except Exception as e:
            self.logger.error(f"内存数据处理失败: {e}")
            return data
    
    def __getattr__(self, name: str):
        """
        动态方法调用 - 向后兼容
        
        如果方法在当前类中不存在，尝试委托给旧版加载器
        """
        if self.legacy_loader and hasattr(self.legacy_loader, name):
            method = getattr(self.legacy_loader, name)
            if callable(method):
                def wrapper(*args, **kwargs):
                    self.logger.debug(f"委托给旧版加载器: {name}")
                    return method(*args, **kwargs)
                return wrapper
        
        raise AttributeError(f"'{self.__class__.__name__}' 对象没有属性 '{name}'")


class DatabaseProxy:
    """数据库代理对象"""

    def __init__(self, connection, adapter):
        self.connection = connection
        self.adapter = adapter
        self.logger = logging.getLogger(self.__class__.__name__)

    def query(self, sql: str, params: tuple = None):
        """执行查询"""
        try:
            operations = self.adapter.get_operations()
            if 'query' in operations:
                # 如果connection是DataProxy，获取实际连接
                actual_connection = self._get_actual_connection()
                return operations['query'](actual_connection, sql, params or ())
            else:
                # 通用查询方法
                actual_connection = self._get_actual_connection()
                return actual_connection.execute(sql, params or ()).fetchall()
        except Exception as e:
            self.logger.error(f"数据库查询失败: {e}")
            raise

    def execute(self, sql: str, params: tuple = None):
        """执行SQL语句"""
        try:
            operations = self.adapter.get_operations()
            if 'execute' in operations:
                # 如果connection是DataProxy，获取实际连接
                actual_connection = self._get_actual_connection()
                return operations['execute'](actual_connection, sql, params or ())
            else:
                # 通用执行方法
                actual_connection = self._get_actual_connection()
                return actual_connection.execute(sql, params or ())
        except Exception as e:
            self.logger.error(f"数据库执行失败: {e}")
            raise

    def _get_actual_connection(self):
        """获取实际数据库连接"""
        # 如果connection是DataProxy，尝试获取实际连接
        if hasattr(self.connection, 'source'):
            return self.connection.source
        elif hasattr(self.connection, 'get_data'):
            try:
                return self.connection.get_data()
            except:
                pass

        # 如果是字符串，可能是连接字符串，需要重新创建连接
        if isinstance(self.connection, str):
            return self.adapter.create_connection(self.connection)

        # 否则直接返回connection
        return self.connection


class APIProxy:
    """API代理对象"""
    
    def __init__(self, connection, adapter):
        self.connection = connection
        self.adapter = adapter
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def get(self, path: str = '', **kwargs):
        """GET请求"""
        return self._request('GET', path, **kwargs)
    
    def post(self, path: str = '', data=None, **kwargs):
        """POST请求"""
        return self._request('POST', path, data=data, **kwargs)
    
    def put(self, path: str = '', data=None, **kwargs):
        """PUT请求"""
        return self._request('PUT', path, data=data, **kwargs)
    
    def delete(self, path: str = '', **kwargs):
        """DELETE请求"""
        return self._request('DELETE', path, **kwargs)
    
    def _request(self, method: str, path: str, **kwargs):
        """通用请求方法"""
        try:
            operations = self.adapter.get_operations()
            if 'request' in operations:
                return operations['request'](self.connection, method, path, **kwargs)
            else:
                # 简化的请求处理
                return {'method': method, 'path': path, 'status': 'success'}
        except Exception as e:
            self.logger.error(f"API请求失败: {e}")
            raise


class FileProxy:
    """文件代理对象"""

    def __init__(self, connection, adapter, options=None, original_file_path=None):
        self.connection = connection
        self.adapter = adapter
        self.options = options or {}
        self.original_file_path = original_file_path
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def read(self):
        """读取文件"""
        try:
            operations = self.adapter.get_operations()
            if 'read' in operations:
                return operations['read'](self.connection, **self.options)
            else:
                # 通用读取方法
                with open(self.connection, 'r', encoding='utf-8') as f:
                    return f.read()
        except Exception as e:
            self.logger.error(f"文件读取失败: {e}")
            raise
    
    def write(self, content: str):
        """写入文件"""
        try:
            operations = self.adapter.get_operations()
            if 'write' in operations:
                return operations['write'](self.connection, content, **self.options)
            else:
                # 通用写入方法
                with open(self.connection, 'w', encoding='utf-8') as f:
                    f.write(content)
                return len(content)
        except Exception as e:
            self.logger.error(f"文件写入失败: {e}")
            raise
    
    def parse(self):
        """解析文件（JSON、CSV等）"""
        try:
            operations = self.adapter.get_operations()
            if 'parse' in operations:
                return operations['parse'](self.connection, **self.options)
            else:
                # 获取实际文件路径
                file_path = self._get_actual_file_path()

                # 根据文件扩展名解析
                if file_path.endswith('.json'):
                    import json
                    with open(file_path, 'r', encoding='utf-8') as f:
                        return json.load(f)
                elif file_path.endswith('.csv'):
                    import csv
                    with open(file_path, 'r', encoding='utf-8') as f:
                        return list(csv.DictReader(f))
                else:
                    return self.read()
        except Exception as e:
            self.logger.error(f"文件解析失败: {e}")
            raise

    def _get_actual_file_path(self):
        """获取实际文件路径"""
        # 如果connection是DataProxy，尝试获取实际数据
        if hasattr(self.connection, 'get_data'):
            try:
                return self.connection.get_data()
            except:
                pass

        # 如果connection是字符串，直接返回
        if isinstance(self.connection, str):
            return self.connection

        # 如果有原始文件路径，使用它
        if hasattr(self, 'original_file_path'):
            return self.original_file_path

        # 最后尝试从connection中提取路径信息
        if hasattr(self.connection, 'source_info'):
            return self.connection.source_info.get('file_path', '')

        raise ValueError("无法获取实际文件路径")


class RemoteProxy:
    """远程主机代理对象"""
    
    def __init__(self, connection, adapter):
        self.connection = connection
        self.adapter = adapter
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def execute(self, command: str):
        """执行远程命令"""
        try:
            operations = self.adapter.get_operations()
            if 'execute' in operations:
                return operations['execute'](self.connection, command)
            else:
                # 模拟远程执行
                return {'command': command, 'status': 'success', 'output': 'mock output'}
        except Exception as e:
            self.logger.error(f"远程命令执行失败: {e}")
            raise


class MemoryProxy:
    """内存数据代理对象"""
    
    def __init__(self, connection, adapter):
        self.connection = connection
        self.adapter = adapter
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def filter(self, criteria: Dict):
        """过滤数据"""
        try:
            operations = self.adapter.get_operations()
            if 'filter' in operations:
                return operations['filter'](self.connection, criteria)
            else:
                # 简单过滤实现
                if isinstance(self.connection, list):
                    return [item for item in self.connection 
                           if all(item.get(k) == v for k, v in criteria.items())]
                return self.connection
        except Exception as e:
            self.logger.error(f"数据过滤失败: {e}")
            raise
    
    def sort(self, key: str, reverse: bool = False):
        """排序数据"""
        try:
            operations = self.adapter.get_operations()
            if 'sort' in operations:
                return operations['sort'](self.connection, key, reverse)
            else:
                # 简单排序实现
                if isinstance(self.connection, list):
                    return sorted(self.connection, key=lambda x: x.get(key, ''), reverse=reverse)
                return self.connection
        except Exception as e:
            self.logger.error(f"数据排序失败: {e}")
            raise
