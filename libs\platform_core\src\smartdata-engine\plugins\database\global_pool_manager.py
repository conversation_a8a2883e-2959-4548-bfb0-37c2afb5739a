#!/usr/bin/env python3
"""
全局连接池管理器

提供跨模板的数据库连接池管理，支持：
- 基于数据库实例+环境的连接池管理
- 连接池复用和智能管理
- 性能监控和健康检查
- 资源优化和并发控制
"""

import asyncio
import logging
import time
from typing import Dict, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta

try:
    from .connectors import ConnectionConfig, ConnectorFactory
    from .connection_pool import ConnectionPoolManager
except ImportError:
    # 处理相对导入问题
    import sys
    import os
    current_dir = os.path.dirname(__file__)
    sys.path.insert(0, current_dir)
    from connectors import ConnectionConfig, ConnectorFactory
    from connection_pool import ConnectionPoolManager


@dataclass
class PoolConfig:
    """连接池配置"""
    min_size: int = 5
    max_size: int = 20
    max_idle_time: float = 3600.0  # 1小时
    health_check_interval: float = 300.0  # 5分钟
    max_lifetime: float = 7200.0  # 2小时最大生命周期
    acquire_timeout: float = 30.0  # 获取连接超时
    
    # 性能优化配置
    enable_monitoring: bool = True
    enable_health_check: bool = True
    enable_auto_scaling: bool = False


@dataclass
class PoolStats:
    """连接池统计信息"""
    pool_key: str
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    last_accessed: datetime = field(default_factory=datetime.now)
    total_queries: int = 0
    avg_query_time: float = 0.0
    error_count: int = 0


class GlobalConnectionPoolManager:
    """全局连接池管理器
    
    单例模式，管理所有数据库连接池：
    - 基于 {db_instance}_{environment}_{db_type} 的唯一键管理
    - 自动创建和清理连接池
    - 性能监控和健康检查
    - 资源优化和并发控制
    """
    
    _instance: Optional['GlobalConnectionPoolManager'] = None
    _lock = asyncio.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._pools: Dict[str, Any] = {}
            self._pool_configs: Dict[str, PoolConfig] = {}
            self._connectors: Dict[str, Any] = {}
            self._pool_stats: Dict[str, PoolStats] = {}
            self._connection_configs: Dict[str, ConnectionConfig] = {}
            
            self.logger = logging.getLogger(f"{__name__}.GlobalConnectionPoolManager")
            self._health_check_task: Optional[asyncio.Task] = None
            self._cleanup_task: Optional[asyncio.Task] = None
            self._initialized = True

            # 延迟启动后台任务，避免在没有事件循环时出错
            self._background_tasks_started = False
    
    def get_pool_key(self, db_instance: str, environment: str, db_type: str) -> str:
        """生成连接池唯一键
        
        Args:
            db_instance: 数据库实例名称 (如: localhost, prod-mysql-01)
            environment: 环境名称 (如: prod, test, dev)
            db_type: 数据库类型 (如: mysql, postgresql)
            
        Returns:
            连接池唯一键
        """
        return f"{db_instance}_{environment}_{db_type}".lower().replace('-', '_')
    
    async def get_or_create_pool(self, 
                               db_instance: str,
                               environment: str, 
                               config: ConnectionConfig,
                               pool_config: Optional[PoolConfig] = None) -> Any:
        """获取或创建连接池
        
        Args:
            db_instance: 数据库实例名称
            environment: 环境名称
            config: 数据库连接配置
            pool_config: 连接池配置
            
        Returns:
            连接池对象
        """
        # 推断数据库类型
        db_type = self._infer_db_type(config)
        pool_key = self.get_pool_key(db_instance, environment, db_type)
        
        # 确保后台任务已启动
        await self._ensure_background_tasks_started()

        if pool_key not in self._pools:
            async with self._lock:
                if pool_key not in self._pools:
                    await self._create_new_pool(pool_key, config, pool_config or PoolConfig())
        
        # 更新访问时间
        if pool_key in self._pool_stats:
            self._pool_stats[pool_key].last_accessed = datetime.now()
        
        return self._pools[pool_key]
    
    async def _create_new_pool(self, pool_key: str, config: ConnectionConfig, pool_config: PoolConfig):
        """创建新的连接池"""
        try:
            self.logger.info(f"创建新连接池: {pool_key}")
            
            # 推断数据库类型
            db_type = self._infer_db_type(config)
            
            # 创建连接器
            connector = ConnectorFactory.create_connector(db_type, enable_debug=False)
            self._connectors[pool_key] = connector
            
            # 更新连接配置以适应连接池
            pool_connection_config = self._adapt_config_for_pool(config, pool_config)
            
            # 创建连接池
            pool = await connector.create_pool(pool_connection_config)
            self._pools[pool_key] = pool
            
            # 保存配置和统计
            self._pool_configs[pool_key] = pool_config
            self._connection_configs[pool_key] = config
            self._pool_stats[pool_key] = PoolStats(pool_key=pool_key)
            
            self.logger.info(f"连接池创建成功: {pool_key} (类型: {db_type})")
            
        except Exception as e:
            self.logger.error(f"创建连接池失败 {pool_key}: {e}")
            raise
    
    def _infer_db_type(self, config: ConnectionConfig) -> str:
        """推断数据库类型"""
        # 如果配置中有明确的数据库类型
        if hasattr(config, 'db_type') and config.db_type:
            return config.db_type
        
        # 根据端口推断
        port_mapping = {
            3306: 'mysql',
            5432: 'postgresql', 
            6379: 'redis',
            27017: 'mongodb',
            9200: 'elasticsearch',
            1521: 'oracle'
        }
        
        if config.port in port_mapping:
            return port_mapping[config.port]
        
        # 默认返回mysql
        return 'mysql'
    
    def _adapt_config_for_pool(self, config: ConnectionConfig, pool_config: PoolConfig) -> ConnectionConfig:
        """适配连接配置以支持连接池"""
        # 创建新的配置对象，设置连接池参数
        adapted_config = ConnectionConfig(
            host=config.host,
            port=config.port,
            database=config.database,
            username=config.username,
            password=config.password,
            charset=getattr(config, 'charset', 'utf8mb4'),
            ssl=getattr(config, 'ssl', False),
            timeout=pool_config.acquire_timeout,
            max_connections=pool_config.max_size,
            min_connections=pool_config.min_size,
            max_idle_time=pool_config.max_idle_time
        )
        
        return adapted_config
    
    async def get_connection(self, pool_key: str) -> Any:
        """从连接池获取连接"""
        if pool_key not in self._pools:
            raise ValueError(f"连接池不存在: {pool_key}")
        
        try:
            pool = self._pools[pool_key]
            connector = self._connectors[pool_key]
            
            # 记录统计信息
            start_time = time.time()
            connection = await connector.acquire_connection(pool)
            acquire_time = time.time() - start_time
            
            # 更新统计
            if pool_key in self._pool_stats:
                stats = self._pool_stats[pool_key]
                stats.active_connections += 1
                stats.total_queries += 1
                
                # 更新平均查询时间
                if stats.total_queries > 1:
                    stats.avg_query_time = (stats.avg_query_time * (stats.total_queries - 1) + acquire_time) / stats.total_queries
                else:
                    stats.avg_query_time = acquire_time
            
            return connection
            
        except Exception as e:
            # 记录错误
            if pool_key in self._pool_stats:
                self._pool_stats[pool_key].error_count += 1
            
            self.logger.error(f"获取连接失败 {pool_key}: {e}")
            raise
    
    async def release_connection(self, pool_key: str, connection: Any):
        """释放连接回连接池"""
        if pool_key not in self._pools:
            self.logger.warning(f"尝试释放连接到不存在的连接池: {pool_key}")
            return
        
        try:
            connector = self._connectors[pool_key]
            await connector.release_connection(connection)
            
            # 更新统计
            if pool_key in self._pool_stats:
                self._pool_stats[pool_key].active_connections = max(0, 
                    self._pool_stats[pool_key].active_connections - 1)
            
        except Exception as e:
            self.logger.error(f"释放连接失败 {pool_key}: {e}")
    
    async def cleanup_pool(self, pool_key: str):
        """清理指定连接池"""
        if pool_key not in self._pools:
            return
        
        try:
            connector = self._connectors[pool_key]
            pool = self._pools[pool_key]
            await connector.close_pool(pool)
            
            # 清理相关数据
            del self._pools[pool_key]
            del self._connectors[pool_key]
            del self._pool_configs[pool_key]
            del self._connection_configs[pool_key]
            if pool_key in self._pool_stats:
                del self._pool_stats[pool_key]
            
            self.logger.info(f"连接池已清理: {pool_key}")
            
        except Exception as e:
            self.logger.error(f"清理连接池失败 {pool_key}: {e}")
    
    async def cleanup_all(self):
        """清理所有连接池"""
        pool_keys = list(self._pools.keys())
        for pool_key in pool_keys:
            await self.cleanup_pool(pool_key)
    
    def get_pool_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有连接池统计信息"""
        stats = {}
        for pool_key, pool_stat in self._pool_stats.items():
            stats[pool_key] = {
                'total_connections': pool_stat.total_connections,
                'active_connections': pool_stat.active_connections,
                'idle_connections': pool_stat.idle_connections,
                'created_at': pool_stat.created_at.isoformat(),
                'last_accessed': pool_stat.last_accessed.isoformat(),
                'total_queries': pool_stat.total_queries,
                'avg_query_time': pool_stat.avg_query_time,
                'error_count': pool_stat.error_count,
                'pool_config': {
                    'min_size': self._pool_configs[pool_key].min_size,
                    'max_size': self._pool_configs[pool_key].max_size,
                    'max_idle_time': self._pool_configs[pool_key].max_idle_time
                }
            }
        return stats
    
    async def _ensure_background_tasks_started(self):
        """确保后台任务已启动"""
        if not self._background_tasks_started:
            try:
                await self._start_background_tasks()
                self._background_tasks_started = True
            except Exception as e:
                self.logger.warning(f"启动后台任务失败: {e}")

    async def _start_background_tasks(self):
        """启动后台任务"""
        try:
            # 健康检查任务
            self._health_check_task = asyncio.create_task(self._health_check_loop())

            # 清理任务
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())

        except Exception as e:
            self.logger.error(f"启动后台任务失败: {e}")
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await asyncio.sleep(300)  # 5分钟检查一次
                await self._perform_health_check()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"健康检查失败: {e}")
    
    async def _cleanup_loop(self):
        """清理循环"""
        while True:
            try:
                await asyncio.sleep(3600)  # 1小时清理一次
                await self._perform_cleanup()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"清理任务失败: {e}")
    
    async def _perform_health_check(self):
        """执行健康检查"""
        for pool_key in list(self._pools.keys()):
            try:
                # 检查连接池是否健康
                pool = self._pools[pool_key]
                connector = self._connectors[pool_key]
                
                # 简单的健康检查：尝试获取和释放连接
                connection = await connector.acquire_connection(pool)
                await connector.release_connection(connection)
                
            except Exception as e:
                self.logger.warning(f"连接池健康检查失败 {pool_key}: {e}")
    
    async def _perform_cleanup(self):
        """执行清理操作"""
        current_time = datetime.now()
        
        for pool_key in list(self._pool_stats.keys()):
            stats = self._pool_stats[pool_key]
            
            # 清理长时间未使用的连接池
            if current_time - stats.last_accessed > timedelta(hours=2):
                self.logger.info(f"清理长时间未使用的连接池: {pool_key}")
                await self.cleanup_pool(pool_key)


# 全局单例实例
global_pool_manager = GlobalConnectionPoolManager()
