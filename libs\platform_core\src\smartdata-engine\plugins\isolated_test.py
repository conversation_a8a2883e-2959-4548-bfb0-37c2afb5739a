#!/usr/bin/env python3
"""
隔离的插件测试

只测试重构的database和remote_file插件，避免其他插件的循环导入问题
"""

import sys
import os
import logging

# 添加路径
current_dir = os.path.dirname(__file__)
sys.path.insert(0, current_dir)

# 配置日志
logging.basicConfig(level=logging.WARNING)  # 减少日志输出
logger = logging.getLogger(__name__)


def test_database_plugin_isolated():
    """隔离测试Database插件"""
    print("🗄️  Database插件隔离测试")
    print("-" * 40)
    
    try:
        # 直接导入database模块的核心组件
        sys.path.insert(0, os.path.join(current_dir, 'database'))
        
        # 测试连接器
        from database.connectors import ConnectorFactory, ConnectionConfig
        factory = ConnectorFactory()
        supported_types = factory.get_supported_types()
        print(f"✅ 连接器工厂: 支持 {len(supported_types)} 种数据库")
        print(f"   类型: {', '.join(supported_types)}")
        
        # 测试连接配置
        config = ConnectionConfig(
            host='localhost',
            port=3306,
            database='test',
            username='user',
            password='pass'
        )
        print(f"✅ 连接配置: {config.host}:{config.port}/{config.database}")
        
        # 测试插件定义
        from database import PLUGIN_DEFINITIONS
        print(f"✅ 插件定义: {len(PLUGIN_DEFINITIONS)} 个插件")
        for plugin_def in PLUGIN_DEFINITIONS:
            print(f"   - {plugin_def['name']} v{plugin_def['version']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database插件测试失败: {e}")
        return False


def test_remote_file_plugin_isolated():
    """隔离测试Remote_file插件"""
    print("\n📁 Remote_file插件隔离测试")
    print("-" * 40)
    
    try:
        # 直接导入remote_file模块的核心组件
        sys.path.insert(0, os.path.join(current_dir, 'remote_file'))
        
        # 测试协议工厂
        from remote_file.protocols import ProtocolFactory, AuthConfig
        factory = ProtocolFactory()
        supported_protocols = factory.get_supported_protocols()
        print(f"✅ 协议工厂: 支持 {len(supported_protocols)} 种协议")
        print(f"   协议: {', '.join(supported_protocols)}")
        
        # 测试认证配置
        auth_config = AuthConfig(
            auth_type='basic',
            username='test_user',
            password='test_pass'
        )
        print(f"✅ 认证配置: {auth_config.auth_type} 认证")
        
        # 测试插件定义
        from remote_file import PLUGIN_DEFINITIONS
        print(f"✅ 插件定义: {len(PLUGIN_DEFINITIONS)} 个插件")
        for plugin_def in PLUGIN_DEFINITIONS:
            print(f"   - {plugin_def['name']} v{plugin_def['version']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Remote_file插件测试失败: {e}")
        return False


def test_performance_components_isolated():
    """隔离测试性能优化组件"""
    print("\n🚀 性能优化组件隔离测试")
    print("-" * 40)

    try:
        # 测试全局连接池管理器
        sys.path.insert(0, os.path.join(current_dir, 'database'))

        # 先测试PoolConfig类
        from database.global_pool_manager import PoolConfig
        pool_config = PoolConfig(min_size=5, max_size=20)
        print(f"✅ 连接池配置: min={pool_config.min_size}, max={pool_config.max_size}")

        # 测试GlobalConnectionPoolManager类（不实例化以避免异步问题）
        from database.global_pool_manager import GlobalConnectionPoolManager

        # 测试类方法（不需要实例化）
        pool_key = GlobalConnectionPoolManager().get_pool_key('localhost', 'test', 'mysql')
        print(f"✅ 连接池键生成: {pool_key}")

        # 测试模板连接管理器类
        from database.template_connection_manager import TemplateConnectionManager

        # 只测试类的创建，不进行异步操作
        template_manager = TemplateConnectionManager('test_template')
        print(f"✅ 模板连接管理器: {template_manager.template_id}")

        # 测试便捷函数的导入
        from database.template_connection_manager import get_template_connection_manager
        print("✅ 模板连接管理器便捷函数: 导入成功")

        return True

    except Exception as e:
        print(f"❌ 性能优化组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_enterprise_compatibility():
    """测试企业级兼容性"""
    print("\n🏢 企业级兼容性测试")
    print("-" * 40)
    
    try:
        # 测试Database企业级兼容性
        sys.path.insert(0, os.path.join(current_dir, 'database'))
        
        from database.connectors import EnterpriseConnectorFactory
        from database.database_processor import EnterpriseDatabaseProcessor
        
        # 测试企业级连接器工厂
        enterprise_factory = EnterpriseConnectorFactory()
        supported_dbs = enterprise_factory.get_supported_databases()
        print(f"✅ 企业级连接器: 支持 {len(supported_dbs)} 种数据库")
        
        # 测试企业级处理器
        enterprise_processor = EnterpriseDatabaseProcessor()
        print(f"✅ 企业级数据库处理器: {enterprise_processor.processor_id}")
        
        # 测试Remote_file企业级兼容性
        sys.path.insert(0, os.path.join(current_dir, 'remote_file'))

        from remote_file.protocols import ProtocolFactory, EnterpriseProtocolFactory
        from remote_file.remote_processor import EnterpriseRemoteFileProcessor

        # 验证企业级别名
        if EnterpriseProtocolFactory == ProtocolFactory:
            print("✅ 企业级协议工厂: 别名映射正确")
        else:
            print("❌ 企业级协议工厂: 别名映射错误")
            return False
        
        # 测试企业级处理器
        enterprise_rf_processor = EnterpriseRemoteFileProcessor()
        print(f"✅ 企业级远程文件处理器: {enterprise_rf_processor.processor_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 企业级兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_plugin_standards():
    """测试插件标准符合性"""
    print("\n📋 插件标准符合性测试")
    print("-" * 40)
    
    try:
        # 检查Database插件标准符合性
        sys.path.insert(0, os.path.join(current_dir, 'database'))
        import database
        
        required_components = [
            'PLUGIN_DEFINITIONS', 'get_plugin_definitions',
            'ConnectorFactory', 'DatabaseProcessor'
        ]
        
        for component in required_components:
            if hasattr(database, component):
                print(f"✅ Database插件.{component}: 存在")
            else:
                print(f"❌ Database插件.{component}: 缺失")
                return False
        
        # 检查Remote_file插件标准符合性
        sys.path.insert(0, os.path.join(current_dir, 'remote_file'))
        import remote_file
        
        required_components = [
            'PLUGIN_DEFINITIONS', 'get_plugin_definitions',
            'ProtocolFactory', 'RemoteFileProcessor'
        ]
        
        for component in required_components:
            if hasattr(remote_file, component):
                print(f"✅ Remote_file插件.{component}: 存在")
            else:
                print(f"❌ Remote_file插件.{component}: 缺失")
                return False
        
        print("✅ 所有插件完全符合标准规范")
        return True
        
    except Exception as e:
        print(f"❌ 插件标准符合性测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 插件重构隔离测试")
    print("=" * 60)
    print("只测试重构的database和remote_file插件核心功能")
    
    tests = [
        ("Database插件隔离测试", test_database_plugin_isolated),
        ("Remote_file插件隔离测试", test_remote_file_plugin_isolated),
        ("性能优化组件测试", test_performance_components_isolated),
        ("企业级兼容性测试", test_enterprise_compatibility),
        ("插件标准符合性测试", test_plugin_standards),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            results.append((test_name, False))
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📊 隔离测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有隔离测试通过！")
        print("✅ 重构的插件功能完整")
        print("✅ 性能优化组件可用")
        print("✅ 企业级兼容性保持")
        print("✅ 符合插件标准规范")
        print("\n💡 建议: 循环导入问题已通过重命名解决")
        print("   - email目录 → email_plugin")
        print("   - http目录 → http_plugin")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
