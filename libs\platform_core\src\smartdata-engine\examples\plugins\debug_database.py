#!/usr/bin/env python3
"""
调试数据库查询问题
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.template_ext import create_template_engine

def debug_database():
    """调试数据库查询"""
    print("=== 调试数据库查询 ===")
    
    # 创建模板引擎
    engine = create_template_engine()
    
    # 测试1: 直接调用sd.database()
    print("\n1. 测试sd.database()返回值")
    test_template = """
{%- set db_connector = sd.database("sqlite:///:memory:") -%}
连接器类型: {{ db_connector.__class__.__name__ }}
连接器原始数据: {{ db_connector.raw_data }}
连接器数据: {{ db_connector.data }}
    """.strip()
    
    try:
        result = engine.render_template(test_template)
        print("渲染结果:")
        print(result)
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试2: 测试query方法的返回值
    print("\n2. 测试query方法返回值")
    query_test_template = """
{%- set db_result = sd.database("sqlite:///:memory:").query("SELECT 'Hello' as message") -%}
查询结果类型: {{ db_result.__class__.__name__ }}
查询结果原始数据: {{ db_result.raw_data }}
查询结果数据: {{ db_result.data }}
查询结果字典形式: {{ db_result.data if db_result.data is mapping else '不是字典' }}
    """.strip()
    
    try:
        result = engine.render_template(query_test_template)
        print("渲染结果:")
        print(result)
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试3: 直接使用Python API
    print("\n3. 直接使用Python API测试")
    try:
        from core.smart_data_object import SmartDataLoader
        loader = SmartDataLoader()
        
        # 测试database方法
        db_connector = loader.database("sqlite:///:memory:")
        print(f"数据库连接器类型: {type(db_connector)}")
        print(f"数据库连接器: {db_connector}")
        
        # 测试query方法
        if hasattr(db_connector, 'query'):
            query_result = db_connector.query("SELECT 'Hello' as message")
            print(f"查询结果类型: {type(query_result)}")
            print(f"查询结果: {query_result}")
            
            if hasattr(query_result, 'data'):
                print(f"查询结果数据: {query_result.data}")
            if hasattr(query_result, 'raw_data'):
                print(f"查询结果原始数据: {query_result.raw_data}")
        else:
            print("❌ 数据库连接器没有query方法")
            
    except Exception as e:
        print(f"❌ Python API测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_database()
