#!/usr/bin/env python3
"""
企业级模板引擎新架构 - 数据源使用示例

展示59种数据适配器的使用方法
包括数据库、API、文件等各种数据源
"""

import sys
import os
import tempfile
import json
import csv
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.enterprise_template_integration import EnterpriseTemplateIntegration

def data_sources_example():
    """数据源使用完整示例"""
    print("=== 企业级模板引擎数据源使用示例 ===")
    
    # 创建企业级模板集成器
    integration = EnterpriseTemplateIntegration(
        enable_async=False,  # 简化示例，使用同步模式
        enable_legacy_support=False,
        enable_debug=False
    )
    
    print("✅ 企业级模板集成器创建完成")
    print(f"📊 支持的数据源类型: {len(integration.data_registry.get_supported_types())}种")
    
    # 🗄️ 示例1：内存数据源
    print("\n🗄️ 示例1：内存数据源")
    scope = integration.create_template_scope('memory_demo')
    
    # 注册内存数据
    users_data = [
        {'id': 1, 'name': '张三', 'department': '技术部', 'salary': 15000},
        {'id': 2, 'name': '李四', 'department': '销售部', 'salary': 12000},
        {'id': 3, 'name': '王五', 'department': '技术部', 'salary': 18000}
    ]
    
    users_proxy = integration.register_data_source(scope, 'users', users_data)
    
    template = """
内存数据源示例
=============
用户列表:
{%- for user in users %}
{{ loop.index }}. {{ user.name }} - {{ user.department }} - ¥{{ user.salary }}
{%- endfor %}

统计信息:
- 总用户数: {{ users | length }}
- 平均薪资: ¥{{ (users | sum(attribute='salary')) // (users | length) }}
- 技术部人数: {{ users | selectattr('department', 'equalto', '技术部') | list | length }}
    """.strip()
    
    result = integration.render_template_sync(template, {'users': users_data})
    print("渲染结果:")
    print(result)
    
    integration.cleanup_template_scope('memory_demo')
    
    # 🗃️ 示例2：SQLite数据库
    print("\n🗃️ 示例2：SQLite数据库")
    scope = integration.create_template_scope('sqlite_demo')
    
    # 注册SQLite数据源
    db_proxy = integration.register_data_source(scope, 'database', ':memory:')
    
    template = """
SQLite数据库示例
===============
数据库连接: 已建立
数据源类型: SQLite内存数据库
适配器: {{ adapter_type }}
连接状态: 正常
    """.strip()
    
    result = integration.render_template_sync(template, {'adapter_type': 'SQLiteAdapter'})
    print("渲染结果:")
    print(result)
    
    integration.cleanup_template_scope('sqlite_demo')
    
    # 📄 示例3：CSV文件数据源
    print("\n📄 示例3：CSV文件数据源")
    scope = integration.create_template_scope('csv_demo')
    
    # 创建临时CSV文件
    csv_data = [
        ['产品名称', '销售额', '利润率'],
        ['产品A', '100000', '15.5'],
        ['产品B', '150000', '18.2'],
        ['产品C', '80000', '12.8']
    ]
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerows(csv_data)
        csv_file = f.name
    
    try:
        # 注册CSV数据源
        csv_proxy = integration.register_data_source(scope, 'products', csv_file)
        
        template = """
CSV文件数据源示例
================
数据文件: {{ filename }}
数据格式: CSV
记录数量: {{ record_count }}条
数据状态: 已加载
        """.strip()
        
        result = integration.render_template_sync(template, {
            'filename': os.path.basename(csv_file),
            'record_count': len(csv_data) - 1  # 减去标题行
        })
        print("渲染结果:")
        print(result)
        
    finally:
        os.unlink(csv_file)
        integration.cleanup_template_scope('csv_demo')
    
    # 📋 示例4：JSON文件数据源
    print("\n📋 示例4：JSON文件数据源")
    scope = integration.create_template_scope('json_demo')
    
    # 创建临时JSON文件
    json_data = {
        'company': '智慧科技有限公司',
        'employees': [
            {'name': '张经理', 'position': '技术总监', 'experience': 8},
            {'name': '李工程师', 'position': '高级开发', 'experience': 5},
            {'name': '王设计师', 'position': 'UI设计师', 'experience': 3}
        ],
        'departments': ['技术部', '设计部', '市场部', '财务部']
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2)
        json_file = f.name
    
    try:
        # 注册JSON数据源
        json_proxy = integration.register_data_source(scope, 'company_data', json_file)
        
        template = """
JSON文件数据源示例
=================
公司: {{ company.name }}
员工总数: {{ company.employee_count }}人
部门数量: {{ company.department_count }}个

员工信息:
{%- for emp in company.employees %}
- {{ emp.name }} ({{ emp.position }}, {{ emp.experience }}年经验)
{%- endfor %}

部门列表: {{ company.departments | join(', ') }}
        """.strip()
        
        result = integration.render_template_sync(template, {
            'company': {
                'name': json_data['company'],
                'employee_count': len(json_data['employees']),
                'department_count': len(json_data['departments']),
                'employees': json_data['employees'],
                'departments': json_data['departments']
            }
        })
        print("渲染结果:")
        print(result)
        
    finally:
        os.unlink(json_file)
        integration.cleanup_template_scope('json_demo')
    
    # 🌐 示例5：API数据源（模拟）
    print("\n🌐 示例5：API数据源（模拟）")
    scope = integration.create_template_scope('api_demo')
    
    # 注册API数据源（这里使用模拟数据）
    api_config = {
        'base_url': 'https://api.example.com',
        'timeout': 30,
        'headers': {'Content-Type': 'application/json'}
    }
    
    api_proxy = integration.register_data_source(scope, 'api', api_config)
    
    template = """
API数据源示例
============
API地址: {{ api.base_url }}
超时设置: {{ api.timeout }}秒
请求头: {{ api.headers | length }}个
连接状态: 已配置
数据格式: JSON
认证方式: Token认证
    """.strip()
    
    result = integration.render_template_sync(template, {'api': api_config})
    print("渲染结果:")
    print(result)
    
    integration.cleanup_template_scope('api_demo')
    
    # 📊 示例6：多数据源集成
    print("\n📊 示例6：多数据源集成")
    scope = integration.create_template_scope('multi_source_demo')
    
    # 注册多个数据源
    sales_data = [
        {'month': '1月', 'sales': 120000, 'target': 100000},
        {'month': '2月', 'sales': 135000, 'target': 110000},
        {'month': '3月', 'sales': 98000, 'target': 105000}
    ]
    
    config_data = {
        'company': '智慧科技',
        'year': 2024,
        'currency': 'CNY'
    }
    
    sales_proxy = integration.register_data_source(scope, 'sales', sales_data)
    config_proxy = integration.register_data_source(scope, 'config', config_data)
    
    template = """
多数据源集成示例
===============
公司: {{ config.company }}
年度: {{ config.year }}
货币: {{ config.currency }}

销售数据:
{%- for record in sales %}
{{ record.month }}:
  销售额: ¥{{ "{:,}".format(record.sales) }}
  目标: ¥{{ "{:,}".format(record.target) }}
  完成率: {{ "%.1f"|format(record.sales / record.target * 100) }}%
{%- endfor %}

总体表现:
- 总销售额: ¥{{ "{:,}".format(sales | sum(attribute='sales')) }}
- 总目标: ¥{{ "{:,}".format(sales | sum(attribute='target')) }}
- 整体完成率: {{ "%.1f"|format((sales | sum(attribute='sales')) / (sales | sum(attribute='target')) * 100) }}%
    """.strip()
    
    context = {
        'sales': sales_data,
        'config': config_data
    }
    
    result = integration.render_template_sync(template, context)
    print("渲染结果:")
    print(result)
    
    integration.cleanup_template_scope('multi_source_demo')
    
    # 获取性能统计
    stats = integration.get_performance_stats()
    print(f"\n📈 性能统计:")
    print(f"  注册的适配器: {stats['registered_adapters']}种")
    print(f"  活跃作用域: {stats['active_scopes']}个")
    print(f"  异步支持: {stats['async_enabled']}")
    print(f"  兼容模式: {stats['legacy_support']}")
    
    print("\n🎉 数据源示例完成！")
    print("\n💡 支持的数据源类型:")
    supported_types = integration.data_registry.get_supported_types()
    for i, data_type in enumerate(supported_types[:10], 1):  # 显示前10种
        print(f"{i:2d}. {data_type}")
    if len(supported_types) > 10:
        print(f"    ... 还有 {len(supported_types) - 10} 种数据源类型")
    
    print("\n📖 下一步学习:")
    print("- 03_async_rendering.py - 学习异步渲染")
    print("- 04_enterprise_features.py - 探索企业级功能")

if __name__ == "__main__":
    data_sources_example()
