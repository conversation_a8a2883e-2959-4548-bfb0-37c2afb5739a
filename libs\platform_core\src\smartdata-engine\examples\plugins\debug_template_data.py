#!/usr/bin/env python3
"""
调试模板数据结构
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.template_ext import create_template_engine

def debug_template_data():
    """调试模板数据结构"""
    print("=== 调试模板数据结构 ===")
    
    # 创建模板引擎
    engine = create_template_engine()
    
    # PostgreSQL连接字符串
    postgres_connection_string = "postgresql://admin:admin123@localhost:5432/nacos_db"
    
    # 调试模板
    debug_template = """
{%- set pg_result = sd.database(postgres_connection_string).query("SELECT version() as db_version, current_database() as db_name") -%}

调试PostgreSQL查询结果:
======================

结果类型: {{ pg_result.__class__.__name__ }}
成功状态: {{ pg_result.success }}
数据类型: {{ pg_result.data.__class__.__name__ if pg_result.data else 'None' }}
数据长度: {{ pg_result.data | length if pg_result.data else 'N/A' }}
数据内容: {{ pg_result.data }}
错误信息: {{ pg_result.error }}
执行时间: {{ pg_result.execution_time }}
处理器类型: {{ pg_result.processor_type }}

{%- if pg_result.data %}
数据详情:
{%- for row in pg_result.data %}
  行 {{ loop.index }}: {{ row }}
  - db_version: {{ row.db_version if row.db_version is defined else '未定义' }}
  - db_name: {{ row.db_name if row.db_name is defined else '未定义' }}
{%- endfor %}
{%- else %}
❌ 数据为空或None
{%- endif %}

条件测试:
- pg_result.success: {{ pg_result.success }}
- pg_result.data: {{ pg_result.data is not none }}
- pg_result.data 长度: {{ pg_result.data | length if pg_result.data else 0 }}
    """.strip()
    
    try:
        print("渲染结果:")
        result = engine.render_template(debug_template, {"postgres_connection_string": postgres_connection_string})
        print(result)
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_template_data()
