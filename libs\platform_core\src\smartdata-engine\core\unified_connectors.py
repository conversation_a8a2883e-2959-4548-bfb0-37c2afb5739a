"""
统一连接器
解决模板引擎中连接器返回数据不一致问题
"""

import logging
from typing import Any, Dict, Optional
from .data_contract import DataResult, TemplateDataContract


class UnifiedDatabaseConnector:
    """
    统一数据库连接器
    
    确保所有数据库操作返回统一的DataResult格式
    """
    
    def __init__(self, connection_string: str, registry=None):
        self.connection_string = connection_string
        self.registry = registry
        self.logger = logging.getLogger(self.__class__.__name__)
        self.data_contract = TemplateDataContract()
        
        # 解析连接字符串
        self.db_config = self._parse_connection_string(connection_string)
    
    def query(self, sql: str, params: Optional[Dict[str, Any]] = None) -> DataResult:
        """
        执行SQL查询

        Args:
            sql: SQL查询语句
            params: 查询参数

        Returns:
            DataResult: 统一的查询结果
        """
        try:
            self.logger.info(f"执行数据库查询: {sql}")

            # 优先使用真实的数据库处理器
            if self.registry:
                result = self._execute_with_processor(sql, params)
                if result:
                    return result

            # 如果没有处理器，尝试直接数据库连接
            result = self._execute_direct_connection(sql, params)
            if result:
                return result

            # 最后才返回错误，不使用模拟数据
            return DataResult.error_result(
                error="无法连接到数据库或执行查询",
                processor_type='database',
                metadata={
                    'query': sql,
                    'connection': self.connection_string,
                    'params': params
                }
            )
            
        except Exception as e:
            self.logger.error(f"数据库查询失败: {e}")
            return DataResult.error_result(
                error=str(e),
                processor_type='database',
                metadata={
                    'query': sql,
                    'connection': self.connection_string,
                    'params': params
                }
            )
    
    def _execute_with_processor(self, sql: str, params: Optional[Dict[str, Any]]) -> Optional[DataResult]:
        """使用处理器执行查询"""
        try:
            # 构建查询数据
            query_data = {
                'type': 'database_query',
                'connection_string': self.connection_string,
                'query': sql,
                'params': params,
                'config': self.db_config
            }
            
            # 尝试获取数据库处理器
            if hasattr(self.registry, 'processors'):
                for processor_id, processor in self.registry.processors.items():
                    if 'database' in processor_id.lower():
                        # 尝试同步调用
                        if hasattr(processor, 'process'):
                            try:
                                from .interfaces import ProcessingContext
                                context = ProcessingContext(metadata={'sync_mode': True})
                                
                                # 检查是否是异步方法
                                import asyncio
                                import inspect
                                
                                if inspect.iscoroutinefunction(processor.process):
                                    # 异步方法，在新线程中运行
                                    import concurrent.futures
                                    with concurrent.futures.ThreadPoolExecutor() as executor:
                                        future = executor.submit(
                                            asyncio.run, 
                                            processor.process(query_data, context=context)
                                        )
                                        result = future.result(timeout=10)
                                else:
                                    # 同步方法
                                    result = processor.process(query_data, context=context)
                                
                                # 转换结果为DataResult
                                if isinstance(result, dict):
                                    if 'success' in result:
                                        return DataResult.from_dict(result)
                                    else:
                                        return DataResult.success_result(
                                            data=result,
                                            processor_type='database',
                                            metadata={'query': sql}
                                        )
                                else:
                                    return DataResult.success_result(
                                        data=result,
                                        processor_type='database',
                                        metadata={'query': sql}
                                    )
                                    
                            except Exception as e:
                                self.logger.debug(f"处理器 {processor_id} 执行失败: {e}")
                                continue
            
            return None
            
        except Exception as e:
            self.logger.debug(f"处理器执行异常: {e}")
            return None

    def _execute_direct_connection(self, sql: str, params: Optional[Dict[str, Any]]) -> Optional[DataResult]:
        """直接连接数据库执行查询"""
        try:
            import time
            start_time = time.time()

            db_type = self.db_config.get('type', '').lower()

            if db_type == 'sqlite':
                return self._execute_sqlite(sql, params, start_time)
            elif db_type == 'postgresql':
                return self._execute_postgresql(sql, params, start_time)
            elif db_type == 'mysql':
                return self._execute_mysql(sql, params, start_time)
            else:
                self.logger.warning(f"不支持的数据库类型: {db_type}")
                return None

        except Exception as e:
            self.logger.error(f"直接数据库连接失败: {e}")
            return None

    def _execute_sqlite(self, sql: str, params: Optional[Dict[str, Any]], start_time: float) -> DataResult:
        """执行SQLite查询"""
        try:
            import sqlite3

            # 解析SQLite连接字符串
            db_path = self.db_config.get('database', ':memory:')
            if db_path == 'default':
                db_path = ':memory:'

            with sqlite3.connect(db_path) as conn:
                conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
                cursor = conn.cursor()

                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)

                if sql.strip().upper().startswith('SELECT'):
                    rows = cursor.fetchall()
                    data = [dict(row) for row in rows]
                    affected_rows = len(data)
                else:
                    data = {'affected_rows': cursor.rowcount}
                    affected_rows = cursor.rowcount

                import time
                execution_time = (time.time() - start_time) * 1000

                return DataResult.success_result(
                    data=data,
                    execution_time=execution_time,
                    affected_rows=affected_rows,
                    processor_type='sqlite_direct',
                    metadata={
                        'query': sql,
                        'connection': self.connection_string,
                        'database_path': db_path
                    }
                )

        except Exception as e:
            self.logger.error(f"SQLite查询失败: {e}")
            return DataResult.error_result(
                error=str(e),
                processor_type='sqlite_direct',
                metadata={'query': sql, 'connection': self.connection_string}
            )

    def _execute_postgresql(self, sql: str, params: Optional[Dict[str, Any]], start_time: float) -> DataResult:
        """执行PostgreSQL查询"""
        try:
            import psycopg2
            import psycopg2.extras

            # 构建连接参数
            conn_params = {
                'host': self.db_config.get('host', 'localhost'),
                'port': self.db_config.get('port', 5432),
                'database': self.db_config.get('database', 'postgres'),
                'user': self.db_config.get('username'),
                'password': self.db_config.get('password')
            }

            with psycopg2.connect(**conn_params) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                    if params:
                        cursor.execute(sql, params)
                    else:
                        cursor.execute(sql)

                    if sql.strip().upper().startswith('SELECT'):
                        rows = cursor.fetchall()
                        # 转换datetime对象为字符串，避免JSON序列化错误
                        data = []
                        for row in rows:
                            row_dict = dict(row)
                            for key, value in row_dict.items():
                                if hasattr(value, 'isoformat'):  # datetime对象
                                    row_dict[key] = value.isoformat()
                            data.append(row_dict)
                        affected_rows = len(data)
                    else:
                        data = {'affected_rows': cursor.rowcount}
                        affected_rows = cursor.rowcount

                    import time
                    execution_time = (time.time() - start_time) * 1000

                    return DataResult.success_result(
                        data=data,
                        execution_time=execution_time,
                        affected_rows=affected_rows,
                        processor_type='postgresql_direct',
                        metadata={
                            'query': sql,
                            'connection': self.connection_string,
                            'host': conn_params['host'],
                            'database': conn_params['database']
                        }
                    )

        except ImportError:
            self.logger.warning("psycopg2未安装，无法连接PostgreSQL")
            return None
        except Exception as e:
            self.logger.error(f"PostgreSQL查询失败: {e}")
            return DataResult.error_result(
                error=str(e),
                processor_type='postgresql_direct',
                metadata={'query': sql, 'connection': self.connection_string}
            )

    def _execute_mysql(self, sql: str, params: Optional[Dict[str, Any]], start_time: float) -> DataResult:
        """执行MySQL查询"""
        try:
            import pymysql

            # 构建连接参数
            conn_params = {
                'host': self.db_config.get('host', 'localhost'),
                'port': self.db_config.get('port', 3306),
                'database': self.db_config.get('database', 'mysql'),
                'user': self.db_config.get('username'),
                'password': self.db_config.get('password'),
                'charset': 'utf8mb4'
            }

            with pymysql.connect(**conn_params) as conn:
                with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                    if params:
                        cursor.execute(sql, params)
                    else:
                        cursor.execute(sql)

                    if sql.strip().upper().startswith('SELECT'):
                        rows = cursor.fetchall()
                        # 转换datetime对象为字符串，避免JSON序列化错误
                        data = []
                        for row in rows:
                            row_dict = dict(row)
                            for key, value in row_dict.items():
                                if hasattr(value, 'isoformat'):  # datetime对象
                                    row_dict[key] = value.isoformat()
                            data.append(row_dict)
                        affected_rows = len(data)
                    else:
                        data = {'affected_rows': cursor.rowcount}
                        affected_rows = cursor.rowcount

                    import time
                    execution_time = (time.time() - start_time) * 1000

                    return DataResult.success_result(
                        data=data,
                        execution_time=execution_time,
                        affected_rows=affected_rows,
                        processor_type='mysql_direct',
                        metadata={
                            'query': sql,
                            'connection': self.connection_string,
                            'host': conn_params['host'],
                            'database': conn_params['database']
                        }
                    )

        except ImportError:
            self.logger.warning("pymysql未安装，无法连接MySQL")
            return None
        except Exception as e:
            self.logger.error(f"MySQL查询失败: {e}")
            return DataResult.error_result(
                error=str(e),
                processor_type='mysql_direct',
                metadata={'query': sql, 'connection': self.connection_string}
            )


    
    def _parse_connection_string(self, conn_str: str) -> Dict[str, Any]:
        """解析连接字符串"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(conn_str)
            
            return {
                'type': parsed.scheme,
                'host': parsed.hostname or 'localhost',
                'port': parsed.port or (5432 if parsed.scheme == 'postgresql' else 3306),
                'database': parsed.path.lstrip('/') if parsed.path else 'default',
                'username': parsed.username,
                'password': parsed.password
            }
        except Exception as e:
            self.logger.warning(f"解析连接字符串失败: {e}")
            return {
                'type': 'unknown',
                'host': 'localhost',
                'port': 5432,
                'database': 'default'
            }


class UnifiedSmartDataLoader:
    """
    统一智能数据加载器
    
    确保所有数据源返回统一的DataResult格式
    """
    
    def __init__(self, registry=None):
        self.registry = registry
        self.logger = logging.getLogger(self.__class__.__name__)
        self.data_contract = TemplateDataContract()
    
    def database(self, connection_string: str) -> UnifiedDatabaseConnector:
        """
        创建数据库连接器
        
        Args:
            connection_string: 数据库连接字符串
            
        Returns:
            UnifiedDatabaseConnector: 统一数据库连接器
        """
        return UnifiedDatabaseConnector(connection_string, self.registry)
    
    def api(self, url: str, **options) -> 'UnifiedApiConnector':
        """创建API连接器"""
        return UnifiedApiConnector(url, self.registry, **options)
    
    def file(self, file_path: str, **options) -> DataResult:
        """加载文件"""
        try:
            # 这里可以集成文件处理器
            return DataResult.success_result(
                data={'file_path': file_path, 'loaded': True},
                processor_type='file',
                metadata={'path': file_path, 'options': options}
            )
        except Exception as e:
            return DataResult.error_result(
                error=str(e),
                processor_type='file',
                metadata={'path': file_path}
            )


class UnifiedApiConnector:
    """统一API连接器"""
    
    def __init__(self, url: str, registry=None, **options):
        self.url = url
        self.registry = registry
        self.options = options
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def get(self, **params) -> DataResult:
        """GET请求"""
        return self._make_request('GET', params)
    
    def post(self, data=None, **params) -> DataResult:
        """POST请求"""
        return self._make_request('POST', params, data)
    
    def _make_request(self, method: str, params: Dict[str, Any], data=None) -> DataResult:
        """执行HTTP请求"""
        try:
            # 模拟API调用
            mock_response = {
                'status': 200,
                'data': {'message': f'{method} request to {self.url}', 'params': params}
            }
            
            return DataResult.success_result(
                data=mock_response,
                execution_time=120.5,
                processor_type='api_mock',
                metadata={
                    'url': self.url,
                    'method': method,
                    'params': params,
                    'mock': True
                }
            )
            
        except Exception as e:
            return DataResult.error_result(
                error=str(e),
                processor_type='api',
                metadata={'url': self.url, 'method': method}
            )


class HybridSmartDataLoader:
    """
    混合智能数据加载器

    对于数据库功能使用统一连接器，其他功能使用原始加载器
    确保不影响现有插件功能
    """

    def __init__(self, original_loader, unified_loader):
        self.original_loader = original_loader
        self.unified_loader = unified_loader
        self.logger = logging.getLogger(self.__class__.__name__)

    def database(self, connection_string: str):
        """数据库功能使用统一连接器"""
        # 直接使用UnifiedDatabaseConnector，避免中间层
        return UnifiedDatabaseConnector(connection_string)

    def __getattr__(self, name: str):
        """其他功能委托给原始加载器"""
        if hasattr(self.original_loader, name):
            return getattr(self.original_loader, name)
        else:
            raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
