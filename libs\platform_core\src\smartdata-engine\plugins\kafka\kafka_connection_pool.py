"""
Kafka连接池管理器

提供企业级Kafka连接池管理，包括：
- 生产者连接池
- 消费者连接池
- 管理客户端连接池
- 连接健康检查
- 自动重连机制
- 性能监控
"""

import logging
import time
import threading
from typing import Any, Dict, List, Optional, Set
from dataclasses import dataclass, asdict
from enum import Enum


class ConnectionType(Enum):
    """连接类型"""
    PRODUCER = "producer"
    CONSUMER = "consumer"
    ADMIN = "admin"


class ConnectionStatus(Enum):
    """连接状态"""
    IDLE = "idle"
    ACTIVE = "active"
    ERROR = "error"
    CLOSED = "closed"


@dataclass
class ConnectionInfo:
    """连接信息"""
    connection_id: str
    connection_type: ConnectionType
    bootstrap_servers: str
    client_id: str
    status: ConnectionStatus
    created_time: float
    last_used_time: float
    use_count: int
    error_count: int
    connection_object: Any = None
    
    @property
    def age(self) -> float:
        """连接年龄（秒）"""
        return time.time() - self.created_time
    
    @property
    def idle_time(self) -> float:
        """空闲时间（秒）"""
        return time.time() - self.last_used_time


@dataclass
class PoolStats:
    """连接池统计"""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    error_connections: int = 0
    producer_connections: int = 0
    consumer_connections: int = 0
    admin_connections: int = 0
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return self.successful_requests / self.total_requests if self.total_requests > 0 else 0.0


class KafkaConnectionPool:
    """Kafka连接池管理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.KafkaConnectionPool")
        
        # 连接池配置
        self.max_connections = self.config.get('max_connections', 50)
        self.max_connections_per_type = self.config.get('max_connections_per_type', 20)
        self.connection_timeout = self.config.get('connection_timeout', 30.0)
        self.idle_timeout = self.config.get('idle_timeout', 300.0)  # 5分钟
        self.max_retries = self.config.get('max_retries', 3)
        self.health_check_interval = self.config.get('health_check_interval', 60.0)  # 1分钟
        
        # 连接存储
        self.connections: Dict[str, ConnectionInfo] = {}
        self.type_connections: Dict[ConnectionType, Set[str]] = {
            ConnectionType.PRODUCER: set(),
            ConnectionType.CONSUMER: set(),
            ConnectionType.ADMIN: set()
        }
        
        # 统计信息
        self.stats = PoolStats()
        
        # 锁
        self._lock = threading.RLock()
        
        # 检查kafka-python可用性
        self._kafka_available = self._check_kafka_availability()
    
    def _check_kafka_availability(self) -> bool:
        """检查kafka-python是否可用"""
        try:
            import kafka
            return True
        except ImportError:
            self.logger.warning("kafka-python未安装，Kafka连接池功能将受限")
            return False
    
    def get_producer(self, bootstrap_servers: str, client_id: str, config: Dict[str, Any] = None) -> Optional[Any]:
        """获取生产者连接"""
        if not self._kafka_available:
            return None
        
        with self._lock:
            # 查找可用的空闲生产者连接
            for conn_id in self.type_connections[ConnectionType.PRODUCER]:
                if conn_id in self.connections:
                    conn = self.connections[conn_id]
                    if (conn.status == ConnectionStatus.IDLE and 
                        conn.bootstrap_servers == bootstrap_servers and
                        conn.error_count < self.max_retries):
                        
                        # 标记为活跃
                        conn.status = ConnectionStatus.ACTIVE
                        conn.last_used_time = time.time()
                        conn.use_count += 1
                        self.stats.total_requests += 1
                        return conn.connection_object
            
            # 检查是否可以创建新连接
            producer_count = len(self.type_connections[ConnectionType.PRODUCER])
            if (len(self.connections) < self.max_connections and 
                producer_count < self.max_connections_per_type):
                
                # 创建新生产者连接
                return self._create_producer(bootstrap_servers, client_id, config or {})
            
            # 连接池已满
            self.logger.warning(f"生产者连接池已满，无法创建新连接")
            return None
    
    def get_consumer(self, bootstrap_servers: str, client_id: str, group_id: str, config: Dict[str, Any] = None) -> Optional[Any]:
        """获取消费者连接"""
        if not self._kafka_available:
            return None
        
        with self._lock:
            # 查找可用的空闲消费者连接
            for conn_id in self.type_connections[ConnectionType.CONSUMER]:
                if conn_id in self.connections:
                    conn = self.connections[conn_id]
                    if (conn.status == ConnectionStatus.IDLE and 
                        conn.bootstrap_servers == bootstrap_servers and
                        conn.client_id == f"{client_id}_{group_id}" and
                        conn.error_count < self.max_retries):
                        
                        # 标记为活跃
                        conn.status = ConnectionStatus.ACTIVE
                        conn.last_used_time = time.time()
                        conn.use_count += 1
                        self.stats.total_requests += 1
                        return conn.connection_object
            
            # 检查是否可以创建新连接
            consumer_count = len(self.type_connections[ConnectionType.CONSUMER])
            if (len(self.connections) < self.max_connections and 
                consumer_count < self.max_connections_per_type):
                
                # 创建新消费者连接
                return self._create_consumer(bootstrap_servers, client_id, group_id, config or {})
            
            # 连接池已满
            self.logger.warning(f"消费者连接池已满，无法创建新连接")
            return None
    
    def get_admin_client(self, bootstrap_servers: str, client_id: str, config: Dict[str, Any] = None) -> Optional[Any]:
        """获取管理客户端连接"""
        if not self._kafka_available:
            return None
        
        with self._lock:
            # 查找可用的空闲管理客户端连接
            for conn_id in self.type_connections[ConnectionType.ADMIN]:
                if conn_id in self.connections:
                    conn = self.connections[conn_id]
                    if (conn.status == ConnectionStatus.IDLE and 
                        conn.bootstrap_servers == bootstrap_servers and
                        conn.error_count < self.max_retries):
                        
                        # 标记为活跃
                        conn.status = ConnectionStatus.ACTIVE
                        conn.last_used_time = time.time()
                        conn.use_count += 1
                        self.stats.total_requests += 1
                        return conn.connection_object
            
            # 检查是否可以创建新连接
            admin_count = len(self.type_connections[ConnectionType.ADMIN])
            if (len(self.connections) < self.max_connections and 
                admin_count < self.max_connections_per_type):
                
                # 创建新管理客户端连接
                return self._create_admin_client(bootstrap_servers, client_id, config or {})
            
            # 连接池已满
            self.logger.warning(f"管理客户端连接池已满，无法创建新连接")
            return None
    
    def _create_producer(self, bootstrap_servers: str, client_id: str, config: Dict[str, Any]) -> Optional[Any]:
        """创建生产者连接"""
        try:
            from kafka import KafkaProducer
            
            # 生产者配置
            producer_config = {
                'bootstrap_servers': bootstrap_servers.split(','),
                'client_id': client_id,
                'retries': config.get('retries', 3),
                'batch_size': config.get('batch_size', 16384),
                'linger_ms': config.get('linger_ms', 0),
                'buffer_memory': config.get('buffer_memory', 33554432),
                **config
            }
            
            # 创建生产者
            producer = KafkaProducer(**producer_config)
            
            # 生成连接ID
            connection_id = f"producer_{bootstrap_servers}_{client_id}_{int(time.time() * 1000)}"
            
            # 创建连接信息
            conn_info = ConnectionInfo(
                connection_id=connection_id,
                connection_type=ConnectionType.PRODUCER,
                bootstrap_servers=bootstrap_servers,
                client_id=client_id,
                status=ConnectionStatus.ACTIVE,
                created_time=time.time(),
                last_used_time=time.time(),
                use_count=1,
                error_count=0,
                connection_object=producer
            )
            
            # 添加到连接池
            self.connections[connection_id] = conn_info
            self.type_connections[ConnectionType.PRODUCER].add(connection_id)
            
            # 更新统计
            self.stats.total_connections += 1
            self.stats.producer_connections += 1
            self.stats.total_requests += 1
            
            self.logger.info(f"创建新生产者连接: {connection_id}")
            return producer
            
        except Exception as e:
            self.logger.error(f"创建生产者连接失败: {e}")
            self.stats.failed_requests += 1
            return None
    
    def _create_consumer(self, bootstrap_servers: str, client_id: str, group_id: str, config: Dict[str, Any]) -> Optional[Any]:
        """创建消费者连接"""
        try:
            from kafka import KafkaConsumer
            
            # 消费者配置
            consumer_config = {
                'bootstrap_servers': bootstrap_servers.split(','),
                'client_id': client_id,
                'group_id': group_id,
                'auto_offset_reset': config.get('auto_offset_reset', 'latest'),
                'enable_auto_commit': config.get('enable_auto_commit', True),
                'session_timeout_ms': config.get('session_timeout_ms', 30000),
                'heartbeat_interval_ms': config.get('heartbeat_interval_ms', 3000),
                **config
            }
            
            # 创建消费者
            consumer = KafkaConsumer(**consumer_config)
            
            # 生成连接ID
            connection_id = f"consumer_{bootstrap_servers}_{client_id}_{group_id}_{int(time.time() * 1000)}"
            
            # 创建连接信息
            conn_info = ConnectionInfo(
                connection_id=connection_id,
                connection_type=ConnectionType.CONSUMER,
                bootstrap_servers=bootstrap_servers,
                client_id=f"{client_id}_{group_id}",
                status=ConnectionStatus.ACTIVE,
                created_time=time.time(),
                last_used_time=time.time(),
                use_count=1,
                error_count=0,
                connection_object=consumer
            )
            
            # 添加到连接池
            self.connections[connection_id] = conn_info
            self.type_connections[ConnectionType.CONSUMER].add(connection_id)
            
            # 更新统计
            self.stats.total_connections += 1
            self.stats.consumer_connections += 1
            self.stats.total_requests += 1
            
            self.logger.info(f"创建新消费者连接: {connection_id}")
            return consumer
            
        except Exception as e:
            self.logger.error(f"创建消费者连接失败: {e}")
            self.stats.failed_requests += 1
            return None
    
    def _create_admin_client(self, bootstrap_servers: str, client_id: str, config: Dict[str, Any]) -> Optional[Any]:
        """创建管理客户端连接"""
        try:
            from kafka import KafkaAdminClient
            
            # 管理客户端配置
            admin_config = {
                'bootstrap_servers': bootstrap_servers.split(','),
                'client_id': client_id,
                **config
            }
            
            # 创建管理客户端
            admin_client = KafkaAdminClient(**admin_config)
            
            # 生成连接ID
            connection_id = f"admin_{bootstrap_servers}_{client_id}_{int(time.time() * 1000)}"
            
            # 创建连接信息
            conn_info = ConnectionInfo(
                connection_id=connection_id,
                connection_type=ConnectionType.ADMIN,
                bootstrap_servers=bootstrap_servers,
                client_id=client_id,
                status=ConnectionStatus.ACTIVE,
                created_time=time.time(),
                last_used_time=time.time(),
                use_count=1,
                error_count=0,
                connection_object=admin_client
            )
            
            # 添加到连接池
            self.connections[connection_id] = conn_info
            self.type_connections[ConnectionType.ADMIN].add(connection_id)
            
            # 更新统计
            self.stats.total_connections += 1
            self.stats.admin_connections += 1
            self.stats.total_requests += 1
            
            self.logger.info(f"创建新管理客户端连接: {connection_id}")
            return admin_client
            
        except Exception as e:
            self.logger.error(f"创建管理客户端连接失败: {e}")
            self.stats.failed_requests += 1
            return None
    
    def release_connection(self, connection_object: Any, success: bool = True):
        """释放连接"""
        with self._lock:
            # 查找连接
            for conn_id, conn in self.connections.items():
                if conn.connection_object == connection_object:
                    if success:
                        conn.status = ConnectionStatus.IDLE
                        conn.error_count = 0
                        self.stats.successful_requests += 1
                    else:
                        conn.error_count += 1
                        if conn.error_count >= self.max_retries:
                            conn.status = ConnectionStatus.ERROR
                        self.stats.failed_requests += 1
                    
                    conn.last_used_time = time.time()
                    break
    
    def close_connection(self, connection_id: str):
        """关闭连接"""
        with self._lock:
            if connection_id in self.connections:
                conn = self.connections[connection_id]
                
                # 关闭连接对象
                try:
                    if hasattr(conn.connection_object, 'close'):
                        conn.connection_object.close()
                except Exception as e:
                    self.logger.error(f"关闭连接对象失败: {e}")
                
                # 从连接池移除
                del self.connections[connection_id]
                self.type_connections[conn.connection_type].discard(connection_id)
                
                # 更新统计
                self.stats.total_connections -= 1
                if conn.connection_type == ConnectionType.PRODUCER:
                    self.stats.producer_connections -= 1
                elif conn.connection_type == ConnectionType.CONSUMER:
                    self.stats.consumer_connections -= 1
                elif conn.connection_type == ConnectionType.ADMIN:
                    self.stats.admin_connections -= 1
                
                self.logger.info(f"关闭连接: {connection_id}")
    
    def cleanup_idle_connections(self):
        """清理空闲连接"""
        current_time = time.time()
        idle_connections = []
        
        with self._lock:
            for conn_id, conn in self.connections.items():
                if (conn.status == ConnectionStatus.IDLE and 
                    current_time - conn.last_used_time > self.idle_timeout):
                    idle_connections.append(conn_id)
        
        for conn_id in idle_connections:
            self.close_connection(conn_id)
        
        if idle_connections:
            self.logger.info(f"清理了{len(idle_connections)}个空闲连接")
    
    def health_check_connections(self):
        """健康检查连接"""
        error_connections = []
        
        with self._lock:
            for conn_id, conn in self.connections.items():
                if conn.status == ConnectionStatus.ERROR:
                    error_connections.append(conn_id)
        
        for conn_id in error_connections:
            self.close_connection(conn_id)
        
        if error_connections:
            self.logger.info(f"清理了{len(error_connections)}个错误连接")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计"""
        with self._lock:
            # 更新实时统计
            active_count = sum(1 for conn in self.connections.values() if conn.status == ConnectionStatus.ACTIVE)
            idle_count = sum(1 for conn in self.connections.values() if conn.status == ConnectionStatus.IDLE)
            error_count = sum(1 for conn in self.connections.values() if conn.status == ConnectionStatus.ERROR)
            
            self.stats.active_connections = active_count
            self.stats.idle_connections = idle_count
            self.stats.error_connections = error_count
            
            return asdict(self.stats)
    
    def close(self):
        """关闭连接池"""
        # 关闭所有连接
        connection_ids = list(self.connections.keys())
        for conn_id in connection_ids:
            self.close_connection(conn_id)
        
        self.logger.info("Kafka连接池已关闭")


# 全局Kafka连接池实例
global_kafka_connection_pool = KafkaConnectionPool()
