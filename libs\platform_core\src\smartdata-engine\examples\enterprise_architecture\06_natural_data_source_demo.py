#!/usr/bin/env python3
"""
自然数据源操作演示

展示在模板中使用自然语言方式处理API、数据库、文件、远程主机等操作
这正是新框架要解决的旧模板引擎的核心问题
"""

import sys
import os
import json
import tempfile
import sqlite3
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.enterprise_template_integration import EnterpriseTemplateIntegration


def natural_data_source_demo():
    """自然数据源操作演示"""
    print("=== 自然数据源操作演示 ===")
    print("解决旧模板引擎在API、数据库、文件、远程主机操作中的问题")
    print("=" * 80)
    
    # 创建企业级模板集成器
    integration = EnterpriseTemplateIntegration(
        enable_async=False,
        enable_legacy_support=False,
        enable_debug=False
    )
    
    # 🗄️ 示例1：数据库自然语言操作
    print("\n🗄️ 示例1：数据库自然语言操作")
    print("-" * 50)
    
    # 创建临时SQLite数据库
    db_file = tempfile.mktemp(suffix='.db')
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    # 创建测试数据
    cursor.execute('''
        CREATE TABLE users (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT NOT NULL,
            department TEXT,
            salary INTEGER
        )
    ''')
    
    test_users = [
        (1, '张三', '<EMAIL>', '技术部', 15000),
        (2, '李四', '<EMAIL>', '销售部', 12000),
        (3, '王五', '<EMAIL>', '技术部', 18000),
        (4, '赵六', '<EMAIL>', '市场部', 13000)
    ]
    
    cursor.executemany('INSERT INTO users VALUES (?, ?, ?, ?, ?)', test_users)
    conn.commit()
    conn.close()
    
    # 数据库操作模板 - 自然语言风格
    # 使用正斜杠避免Windows路径问题
    db_file_normalized = db_file.replace('\\', '/')
    database_template = f"""
数据库操作演示
=============

{{%- set db_conn = sd.database('sqlite:///{db_file_normalized}') -%}}

1. 查询所有用户:
{{%- set all_users = db_conn.query('SELECT * FROM users ORDER BY salary DESC') -%}}
{{%- for user in all_users %}}
- {{{{ user.name }}}} ({{{{ user.department }}}}) - 薪资: ¥{{{{ "{{:,}}".format(user.salary) }}}}
{{%- endfor %}}

2. 技术部员工统计:
{{%- set tech_users = db_conn.query('SELECT * FROM users WHERE department = ?', ('技术部',)) -%}}
技术部共有 {{{{ tech_users | length }}}} 名员工
平均薪资: ¥{{{{ "{{:,}}".format((tech_users | sum(attribute='salary')) // (tech_users | length)) }}}}

3. 薪资统计:
{{%- set salary_stats = db_conn.query('SELECT department, AVG(salary) as avg_salary, COUNT(*) as count FROM users GROUP BY department') -%}}
各部门薪资统计:
{{%- for stat in salary_stats %}}
- {{{{ stat.department }}}}: 平均¥{{{{ "{{:,.0f}}".format(stat.avg_salary) }}}} ({{{{ stat.count }}}}人)
{{%- endfor %}}

4. 动态查询 - 高薪员工:
{{%- set high_salary_users = db_conn.query('SELECT name, salary FROM users WHERE salary > ? ORDER BY salary DESC', (15000,)) -%}}
高薪员工 (>¥15,000):
{{%- for user in high_salary_users %}}
- {{{{ user.name }}}}: ¥{{{{ "{{:,}}".format(user.salary) }}}}
{{%- endfor %}}
    """.strip()
    
    try:
        result = integration.render_template_sync(database_template)
        print("✅ 数据库自然语言操作成功:")
        print(result)
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理数据库文件
        if os.path.exists(db_file):
            os.unlink(db_file)
    
    # 📁 示例2：文件自然语言操作
    print("\n📁 示例2：文件自然语言操作")
    print("-" * 50)
    
    # 创建临时JSON文件
    json_data = {
        "company": {
            "name": "智慧科技有限公司",
            "founded": 2020,
            "employees": [
                {"id": 1, "name": "Alice", "role": "CTO", "skills": ["Python", "AI", "Management"]},
                {"id": 2, "name": "Bob", "role": "Developer", "skills": ["JavaScript", "React", "Node.js"]},
                {"id": 3, "name": "Charlie", "role": "Designer", "skills": ["UI/UX", "Figma", "Photoshop"]}
            ],
            "projects": [
                {"name": "项目A", "status": "进行中", "team_size": 5},
                {"name": "项目B", "status": "已完成", "team_size": 3},
                {"name": "项目C", "status": "计划中", "team_size": 8}
            ]
        }
    }
    
    json_file = tempfile.mktemp(suffix='.json')
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2)
    
    # 创建临时CSV文件
    csv_data = """id,product,price,category,stock
1,笔记本电脑,8999,电子产品,50
2,无线鼠标,199,电子产品,200
3,机械键盘,599,电子产品,80
4,显示器,2999,电子产品,30
5,办公椅,1299,办公用品,25"""
    
    csv_file = tempfile.mktemp(suffix='.csv')
    with open(csv_file, 'w', encoding='utf-8') as f:
        f.write(csv_data)
    
    # 文件操作模板 - 自然语言风格
    # 使用正斜杠避免Windows路径问题
    json_file_normalized = json_file.replace('\\', '/')
    csv_file_normalized = csv_file.replace('\\', '/')
    file_template = f"""
文件操作演示
===========

1. JSON文件处理:
{{%- set company_data = sd.file('{json_file_normalized}').parse() -%}}
公司信息: {{{{ company_data.company.name }}}} (成立于{{{{ company_data.company.founded }}}}年)
员工数量: {{{{ company_data.company.employees | length }}}}人

员工技能统计:
{{%- set all_skills = [] -%}}
{{%- for emp in company_data.company.employees -%}}
  {{%- for skill in emp.skills -%}}
    {{%- set _ = all_skills.append(skill) -%}}
  {{%- endfor -%}}
{{%- endfor -%}}
{{%- set unique_skills = all_skills | unique | list -%}}
技能种类: {{{{ unique_skills | length }}}}种 ({{{{ unique_skills | join(', ') }}}})

项目状态分布:
{{%- for status in ['进行中', '已完成', '计划中'] -%}}
{{%- set projects_by_status = company_data.company.projects | selectattr('status', 'equalto', status) | list -%}}
- {{{{ status }}}}: {{{{ projects_by_status | length }}}}个项目
{{%- endfor -%}}

2. CSV文件处理:
{{%- set products = sd.file('{csv_file_normalized}').parse() -%}}
产品目录 (共{{{{ products | length }}}}种产品):
{{%- for product in products %}}
- {{{{ product.product }}}}: ¥{{{{ "{{:,}}".format(product.price | int) }}}} (库存: {{{{ product.stock }}}})
{{%- endfor %}}

价格统计:
- 总价值: ¥{{{{ "{{:,}}".format(products | sum(attribute='price') | int) }}}}
- 平均价格: ¥{{{{ "{{:,}}".format((products | sum(attribute='price') | int) // (products | length)) }}}}
- 最高价格: ¥{{{{ "{{:,}}".format(products | map(attribute='price') | map('int') | max) }}}}

库存统计:
- 总库存: {{{{ products | sum(attribute='stock') | int }}}}件
- 平均库存: {{{{ (products | sum(attribute='stock') | int) // (products | length) }}}}件
    """.strip()
    
    try:
        result = integration.render_template_sync(file_template)
        print("✅ 文件自然语言操作成功:")
        print(result)
    except Exception as e:
        print(f"❌ 文件操作失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理临时文件
        for temp_file in [json_file, csv_file]:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
    
    # 🌐 示例3：API自然语言操作（模拟）
    print("\n🌐 示例3：API自然语言操作（模拟）")
    print("-" * 50)
    
    # API操作模板 - 自然语言风格
    api_template = """
API操作演示 (模拟)
================

1. 用户API调用:
{%- set api_client = sd.api('https://jsonplaceholder.typicode.com') -%}
{%- set users = api_client.get('/users') -%}
API状态: {{ "成功" if users else "失败" }}

2. 数据API调用:
{%- set data_api = sd.api({'base_url': 'https://api.example.com', 'auth': 'Bearer token123'}) -%}
{%- set posts = data_api.get('/posts?limit=5') -%}
数据获取: {{ "成功" if posts else "失败" }}

3. 提交数据:
{%- set new_post = {'title': '新文章', 'content': '这是内容', 'author': 'AI助手'} -%}
{%- set create_result = data_api.post('/posts', new_post) -%}
创建结果: {{ create_result.status if create_result else "失败" }}

4. RESTful操作:
{%- set rest_api = sd.api('https://api.restful.com') -%}
- GET请求: {{ rest_api.get('/resources').method }}
- POST请求: {{ rest_api.post('/resources', {'name': 'test'}).method }}
- PUT请求: {{ rest_api.put('/resources/1', {'name': 'updated'}).method }}
- DELETE请求: {{ rest_api.delete('/resources/1').method }}
    """.strip()
    
    try:
        result = integration.render_template_sync(api_template)
        print("✅ API自然语言操作成功:")
        print(result)
    except Exception as e:
        print(f"❌ API操作失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 💾 示例4：内存数据自然语言操作
    print("\n💾 示例4：内存数据自然语言操作")
    print("-" * 50)
    
    # 内存数据操作模板
    memory_template = """
内存数据操作演示
===============

{%- set raw_data = [
    {'id': 1, 'name': '产品A', 'category': '电子', 'price': 999, 'rating': 4.5, 'active': True},
    {'id': 2, 'name': '产品B', 'category': '服装', 'price': 299, 'rating': 4.2, 'active': True},
    {'id': 3, 'name': '产品C', 'category': '电子', 'price': 1599, 'rating': 4.8, 'active': False},
    {'id': 4, 'name': '产品D', 'category': '家居', 'price': 799, 'rating': 4.0, 'active': True},
    {'id': 5, 'name': '产品E', 'category': '电子', 'price': 2999, 'rating': 4.9, 'active': True}
] -%}

1. 数据过滤:
{%- set memory_data = sd.memory(raw_data) -%}
{%- set active_products = memory_data.filter({'active': True}) -%}
活跃产品: {{ active_products | length }}个

{%- set electronics = memory_data.filter({'category': '电子', 'active': True}) -%}
活跃电子产品: {{ electronics | length }}个

2. 数据排序:
{%- set sorted_by_price = memory_data.sort('price', True) -%}
按价格排序 (高到低):
{%- for product in sorted_by_price[:3] %}
- {{ product.name }}: ¥{{ "{:,}".format(product.price) }} (评分: {{ product.rating }})
{%- endfor %}

{%- set sorted_by_rating = memory_data.sort('rating', True) -%}
按评分排序 (高到低):
{%- for product in sorted_by_rating[:3] %}
- {{ product.name }}: {{ product.rating }}⭐ (¥{{ "{:,}".format(product.price) }})
{%- endfor %}

3. 数据统计:
总产品数: {{ raw_data | length }}
平均价格: ¥{{ "{:,}".format((raw_data | sum(attribute='price')) // (raw_data | length)) }}
平均评分: {{ "%.1f"|format((raw_data | sum(attribute='rating')) / (raw_data | length)) }}⭐
活跃率: {{ "%.1f"|format((raw_data | selectattr('active') | list | length) / (raw_data | length) * 100) }}%
    """.strip()
    
    try:
        result = integration.render_template_sync(memory_template)
        print("✅ 内存数据自然语言操作成功:")
        print(result)
    except Exception as e:
        print(f"❌ 内存数据操作失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🎉 自然数据源操作演示完成！")
    print("\n💡 新架构解决的核心问题:")
    print("1. ✅ 数据库操作：模板中直接使用 sd.database().query()")
    print("2. ✅ 文件处理：模板中直接使用 sd.file().parse()")
    print("3. ✅ API调用：模板中直接使用 sd.api().get()")
    print("4. ✅ 内存数据：模板中直接使用 sd.memory().filter()")
    print("5. ✅ 自然语法：贴近Python原生编程体验")
    print("6. ✅ 统一接口：所有数据源使用相同的调用方式")
    print("7. ✅ 适配器支持：底层使用59种强大的数据适配器")
    
    print("\n📈 架构优势对比:")
    print("旧模板引擎: 功能强大但外部数据源操作复杂")
    print("新架构: 保持强大功能 + 自然语言接口 + 适配器系统")
    print("统一架构: 完美解决旧模板引擎的核心痛点")


if __name__ == "__main__":
    natural_data_source_demo()
