"""
HTTP插件 - 标准化版本

提供透明的HTTP/REST API访问能力，用户可以像使用原生httpx一样自然地使用。
插件在后台透明处理智能适配、缓存等功能。

使用方式：
    # 在模板中使用 - 完全透明
    {% set response = sd.http('https://api.example.com/data') %}
    {% set response = sd.http({'url': 'https://api.example.com', 'method': 'POST', 'json': data}) %}
    
    # Python中使用
    from plugins.http.smart_loader import global_loader
    result = global_loader.load('https://api.example.com/data')
"""

# 标准插件定义
PLUGIN_DEFINITIONS = [
    {
        # 基础信息
        'id': 'http',
        'name': 'HTTP插件',
        'description': '提供透明的HTTP/REST API访问能力，支持所有HTTP方法',
        'version': '2.0.0',
        'type': 'processor',
        'category': 'network',
        'priority': 80,
        
        # 加载信息
        'class_name': 'HttpProcessor',
        'module_file': 'http_processor',
        'auto_load': True,
        'enabled': True,
        
        # 功能信息
        'capabilities': [
            'http_requests',
            'rest_api',
            'json_processing',
            'async_support',
            'smart_caching'
        ],
        'supported_formats': [
            'url',
            'http_config',
            'rest_config'
        ],
        
        # 依赖信息
        'dependencies': [],
        'optional_dependencies': ['cache', 'monitoring'],
        
        # 元数据
        'author': 'SmartData Engine Team',
        'license': 'MIT',
        'tags': ['http', 'rest', 'api', 'network']
    }
]

def get_plugin_definitions():
    """获取插件定义"""
    return PLUGIN_DEFINITIONS

# 导出主要组件
from .http_processor import HttpProcessor
from .smart_loader import global_loader

__all__ = ['HttpProcessor', 'global_loader', 'get_plugin_definitions']
