"""
远程文件数据处理插件

提供远程文件系统访问能力，支持：
- SFTP/SSH 文件传输
- FTP/FTPS 文件传输
- HTTP/HTTPS 文件下载
- 云存储服务（AWS S3, 阿里云OSS, 腾讯云COS）
- 网络共享（SMB/CIFS）
- 连接池和缓存管理
"""

# 插件定义 - 按照统一标准
PLUGIN_DEFINITIONS = [
    {
        'id': 'remote_file_processor',
        'name': '远程文件数据处理器',
        'description': '企业级远程文件处理，支持多协议、智能缓存、断点续传等功能',
        'version': '2.0.0',
        'type': 'processor',
        'category': 'remote_file',
        'priority': 65,
        'class_name': 'RemoteFileProcessor',
        'module_file': 'remote_processor',
        'auto_load': True,
        'enabled': True,
        'capabilities': [
            'multi_protocol_support',
            'ftp_sftp_support',
            's3_compatible_storage',
            'webdav_support',
            'http_https_download',
            'intelligent_caching',
            'cache_strategy_config',
            'cache_invalidation',
            'distributed_cache',
            'resume_download',
            'concurrent_download',
            'progress_monitoring',
            'security_authentication',
            'connection_pooling',
            'retry_mechanism',
            'async_processing',
            'batch_processing'
        ],
        'supported_protocols': [
            'http', 'https', 'ftp', 'ftps', 'sftp', 's3', 'webdav', 'smb', 'cifs'
        ],
        'supported_features': [
            'download', 'upload', 'list', 'delete', 'move', 'copy',
            'resume', 'progress', 'auth', 'ssl', 'compression'
        ],
        'dependencies': [],
        'optional_dependencies': [
            'aiohttp', 'aioftp', 'paramiko', 'boto3', 'webdavclient3',
            'smbprotocol', 'redis', 'memcached'
        ],
        'author': 'SmartData Team',
        'license': 'MIT',
        'tags': ['remote', 'file', 'download', 'upload', 'cache']
    }
]

try:
    # 统一组件（包含企业级功能）
    from .remote_processor import RemoteFileProcessor, EnterpriseRemoteFileProcessor
    from .protocols import (
        HttpHandler,
        FtpHandler,
        SftpHandler,
        ProtocolFactory,
        AuthConfig,
        DownloadProgress,
        DownloadResult,
        # 企业级兼容接口
        EnterpriseHttpHandler,
        EnterpriseFtpHandler,
        EnterpriseSftpHandler,
        EnterpriseProtocolFactory
    )
    from .connection_pool import (
        RemoteConnectionPool,
        global_connection_pool,
        ConnectionStatus,
        # 企业级兼容接口
        EnterpriseConnectionPool
    )
    from .cache_manager import RemoteFileCacheManager
    from .intelligent_cache import (
        IntelligentCacheManager,
        global_cache_manager,
        CacheStrategy,
        CacheLevel
    )
    from .smart_remote_loader import (
        SmartRemoteFileLoader,
        global_remote_loader,
        download_file,
        download_files,
        download_file_async,
        download_files_async,
        upload_file_async,
        list_remote_files_async,
        get_cache_stats,
        get_connection_stats,
        clear_cache_async,
        cleanup_cache_async
    )

    __all__ = [
        # 主要处理器
        'RemoteFileProcessor',
        'EnterpriseRemoteFileProcessor',  # 兼容接口

        # 协议处理器
        'HttpHandler',
        'FtpHandler',
        'SftpHandler',
        'ProtocolFactory',
        'AuthConfig',
        'DownloadProgress',
        'DownloadResult',

        # 企业级兼容接口
        'EnterpriseHttpHandler',
        'EnterpriseFtpHandler',
        'EnterpriseSftpHandler',
        'EnterpriseProtocolFactory',

        # 连接池管理
        'RemoteConnectionPool',
        'EnterpriseConnectionPool',  # 兼容接口
        'global_connection_pool',
        'ConnectionStatus',

        # 缓存管理
        'RemoteFileCacheManager',
        'IntelligentCacheManager',
        'global_cache_manager',
        'CacheStrategy',
        'CacheLevel',

        # 智能加载器
        'SmartRemoteFileLoader',
        'global_remote_loader',
        'download_file',
        'download_files',
        'download_file_async',
        'download_files_async',
        'upload_file_async',
        'list_remote_files_async',
        'get_cache_stats',
        'get_connection_stats',
        'clear_cache_async',
        'cleanup_cache_async',

        # 插件定义
        'PLUGIN_DEFINITIONS',
        'get_plugin_definitions'
    ]

except ImportError as e:
    # 优雅处理导入错误
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"Remote File插件部分功能导入失败: {e}")

    # 保持基本功能可用
    from .remote_processor import RemoteFileProcessor

    __all__ = ['RemoteFileProcessor']


# 插件定义 - 符合插件标准规范
PLUGIN_DEFINITIONS = [
    {
        # 基础信息 (必需)
        'id': 'remote_file_processor',
        'name': '远程文件处理器',
        'description': '提供完整的远程文件处理能力，支持多种协议和企业级功能',
        'version': '2.0.0',
        'type': 'processor',
        'category': 'remote_file',
        'priority': 65,

        # 加载信息 (必需)
        'class_name': 'RemoteFileProcessor',
        'module_file': 'remote_processor',
        'auto_load': True,
        'enabled': True,

        # 功能信息 (推荐)
        'capabilities': [
            'multi_protocol_support',
            'intelligent_caching',
            'concurrent_download',
            'resume_download',
            'progress_monitoring',
            'security_authentication',
            'connection_pooling',
            'batch_processing',
            'async_processing'
        ],
        'supported_formats': [
            'http', 'https', 'ftp', 'ftps', 'sftp'
        ],

        # 依赖信息 (可选)
        'dependencies': [],
        'optional_dependencies': ['paramiko', 'aiohttp', 'aioftp'],

        # 元数据 (可选)
        'author': 'SmartData Engine Team',
        'license': 'MIT',
        'tags': ['remote', 'file', 'download', 'enterprise']
    }
]


def get_plugin_definitions():
    """获取插件定义 - 支持动态加载"""
    return PLUGIN_DEFINITIONS
