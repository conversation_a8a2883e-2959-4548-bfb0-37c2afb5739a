
# smartdata-engine Usage Guide

This guide explains how to use the `smartdata-engine`.

## Basic Usage:

```python
from smartdata_engine.core.engine import SmartDataEngine

engine = SmartDataEngine()

template = "Hello, {{ user.name }}!"
data = {"user": {"name": "World"}}

result = engine.render(template, data)
print(result) # Output: Hello, World!
```

## Advanced Features:

(To be added as features are implemented)
