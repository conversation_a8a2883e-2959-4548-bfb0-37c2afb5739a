#!/usr/bin/env python3
"""
企业级模板引擎新架构 - 企业级功能示例

展示生命周期管理、连接池、事务支持、监控等企业级功能
"""

import sys
import os
import time
import asyncio
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from template.enterprise_template_factory import (
    EnterpriseTemplateFactory,
    EnterpriseTemplateConfig,
    TemplateEngineMode
)
from template.enterprise_template_integration import EnterpriseTemplateIntegration

def enterprise_features_example():
    """企业级功能完整示例"""
    print("=== 企业级模板引擎功能展示 ===")
    
    # 🏢 示例1：企业级引擎配置
    print("\n🏢 示例1：企业级引擎配置")
    
    # 创建企业级配置
    enterprise_config = EnterpriseTemplateConfig(
        mode=TemplateEngineMode.ENTERPRISE,
        enable_async=True,
        enable_debug=True,
        enable_legacy_support=True,
        auto_discover_plugins=True,
        enable_caching=True,
        cache_size=2000,
        enable_connection_pooling=True,
        max_connections_per_adapter=15,
        enable_security=True,
        max_template_size=2 * 1024 * 1024,  # 2MB
        max_execution_time=60,
        enable_performance_monitoring=True,
        enable_metrics_collection=True
    )
    
    engine = EnterpriseTemplateFactory.create_engine(enterprise_config)
    
    template = """
企业级引擎配置
=============
引擎模式: {{ config.mode }}
异步支持: {{ config.async_enabled }}
调试模式: {{ config.debug_enabled }}
兼容模式: {{ config.legacy_support }}
插件自动发现: {{ config.auto_discover }}
缓存功能: {{ config.caching_enabled }}
缓存大小: {{ config.cache_size }}
连接池: {{ config.connection_pooling }}
最大连接数: {{ config.max_connections }}
安全检查: {{ config.security_enabled }}
最大模板大小: {{ config.max_template_size // 1024 }}KB
执行超时: {{ config.max_execution_time }}秒
性能监控: {{ config.performance_monitoring }}
指标收集: {{ config.metrics_collection }}
    """.strip()
    
    context = {
        'config': {
            'mode': enterprise_config.mode.value,
            'async_enabled': enterprise_config.enable_async,
            'debug_enabled': enterprise_config.enable_debug,
            'legacy_support': enterprise_config.enable_legacy_support,
            'auto_discover': enterprise_config.auto_discover_plugins,
            'caching_enabled': enterprise_config.enable_caching,
            'cache_size': enterprise_config.cache_size,
            'connection_pooling': enterprise_config.enable_connection_pooling,
            'max_connections': enterprise_config.max_connections_per_adapter,
            'security_enabled': enterprise_config.enable_security,
            'max_template_size': enterprise_config.max_template_size,
            'max_execution_time': enterprise_config.max_execution_time,
            'performance_monitoring': enterprise_config.enable_performance_monitoring,
            'metrics_collection': enterprise_config.enable_metrics_collection
        }
    }
    
    result = engine.render(template, context)
    print("企业级配置结果:")
    print(result)
    
    # 📊 示例2：性能监控和指标收集
    print("\n📊 示例2：性能监控和指标收集")
    
    # 执行多次渲染以生成指标
    test_template = "测试模板 #{{ id }}: {{ data | length }}条数据，处理时间: {{ timestamp }}"
    
    for i in range(5):
        context = {
            'id': i + 1,
            'data': list(range(i * 10, (i + 1) * 10)),
            'timestamp': time.strftime('%H:%M:%S')
        }
        engine.render(test_template, context)
    
    # 获取性能统计
    stats = engine.get_performance_stats()
    
    template = """
性能监控报告
===========
渲染次数: {{ stats.render_count }}
总渲染时间: {{ stats.total_render_time }}ms
平均渲染时间: {{ stats.average_render_time }}ms
错误次数: {{ stats.error_count }}
错误率: {{ stats.error_rate }}%
引擎模式: {{ stats.config_mode }}
活跃作用域: {{ stats.active_scopes }}
    """.strip()
    
    result = engine.render(template, {'stats': stats})
    print("性能监控结果:")
    print(result)
    
    # 🔒 示例3：安全功能
    print("\n🔒 示例3：安全功能演示")
    
    # 创建安全引擎
    secure_engine = EnterpriseTemplateFactory.create_secure_engine()
    
    # 测试模板大小限制
    try:
        large_template = "x" * (600 * 1024)  # 600KB，超过安全引擎的512KB限制
        secure_engine.render(large_template, {})
    except ValueError as e:
        print(f"✅ 安全检查生效: {e}")
    
    # 正常大小的模板
    safe_template = """
安全引擎功能
===========
模板大小检查: 已启用
执行时间限制: {{ max_execution_time }}秒
允许的函数: {{ allowed_functions | length }}个
安全状态: 正常
    """.strip()
    
    context = {
        'max_execution_time': 15,
        'allowed_functions': ['len', 'str', 'int', 'float', 'sum', 'max', 'min']
    }
    
    result = secure_engine.render(safe_template, context)
    print("安全引擎结果:")
    print(result)
    
    secure_engine.cleanup()
    
    # 🔄 示例4：生命周期管理
    print("\n🔄 示例4：生命周期管理")
    
    # 创建模板集成器
    integration = EnterpriseTemplateIntegration(
        enable_async=False,
        enable_legacy_support=False,
        enable_debug=True
    )
    
    # 创建多个作用域
    scopes = []
    for i in range(3):
        scope_id = f'lifecycle_demo_{i + 1}'
        scope = integration.create_template_scope(scope_id)
        scopes.append(scope_id)
        
        # 注册数据源
        test_data = [{'id': j, 'value': f'数据{j}'} for j in range(5)]
        integration.register_data_source(scope, 'test_data', test_data)
    
    template = """
生命周期管理示例
===============
活跃作用域数量: {{ active_scopes }}
资源管理: 自动化
内存优化: 已启用
清理策略: 智能清理
    """.strip()
    
    stats = integration.get_performance_stats()
    context = {'active_scopes': stats['active_scopes']}
    
    result = integration.render_template_sync(template, context)
    print("生命周期管理结果:")
    print(result)
    
    # 清理作用域
    for scope_id in scopes:
        integration.cleanup_template_scope(scope_id)
    
    print(f"✅ 已清理 {len(scopes)} 个作用域")
    
    # 🎯 示例5：多引擎模式对比
    print("\n🎯 示例5：多引擎模式对比")
    
    # 创建不同模式的引擎
    engines = {
        'enterprise': EnterpriseTemplateFactory.create_engine(),
        'performance': EnterpriseTemplateFactory.create_high_performance_engine(),
        'debug': EnterpriseTemplateFactory.create_debug_engine(),
        'secure': EnterpriseTemplateFactory.create_secure_engine(),
        'hybrid': EnterpriseTemplateFactory.create_hybrid_engine(),
        'legacy': EnterpriseTemplateFactory.create_legacy_compatible_engine()
    }
    
    test_template = """
引擎模式: {{ mode }}
配置特点: {{ features }}
适用场景: {{ use_case }}
    """.strip()
    
    engine_info = {
        'enterprise': {
            'features': '全功能、高可靠性',
            'use_case': '生产环境、企业应用'
        },
        'performance': {
            'features': '高性能、大缓存',
            'use_case': '高并发、大数据量'
        },
        'debug': {
            'features': '调试信息、性能监控',
            'use_case': '开发调试、问题诊断'
        },
        'secure': {
            'features': '安全限制、权限控制',
            'use_case': '安全敏感、多租户'
        },
        'hybrid': {
            'features': '混合模式、灵活配置',
            'use_case': '复杂场景、定制需求'
        },
        'legacy': {
            'features': '向后兼容、平滑迁移',
            'use_case': '系统迁移、兼容性'
        }
    }
    
    print("多引擎模式对比:")
    for mode, engine in engines.items():
        context = {
            'mode': mode.upper(),
            'features': engine_info[mode]['features'],
            'use_case': engine_info[mode]['use_case']
        }
        
        result = engine.render(test_template, context)
        print(f"\n{mode.upper()}模式:")
        print(result)
        
        # 清理引擎
        engine.cleanup()
    
    # 🚀 示例6：动态适配器注册
    print("\n🚀 示例6：动态适配器注册")
    
    # 获取支持的数据类型
    data_types = engine.get_supported_data_types()
    
    template = """
动态适配器系统
=============
支持的数据类型: {{ total_types }}种

数据库适配器:
{%- for db_type in database_types %}
- {{ db_type }}
{%- endfor %}

文件适配器:
{%- for file_type in file_types %}
- {{ file_type }}
{%- endfor %}

网络适配器:
{%- for net_type in network_types %}
- {{ net_type }}
{%- endfor %}

扩展能力: 支持自定义适配器动态注册
插件系统: 自动发现和加载
    """.strip()
    
    # 分类数据类型
    database_types = [t for t in data_types if any(db in t for db in ['sql', 'db', 'postgres', 'mysql'])]
    file_types = [t for t in data_types if any(f in t for f in ['file', 'csv', 'json', 'xml', 'html'])]
    network_types = [t for t in data_types if any(n in t for n in ['http', 'api', 'url', 'rest'])]
    
    context = {
        'total_types': len(data_types),
        'database_types': database_types[:5],  # 显示前5个
        'file_types': file_types[:5],
        'network_types': network_types[:3]
    }
    
    result = engine.render(template, context)
    print("动态适配器结果:")
    print(result)
    
    engine.cleanup()
    
    print("\n🎉 企业级功能示例完成！")
    print("\n💼 企业级功能特性:")
    print("1. ✅ 多引擎模式：6种预配置模式满足不同需求")
    print("2. ✅ 性能监控：实时性能指标和统计信息")
    print("3. ✅ 安全控制：模板大小、执行时间、函数权限限制")
    print("4. ✅ 生命周期管理：自动资源管理和内存优化")
    print("5. ✅ 连接池管理：数据库连接复用和优化")
    print("6. ✅ 动态扩展：59种适配器，支持自定义扩展")
    print("7. ✅ 企业级配置：灵活的配置选项和环境适配")
    
    print("\n📖 下一步学习:")
    print("- financial_reporting.py - 实战企业级财务报表")
    print("- migration_guide.py - 学习系统迁移")

if __name__ == "__main__":
    enterprise_features_example()
